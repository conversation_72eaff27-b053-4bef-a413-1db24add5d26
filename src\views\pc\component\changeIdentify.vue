<template>
      <div>
         <el-dialog title="" v-model="choiceIdentifyVisibleShow" width="456px" :close-on-click-modal='false'>
           
                <div class="header">
                  <img src="@/assets/pc/logo.png" alt="" class="header-bg">
                  <img src="@/assets/pc/dialog-del.png" alt="" class="dialog-del" @click="goCancel"/>
                </div>
                
                <div class="tab flex_around">
                    <div class="active" >身份选择</div>
                </div>
                 <div class="all">
                    <div class="identify flex_end"  @click="changeIdentify('teacher')">
                         <div class="flex_start teacher">
                                  <h4>我是老师</h4>
                                 <img src="@/assets/pc/circle.png" alt="" v-show="currentInfo.type!='teacher'">
                                 <img src="@/assets/pc/circled.png" alt="" v-show="currentInfo.type=='teacher'"  >
                         </div>
                    </div>
                     <div class="identify identify-student flex_end" @click="changeIdentify('student')">
                         <div class="flex_start teacher">
                                  <h4>我是学生</h4>
                                 <img src="@/assets/pc/circle.png" alt="" v-show="currentInfo.type!='student'"  >
                                 <img src="@/assets/pc/circled.png" alt="" v-show="currentInfo.type=='student'"  >
                         </div>
                    </div>
                 </div>
            <!-- 1 -->
         </el-dialog>
      </div>
</template>
<script setup>
import {reactive, ref} from 'vue'
import {storeToRefs} from 'pinia'
// import { useUserStore } from "@/store/modules/user"
import { getToken, } from "@/utils/cache/cookies"
import {sendCaptcha,resetPassword,getQrLoginCode,sendCaptchaToLogin} from '@/api/pc/index.js'
import {useUserInfoStore} from "@/store/modules/pc"
import { ElMessage } from 'element-plus'
let status=ref('EXPIRED')
// let userStore=useUserStore()
let useUserInfo=useUserInfoStore()
let { choiceIdentifyVisibleShow,qrloginStatus,currentInfo,allInfo } = storeToRefs(useUserInfo)
let goCancel=()=>{
    choiceIdentifyVisibleShow.value=false
}
let changeIdentify=(type)=>{
    let arr=allInfo.value.filter(item=>{
         return item.type==type
    })
    currentInfo.value=arr[0]
    choiceIdentifyVisibleShow.value=false
}
</script>
<style scoped lang='scss'>
.header{
    position: relative;
   .header-bg{
    width: 100%;
  }
  .dialog-del{
    position: absolute;
    top:24px;
    right: 24px;
    height: 16px;
    width: 16px;
    cursor: pointer;
  }
}
:deep(.el-dialog__header){
    display: none;
}
:deep(.el-overlay-dialog .el-dialog){
    padding: 0;
    border-radius: 7px;
}
:deep(.el-dialog__body){
    padding: 0;
    border-radius:8px ;
}
.tab{
    font-size: 18px;
    color: #6D6F75;
    padding: 24px 0  8px 0;
    z-index: 12;
    div{
        cursor: pointer;
        padding-bottom: 6px;
      
    }
    .active{
        font-size: 18px;
        color: #3071FF;
        font-weight: bold;
         background: url(@/assets/pc/bottom-tab.png) no-repeat;
         background-size: 74px 12px;
         background-position: center 100%;
     
    }
}
.all{
    padding: 0 20px 20px 20px;
    .identify{
        height: 130px;
        border-radius: 12px;
        width: 100%;
        margin-top: 24px;
        background:#EEF3FF url(@/assets/pc/changeteacher.png) no-repeat 10% 100%;
        background-size: 200px 90%;
        .teacher{
            padding-right: 40px;
            font-weight: bold;
            font-size: 20px;
            color: #2B2C33;
            line-height: 28px;
            img{
                width: 20px;
                height: auto;
                margin-left: 12px;
                cursor: pointer;
            }
        }
   }
   .identify-student{
        background:#EEF3FF url(@/assets/pc/changestudent.png) no-repeat 10% 100%;
        background-size: 200px 90%;
   }
}


 
</style>
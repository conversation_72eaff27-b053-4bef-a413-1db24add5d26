import { ref } from "vue"
import { pinia } from "@/store"
import { defineStore } from "pinia"
import { DeviceEnum } from "@/constants/app-key"

export const useAppStore = defineStore("app", () => {
  /** 设备类型 */
  const device = ref(DeviceEnum.Desktop)
  /** 切换设备类型 */
  const toggleDevice = (value) => {
    device.value = value
  }

  return { device, toggleDevice }
})

/**
 * 在 SPA 应用中可用于在 pinia 实例被激活前使用 store
 * 在 SSR 应用中可用于在 setup 外使用 store
 */
export function useAppStoreHook() {
  return useAppStore(pinia)
}

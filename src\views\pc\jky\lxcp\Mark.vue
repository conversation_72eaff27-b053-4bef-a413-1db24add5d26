<template>
    <div class="mark-main">
        <el-form :model="mark_form" ref="addRef" :rules="rules" class="search_form">
            <el-form-item label="初评分数（100分）" prop="reason" style="margin-right: 16px;">
                <el-input v-model="mark_form.reason" type="text" class="my-textarea" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="初评意见" prop="reason" style="margin-right: 16px;">
                <el-input v-model="mark_form.reason" :rows="4" type="textarea" class="my-textarea2" placeholder="请输入"></el-input>
            </el-form-item>
        </el-form>
        <div class="flex_center">
            <el-button class="plain-btn btn_96_48" type="plain" @click="emit('close')">取消</el-button>
            <el-button class="primary-btn btn_96_48" type="primary" @click="submitClick">确认</el-button>
        </div>
    </div>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    const formObj = function(obj = {}){
        this.reason = obj.reason || ''
    }

    const mark_form = ref(new formObj({}))
    onMounted(() => {
    })
</script>
<style lang="scss" scoped>
.mark-main{}
</style>
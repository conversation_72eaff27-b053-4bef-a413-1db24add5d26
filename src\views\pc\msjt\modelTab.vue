<template>
<div class="tab-box">
   <div class="model-tab flex_between">
        <div class="flex_1  left"></div>
        <div class="middle flex_between">
             <h3 class="logo">名师讲堂</h3>
             <ul class="flex_center tab-list" >
                <li :class="['flex_center','flex_column',$route.meta.model==1?'active':'']"  @click="goModel(1)">
                    <img src="@/assets/pc/msjt/live-active.png" alt="" v-show="$route.meta.model==1">
                    <img src="@/assets/pc/msjt/live.png" alt="" v-show="$route.meta.model!=1">
                    <p :style="$route.meta.model==1?'color:#1E78E6':''">直播间</p>
                </li>
                <li v-if="show_sch_export" :class="['flex_center','flex_column',$route.meta.model==2?'active':'']"  @click="goModel(2)">
                    <img src="@/assets/pc/msjt/check.png" alt=""  v-show="$route.meta.model!=2">
                     <img src="@/assets/pc/msjt/check-active.png" alt=""  v-show="$route.meta.model==2">
                    <p :style="$route.meta.model==2?'color:#1E78E6':''">直播审核</p>
                </li>
                <li :class="['flex_center','flex_column',$route.meta.model==3?'active':'']" @click="goModel(3)">
                    <img src="@/assets/pc/msjt/apply.png" alt=""  v-show="$route.meta.model!=3">
                    <img src="@/assets/pc/msjt/apply-active.png" alt=""  v-show="$route.meta.model==3">
                    <p :style="$route.meta.model==3?'color:#1E78E6':''">申请列表</p>
                </li>
             </ul>
            <div></div>
        </div>
        <div class="flex_1  right flex_start">
        </div>
    </div>
</div>
  
</template>
<script setup>
    import {ref, onMounted} from 'vue'
    import {useRouter} from 'vue-router'
    import {storeToRefs} from 'pinia'
    import { useUserInfoStore } from "@/store/modules/pc"
    let useUserInfo=useUserInfoStore()
    let { userInfo } = storeToRefs(useUserInfo)
    let router=useRouter()
    let toastShow=ref(false)
    let show_sch_export = ref(false)
    let goModel=(type)=>{
        if(type==1){
            router.push('/msjt/live/index')
        }else if(type==2){
            router.push('/msjt/check/index')
        }else if(type==3){
            router.push('/msjt/apply/index?type=1')
        }
    }
    let goUpNotice=()=>{ 
        router.push('/actResearchAssociation/notice/upload')
    }
    onMounted(() => {
        if(userInfo && userInfo.value.userTenants && userInfo.value.userTenants.length > 0){
            show_sch_export.value = userInfo.value.userTenants.some(item => (item.tenantType === 30 && item.isAdmin === true));
        }
    })
</script>
<style lang='scss' scoped>
.tab-box{
 background: #fff;
}
.model-tab{font-family: PingFangSC, PingFang SC;

    margin: 0 auto;
    .middle{
        width: 100%;
         max-width: 1040px;
       .logo{
        font-size: 22px;
        color: #2B2C33;
        font-weight: bold;
       }
       .tab-list{
        padding: 11px 0 0 0;
        min-width: 480px;
        li{
            font-size: 12px;
            cursor: pointer;
            color: #94959C;
            margin-right: 130px;
            &:last-child{
                margin-right: 0;
            }
            &::after{
                content: '';
                display: block;
                height: 4px;
                width: 100%;
                background: #fff;
                // background: linear-gradient( 270deg, #496EEE 0%, #0AC1FA 100%);
                margin-top: 6px;
            }
            img{
                width: 18px;
                height: auto;
                margin-bottom: 6px
            }
        }
        .active{
              &::after{
                content: '';
                display: block;
                height: 4px;
                width: 100%;
                // background: #fff;
                background: linear-gradient( 270deg, #496EEE 0%, #0AC1FA 100%);
                margin-top: 6px;
            }
        }
       }
    }
    .right{
        font-size: 16px;
       color: #2B2C33;
       font-weight: bold;
       position: relative;
       white-space:nowrap;
        img{
            width: 32px;
            height: auto;
            margin-right: 8px;
        }
      
        .up-notice{
            font-size: 14px;
            color: #FFFFFF;
            cursor: pointer;
            background: linear-gradient( 270deg, #508CFF 0%, #535DFF 100%);
             border-radius: 12px;
            padding: 0 10px;
             height: 24px;
             cursor: pointer;
             margin-right: 32px;
             white-space: nowrap;
             img{
                width: 14px;
                height: auto;
             }
        }
    }
}
</style>
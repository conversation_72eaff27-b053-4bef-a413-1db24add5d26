<template>
    <div class="comment-box mt_16 flex_column_between">
        <p class="comment-title">评论</p>
         <!-- -->
        <div style="position:relative;" :class="props.im_info.chatOpen && props.live_state == 1 ? 'has-input' : 'no-input'">
            <div class="comment-ul-container flex_1" ref="scrollContainer">
                <ul class="comment-ul" ref="commentList">
                    <li v-for="(item, index) in comment_list" :key="item.id || index" class="flex_start_1 text_14" 
                        :class="userInfo && userInfo.user && item.userId == userInfo.user.id ? 'flex_reverse' : ''">
                        <img class="comment-avatar" src="@/assets/pc/msjt/comment-avatar.png" alt="">
                        <div :class="userInfo && userInfo.user && item.userId == userInfo.user.id ? 'flex_column_end_end' : ''">
                            <p class="grey3">{{item.userName}}</p>
                            <p class="grey4 comment-content">{{item.content}}</p>
                        </div>
                    </li>
                </ul>
            </div>
            <div v-if="hasNewMessages && !shouldAutoScroll && newMessageCount!=0" class="new-message-indicator flex_center pointer" @click="scrollToBottom">
                <img src="@/assets/pc/msjt/more.png" style="width:10px;height:10px;margin-right:4px;" alt="">
                {{newMessageCount}} 条新消息
            </div>
        </div>
        <!-- <p>hasNewMessages: {{hasNewMessages}} ---- shouldAutoScroll:{{shouldAutoScroll}}  ----- newMessageCount:{{newMessageCount}} </p> -->
         <!---->
        <div class="comment-input-box" v-if="props.im_info.chatOpen && props.live_state == 1">
            <el-input 
                v-model="chat_text" 
                :rows="3" 
                class="my-textarea" 
                type="textarea" 
                placeholder="请输入你的评论" 
            />
                <!-- :disabled="!isChatReady" -->
            <div class="flex_end mt_10">
                <el-button 
                    class="primary-btn btn_64_32" 
                    type="primary" 
                    @click="send"
                    :loading="sending"
                >
                发送
                <!-- :disabled="!isChatReady || !chat_text.trim()" -->
                    <!-- {{ isChatReady ? '发送' : '加载中...' }} -->
                </el-button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { reactive, ref, onBeforeMount, onMounted, nextTick, watch } from 'vue'
import { userSign, chatList, imConfig } from '@/api/pc/msjt'
import TencentCloudChat from '@tencentcloud/chat';
import TIMUploadPlugin from 'tim-upload-plugin';
import { storeToRefs } from 'pinia'
import { useUserInfoStore } from "@/store/modules/pc"

let useUserInfo = useUserInfoStore()
let { loginVisibleShow, userInfo } = storeToRefs(useUserInfo)

const props = defineProps(['im_info','live_state'])

const SDKAppID = ref('');
const userID = ref('');
const userSig = ref('');

const chat = ref(null)
const chat_text = ref('')
const isChatReady = ref(false)
const loading = ref(false)
const finished = ref(false)
const errorMessage = ref('')
const sending = ref(false)
const comment_list = ref([])
const pageNum = ref(1) // 从第1页开始
const pageSize = ref(10)
const maxPage = ref(1)
const scrollContainer = ref(null)
const commentList = ref(null)
const isLoadingMore = ref(false)

const shouldAutoScroll = ref(true) // 是否应该自动滚动
const hasNewMessages = ref(false) // 是否有新消息
const newMessageCount = ref(0) // 新消息数量

// 生成唯一ID
const generateUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

const getConfig = async () => {
    let res = await imConfig()
    if(res){
        SDKAppID.value = res.data.SDKAppID
        getSign()
    }
}

// 获取用户签名
const getSign = async () => {
    try {
        console.log(userInfo.value.user.id,'userInfo.value.user.id')
        loading.value = true
        errorMessage.value = ''
        
        userID.value = userInfo.value && userInfo.value.user && userInfo.value.user.id ? userInfo.value.user.id : 'youke'
        // userID.value = userInfo.value && userInfo.value.user && userInfo.value.user.id ? userInfo.value.user.id : generateUUID()
        
        let res = await userSign(userID.value)
        if (res && res.data) {
            userSig.value = res.data
            await init()
        } else {
            throw new Error('获取用户签名失败')
        }
    } catch (error) {
        console.error('获取签名错误:', error)
        errorMessage.value = '初始化失败，请重试'
    } finally {
        loading.value = false
    }
}

// 集成SDK
const init = async () => {
    try {
        if (!SDKAppID.value || !userID.value || !userSig.value) {
            throw new Error('缺少必要的初始化参数')
        }
        
        let option = {
            SDKAppID: SDKAppID.value,
            userID: userID.value.toString(),
            userSig: userSig.value,
        }
        console.log(option, 'option')
        
        chat.value = TencentCloudChat.create(option);
        chat.value.setLogLevel(0);
        
        // 注册腾讯云即时通信IM上传插件
        chat.value.registerPlugin({'tim-upload-plugin': TIMUploadPlugin});
        
        // 登录Chat
        await chat.value.login({ userID: option.userID, userSig: option.userSig })
        console.log('登录成功')
        
        // 加入群组
        await joinGroup()

        await getMyProfile()
        
        // 设置监听器
        listener()
        
        // 获取历史消息
        await getHistory()
        
        isChatReady.value = true
    } catch (error) {
        console.error('初始化错误:', error)
        errorMessage.value = '连接失败，请重试'
    }
}

// 获取个人信息
const getMyProfile = async () => {
    let promise = chat.value.getMyProfile();
    promise.then(function(imResponse) {
        console.log(imResponse.data); // 个人资料 - Profile 实例
        updateMyProfile()
    }).catch(function(imError) {
    console.warn('getMyProfile error:', imError); // 获取个人资料失败的相关信息
    });
}

// 更新个人信息
const updateMyProfile = () => {
    // 修改个人标配资料
    let promise = chat.value.updateMyProfile({
        nick: userInfo.value.user.name,
        // avatar: 'http(s)://url/to/image.jpg',
        gender: TencentCloudChat.TYPES.GENDER_MALE,
        // selfSignature: '我的个性签名',
        allowType: TencentCloudChat.TYPES.ALLOW_TYPE_ALLOW_ANY
    });
    promise.then(function(imResponse) {
        console.log(imResponse.data); // 更新资料成功
    }).catch(function(imError) {
        console.warn('updateMyProfile error:', imError); // 更新资料失败的相关信息
    });
}


// 加入群组
const joinGroup = async () => {
    try {
        if (!props.im_info.groupId) {
            throw new Error('缺少群组ID')
        }
        
        let promise = chat.value.joinGroup({ 
            groupID: props.im_info.groupId, 
            type: TencentCloudChat.TYPES.GRP_AVCHATROOM 
        });
        
        const imResponse = await promise
        
        switch (imResponse.data.status) {
            case TencentCloudChat.TYPES.JOIN_STATUS_SUCCESS: 
                console.log(imResponse.data.group); 
                break;
            case TencentCloudChat.TYPES.JOIN_STATUS_ALREADY_IN_GROUP: 
                console.log('已经在群中'); 
                break;
            default:
                console.log('加入群组状态:', imResponse.data.status);
        }
    } catch (error) {
        console.error('加入群组错误:', error)
        errorMessage.value = '加入群组失败'
    }
}

// 发送消息
const send = async () => {
    if(!userInfo.value.user){
        loginVisibleShow.value=true
        return
    }
    if (!isChatReady.value || !chat_text.value.trim()) return
    
    try {
        sending.value = true
        // 创建消息实例
        let message = chat.value.createTextMessage({
            to: props.im_info.groupId,
            conversationType: TencentCloudChat.TYPES.CONV_GROUP,
            payload: {
                text: chat_text.value
            }
        });
        
        // 发送消息
        await chat.value.sendMessage(message)
        
        // 更新本地消息列表
        comment_list.value.push({
            id: Date.now(),
            userId: userInfo.value.user.id,
            userName: userInfo.value.user.name,
            content: chat_text.value
        })

        // 清空输入框
        chat_text.value = ''
        
        // 滚动到底部
        await scrollToBottom()
    } catch (error) {
        console.error('发送消息错误:', error)
        errorMessage.value = '发送消息失败，请重试'
    } finally {
        sending.value = false
    }
}

// 消息接收处理
const onMessageReceived = function(event) {
    console.log('接受消息', event)
    const messageList = event.data;
    messageList.forEach((message) => {
        if (message.type === TencentCloudChat.TYPES.MSG_TEXT) {
            console.log(message, 'message')
            // 添加到消息列表
            comment_list.value.push({
                id: message.id,
                userName: message.nick,
                content: message.payload.text
            })
        } 
    });

    // 记录消息前的滚动状态
    const wasAtBottom = isAtBottom()

    // 如果用户在底部，滚动到底部；否则显示新消息提示
    if (wasAtBottom) {
        scrollToBottom()
        hasNewMessages.value = false
        newMessageCount.value = 0
    } else {
        hasNewMessages.value = true
        let msg_list = messageList.filter(item => item.type === TencentCloudChat.TYPES.MSG_TEXT)
        newMessageCount.value += msg_list.length
    }
};


// 设置监听器
const listener = () => {
    if (chat.value) {
        chat.value.on(TencentCloudChat.EVENT.MESSAGE_RECEIVED, onMessageReceived);
    }
}

// 获取历史消息
const getHistory = async (isLoadMore = false) => {
    if(!props.im_info.groupId) return
    if (isLoadingMore.value || finished.value) return
    
    isLoadingMore.value = true
    
    try {
        if (!isLoadMore) {
            pageNum.value = 1 // 重置为第一页
        } else {
            pageNum.value++ // 加载下一页
        }
        
        let groupId = encodeURIComponent(props.im_info.groupId)
        let res = await chatList(groupId, { pageNum: pageNum.value, pageSize: pageSize.value })
        
        if (res && res.data && res.data.list) {
            if (isLoadMore) {
                // 加载更多，追加数据
                comment_list.value = [...res.data.list.reverse(), ...comment_list.value]
                
                // 记录滚动位置
                const prevHeight = commentList.value.scrollHeight - scrollContainer.value.scrollTop
                
                // 等待DOM更新后恢复滚动位置
                await nextTick()
                scrollContainer.value.scrollTop = commentList.value.scrollHeight - prevHeight
            } else {
                // 初始化或刷新，替换数据
                comment_list.value = res.data.list.reverse()
                
                // 等待DOM更新后滚动到底部
                await nextTick()
                await scrollToBottom()
            }
            
            maxPage.value = res.data.pages || 1
            finished.value = pageNum.value >= maxPage.value
        } else {
            finished.value = true
        }
    } catch (error) {
        console.error('获取历史消息错误:', error)
        errorMessage.value = '加载消息失败，请重试'
    } finally {
        isLoadingMore.value = false
    }
}

// 检查是否在底部
const isAtBottom = () => {
    if (!scrollContainer.value || !commentList.value) return true
    
    const { scrollTop, scrollHeight, clientHeight } = scrollContainer.value

    // 如果滚动条在底部附近，认为在底部
    return scrollHeight - scrollTop - clientHeight < 20
}

// 滚动到底部
const scrollToBottom = () => {
    return new Promise((resolve) => {
        nextTick(() => {
            if (scrollContainer.value && commentList.value) {
                scrollContainer.value.scrollTop = commentList.value.scrollHeight
            }
            resolve()
        })
    })
}

// 滚动事件处理
const handleScroll = () => {
    if (!scrollContainer.value || !commentList.value || isLoadingMore.value || finished.value) return
    
    const { scrollTop, scrollHeight, clientHeight } = scrollContainer.value
    
    // 滚动到顶部，加载更多
    if (scrollTop <= 5) {
        getHistory(true)
    }

    // 检查是否滚动到底部
    shouldAutoScroll.value = isAtBottom()
    hasNewMessages.value = shouldAutoScroll.value ? false : true
    newMessageCount.value = shouldAutoScroll.value ? 0 : newMessageCount.value
}

// 滚动事件处理
// const handleScroll = () => {
//     if (!scrollContainer.value || !commentList.value) return
    
//     // 检查是否滚动到底部
//     shouldAutoScroll.value = isAtBottom()
// }

onMounted(() => {
    getConfig()
    
    // 添加滚动事件监听
    if (scrollContainer.value) {
        scrollContainer.value.addEventListener('scroll', handleScroll)
    }
})

onBeforeMount(() => {
    // 清理资源
    if (scrollContainer.value) {
        scrollContainer.value.removeEventListener('scroll', handleScroll)
    }
    
    if (chat.value) {
        chat.value.off(TencentCloudChat.EVENT.MESSAGE_RECEIVED, onMessageReceived);
        logout()
    }
})

// 登出
const logout = () => {
    if (chat.value) {
        let promise = chat.value.logout();
        promise.then(function(imResponse) {
            console.log(imResponse.data); // 登出成功
        }).catch(function(imError) {
            console.warn('logout error:', imError);
        });
    }
}

// 监听props.im_info变化，重新加载数据
watch(() => props.im_info.groupId, (newVal, oldVal) => {
    if (newVal && newVal !== oldVal) {
        getHistory()
    }
})
</script>

<style lang="scss" scoped>
.comment-box{
    width: 271px;
    border-radius: 8px;
    background: #FFFFFF;
    padding: 24px 16px;
    // max-height: calc(100vh - 180px);
    overflow: auto;
}

.comment-title{
    font-weight: 600;
    font-size: 18px;
    color: #508CFF;
    line-height: 26px;
    text-align: left;
    border-bottom: 1px solid #508CFF;
    padding-bottom: 8px;
}

.comment-ul-container{
    margin-top: 10px;
    overflow: auto;
    // height: calc(100% - 60px); // 减去标题和输入框的高度
    position: relative;
    height: 100%;
    
    .comment-ul{
        // min-height: 100%;
        position: relative;
        // max-height: 360px;
        li{
            margin-top: 18px;
            
            .comment-avatar{
                width: 24px;
                height: 24px;
                margin-right: 8px;
            }
            
            .comment-content{
                background: #F6F9FB;
                padding: 8px 10px;
                margin-top: 14px;
                display: inline-block;
                text-align: justify;
            }
        }
    }
}

.comment-input-box{
    border: 1px solid #E2E3E6;
    background: #F4F5F7;
    border-radius: 8px;
    padding: 0 10px 10px;
    margin-top: 20px;
}

:deep(.el-textarea__inner){
    box-shadow: none;
}

.loading-indicator{
    text-align: center;
    padding: 10px;
    color: #999;
}

.no-more-data{
    text-align: center;
    padding: 10px;
    color: #999;
}

.flex_reverse{
    flex-direction: row-reverse;
    
    .comment-avatar{
        margin-right: 0;
        margin-left: 8px;
    }
    
    .comment-content{
        text-align: right;
    }
}
.new-message-indicator{
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 0);
    background: #FFFFFF;display: inline-block;width:fit-content;padding: 8px 14px;font-weight: 400;
font-size: 14px;
color: #508CFF;
line-height: 20px;
text-align: center;
box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.08);
border-radius: 18px;
border: 1px solid #E2E3E6;
}
.no-input{max-height: calc(100vh - 350px);}
.has-input{max-height: calc(100vh - 450px);height:100%;}
</style>    
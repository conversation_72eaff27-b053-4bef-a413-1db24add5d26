import { request } from "@/utils/service"

/** 获取VOD签名 */
export function getVodSignApi() {
  return request({
    url: "/vod/getVodSign",
    method: "get"
  })
}

export function getCos() {
  return request({
    url: "/common/cos",
    method: "get"
  })
}

// 上传文件
export function uploadFileApi(data) {
  return request({
    url: "fs/file",
    method: "post",
    data,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  })
}

// 下载文件
export function downloadFileApi(fileId) {
  return request({
    url: `fs/file/${fileId}`,
    method: "get"
  })
}
export function wxLogin(data) {
  return request({
    url: '/wx/login?code='+data.code,
    method: "post",
  })
}
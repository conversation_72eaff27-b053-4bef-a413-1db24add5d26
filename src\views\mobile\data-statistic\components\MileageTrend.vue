<script setup>
import { ref, watch, onMounted } from "vue"
import { EchartsUI, useEcharts } from "@/components/Echart"

const chartRef = ref()
const { renderEcharts } = useEcharts(chartRef)

const props = defineProps({
  chartData: {
    type: Object,
    default: () => ({
      data: [],
      color: []
    })
  }
})

watch(
  () => props.chartData,
  () => {
    paintChart()
  },
  { deep: true }
)

onMounted(() => {
  paintChart()
})

const paintChart = () => {
  const data = props.chartData.data
  const xAxisData = data.map(item => item.date)
  const seriesData = data.map(item => item.points)
  
  // 判断是否需要dataZoom
  const showDataZoom = xAxisData.length > 7

  renderEcharts({
    grid: {
      top: 20,
      left: 10,
      right: 20,
      bottom: showDataZoom ? 35 : 15, // 给dataZoom留空间
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#8DA2B5',
        fontSize: 10,
        overflow: 'break',
        align: 'center',
        interval: 0, // 全部显示
        // formatter去除，仅显示原始值
      }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: '#F0F3F7'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#8DA2B5',
        fontSize: 10
      }
    },
    series: [{
      data: seriesData,
      type: 'line',
      smooth: true,
      symbol: 'none',
      lineStyle: {
        width: 0
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 1,
          y: 0,
          x2: 0,
          y2: 0,
          colorStops: [{
            offset: 0,
            color: 'rgba(80, 140, 255, 0.3)'
          }, {
            offset: 1,
            color: 'rgba(2, 204, 250, 0.3)'
          }]
        }
      }
    }],
    ...(showDataZoom ? {
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: 0,
          height: 0, // 设置为0隐藏滚动条
          bottom: 0, // 滚动条高度为0时bottom也设为0
          start: 0,
          end: Math.round(7 / xAxisData.length * 100),
          handleSize: '80%',
          handleStyle: {
            color: '#5090ff'
          },
          borderColor: 'rgba(0,0,0,0)',
          backgroundColor: 'rgba(0,0,0,0)', // 背景透明
          fillerColor: 'rgba(0,0,0,0)', // 填充透明
          showDetail: false
        },
        {
          type: 'inside',
          xAxisIndex: 0,
          start: 0,
          end: Math.round(7 / xAxisData.length * 100),
          zoomOnMouseWheel: true,
          moveOnMouseMove: true,
          moveOnMouseWheel: true,
          throttle: 50
        }
      ]
    } : {})
  })
}
</script>

<template>
  <EchartsUI ref="chartRef" :height="'233px'" />
</template>

<style scoped></style>

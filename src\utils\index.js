import dayjs from "dayjs"

/** 格式化时间 */
export const formatDateTime = (time) => {
  return time ? dayjs(new Date(time)).format("YYYY-MM-DD HH:mm:ss") : "N/A"
}

/** 格式化日期 */
export const formatDate = (time) => {
  return time ? dayjs(new Date(time)).format("YYYY-MM-DD") : "N/A"
}

/** 用 JS 获取全局 css 变量 */
export const getCssVariableValue = (cssVariableName) => {
  let cssVariableValue = ""
  try {
    // 没有拿到值时，会返回空串
    cssVariableValue = getComputedStyle(document.documentElement).getPropertyValue(cssVariableName)
  } catch (error) {
    console.error(error)
  }
  return cssVariableValue
}

/** 用 JS 设置全局 CSS 变量 */
export const setCssVariableValue = (cssVariableName, cssVariableValue) => {
  try {
    document.documentElement.style.setProperty(cssVariableName, cssVariableValue)
  } catch (error) {
    console.error(error)
  }
}

// 获取url参数
export const getHashSearchParam = (key, url = window.location.href) => {
  // const url = window.location.href
  // 获取 hash 值，不包含 '#' 号
  const hash = url.substring(url.indexOf("#") + 1)
  // 查找 '?' 号所在的索引
  const searchIndex = hash.indexOf("?")
  // '?' 号后面接的是索引参数，如果找到则+1，去除'?' 号
  const search = searchIndex !== -1 ? hash.substring(searchIndex + 1) : ""
  // 把搜索参数字符串提过URLSearchParams转成对象形式
  const usp = new URLSearchParams(search)
  // 通过URLSearchParams自带的get方法，查询键所对应的值
  return usp.get(key)
}

// 导出 excel 文件
export function downloadExcelFile(binaryData, filename) {
  // 创建一个Blob对象，这里假设binaryData是一个Uint8Array或ArrayBuffer
  const blob = new Blob([binaryData], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" })

  // 创建一个指向Blob的URL
  const url = window.URL.createObjectURL(blob)

  // 创建一个a元素用于下载
  const a = document.createElement("a")
  a.href = url
  a.download = filename || "download.xlsx"

  // 将a元素添加到文档中，并触发点击事件
  document.body.appendChild(a)
  a.click()

  // 释放URL对象
  window.URL.revokeObjectURL(url)

  // 移除a元素
  document.body.removeChild(a)
}
  
export function getdate(type = "") {
  const myDate = new Date()
  const year = myDate.getFullYear() //获取当前年
  const mon = myDate.getMonth() + 1 //获取当前月
  const date = myDate.getDate() //获取当前日
  const sp = "-"
  if (type == "month") {
    return year + sp + mon
  } else {
    return year + sp + mon + sp + date
  }
}

export function randomString(len, charSet) {
  charSet = charSet || "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
  let randomString = ""
  for (let i = 0; i < len; i++) {
    const randomPoz = Math.floor(Math.random() * charSet.length)
    randomString += charSet.substring(randomPoz, randomPoz + 1)
  }
  return randomString
}

export function downloadFile(res, type, fileName) {
  const blob = new Blob([res], {
    type: type
  })
  
  // 检测是否为微信浏览器
  const isWechat = /MicroMessenger/i.test(navigator.userAgent)
  
  if (isWechat) {
    // 微信浏览器中的处理
    const reader = new FileReader()
    reader.onload = function(e) {
      // 创建一个临时的iframe
      const iframe = document.createElement('iframe')
      iframe.style.display = 'none'
      iframe.src = e.target.result
      document.body.appendChild(iframe)
      setTimeout(() => {
        document.body.removeChild(iframe)
        // 提示用户在浏览器中打开
        alert('请点击右上角按钮，选择"在浏览器中打开"，然后下载文件')
      }, 100)
    }
    reader.readAsDataURL(blob)
    return
  }
  
  // 非微信浏览器的正常处理
  const a = document.createElement("a")
  const URL = window.URL || window.webkitURL
  const href = URL.createObjectURL(blob)
  a.href = href
  a.download = fileName
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  window.URL.revokeObjectURL(href)
}

/**
 * 比较两个对象中共有属性是否完全相同
 * @param obj1 第一个对象
 * @param obj2 第二个对象
 * @returns boolean 如果共有属性都相同返回true，否则返回false
 */
export function compareCommonProperties(obj1, obj2) {
  // 获取两个对象的所有属性键
  const keys1 = Object.keys(obj1)
  const keys2 = Object.keys(obj2)

  // 找出共有的属性键
  const commonKeys = keys1.filter((key) => keys2.includes(key))

  // 比较每个共有属性的值
  return commonKeys.every((key) => obj1[key] === obj2[key])
}

export const getExtensionString = (str) => {
  var index = str.lastIndexOf('.')
  // 如果找到了第一个"."并且它不在字符串的起始处或结尾处
  if (index != -1 && index != 0 && index + 1 != str.length) {
      return str.substr(index + 1)
  } else {
      return ''
  }
}
export function getQueryParams(src,type) {
  const url = new URL(src);
  const params = url.searchParams.get(type);
  return params
}

export const getQueryVariable = (variable) => {
  var location = decodeURIComponent(window.location.search);
            var query = location.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == variable) { return pair[1]; }
            }
            return (false);
}

export function getWeekday(dateString) {
  const date = new Date(dateString);
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  return weekdays[date.getDay()];
}


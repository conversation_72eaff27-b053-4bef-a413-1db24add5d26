<script setup>
import { ref, onMounted, nextTick, onBeforeUnmount, watch, reactive, computed, onActivated, onDeactivated } from "vue"
import { useRouter } from "vue-router"
import dayjs from "dayjs"
import { showConfirmDialog, showSuccessToast } from "vant"
import {
  getAllDiary<PERSON>ist<PERSON>pi,
  getDirectorDiary<PERSON>pi,
  hideDiary<PERSON>pi,
  deleteDiary<PERSON><PERSON>,
  getMyGroupApi,
  getRecommendDiaryApi,
  getDirectorRecommendApi,
  getTutorDiaryApi,
  getTutorRecommendApi,
  getWxMpRecommendApi
} from "@/api/mobile/diarySquare"
import {
  recommendDiaryApi,
  cancelRecommendDiaryApi,
  likeDiary<PERSON>pi,
  favoriteDiaryApi
} from "@/api/mobile/memberOperation"
import { useUserStore } from "@/store/modules/user"
import DefaultAvatar from "@/assets/layouts/default-avatar.png"
const router = useRouter()
const userStore = useUserStore()

const isViceDirector = computed(() => userStore.roles?.includes("VICE_DIRECTOR"))
const isMember = computed(() => userStore.roles?.includes("MEMBER"))
const isDirector = computed(() => userStore.roles?.includes("DIRECTOR"))
const isTutor = computed(() => userStore.roles?.includes("TUTOR"))
const weekMap = {
  Monday: "周一",
  Tuesday: "周二",
  Wednesday: "周三",
  Thursday: "周四",
  Friday: "周五",
  Saturday: "周六",
  Sunday: "周日"
}

const myGroupList = ref([])
const getMyGroupList = async () => {
  try {
    const res = await getMyGroupApi()
    if (res) {
      myGroupList.value = res.data || []

      if (!isDirector.value) {
        myGroupList.value.unshift(
          {
            label: "全部",
            value: 0
          },
          {
            label: "社长日记",
            value: -1
          },
          {
            label: "社长推荐",
            value: -2
          },
          {
            label: "导师日记",
            value: -3
          },
          {
            label: "导师推荐",
            value: -4
          }
        )
      } else {
        myGroupList.value.unshift(
          {
            label: "全部",
            value: 0
          },
          {
            label: "社长日记",
            value: -1
          },
          {
            label: "社长推荐",
            value: -2
          },
          {
            label: "导师日记",
            value: -3
          },
          {
            label: "导师推荐",
            value: -4
          },
          {
            label: "公众号推荐",
            value: -5
          }
        )
      }
      activeTab.value = myGroupList.value[0].value
    }
  } catch (err) {
    console.log(err)
  }
}

const activeTab = ref(0)
const handleClickTab = (tab) => {
  activeTab.value = tab.name
  page.pageNum = 1
  finished.value = false
  // sortActions.value = sortActions.value.filter((item) => item.id !== "TUTOR_READ" && item.id !== "TUTOR_UNREAD")

  // if (tab.name !== -1 && tab.name !== -2 && tab.name !== -3 && tab.name !== -4) {

  //   if (userStore.userInfo?.xingZhiShe?.clubGroupId === tab.name || activeTab.value === 0) {

  //     sortActions.value = sortActions.value.concat([
  //       {
  //         id: "TUTOR_READ",
  //         name: "导师已阅"
  //       },
  //       {
  //         id: "TUTOR_UNREAD",
  //         name: "导师未阅"
  //       }
  //     ])
  //   }
  // }

  activeSortAction.value = "TIME_DESC"
  activeSortName.value = "时间排序(倒序)"
  getList()
  // 切换tab时滚动位置归0
  const listArea = document.querySelector(".list-area")
  if (listArea) {
    listArea.scrollTop = 0
  }
  scrollPosition.value = 0
  sessionStorage.removeItem("diarySquareScrollPosition")
}

// 打开排序弹窗
const sortSheetVisible = ref(false)
const activeSortAction = ref("TIME_DESC")
const activeSortName = ref("时间排序(倒序)")
const openSortSheet = () => {
  sortSheetVisible.value = true
}
const sortActions = ref([
  {
    id: "TIME_DESC",
    name: "时间排序(倒序)"
  },
  {
    id: "TIME_ASC",
    name: "时间排序(正序)"
  },
  {
    id: "VIEWS",
    name: "阅读数排序"
  },
  {
    id: "SCORE",
    name: "得星数排序"
  },
  {
    id: "EVALUATIONS",
    name: "评价数排序"
  },
  {
    id: "COMMENTS",
    name: "评论数排序"
  },
  {
    id: "LIKES",
    name: "点赞数排序"
  }
  // {
  //   id: "TUTOR_READ",
  //   name: "导师已阅"
  // },
  // {
  //   id: "TUTOR_UNREAD",
  //   name: "导师未阅"
  // }
])
const selectSortAction = (item) => {
  activeSortAction.value = item.id
  activeSortName.value = item.name
  sortSheetVisible.value = false
  page.pageNum = 1
  finished.value = false
  getList()
}

// 高级筛选相关的状态
const filterOptions = reactive({
  isViewed: null,
  isLike: null,
  isCommented: null,
  tutorIsRead: null
})

// 保存的筛选条件
const savedFilterOptions = reactive({
  isViewed: null,
  isLike: null,
  isCommented: null,
  startDate: "",
  endDate: "",
  tutorIsRead: null
})

// 开始日期和结束日期分开显示
const startDate = ref("")
const endDate = ref("")

// 打开高级筛选弹窗
const showHighFilterDialog = ref(false)
const openHighFilterDialog = () => {
  // 恢复保存的筛选条件
  filterOptions.isViewed = savedFilterOptions.isViewed
  filterOptions.isLike = savedFilterOptions.isLike
  filterOptions.isCommented = savedFilterOptions.isCommented
  filterOptions.tutorIsRead = savedFilterOptions.tutorIsRead
  startDate.value = savedFilterOptions.startDate
  endDate.value = savedFilterOptions.endDate
  showHighFilterDialog.value = true
}

// 重置高级筛选
const resetHighFilter = () => {
  // 重置临时筛选条件
  filterOptions.isViewed = null
  filterOptions.isLike = null
  filterOptions.isCommented = null
  filterOptions.tutorIsRead = null
  startDate.value = ""
  endDate.value = ""

  // 同时重置保存的筛选条件
  savedFilterOptions.isViewed = null
  savedFilterOptions.isLike = null
  savedFilterOptions.isCommented = null
  savedFilterOptions.tutorIsRead = null
  savedFilterOptions.startDate = ""
  savedFilterOptions.endDate = ""

  activeFilterDate.value = []
  activeFilterDateForHtml.value = ""
  page.pageNum = 1
  finished.value = false
  getList()
  showHighFilterDialog.value = false
}

// 确认高级筛选
const confirmHighFilter = () => {
  // 保存筛选条件
  savedFilterOptions.isViewed = filterOptions.isViewed
  savedFilterOptions.isLike = filterOptions.isLike
  savedFilterOptions.isCommented = filterOptions.isCommented
  savedFilterOptions.tutorIsRead = filterOptions.tutorIsRead
  savedFilterOptions.startDate = startDate.value
  savedFilterOptions.endDate = endDate.value

  // 同步到查询参数
  activeFilterDate.value = startDate.value && endDate.value ? [startDate.value, endDate.value] : []
  if (startDate.value && endDate.value) {
    activeFilterDateForHtml.value = `${startDate.value}至${endDate.value}`
  } else {
    activeFilterDateForHtml.value = ""
  }

  page.pageNum = 1
  finished.value = false
  getList()
  showHighFilterDialog.value = false
}

// 修改日期筛选函数
const handleFilterDateConfirm = (value) => {
  activeFilterDate.value = [dayjs(value?.[0]).format("YYYY-MM-DD"), dayjs(value?.[1]).format("YYYY-MM-DD")]
  startDate.value = dayjs(value?.[0]).format("YYYY-MM-DD")
  endDate.value = dayjs(value?.[1]).format("YYYY-MM-DD")
  activeFilterDateForHtml.value = `${dayjs(value?.[0]).format("YYYY-MM-DD")}至${dayjs(value?.[1]).format("YYYY-MM-DD")}`
  filterDateSheetVisible.value = false
}

// 打开筛选日期弹窗
const filterDateSheetVisible = ref(false)
const activeFilterDate = ref([])
const activeFilterDateForHtml = ref("")
const minDate = ref()
const maxDate = ref()
const openFilterDateSheet = () => {
  // 获取年月日
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1
  const day = now.getDate()
  maxDate.value = new Date(year, month, day)
  minDate.value = new Date(year, month - 6, day)
  filterDateSheetVisible.value = true
}

const filterDateSheetRef = ref()
const resetFilterDate = () => {
  filterDateSheetRef.value?.reset()
  activeFilterDate.value = []
  activeFilterDateForHtml.value = ""
  filterDateSheetVisible.value = false
  page.pageNum = 1
  finished.value = false
  getList()
}

const page = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 获取列表
const list = ref([])
const loading = ref(false)
const finished = ref(false)
const scrollPosition = ref(0)

const getList = async () => {
  try {
    loading.value = true

    const orderBy = activeSortAction.value.startsWith("TIME")
      ? activeSortAction.value.split("_")[0]
      : activeSortAction.value === "TUTOR_READ" || activeSortAction.value === "TUTOR_UNREAD"
        ? ""
        : activeSortAction.value
    const orderRule = activeSortAction.value.startsWith("TIME") ? activeSortAction.value.split("_")[1] : ""

    const query = {
      pageNum: page.pageNum,
      pageSize: page.pageSize,
      clubGroupId:
        activeTab.value === 0 ||
        activeTab.value === -1 ||
        activeTab.value === -2 ||
        activeTab.value === -3 ||
        activeTab.value === -4
          ? null
          : activeTab.value,
      orderBy,
      orderRule,
      beginRecordDate: activeFilterDate.value?.[0] ? dayjs(activeFilterDate.value?.[0]).format("YYYY-MM-DD") : "",
      endRecordDate: activeFilterDate.value?.[1] ? dayjs(activeFilterDate.value?.[1]).format("YYYY-MM-DD") : "",
      score: filterOptions.tutorIsRead === false ? -1 : undefined,
      isViewed: filterOptions.isViewed,
      isLike: filterOptions.isLike,
      isCommented: filterOptions.isCommented
    }

    let res

    if (activeTab.value === -1) {
      res = await getDirectorDiaryApi(query)
    } else if (activeTab.value === -2) {
      res = await getDirectorRecommendApi(query)
    } else if (activeTab.value === -3) {
      res = await getTutorDiaryApi(query)
    } else if (activeTab.value === -4) {
      res = await getTutorRecommendApi(query)
    } else if (activeTab.value === -5) {
      res = await getWxMpRecommendApi(query)
    } else {
      res = await getAllDiaryListApi(query)
    }

    if (res) {
      res.data.list.forEach((item) => {
        item.score = item.score === -1 || item.score === null ? 0 : item.score
      })
      if (page.pageNum === 1) {
        list.value = res.data.list
      } else {
        list.value = [...list.value, ...res.data.list]
      }
      page.total = res.data.total
      if (list.value?.length >= page.total) {
        finished.value = true
      } else {
        page.pageNum++
      }
    }

    loading.value = false
  } catch (err) {
    console.log(err)
  }
}

// 删除
const handleDelete = (id) => {
  showConfirmDialog({
    title: "删除",
    message: "确定删除该日记吗？",
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    confirmButtonColor: "#508CFF",
    cancelButtonColor: "#C7CFDF",
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      try {
        const res = await deleteDiaryApi(id)
        if (res) {
          showSuccessToast("删除成功")
          list.value = list.value.filter((item) => item.id !== id)
        }
      } catch (err) {
        console.log(err)
      }
    })
    .catch(() => {})
}

// 隐藏
const handleHidden = (id, isNormal) => {
  showConfirmDialog({
    title: "隐藏",
    message: "确定隐藏该日记吗？",
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    confirmButtonColor: "#508CFF",
    cancelButtonColor: "#C7CFDF",
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      try {
        const res = await hideDiaryApi(id, {
          isSetNormal: isNormal
        })
        if (res) {
          showSuccessToast("隐藏成功")
          list.value.find((item) => item.id === id).isHidden = !list.value.find((item) => item.id === id).isHidden
        }
      } catch (err) {
        console.log(err)
      }
    })
    .catch(() => {})
}

const clickLike = async (row, isLike) => {
  try {
    if (!row.isLikeCapable) {
      return
    }
    const res = await likeDiaryApi(row.id)
    if (res) {
      const item = list.value.find((item) => item.id === row.id)
      item.isLike = !item.isLike
      item.likes = Number(item.likes) + (isLike ? 1 : -1)
      showSuccessToast(isLike ? "点赞成功" : "取消点赞成功")
    }
  } catch (err) {
    console.log(err)
  }
}

const clickFavorite = async (id, isFavorite) => {
  try {
    const res = await favoriteDiaryApi(id, {
      favorite: isFavorite
    })
    if (res) {
      list.value.find((item) => item.id === id).isFavorite = !list.value.find((item) => item.id === id).isFavorite
      list.value.find((item) => item.id === id).favorites =
        Number(list.value.find((item) => item.id === id).favorites) + (isFavorite ? 1 : -1)
      showSuccessToast(isFavorite ? "收藏成功" : "取消收藏成功")
    }
  } catch (err) {
    console.log(err)
  }
}

// 存储溢出状态的对象
const textOverflowMap = ref({})
// 存储已展开项的对象
const expandedItems = ref({})

// 检查文本是否溢出
const isTextOverflow = (id) => {
  return textOverflowMap.value[id] || false
}

// 添加保存滚动位置的方法
const saveScrollPosition = () => {
  const listArea = document.querySelector(".list-area")
  if (listArea) {
    scrollPosition.value = listArea.scrollTop
    // 同时保存到 sessionStorage，以防页面刷新
    console.log("页面离开，保存滚动位置", scrollPosition.value)
    sessionStorage.setItem("diarySquareScrollPosition", scrollPosition.value.toString())
  }
}

// 添加恢复滚动位置的方法
const restoreScrollPosition = () => {
  nextTick(() => {
    const listArea = document.querySelector(".list-area")
    if (listArea) {
      // 优先使用 ref 中保存的位置，如果没有则尝试从 sessionStorage 获取
      const savedPosition = scrollPosition.value || Number(sessionStorage.getItem("diarySquareScrollPosition"))
      if (savedPosition) {
        listArea.scrollTop = savedPosition
      }
    }
  })
}

// 修改 goToDetail 方法
const goToDetail = (id) => {
  // 保存滚动位置
  saveScrollPosition()

  const orderBy = activeSortAction.value.startsWith("TIME")
    ? activeSortAction.value.split("_")[0]
    : activeSortAction.value === "TUTOR_READ" || activeSortAction.value === "TUTOR_UNREAD"
      ? ""
      : activeSortAction.value
  const orderRule = activeSortAction.value.startsWith("TIME") ? activeSortAction.value.split("_")[1] : ""

  const query = {
    clubGroupId:
      activeTab.value === 0 ||
      activeTab.value === -1 ||
      activeTab.value === -2 ||
      activeTab.value === -3 ||
      activeTab.value === -4
        ? null
        : activeTab.value,
    orderBy,
    orderRule,
    beginRecordDate: activeFilterDate.value?.[0] ? dayjs(activeFilterDate.value?.[0]).format("YYYY-MM-DD") : "",
    endRecordDate: activeFilterDate.value?.[1] ? dayjs(activeFilterDate.value?.[1]).format("YYYY-MM-DD") : "",
    score: filterOptions.tutorIsRead === false ? -1 : undefined
  }

  sessionStorage.setItem("m-diaryLocation", JSON.stringify(query))
  sessionStorage.setItem("m-diaryId", JSON.stringify(id))
  router.push({
    path: "/personalSpace/detail",
    query: {
      id,
      from: "diarySquare",
      activeTab: activeTab.value
    }
  })
}

// 添加新的计算方法
const isLastLineOverflow = (id) => {
  const contentText = document.querySelector(`[data-id="${id}"] .content-text`)
  if (!contentText) return false

  // 获取行高
  const lineHeight = parseInt(window.getComputedStyle(contentText).lineHeight)
  // 内容实际高度
  const actualHeight = contentText.scrollHeight
  // 三行的理论高度
  const threeLineHeight = lineHeight * 3

  // 如果实际高度大于三行高度，说明最后一行是被截断的
  return actualHeight > threeLineHeight
}

// 修改原有的 checkTextOverflow 方法
const checkTextOverflow = () => {
  nextTick(() => {
    const items = document.querySelectorAll(".list-item")
    items.forEach((item) => {
      const id = item.getAttribute("data-id")
      const contentText = item.querySelector(".content-text")
      if (contentText) {
        // 检查元素是否溢出
        const isOverflow = contentText.scrollHeight > contentText.clientHeight
        textOverflowMap.value[id] = isOverflow
      }
    })
  })
}

watch(
  () => list.value,
  () => {
    nextTick(() => {
      checkTextOverflow()
    })
  },
  { deep: true }
)

const handleRecommend = (diary) => {
  showConfirmDialog({
    title: "提示",
    message: `是否${diary.isRecommended ? "取消" : "设为"}推荐`,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    confirmButtonColor: "#508CFF"
  }).then(async () => {
    try {
      let res

      if (diary.isRecommended) {
        res = await cancelRecommendDiaryApi(diary.id, {
          recommendType: "1"
        })
      } else {
        res = await recommendDiaryApi(diary.id, {
          recommendType: "1"
        })
      }
      if (res) {
        showSuccessToast(diary.isRecommended ? "取消推荐成功" : "设为推荐成功")
        // 保存当前滚动位置
        saveScrollPosition()
        // 重新请求列表
        await restoreGetList()
        // 恢复滚动位置
        restoreScrollPosition()
      }
    } catch (err) {
      console.log(err)
    }
  })
}

const handleWxMpRecommend = (diary) => {
  showConfirmDialog({
    title: "提示",
    message: `是否${diary.isWxMpRecommended ? "取消" : "设为"}公众号推荐`,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    confirmButtonColor: "#508CFF"
  }).then(async () => {
    try {
      let res

      if (diary.isWxMpRecommended) {
        res = await cancelRecommendDiaryApi(diary.id, {
          recommendType: "2"
        })
      } else {
        res = await recommendDiaryApi(diary.id, {
          recommendType: "2"
        })
      }
      if (res) {
        showSuccessToast(diary.isWxMpRecommended ? "取消公众号推荐成功" : "设为公众号推荐成功")
        // 保存当前滚动位置
        saveScrollPosition()
        // 重新请求列表
        finished.value = false
        await restoreGetList()
        // 恢复滚动位置
        restoreScrollPosition()
      }
    } catch (err) {
      console.log(err)
    }
  })
}

onMounted(() => {
  getMyGroupList()
  getList()
  // 在列表加载后检查文本溢出
  nextTick(() => {
    checkTextOverflow()
    window.addEventListener("resize", checkTextOverflow)
  })
})

onActivated(() => {
  // 从详情页或用户日记列表返回时，重新获取列表并更新点赞和收藏状态
  if (list.value.length > 0) {
    refreshListStatus()
    // 恢复滚动位置
    restoreScrollPosition()
  }
})

// 添加 deactivated 钩子
onDeactivated(() => {
  // 页面离开前保存滚动位置
  // saveScrollPosition()
})

// 修改 refreshListStatus 方法，避免重置列表
const refreshListStatus = async () => {
  try {
    // 保存当前列表状态
    const currentList = [...list.value]

    // 获取最新数据
    let res
    const query = {
      pageNum: 1,
      pageSize: list.value.length, // 获取与当前列表相同数量的数据
      clubGroupId:
        activeTab.value === 0 ||
        activeTab.value === -1 ||
        activeTab.value === -2 ||
        activeTab.value === -3 ||
        activeTab.value === -4
          ? null
          : activeTab.value,
      orderBy: activeSortAction.value.startsWith("TIME")
        ? activeSortAction.value.split("_")[0]
        : activeSortAction.value === "TUTOR_READ" || activeSortAction.value === "TUTOR_UNREAD"
          ? ""
          : activeSortAction.value,
      orderRule: activeSortAction.value.startsWith("TIME") ? activeSortAction.value.split("_")[1] : "",
      beginRecordDate: activeFilterDate.value?.[0] ? dayjs(activeFilterDate.value?.[0]).format("YYYY-MM-DD") : "",
      endRecordDate: activeFilterDate.value?.[1] ? dayjs(activeFilterDate.value?.[1]).format("YYYY-MM-DD") : "",
      score: filterOptions.tutorIsRead === false ? -1 : undefined,
      isViewed: filterOptions.isViewed,
      isLike: filterOptions.isLike,
      isCommented: filterOptions.isCommented
    }

    if (activeTab.value === -1) {
      res = await getDirectorDiaryApi(query)
    } else if (activeTab.value === -2) {
      res = await getDirectorRecommendApi(query)
    } else if (activeTab.value === -3) {
      res = await getTutorDiaryApi(query)
    } else if (activeTab.value === -4) {
      res = await getTutorRecommendApi(query)
    } else if (activeTab.value === -5) {
      res = await getWxMpRecommendApi(query)
    } else {
      res = await getAllDiaryListApi(query)
    }

    if (res && res.data && res.data.list) {
      // 创建一个映射表，用于快速查找新数据
      const newDataMap = {}
      res.data.list.forEach((item) => {
        newDataMap[item.id] = item
      })

      // 过滤掉在新数据中不存在的旧数据，并更新现有数据的状态
      list.value = currentList
        .filter((item) => {
          // 如果在新数据中不存在，则删除该条数据
          return newDataMap[item.id] !== undefined
        })
        .map((item) => {
          const newItem = newDataMap[item.id]
          if (newItem) {
            return {
              ...item,
              ...newItem,
              score: newItem.score === -1 || newItem.score === null ? 0 : newItem.score
            }
          }
          return item
        })
    }
  } catch (err) {
    console.log(err)
  }
}

onBeforeUnmount(() => {
  window.removeEventListener("resize", checkTextOverflow)
})

// 添加新的方法来处理展开/收起
const toggleContent = (id, event) => {
  event.stopPropagation()
  expandedItems.value[id] = !expandedItems.value[id]
}

// 添加一个解码HTML实体的函数
const decodeHtmlEntities = (text) => {
  if (!text) return ""
  const textArea = document.createElement("textarea")
  textArea.innerHTML = text
  return textArea.value
}

const goToUserRecord = (item) => {
  // 保存当前滚动位置
  saveScrollPosition()

  router.push({
    path: "/userRecord",
    query: {
      userId: item.userId,
      userName: item.userName
    }
  })
}

// 添加 restoreGetList 方法，用于恢复列表状态
const restoreGetList = async () => {
  try {
    console.log("restoreGetList")

    // 保存当前的页码和每页数量
    const currentPageNum = page.pageNum
    const currentPageSize = page.pageSize
    // 修改为获取所有当前已加载的数据
    page.pageSize = page.pageNum * page.pageSize
    page.pageNum = 1

    loading.value = true
    finished.value = false

    const orderBy = activeSortAction.value.startsWith("TIME")
      ? activeSortAction.value.split("_")[0]
      : activeSortAction.value === "TUTOR_READ" || activeSortAction.value === "TUTOR_UNREAD"
        ? ""
        : activeSortAction.value
    const orderRule = activeSortAction.value.startsWith("TIME") ? activeSortAction.value.split("_")[1] : ""

    const query = {
      pageNum: page.pageNum,
      pageSize: page.pageSize,
      clubGroupId:
        activeTab.value === 0 ||
        activeTab.value === -1 ||
        activeTab.value === -2 ||
        activeTab.value === -3 ||
        activeTab.value === -4
          ? null
          : activeTab.value,
      orderBy,
      orderRule,
      beginRecordDate: activeFilterDate.value?.[0] ? dayjs(activeFilterDate.value?.[0]).format("YYYY-MM-DD") : "",
      endRecordDate: activeFilterDate.value?.[1] ? dayjs(activeFilterDate.value?.[1]).format("YYYY-MM-DD") : "",
      score: filterOptions.tutorIsRead === false ? -1 : undefined,
      isViewed: filterOptions.isViewed,
      isLike: filterOptions.isLike,
      isCommented: filterOptions.isCommented
    }

    let res

    if (activeTab.value === -1) {
      res = await getDirectorDiaryApi(query)
    } else if (activeTab.value === -2) {
      res = await getDirectorRecommendApi(query)
    } else if (activeTab.value === -3) {
      res = await getTutorDiaryApi(query)
    } else if (activeTab.value === -4) {
      res = await getTutorRecommendApi(query)
    } else if (activeTab.value === -5) {
      res = await getWxMpRecommendApi(query)
    } else {
      res = await getAllDiaryListApi(query)
    }

    if (res) {
      list.value = res.data.list.map((item) => ({
        ...item,
        score: item.score === -1 || item.score === null ? 0 : item.score
      }))
      page.total = res.data.total

      // 恢复原来的页码和每页数量
      page.pageNum = currentPageNum
      page.pageSize = currentPageSize

      // 根据已加载的数据判断是否已完成
      if (list.value?.length >= page.total) {
        finished.value = true
      }
    }

    loading.value = false
  } catch (err) {
    console.log(err)
  }
}
</script>

<template>
  <div class="diary-square">
    <header class="header-area flex-between-center">
      <van-tabs v-model:active="activeTab" @click-tab="handleClickTab" swipe-threshold="3">
        <van-tab :name="item.value" v-for="item in myGroupList" :key="item.id">
          <template #title>
            <div class="flex-start-center">
              <span class="m-pl4">{{ item.label }}</span>
            </div>
          </template>
        </van-tab>
      </van-tabs>
    </header>
    <div class="flex-between-center sort-area">
      <div class="sort-item flex-start-center" @click="openSortSheet">
        <span class="m-text-14-22-400 grey1 pr5">{{ activeSortName || "时间排序" }}</span>
        <img src="@/assets/mobile/personal-space/arrow-down.png" alt="" width="8" height="6" />
      </div>
      <div class="flex-between-center">
        <div class="sort-item flex-start-center" @click="openHighFilterDialog">
          <span
            class="m-text-14-22-400 grey1 pr5"
            :class="{
              'has-filter':
                filterOptions.isViewed === false ||
                filterOptions.isLike === false ||
                filterOptions.isCommented === false ||
                startDate ||
                endDate
            }"
            >{{ "高级筛选" }}</span
          >
          <img src="@/assets/mobile/personal-space/arrow-down.png" alt="" width="8" height="6" />
        </div>
        <img
          class="m-ml12"
          src="@/assets/mobile/personal-space/reset.png"
          alt=""
          width="18"
          height="16"
          @click="resetHighFilter"
        />
      </div>
    </div>
    <section class="list-area">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="getList"
        :immediate-check="false"
      >
        <div
          class="list-item flex-start-start m-mb12"
          v-for="item in list"
          :key="item.id"
          :data-id="item.id"
          @click="goToDetail(item.id)"
        >
          <div class="left-box" @click.stop="goToUserRecord(item)">
            <img :src="item.userAvatarUrl || DefaultAvatar" alt="" width="32" height="32" style="border-radius: 50%" />
          </div>
          <div class="right-box flex-1 m-pl5">
            <div class="right-top flex-between-center">
              <span class="flex-start-center">
                <span @click.stop="goToUserRecord(item)" class="m-text-14-20-600 grey1 m-pr8">{{ item.userName }}</span>

                <img
                  v-if="item?.isActiveStar"
                  src="@/assets/mobile/data-statistic/active-star.png"
                  alt=""
                  width="20"
                  height="20"
                />
                <img
                  v-if="item?.isAwesomeAuthor"
                  src="@/assets/mobile/data-statistic/good-creator.png"
                  alt=""
                  width="20"
                  height="20"
                />
                <span class="m-text-12-18-400 grey3 m-pl8">{{ item.recordDate }}</span>
                <div class="flex-start-center mileage" :class="item.points < 0 ? 'grey' : ''">
                  <img v-if="item.points >= 0" src="@/assets/mobile/act-mileage/my-mileage/plus-step.png" alt="" />
                  <img v-else src="@/assets/mobile/act-mileage/my-mileage/minus-step.png" alt="" />
                  <span class="m-text-12-18-400">{{ item.points }}</span>
                </div>
              </span>
            </div>
            <div class="m-text-14-20-600 m-pt2 m-pb2">
              <span class="m-text-14-20-600" v-if="item.title">标题：</span>{{ item.title }}
            </div>
            <div class="right-middle">
              <div class="content-wrapper">
                <!-- 注释掉展开功能的类绑定 -->
                <p class="m-text-14-20-400 grey1 content-text" v-html="decodeHtmlEntities(item.content)"></p>
                <!-- <div class="ellipsis-container with-ellipsis" v-if="isTextOverflow(item.id) && !expandedItems[item.id]">
                  <span class="ellipsis" v-if="isLastLineOverflow(item.id)">...</span>
                  <span class="m-text-14-20-400 blue1 show-all" @click.stop="toggleContent(item.id, $event)">全部</span>
                </div>
                <div class="ellipsis-container" v-if="expandedItems[item.id]">
                  <span class="m-text-14-20-400 blue1 show-all" @click.stop="toggleContent(item.id, $event)">收起</span>
                </div> -->
              </div>
            </div>
            <div class="right-bottom flex-between-start">
              <div
                class="flex-start-center flex-wrap"
                v-if="item.userClubRole !== 'DIRECTOR' && item.topics?.length > 0"
              >
                <div
                  v-for="label in item.topics"
                  :key="label.id"
                  class="label m-text-12-18-400 grey1 m-mr8 flex-start-center m-mb5"
                >
                  <img src="@/assets/mobile/personal-space/label.png" alt="" width="12" height="12" />
                  <span class="m-ml4">{{ label.name }}</span>
                </div>
                <van-rate
                  v-if="item.userClubRole === 'LEADER' || item.userClubRole === 'MEMBER'"
                  class="m-pb5"
                  v-model="item.score"
                  :size="14"
                  color="#FFB160"
                  void-icon="star"
                  void-color="#C7CFDF"
                  :count="6"
                />
              </div>
              <div v-else></div>
              <div class="flex-column-end-end">
                <div class="flex-end-center" v-if="item.isHideCapable">
                  <!-- <van-button class="inner-btn delete-btn" @click.stop="handleDelete(item.id)"
                    v-if="item.isDeleteCapable">
                    <template #icon>
                      <img src="@/assets/mobile/personal-space/delete.png" alt="" width="11" height="11" />
                    </template>
                    <span class="m-text-12-18-400 red1">删除</span>
                  </van-button> -->
                  <van-button
                    v-if="item.isHideCapable && !item.isHidden"
                    class="inner-btn hidden-btn"
                    @click.stop="handleHidden(item.id, false)"
                  >
                    <template #icon>
                      <img src="@/assets/mobile/personal-space/hidden.png" alt="" width="12" height="8" />
                    </template>
                    <span class="m-text-12-18-400 blue1">隐藏</span>
                  </van-button>
                  <van-button
                    v-if="item.isHideCapable && item.isHidden"
                    class="inner-btn has-hidden-btn"
                    @click.stop="handleHidden(item.id, true)"
                  >
                    <template #icon>
                      <img src="@/assets/mobile/personal-space/has-hidden.png" alt="" width="12" height="8" />
                    </template>
                    <span class="m-text-12-18-400 grey4">已隐藏</span>
                  </van-button>
                </div>
              </div>
            </div>
            <div class="flex-start-center">
              <van-button
                v-if="item.isRecommended !== null"
                class="inner-btn recommend-btn m-mb8 m-mr8"
                :class="{ 'has-recommend': item.isRecommended }"
                @click.stop="handleRecommend(item)"
              >
                {{ item.isRecommended ? "取消栏目推荐" : "栏目推荐" }}
              </van-button>
              <van-button
                v-if="item.isWxMpRecommended !== null"
                class="inner-btn wx-recommend-btn m-mb8"
                :class="{ 'has-recommend': item.isWxMpRecommended }"
                @click.stop="handleWxMpRecommend(item)"
              >
                {{ item.isWxMpRecommended ? "取消公众号推荐" : "公众号推荐" }}
              </van-button>
            </div>
            <span class="m-text-12-18-400 grey3 flex-end-center" v-if="item.lastUpdateTime"
              >最后修改时间:
              <span class="m-text-12-18-400 grey3">{{ item.lastUpdateTime }}</span>
            </span>
            <div class="bottom flex-start-center m-pt8">
              <div class="count-item view-count flex-start-center m-mr24">
                <img
                  v-if="item.isViewed"
                  class="m-mr6"
                  src="@/assets/mobile/personal-space/view-active.png"
                  alt=""
                  width="14"
                  height="10"
                />
                <img v-else class="m-mr6" src="@/assets/mobile/personal-space/view.png" alt="" width="14" height="10" />
                <span class="m-text-12-18-400 grey3">{{ item.views }}</span>
              </div>
              <div
                class="count-item like-count flex-start-center m-mr24"
                @click.stop="clickFavorite(item.id, !item?.isFavorite)"
              >
                <img
                  v-if="!item?.isFavorite"
                  class="m-mr6"
                  src="@/assets/mobile/personal-space/like.png"
                  alt=""
                  width="14"
                  height="13"
                />
                <img
                  v-else
                  class="m-mr6"
                  src="@/assets/mobile/personal-space/like-active.png"
                  alt=""
                  width="14"
                  height="13"
                />
                <span class="m-text-12-18-400 grey3">{{ item?.favorites || 0 }}</span>
              </div>
              <div class="count-item comment-count flex-start-center m-mr24">
                <img
                  v-if="item.isCommented"
                  class="m-mr6"
                  src="@/assets/mobile/personal-space/comment-active.png"
                  alt=""
                  width="14"
                  height="14"
                />
                <img
                  v-else
                  class="m-mr6"
                  src="@/assets/mobile/personal-space/comment.png"
                  alt=""
                  width="14"
                  height="14"
                />
                <span class="m-text-12-18-400 grey3">{{ item.comments }}</span>
              </div>
              <div class="count-item good-count flex-start-center" @click.stop="clickLike(item, !item?.isLike)">
                <img
                  v-if="!item.isLike"
                  class="m-mr6"
                  src="@/assets/mobile/personal-space/good.png"
                  alt=""
                  width="14"
                  height="14"
                />
                <img
                  v-else
                  class="m-mr6"
                  src="@/assets/mobile/personal-space/good-active.png"
                  alt=""
                  width="14"
                  height="14"
                />
                <span class="m-text-12-18-400 grey3">{{ item.likes }}</span>
              </div>
            </div>
          </div>
        </div>
      </van-list>
    </section>

    <van-action-sheet
      v-model:show="sortSheetVisible"
      cancel-text="取消"
      close-on-click-action
      :actions="sortActions"
      @select="selectSortAction"
      safe-area-inset-bottom
    />

    <van-calendar
      ref="filterDateSheetRef"
      v-model:show="filterDateSheetVisible"
      type="range"
      @confirm="handleFilterDateConfirm"
      safe-area-inset-bottom
      :min-date="minDate"
      :max-date="maxDate"
      allow-same-day
    />

    <!-- 高级筛选弹窗 -->
    <van-popup
      v-model:show="showHighFilterDialog"
      position="bottom"
      round
      safe-area-inset-bottom
      :style="{ height: '50%' }"
    >
      <div class="high-filter-dialog">
        <div class="filter-header flex-center-center">
          <span class="m-text-16-24-600 grey1">高级筛选</span>
        </div>

        <div class="filter-body">
          <div class="filter-section">
            <div class="section-title m-text-14-22-600 grey1">日记筛选</div>
            <div class="filter-options">
              <div
                class="filter-option"
                @click="filterOptions.isViewed = filterOptions.isViewed === false ? null : false"
              >
                <div class="filter-option-item" :class="filterOptions.isViewed === false ? 'active' : ''">未阅读</div>
              </div>
              <div class="filter-option" @click="filterOptions.isLike = filterOptions.isLike === false ? null : false">
                <div class="filter-option-item" :class="filterOptions.isLike === false ? 'active' : ''">未点赞</div>
              </div>
              <div
                class="filter-option"
                @click="filterOptions.isCommented = filterOptions.isCommented === false ? null : false"
              >
                <div class="filter-option-item" :class="filterOptions.isCommented === false ? 'active' : ''">
                  未评论
                </div>
              </div>
              <div
                class="filter-option"
                v-if="isTutor && activeTab === userStore.userInfo?.xingZhiShe?.clubGroupId"
                @click="filterOptions.tutorIsRead = filterOptions.tutorIsRead === false ? null : false"
              >
                <div class="filter-option-item" :class="filterOptions.tutorIsRead === false ? 'active' : ''">
                  未评星
                </div>
              </div>
            </div>
          </div>

          <div class="filter-section">
            <div class="section-title m-text-14-22-600 grey1">日期筛选</div>
            <div class="date-filter-row">
              <div class="date-input" @click="openFilterDateSheet">
                <div class="date-value m-text-14-22-400 grey1" :class="{ 'no-value': !startDate }">
                  {{ startDate || "开始日期" }}
                </div>
              </div>
              <div class="separator"></div>
              <div class="date-input" @click="openFilterDateSheet">
                <div class="date-value m-text-14-22-400 grey1" :class="{ 'no-value': !endDate }">
                  {{ endDate || "结束日期" }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="filter-footer">
          <div class="reset pointer" @click="resetHighFilter">重置</div>
          <div class="confirm pointer" @click="confirmHighFilter">确认筛选</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<style lang="scss" scoped>
@use "@/views/mobile/styles/custom-global-mobile-style.scss";

.diary-square {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .header-area {
    width: 100%;
    height: 57px;
    background: linear-gradient(270deg, #508cff 0%, #535dff 100%), #508cff;
    padding: 12px 20px 14px;

    :deep(.van-tabs) {
      flex: 1;
      padding-right: 6px;

      .van-tabs__nav {
        background: transparent;
      }

      .van-tab {
        color: #e2e3e6;
        font-size: 14px;

        &--active {
          color: #ffffff !important;
          font-weight: 600 !important;
        }
      }

      .van-tabs__wrap {
        height: 30px;
      }

      .van-tabs__line {
        background: #fff;
        border-radius: 3px;
      }
    }

    :deep(.van-checkbox) {
      .van-checkbox__label {
        font-size: 14px;
        color: #fff;
      }

      .van-checkbox__icon {
        .van-icon {
          border: 1px solid #c2c9ce;
          background-color: #fff;
        }

        &--checked {
          .van-icon {
            color: #508cff;
          }
        }
      }
    }
  }

  .sort-area {
    padding: 16px 15px 12px;
  }

  .list-area {
    position: relative;
    flex: 1;
    overflow-y: auto;

    .list-item {
      width: 100%;
      background-color: #fff;
      padding: 15px;

      .right-middle {
        position: relative;

        .content-wrapper {
          display: block;
        }

        .content-text {
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
          word-break: break-all;
          margin: 0;

          /* 注释掉展开功能，始终保持3行显示 */
          /* &.show-full {
            display: block;
            -webkit-line-clamp: unset;
            overflow: visible;
          } */

          :deep(img) {
            max-width: 100% !important;
            height: auto !important;
            object-fit: contain;
          }

          :deep(p) {
            img {
              margin: 8px 0;
            }
          }
        }

        .ellipsis-container {
          position: relative;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          background: #fff;
          margin-top: 8px;

          &.with-ellipsis {
            position: absolute;
            right: 0;
            bottom: 0;
            margin-top: 0;
            padding-left: 4px;
          }

          .ellipsis {
            color: #333;
            margin-right: 2px;
          }

          .show-all {
            color: #508cff;
            cursor: pointer;
          }
        }
      }

      .right-bottom {
        padding-top: 8px;

        .label {
          padding: 0 7px;
          border-radius: 19px;
          border: 1px solid #e2e3e6;
          height: 24px;
          line-height: 24px;
        }

        .clocking-time {
          padding: 0 11px;
          border-radius: 19px;
          background-color: #fff5e7;
          height: 24px;
          line-height: 24px;
        }
      }

      .mileage {
        background-color: #fef8ec;
        border-radius: 100px;
        margin-left: 12px;
        color: #ff9832;
        padding-right: 8px;

        &.grey {
          background-color: #f1f2f5;
          color: #94959c;
        }

        img {
          margin-right: 4px;
          width: 20px;
          height: 20px;
        }
      }

      .recommend-btn {
        width: auto;
        height: 24px;
        line-height: 24px;
        border-radius: 19px;
        background: #fff;
        color: #ff9b00;
        font-size: 12px;
        border: 1px solid #e2e3e6;
        padding: 0 26px;

        &.has-recommend {
          background: linear-gradient(180deg, #ffc33b 0%, #ffa919 100%);
          color: #fff;
          border: none;
        }
      }

      .wx-recommend-btn {
        width: auto;
        height: 24px;
        line-height: 24px;
        border-radius: 19px;
        background: #fff;
        color: #60ae24;
        font-size: 12px;
        border: 1px solid #e2e3e6;
        padding: 0 26px;

        &.has-recommend {
          background: linear-gradient(180deg, #8ce049 0%, #6dbb31 100%);
          color: #fff;
          border: none;
        }
      }

      .bottom {
        .count-item {
        }
      }
    }
  }

  .inner-btn {
    width: 56px;
    height: 24px;
    line-height: 24px;
    border-radius: 4px;
    border: 1px solid #e2e3e6;
    padding: 0 4px;

    &.delete-btn {
      margin-right: 6px;
    }

    &.has-hidden-btn {
      width: 68px;
    }
  }

  ::-webkit-scrollbar {
    display: none;
  }
}

.high-filter-dialog {
  display: flex;
  flex-direction: column;
  height: 100%;

  .filter-header {
    padding: 16px;
    margin-bottom: 2px;
  }

  .filter-body {
    flex: 1;
  }

  .filter-section {
    padding: 0 16px;
    margin-bottom: 40px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      margin-bottom: 16px;
    }

    .filter-options {
      display: flex;
      gap: 10px;

      .filter-option {
        width: 100%;

        &-item {
          background-color: #f6f6f6;
          border-radius: 18px;
          padding: 8px;
          text-align: center;
          font-size: 14px;
          color: #2b2c33;

          &.active {
            padding: 7px 8px;
            color: #508cff;
            border: 1px solid #508cff;
            background-color: #dfeaff;
          }
        }
      }
    }
  }

  .date-filter-row {
    display: flex;
    gap: 5px;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;

    .date-input {
      width: 100%;
      text-align: center;
      padding: 8px;
      background: #f7f8fa;
      border-radius: 18px;

      .date-value {
        &.no-value {
          color: #b4b6be;
        }
      }
    }

    .separator {
      width: 10px;
      height: 2px;
      background-color: #b4b6be;
    }
  }

  .filter-footer {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    border-top: 5px solid #f9f9f9;
    height: 62px;

    .reset {
      width: 100%;
      color: #2b2c33;
      font-size: 16px;
      text-align: center;
      line-height: 62px;
    }

    .confirm {
      width: 100%;
      color: #508cff;
      font-size: 16px;
      text-align: center;
      line-height: 62px;
    }
  }
}

.has-filter {
  color: #508cff !important;
}

// 修改日记筛选的复选框样式
:deep(.van-checkbox) {
  .van-checkbox__label {
    color: #333;
    font-size: 14px;
  }

  .van-checkbox__icon {
    .van-icon {
      border: 1px solid #c2c9ce;
      background-color: #fff;
    }

    &--checked {
      .van-icon {
        background-color: #508cff;
        border-color: #508cff;
        color: white;
      }
    }
  }
}
</style>

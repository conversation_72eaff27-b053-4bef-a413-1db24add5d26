import { request } from "@/utils/service"

/* 事件相关 */

// 事件类型
export function getMileageEventTypeApi(data) {
  return request({
    url: "xzs/mileage/event-type/option",
    method: "get",
    params: data
  })
}

// 事件组
export function getMileageEventGroupApi(data) {
  return request({
    url: "xzs/mileage/event-group/option",
    method: "get",
    params: data
  })
}

/* 里程标志 */
export function getMileageFlagApi() {
  return request({
    url: "xzs/mileage/me/flag",
    method: "get"
  })
}

/* 工具接口 */

// 周
export function getWeekListApi() {
  return request({
    url: "xzs/mileage/util/week",
    method: "get"
  })
}

// 月
export function getMonthListApi() {
  return request({
    url: "xzs/mileage/util/month",
    method: "get"
  })
}


/* 我的里程 */

// 本周里程
export function getCurrentWeekMileageApi() { 
  return request({
    url: "xzs/mileage/me/current-weekly-profile",
    method: "get"
  })
}

// 本周里程排名
export function getCurrentWeekMileageRankApi() { 
  return request({
    url: "xzs/mileage/me/current-weekly-rank",
    method: "get"
  })
}

// 每日任务
export function getDailyTaskApi() { 
  return request({
    url: "xzs/mileage/me/current-daily-task",
    method: "get"
  })
}

// 里程明细
export function getMileageDetailApi(data) { 
  return request({
    url: "xzs/mileage/me/event-log",
    method: "get",
    params: data
  })
}

// 历史总分
export function getHistoryTotalMileageApi() { 
  return request({
    url: "xzs/mileage/me/history-total-points",
    method: "get"
  })
}

/* 里程周报 */

// 里程概览
export function getWeekOverviewApi(data) { 
  return request({
    url: "xzs/mileage/me/weekly-report/event-group-points",
    method: "get",
    params: data
  })
}

// 能力分析
export function getWeekAbilityApi(data) { 
  return request({
    url: "xzs/mileage/me/weekly-report/event-group/mine-vs-avg",
    method: "get",
    params: data
  })
}

// 活跃趋势分析
export function getWeekActiveTrendApi(data) { 
  return request({
    url: "xzs/mileage/me/weekly-report/daily-points",
    method: "get",
    params: data
  })
}

// 数据统计
export function getWeekDataStatisticsApi(data) { 
  return request({
    url: "xzs/mileage/me/weekly-report/event-type-count",
    method: "get",
    params: data
  })
}

// 排名
export function getWeekRankApi(data) { 
  return request({
    url: "xzs/mileage/me/weekly-report/rank",
    method: "get",
    params: data
  })
}

/* 里程月报 */

// 里程概览
export function getMonthOverviewApi(data) { 
  return request({
    url: "xzs/mileage/me/monthly-report/event-group-points",
    method: "get",
    params: data
  })
}

// 能力分析
export function getMonthAbilityApi(data) { 
  return request({
    url: "xzs/mileage/me/monthly-report/event-group/mine-vs-avg",
    method: "get",
    params: data
  })
}

// 活跃趋势分析
export function getMonthActiveTrendApi(data) { 
  return request({
    url: "xzs/mileage/me/monthly-report/daily-points",
    method: "get",
    params: data
  })
}

// 数据统计
export function getMonthDataStatisticsApi(data) { 
  return request({
    url: "xzs/mileage/me/monthly-report/event-type-count",
    method: "get",
    params: data
  })
}

// 排名
export function getMonthRankApi(data) { 
  return request({
    url: "xzs/mileage/me/monthly-report/rank",
    method: "get",
    params: data
  })
}
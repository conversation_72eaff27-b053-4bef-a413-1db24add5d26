<template>
    <div class="stage"  @drop="handleDrop"   @dragover.prevent> 
     
            <div class="stage-title flex_start">
                 <p v-show="!showIpt">{{processName}}</p>
                 <el-input placeholder="请输入阶段名称" v-model="processName" @change="changeProcessName" style="width:160px;height:40px" v-show="showIpt"></el-input>
                <img src="@/assets/pc/edit-bk.png" alt="" @click="showIpt=!showIpt">
            </div>
            <div class="hasfile" v-show="row.fileId">
                <div class="file-title flex_start">
                    <img :src="coverurl" alt="">
                    <p>{{row.fileName}}</p>
                </div>
                <wangeditorNoMenu @emitHtml='emitHtml' :html='row.html'/>
                <!-- <el-input placeholder="请输入" type="textarea" rows="4" resize="none"  style="margin-top:16px" v-model="html"></el-input>
                {{html}} -->
            </div>
            <div>
              <el-upload
                   v-show="!row.fileId"
                    class="upload-demo"
                    action="#"
                    multiple
                    :before-upload='beforeUpload'
                    :http-request="upload"
                    :show-file-list='false'
                >
                    <div class="el-upload__text flex_center" style="height:150px;width:100%">
                        请从左侧资源列表拖入此处，或 <em>从本地选择</em>
                    </div>
                </el-upload>
        </div>
        <img src="@/assets/pc/del-item.png" alt="" class="del-item" @click="goDel" />
    </div>
</template>
<script setup>
import {resourcePortalDetail} from '@/api/pc/index.js'
import { ElMessage } from 'element-plus'
import {ref,defineProps, watch} from 'vue'
 import {uploadFileFunc} from '@/utils/pc-upload.js'
 import wangeditorNoMenu from '../wangeditorNoMenu.vue'
let fileobj=''
let props=defineProps(['row'])
let emits=defineEmits(['emitUpFile','emitUpPrcoess','emitUpHtml','emitDel'])
let coverurl=ref('')
let canshowIpt=ref(false)
let html=ref('123123 123')
let processName=ref('')
let showIpt=ref(false)
let params={
    id:'',fileId:'',coverFileId:'',fileName:''
}
let handleDrop=(event)=>{
// event.preventDefault(); // 阻止默认行为
  var data = event.dataTransfer.getData('text/plain'); // 获取传递的数据
  getDetail(data)
}
let emitHtml=(val)=>{
   html.value=val
   emits('emitUpHtml',val,props.row.id)
}
let changeProcessName=()=>{
   emits('emitUpProcess',processName.value,props.row.id)
}
let goDel=()=>{
    emits('emitDel',props.row.id)
}
let getDetail=async(resourceId)=>{
    let res=await resourcePortalDetail({
        resourceId:resourceId
    })
    if(res){
       
        params={
        id:props.row.id,
        coverFileId:res.data.coverFileId,
        fileId:res.data.fileId,
        fileName:res.data.name,
         fileType:res.data.fileType
       }
       emits('emitUpFile',params)
        // row.value=res.data
    }
}
watch(()=>props.row,(newval)=>{
    // console.log(newval,'newval')
      let obj=JSON.parse(import.meta.env.VITE_FILE)
      processName.value=newval.processName
     coverurl.value=obj[newval.coverFileId]?(import.meta.env.VITE_BASE_URL+'/fs/file?fileId='+obj[newval.coverFileId]):(import.meta.env.VITE_BASE_URL+'/fs/file?fileId='+newval.coverFileId)
},{
    deep:true,
    immediate:true
});
let changeFile=(file)=>{
    console.log(file)
}
let whiteList=['pdf','ppt','pptx','doc','docx','xls','xlsx']


const getExtensionString = (str) => {
    var index = str.lastIndexOf('.')
    // 如果找到了第一个"."并且它不在字符串的起始处或结尾处
    if (index != -1 && index != 0 && index + 1 != str.length) {
        return str.substr(index + 1)
    } else {
        return ''
    }
}

let beforeUpload=(file)=>{
    console.log(file)
  let type=getExtensionString(file.name)
  if(whiteList.indexOf(type)>-1){
    fileobj=file
   
    return true
  }else{
    ElMessage.warning('请上传文本格式的文件（例：doc,xlsx,ppt,pdf）')
    return false
  }
}
function getQueryParams(src,type) {
    const url = new URL(src);
    const params = url.searchParams.get(type);
    return params
}
let upload=()=>{
  uploadFileFunc(fileobj,(val)=>{
    let type=getExtensionString(fileobj.name)
    let fileId=getQueryParams(val,'fileId')
      if(type=='ppt'||type=='pptx'){
        params.coverFileId=import.meta.env.VITE_PPT
     }else if(type=='doc'||type=='docx'){
         params.coverFileId=import.meta.env.VITE_DOC
     }else if(type=='xlsx'||type=='xls'){
        params.coverFileId=import.meta.env.VITE_EXCEL
     }else if(type=='pdf'){
        params.coverFileId=import.meta.env.VITE_PDF
     }else{
         params.coverFileId=fileId
     }
     params.id=props.row.id
     params.fileId=fileId
     params.fileName=fileobj.name
     params.fileType=1
      emits('emitUpFile',params)
    })
}
</script>
<style scoped lang='scss'>
.stage{
    background: #fff;
    padding: 24px;
    border-radius: 12px;
    position: relative;
    .stage-title{
        font-size: 16px;
        color: #2B2C33;
        font-weight: bold;
        margin-right: 6px;
        img{
            width: 12px;
            margin-left: 6px;
            height: auto;
            cursor: pointer;
        }
    }
    .file-title{
        font-size: 16px;
        color: #2B2C33;
        margin-top:16px;
        img{
            width: 40px;
            height: auto;
            margin-right: 8px;
        }
    }
}
:deep(.el-upload-dragger){
    border: none;
    height: 130px;
    display: flex;
    align-items: center;
    justify-content: center;
}
:deep(.el-upload){
    width: 100%;
}
.el-upload__text{
font-size: 16px;
color: #94959C;
em{
    font-size: 16px;
    color: #508CFF;
   }
}
.del-item{
    position: absolute;
    top:24px;
    right: 24px;
    width: 24px;
    height: auto;
    cursor: pointer;
}
</style>
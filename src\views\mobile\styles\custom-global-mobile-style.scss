/* 移动端使用全局样式请务必使用此css文件的样式，不然vw转换会将相同的样式名转换为vw */


/* 相关字体 */
.m-text-24-32-400 {
  font-size: 24px;
  line-height: 32px;
  font-weight: 400;
}

.m-text-20-28-600 {
  font-size: 20px;
  line-height: 28px;
  font-weight: 600;
}

.m-text-20-28-400 {
  font-size: 20px;
  line-height: 28px;
  font-weight: 400;
}

.m-text-18-26-600 {
  font-size: 18px;
  line-height: 26px;
  font-weight: 600;
}

.m-text-18-26-400 {
  font-size: 18px;
  line-height: 26px;
  font-weight: 400;
}

.m-text-16-24-600 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
}

.m-text-16-24-400 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
}

.m-text-14-20-600 {
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
}

.m-text-14-20-400 {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.m-text-12-18-600 {
  font-size: 12px;
  line-height: 18px;
  font-weight: 600;
}

.m-text-12-18-400 {
  font-size: 12px;
  line-height: 18px;
  font-weight: 400;
}

/* 全局间距 */
// 循环创建全局变量 形如 mb ml mr mt
@for $i from 0 through 120 {
  .m-m#{$i} {
    margin: #{$i}px !important;
  }
  .m-mt#{$i} {
    margin-top: #{$i}px !important;
  }
  .m-mr#{$i} {
    margin-right: #{$i}px !important;
  }
  .m-mb#{$i} {
    margin-bottom: #{$i}px !important;
  }
  .m-ml#{$i} {
    margin-left: #{$i}px !important;
  }
  .m-p#{$i} {
    padding: #{$i}px !important;
  }
  .m-pt#{$i} {
    padding-top: #{$i}px !important;
  }
  .m-pr#{$i} {
    padding-right: #{$i}px !important;
  }
  .m-pb#{$i} {
    padding-bottom: #{$i}px !important;
  }
  .m-pl#{$i} {
    padding-left: #{$i}px !important;
  }
}

:deep(.m-save-btn) {
  width: 120px;
  height: 44px;
  border-radius: 4px;
  border: 1px solid #508CFF;
  font-size: 16px;
  line-height: 44px;
  background-color: #fff;
  color: #508CFF;
}
:deep(.m-submit-btn) {
  width: 120px;
  height: 44px;
  border-radius: 4px;
  font-size: 16px;
  line-height: 44px;
  background-color: #508CFF;
  color: #fff;
}

.m-btn-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
}
<template>
     <div class="content-box  upload mt10" >
           <div class="select-form">
             <div class="flex_start title" @click="goCancel">
                <el-icon style="margin-right: 4px;"><ArrowLeft /></el-icon>
               
                {{$route.path=='/bk/res/editres'?'编辑课件资源':'创建课件资源'}}
            </div>
           
            <el-form :inline="true" ref='iptRef' :model="formInline" :rules="rules" class="demo-form-inline flex_between_0 flex_wrap" label-width='50px' label-position='top' style="margin-top:24px">
                <el-form-item label="课题" style="width:45%" prop='name'>
                   <el-input placeholder="请输入"  v-model="formInline.name"></el-input>
                </el-form-item>
                  <el-form-item label="教学目标" style="width:45%" prop='teachingGoals'>
                     <el-input placeholder="请输入" type="textarea" rows="6" resize="none" v-model="formInline.teachingGoals"></el-input>
                </el-form-item>
                  <el-form-item label="重点" style="width:45%" prop='keynotes'>
                     <el-input placeholder="请输入" type="textarea" rows="6" resize="none" v-model="formInline.keynotes"></el-input>
                </el-form-item>
                  <el-form-item label="难点" style="width:45%" prop='difficultPoints'>
                     <el-input placeholder="请输入" type="textarea" rows="6" resize="none" v-model="formInline.difficultPoints"></el-input>
                </el-form-item>
            </el-form>
        </div>
        <div class="select-form" style="margin-top:10px">
            <el-form :inline="true" ref='selectRef' :model="formInline" :rules="rules" class="demo-form-inline flex_between flex_wrap" label-width='50px' label-position='top' style="margin-top:24px">
                <el-form-item label="学段" style="width:45%" prop='stage'>
                   <el-select
                        v-model="formInline.stage"
                        placeholder="请选择学段"
                        clearable
                        @change="changeStage"
                    >
                        <el-option :label="item.title" :value="item.id" v-for="item in stages" :key="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="学科" style="width:45%" prop='subjectId'>
                    <el-select
                        v-model="formInline.subjectId"
                        placeholder="请选择学科"
                        clearable
                        @change="changeSubject"
                    >
                         <el-option :label="item.label" :value="item.value" v-for="item in subjects" :key="item.value" />
                    </el-select>
                </el-form-item>
                 <el-form-item label="教材" style="width:45%">
                    <el-select
                        v-model="formInline.editionId"
                        placeholder="请选择教材"
                        clearable
                        @change="changeEdition"
                    >
                      <el-option :label="item.label" :value="item.value" v-for="item in editions" :key="item.value" />
                    </el-select>
                </el-form-item>
                 <el-form-item label="册" style="width:45%">
                    <el-select
                        v-model="formInline.volumeId"
                        placeholder="请选择册"
                        clearable
                        @change='changeVolume'
                    >
                         <el-option :label="item.label" :value="item.value" v-for="item in volumes" :key="item.value" />
                    </el-select>
                </el-form-item>
                 <el-form-item label="章/节" style="width:45%">
                       <el-cascader :options="chapters" v-model="formInline.chapterIds" :props="{
                        checkStrictly: true,
                       
                        }" clearable />
                 
                </el-form-item>
            </el-form>
        </div>
        <h3 class="process-title">
            授课流程
        </h3>
        <div class="process flex_start_0">
            <div class="file">
                  <ul>
                      <li class="flex_start" draggable="true" data-drop='item'   @dragstart="(e)=>handleDrop(e,item)"  v-for="item in baskets" :key="item.id">
                         <img :src="item.url" alt="">
                        <p>{{item.resourceName}}</p>
                      </li>
                  </ul>
            </div>
            <div class="choicedfile flex_1">
                 <Stage   @emitUpFile='emitUpFile' @emitUpHtml='emitUpHtml' @emitUpProcess='emitUpProcess' @emitDel='emitDel'  v-for="item in formInline.process" :key="item.id" :row='item' style="margin-bottom:10px"/>

                 <div class="flex_center add-process" @click="addProcess">
                         <img src="@/assets/pc/question-add.png" alt="">
                        <p>插入阶段</p>
                 </div>
            </div>
        </div>
        <div class="btn-list flex_end">
             <el-buton class="cancel" @click="goCancel">返回</el-buton>
             <el-buton class="submit" @click="goEdit">确认</el-buton>
        </div>
    </div>
</template>
<script setup>
import WangEditor from '@/views/pc/component/wangeditor.vue'
import Stage from '@/views/pc/component/bk/stage.vue'
import {ref,reactive,onMounted} from 'vue'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {subjectList,editionList,volumeList,chapterTree,questionAdd,resourceAdminDetail,questionEdit,resourceBatchUpload,resourceAdminEdit,userTenant} from '@/api/pc/index.js'
import {ElMessage,ElMessageBox} from 'element-plus'
import {storeToRefs} from 'pinia'
import {basketList,packageAdd,packageEdit,packageDetail} from '@/api/pc/bk.js'
import {useRouter,useRoute} from 'vue-router'
let router=useRouter()
let route=useRoute()
let packageId=ref('')
let userInfoStore=useUserInfoStoreHook()
let {stages,userInfo}=storeToRefs(userInfoStore)
let subjects=ref([])
let editions=ref([])
let volumes=ref([])
let chapters=ref([])
let selectRef=ref()
let iptRef=ref()
let baskets=ref([])
let questionId=ref('')
function generateSecureId() {
    const array = new Uint32Array(3); // Generate 3 random 32-bit integers
    window.crypto.getRandomValues(array);
    return Array.from(array, num => num.toString(36)).join('').padStart(10, '0'); // Convert to base 36 and pad to 10 digits
}
let addProcess=()=>{
formInline.process.push({
       id:generateSecureId(),
        fileId:'',
        coverFileId:'',
        fileName:'',
        processName:'',
        html:""
})
}
let handleDrop=(e,item)=>{
    e.dataTransfer.setData('text/plain',item.resourceId);
}
let emitDel=(id)=>{
   let process=formInline.process.filter(item=>item.id!=id)
   Object.assign(formInline,{
    process:process
   })
}
let emitUpHtml=(arg,id)=>{
 formInline.process.forEach(item=>{
        if(item.id==id){
            item.html=arg            
        }
    })
}
let emitUpProcess=(arg,id)=>{
     formInline.process.forEach(item=>{
        if(item.id==id){
            item.processName=arg
            
        }
    })
}
let emitUpFile=(val)=>{
    formInline.process.forEach(item=>{
        if(item.id==val.id){
            item.coverFileId=val.coverFileId
            item.fileId=val.fileId
            item.fileName=val.fileName
            item.fileType=val.fileType
        }
    })
}

let drap=()=>{
    // console.log(222)
}
let formInline=reactive({
    teachingGoals:'',
    name:'',
    keynotes:'',
    difficultPoints:'',

    stage:'',
    subjectId:'',
    editionId:'',
    volumeId:'',
    chapterIds:[],
    chapter1Id:'',
    chapter2Id:'',
    process:[{
        id:1,
        fileId:'',
        coverFileId:'',
        fileName:'',
        processName:'',
        html:""
    }]
})
let id=2
let changeStage=async()=>{
     editions.value=[]
     volumes.value=[]
     chapters.value=[]
    Object.assign(formInline,{
        subjectId:'',
        editionId:'',
        volumeId:'',
        chapterIds:[]
    })
    let res=await subjectList({
        stage:formInline.stage
    })
    if(res){
        subjects.value=res.data
    }
}


onMounted(async()=>{
  
    packageId.value=route.query.id
    // getUserTenant()
   if(packageId.value){
      let res=await packageDetail({
        packageId:packageId.value
      })  
      if(res){
        let chapterIds=[]
        //上面的基础信息
        Object.assign(formInline,{
            stage:res.data.stage.toString(),
            subjectId:res.data.subjectId,
            editionId:res.data.editionId?res.data.editionId:"",
            volumeId:res.data.volumeId?res.data.volumeId:"",
            chapterIds:[],
            chapter1Id:'',
            chapter2Id:'',
           
            teachingGoals:res.data.teachingGoals,
            name:res.data.name,
            keynotes:res.data.keynotes,
            difficultPoints:res.data.difficultPoints,
            process:res.data.process?JSON.parse(res.data.process):[]
        })
      
        if(res.data.chapter1Id){
            chapterIds[0]=res.data.chapter1Id
        }
        if(res.data.chapter2Id){
            chapterIds[1]=res.data.chapter2Id
        }
        Object.assign(formInline,{
            chapterIds:chapterIds
        })
          let res0=await subjectList({
                stage:formInline.stage
            })
            if(res0){
                subjects.value=res0.data
            }
           let res1=await editionList({
                stage:formInline.stage,
                subjectId:formInline.subjectId,
            })
            if(res1){
                editions.value=res1.data
            }
            if(formInline.editionId){
                let res2=await volumeList({
                    editionId:formInline.editionId
                })
                if(res2){
                    volumes.value=res2.data
                }
            }
             if(formInline.volumeId){
                let res3=await chapterTree({
                    volumeId:formInline.volumeId
                })
                if(res3){
                    chapters.value=res3.data
                }
             }
          
      } 
   }
    getBasket()
})
let changeSubject=async()=>{
      volumes.value=[],
      chapters.value=[]
       Object.assign(formInline,{
        editionId:'',
        volumeId:'',
        chapterIds:[]
    })
    let res=await editionList({
        stage:formInline.stage,
        subjectId:formInline.subjectId,
    })
    if(res){
        editions.value=res.data
    }
}
let changeEdition=async()=>{
    chapters.value=[]
    
     Object.assign(formInline,{
        volumes:'',
        chapterIds:[]
    })
     let res=await volumeList({
        editionId:formInline.editionId
     })
     if(res){
        volumes.value=res.data
     }
}
let changeVolume=async()=>{
     Object.assign(formInline,{
        chapterIds:[]
    })
    let res=await chapterTree({
        volumeId:formInline.volumeId
    })
    if(res){
        chapters.value=res.data
    }
}

let rules={
    name:[{ required: true, message: '请输入课题名称', trigger: 'blur' }],
    stage:[{ required: true, message: '请选择学段', trigger: 'blur' }],
    subjectId:[{ required: true, message: '请选择学科', trigger: 'blur' }],
}

let goCancel=()=>{
    router.go(-1)
}

let init=()=>{
     id=id+1
    Object.assign(formInline,{
         stage:'',
        subjectId:'',
        editionId:'',
        volumeId:'',
        chapterIds:[],
        chapter1Id:'',
        chapter2Id:'',
    })
}
let getBasket=async()=>{
    let res=await basketList()
    if(res){
       
         res.data.forEach(item=>{
            let obj=JSON.parse(import.meta.env.VITE_FILE)
            item.url=obj[item.coverFileId]?(import.meta.env.VITE_BASE_URL+'/fs/file?fileId='+obj[item.coverFileId]):(import.meta.env.VITE_BASE_URL+'/fs/file?fileId='+item.coverFileId)
        })
        baskets.value=res.data
    }
}
let goEdit=()=>{
   
    
    let p1=new Promise((resolve,reject)=>{
         selectRef.value.validate((valid, fields) => {
            if (valid) {
               resolve()
            } else {
              reject()
            }
        })
    })
     let p2=new Promise((resolve,reject)=>{
         iptRef.value.validate((valid, fields) => {
            if (valid) {
               resolve()
            } else {
              reject()
            }
        })
    })
    Promise.all([p1,p2]).then(data=>{
           let params={
            teachingGoals:formInline.teachingGoals,
            name:formInline.name,
            keynotes:formInline.keynotes,
            difficultPoints:formInline.difficultPoints,
            // resourceId:resourceId.value,
            stage:formInline.stage,
            subjectId:formInline.subjectId,
            editionId:formInline.editionId,
            volumeId:formInline.volumeId,
            chapter1Id:formInline.chapterIds[0]?formInline.chapterIds[0]:'',
            chapter2Id:formInline.chapterIds[1]?formInline.chapterIds[1]:'',
            process:JSON.stringify(formInline.process)
           }
          
          ElMessageBox.confirm(
            route.path=='/bk/res/editres'?'确认提交编辑吗':'确认提交吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
            if(route.path=='/bk/res/editres'){
              params.packageId=packageId.value
              let res=await packageEdit(params)
              if(res){
                 ElMessage.success('编辑成功')
                 router.go(-1)
              }
            }else{
             let res=await packageAdd(params)
                if(res){
                    ElMessage.success('新增成功')
                    router.go(-1)
                }
            }
            
        })
        .catch(() => {
        })
    }).catch(err=>{
        console.log(err)
    })

}
// 
let goSubmit=(type)=>{
    
    let p1=new Promise((resolve,reject)=>{
         selectRef.value.validate((valid, fields) => {
            if (valid) {
               resolve()
            } else {
              reject()
            }
        })
    })

    Promise.all([p1]).then(data=>{
        let params=[]
          ElMessageBox.confirm(
            '确认提交吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
            let res=await resourceBatchUpload(params)
            if(res){
              
                ElMessage.success('新增成功')
                if(type=1){
                    init()
                }else{
                    router.go(-1)
                }
            }
              
        })
        .catch(() => {
      
        })
       
       
    }).catch(err=>{
        console.log(err)
    })
}
</script>
<style lang="scss" scoped>
 .select-form{
    border-radius: 8px;
    padding: 24px 24px 0 24px;
    background: #fff;
   
  
    .title{
        font-size: 20px;
        color: #2B2C33;
        font-weight: bold;
        cursor: pointer;
    }
    :deep(.el-form--inline .el-form-item){
        margin-right: 0;
        margin-bottom: 24px;
    }
    :deep(.el-form-item--label-top .el-form-item__label){
        font-size: 16px;
        color: #2B2C33;
        font-weight: bold;
    }
    :deep(.el-select__wrapper){
        height: 48px;
    }
    :deep(.el-radio.el-radio--large .el-radio__label){
        font-size: 16px;color: #2B2C33;
    }
    :deep(.el-radio.el-radio--large .el-radio__inner){
        height: 24px;
        width: 24px;
        &::after{
            transform: translate(-50%,-50%) scale(2.4);
        }
    }
    :deep(.el-radio__input.is-checked .el-radio__inner){
        background: rgb(36,198,152);
        border-color: rgb(36,198,152);
    }
    :deep(.el-cascader){
        width: 100%;
    }
    :deep(.el-input__wrapper){
        height: 48px;
    }
 }

 .btn-list{
    padding: 16px 0 24px 0;
    .cancel{
        font-weight: bold;
        font-size: 18px;
        color: #6D6F75;
        line-height: 26px;
        padding: 11px 42px;
        border-radius: 12px;
        border: 1px solid #E6E6E6;
        background: #fff; cursor: pointer;
    }
    .submit{
        padding: 11px 46px;
         line-height: 26px;
        background: #508CFF;
        border-radius: 12px;
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        margin-left: 16px;
        cursor: pointer;
    }
 }
 .process-title{
    margin: 16px 0;
 }
 .process{
    .file{
        margin-right: 10px;
        padding: 24px;
        background: #FFFFFF;
        border-radius: 12px;
        width: 30%;
        ul{
            li{
                cursor: pointer;
                margin-bottom: 38px;
                &:last-child{
                    margin-bottom: 0;
                }
                img{
                    width: 24px;
                    height: auto;
                    margin-right: 12px;
                }
                p{
                    font-weight: 600;
                    font-size: 16px;
                    color: #2B2C33;
                }
            }
        }
    }
    .add-process{
        background: #FAFBFF;
        border-radius: 12px;
        border: 1px dashed #508CFF;
        font-size: 16px;
        color: #4260FD;
        padding: 16px 0;
        
        cursor: pointer;
        img{
            width: 24px;
            height: auto;
            margin-right: 6px;
        }
    }
 }
</style>
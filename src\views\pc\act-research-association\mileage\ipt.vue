<template>
      <div class="ipt-container">
                <div class="file-export flex_end">
                     <p class="download">模板下载</p>
                     <el-button class="import">导入</el-button>
                     <el-button class="submit">提交</el-button>
                </div>
                <div class="module">
                    <p class="module-title">自我学习进步</p>
                    <el-table :data="tableData" style="width: 100%">
                        <el-table-column prop="address" label="里程类型" align="center" >
                           <template #default>
                                 <el-select placeholder="请选择">
                                    <el-option label='1' value='1'></el-option>
                                 </el-select>
                            </template>
                        </el-table-column>
                         <el-table-column prop="address" label="行为类型" align="center" >
                           <template #default>
                                 <el-select placeholder="请选择">
                                    <el-option label='1' value='1'></el-option>
                                 </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="name" label="里程步数" align="center">
                            <template #default>
                                 <el-input placeholder="请输入" v-model="num"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column prop="address" label="社员" align="center" >
                           <template #default>
                                 <el-select placeholder="请选择">
                                    <el-option label='1' value='1'></el-option>
                                 </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="address" label="录入日期" align="center">
                             
                        </el-table-column>
                        <el-table-column prop="date" label="操作" align="center">
                             <template #default>
                                 <img src="@/assets/pc/action-del.png" alt="" style="width:18px;height:auto;cursor:pointer" />
                             </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="addItem flex_center">
                    <el-icon style="color:#508CFF;font-size:12px"><Plus /></el-icon>
                    新增
                </div>
            </div>
</template>
<script setup>
import {onActivated, onMounted, reactive, ref} from 'vue'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {useRouter} from 'vue-router'
import {recordProfile,memberDiary,leaderDiary,leaderRecommendedDiary,tutorRecommendedDiary,myCollect,} from '@/api/pc/index.js'
import FriendLink from '@/views/pc/component/act-research-association/friendLink.vue'

import {storeToRefs} from 'pinia'
let userInfoStore=useUserInfoStoreHook()
let {personcenterCurrentInfo,userInfo}=storeToRefs(userInfoStore)
let diaryList=ref([{id:'1',title:'规则设置'},{id:'5',title:'线下录入'}])
let activeIndex=ref(1)
let router=useRouter()
let num=ref('')
let tableData=ref([
    {
        date:'登录成功成功',
        num:''
    }
])

</script>
<style lang='scss' scoped>
.ipt-container{
    padding: 18px;
    background: #FFFFFF;
    border-radius: 4px;
    .file-export{
        margin-bottom: 16px;
        .download{
            font-size: 16px;
            cursor: pointer;
            color: #508CFF;
            line-height: 24px;
            text-decoration: underline;
            margin-right: 16px;
        }
        .import{
            width: 132px;
            height: 40px;
            background: #23D486;
            border-radius: 8px;
            color: #fff;
            font-size: 16px;
        }
        .submit{
            width: 132px;
            height: 40px;
            background: #508CFF;
            border-radius: 8px;
             color: #fff;
            font-size: 16px;
        }
    }
    .module{
   
        border-radius: 4px;
        background: #fff;
        margin-bottom: 10px;
        &:last-child{
            margin-bottom: none;
        }
    .module-title{
        background: linear-gradient( 270deg, #88C0FF 0%, #508CFF 100%);
        border-radius: 4px 4px 0px 0px;
        width: 100%;
        padding: 9px 0;
        font-size: 16px;
        color: #FFFFFF;
        text-align: center;
        font-weight: bold;
    }
    :deep(.el-table thead .cell){
        height: 40px;
        line-height: 40px;
    }
    :deep(.el-table thead .el-table__cell){
        padding: 0;
        background: #DFEAFF;
        font-size: 14px;
        color: #3E73D9;
        font-weight: bold;
        border-right: 2px solid #fff;
        &:last-child{
            border-right: none;
        }
    }
    :deep(.el-input__inner){
        height: 38px;
    }
    :deep(.el-select__wrapper){
        height: 38px;
    }
    :deep(.el-table tbody .cell){
        
        font-size: 14px;
        color: #2B2C33;
        line-height: 20px;
        white-space: pre-wrap;
        height: auto;
    } 
   }
}
 .addItem{
    font-size: 14px;
    color: #508CFF;
    padding: 10px 0;
    border: 1px dashed #508CFF;
    background: #F5F9FF;
    border-radius: 8px;
    margin-top: 20px;
    cursor: pointer;
 }
</style>
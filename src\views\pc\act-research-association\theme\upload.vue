<template>
    <div>
        <div class="content-box  upload mt10" >
          <div class="flex_start title" @click="goBack">
            <el-icon style="margin-right: 4px;"><ArrowLeft /></el-icon>
              {{$route.name=='ActResearchAssociation-theme-edit'?'编辑讨论':'发布讨论'}}
          </div>
               <el-form
                ref="addRef"
                :model="form"
                :rules="rules"
                label-width="auto"
                class="demo-ruleForm"
              style="margin-top:20px"
            >
             <el-form-item label="主题" label-position='top' prop='clubTopicName'>
                <el-input v-model="form.clubTopicName" placeholder='请输入主题'></el-input>
                    <!-- <div class="theme-value flex_start flex_wrap">
                        <ul class="flex_start tag-list">
                            <li :class="['flex_start']" >
                              
                                <img src="@/assets/pc/tag.png" alt="" />
                                <p>{{form.clubTopicName}}</p>
                            </li>
                        </ul>
                     </div> -->
                </el-form-item>
                <el-form-item label="发布内容" prop="content" label-position='top'>
                     <WangEditor :html='form.content' @emitHtml='emitHtml' />
                </el-form-item>
                  <el-form-item label="组别" prop="clubGroupId" label-position='top' v-if="form.clubPostType==1">
                       <el-select placeholder="请选择组别" v-model="form.clubGroupId" style="width:240px;height:44px">
                           <el-option v-for="item in groups" :key="item.id" :value='item.id' :label='item.name'></el-option>
                       </el-select>
                </el-form-item>
              
            </el-form>
        </div>
        <div class="btn-list flex_center">
             <el-button class="primary" @click="goSubmit" :loading='loading'>提交</el-button>
           
             <el-button class="cancel" @click="goCancel" :loading='loading'>取消</el-button>
        </div>
    </div>
  
</template>
<script setup>
import WangEditor from '@/views/pc/component/wangeditor.vue'
import {themeEdit,themeDetail,themePostAdd,themeGroups} from '@/api/pc/index.js'
import {onMounted, ref} from 'vue'
import {useRouter,useRoute} from 'vue-router'
import {ElMessageBox,ElMessage} from 'element-plus'
let form=ref({
    content:'',
    // clubTopicId:'',
    clubTopicName:'',
    clubPostType:'',
    clubGroupId:''
})
let route=useRoute()
let router=useRouter()
let loading=ref(false)
let clubPostId=ref('')
let addRef=ref()
let rules=ref({
    content:[
        {required: true,message: '请输入发布内容',trigger: 'change'}],
    clubTopicName:[{required: true,message: '请输入主题',trigger: 'change'}],
    clubGroupId:[  {required: true,message: '请选择组别',trigger: 'change'}]
})
let groups=ref([])
let getDetail=async()=>{
  form.value.clubPostType=route.query.clubPostType
//   form.value.clubTopicName=route.query.clubTopicName
}
const getGroups=async()=>{
    let res=await themeGroups()
    if(res){
        console.log(res)
        groups.value=res.data
    }
}
let goBack=()=>{
    router.replace('/actResearchAssociation/theme?clubPostType='+route.query.clubPostType)
}
let getEditDetail=async()=>{
  let res=await themeDetail({
    clubPostId:clubPostId.value
  })
  if(res){
    form.value={
        content:res.data.content,
        clubTopicName:res.data.clubTopicName,
        clubPostType:res.data.postType,
        clubGroupId:res.data.clubGroupId
    }
  }
}
let emitHtml=(val)=>{
    console.log(val)
    form.value.content=val
}
let goSubmit=async(type)=>{
   if (!addRef.value) return
  await addRef.value.validate((valid, fields) => {
    if (valid) {
       ElMessageBox.confirm(
           route.name=='ActResearchAssociation-theme-edit'?'确认编辑评论吗':'确认发布讨论吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
            loading.value=true
            let params={
                content: form.value.content,
                clubPostType: form.value.clubPostType,
                clubTopicName:form.value.clubTopicName,
                clubGroupId:form.value.clubGroupId
            }
            if(route.name=='ActResearchAssociation-theme-edit'){
                params.clubPostId=clubPostId.value
                themeEdit(params).then(data=>{
                 ElMessage({
                    type: 'success',
                    message: '保存成功',
                })
                goBack()
                loading.value=false
             }).catch(error=>{
                 loading.value=false
             })
            }else{
               themePostAdd(params).then(data=>{
                 ElMessage({
                    type: 'success',
                    message: '保存成功',
                })
                goBack()
                loading.value=false
             }).catch(error=>{
                 loading.value=false
             })
            }
            
        })
        .catch(() => {
      
        })
    } else {
      console.log('error submit!', fields)
    }
  })
}

let goCancel=()=>{
    goBack()
}

onMounted(()=>{
    if(route.name=='ActResearchAssociation-theme-edit'){
            clubPostId.value=route.query.id
            getEditDetail()
    }else{
        getDetail()
    }
   
    getGroups()
   
})
</script>
<style lang="scss" scoped>
.upload{
    background: #fff;
    border-radius: 4px;
    padding: 18px 24px 6px 24px;
    .title{
        cursor: pointer;
        font-weight: bold;
        font-size: 20px;
         color: #2B2C33;
    }
    :deep(.el-select__wrapper){
        height: 100%;
    }
    .label{
            font-size: 18px;
            color: #2B2C33;
            font-weight: bold;
            margin-right: 10px;
        }
    .editor{
        margin-top: 20px;
    }
     .tag-list{
            li{
                padding: 4px 10px;
                border-radius: 100px;
                font-size: 16px;
                border: 1px solid #E2E3E6;
                color: #2B2C33;
                cursor: pointer;
                margin-right: 10px;
                cursor: pointer;
                img{
                    width: 16px;
                    height: auto;
                    margin-right: 8px;  
                }
               p{
                line-height: 100%;
               }
               &.active{
                background: linear-gradient( 270deg, #535DFF 0%, #508CFF 100%);
                color: #fff;
               }
            }
            
        }
    // .theme{
    //     margin-top: 30px;
       
    //     .add{
    //              width: 30px;
    //              height: auto;cursor: pointer;
    //         }
    // }

}
.btn-list{
    margin-top: 24px;
    .primary{
        width: 120px;
        height: 44px;
        background: #508CFF;
        border-radius: 4px;font-weight: 500;
        font-size: 18px;
        color: #FFFFFF;
        outline: none;border:none; cursor: pointer;margin-right: 24px;
    }
    .save{
        width: 120px;
        height: 44px;
        background: #fff;border: 1px solid #508CFF; cursor: pointer;
        border-radius: 4px;font-weight: 500;
        font-size: 18px;
        color: #508CFF; cursor: pointer;
        outline: none;margin-right: 24px;
    }
    .cancel{
        cursor: pointer;
        width: 120px;
        height: 44px;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E2E3E6;
        font-size: 18px;
        color: #94959C;
    }
}
.app-scrollbar .app-container-grow{
    flex-grow: 0 !important;
}
:deep(.el-form-item__label){
    font-weight: bold;
    font-size: 18px;
}
:deep(.el-input__wrapper),:deep(.el-input__wrapper),:deep(.el-date-editor.el-input){
    height: 40px;
}
</style>
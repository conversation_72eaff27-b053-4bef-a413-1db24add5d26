<template>
<div class="message">
      
    </div>
</template>
<script setup>
import {onMounted, ref} from 'vue'
import {useRoute} from 'vue-router'
import {useUserStore} from "@/store/modules/user"
import {useUserInfoStore} from "@/store/modules/pc"
import { ElMessage } from 'element-plus'
let route=useRoute()
// let userStore=useUserStore()
let useUserInfoMobile=useUserStore()
let  useUserInfoPc=useUserInfoStore()
const MAX_MOBILE_WIDTH = 750
const _isMobile = () => {
  const rect = document.body.getBoundingClientRect()
  return rect.width - 1 < MAX_MOBILE_WIDTH
}
onMounted(() => {
  useUserInfoMobile.resetToken()
    if(_isMobile){
       useUserInfoMobile.wxLoginByCode(route.query.code)
    }else{
      useUserInfoPc.wxLoginByCode(route.query.code)
    }
  
}) 
</script>
<style scoped lang='scss'>
@use "@/views/mobile/styles/custom-global-mobile-style.scss";

</style>
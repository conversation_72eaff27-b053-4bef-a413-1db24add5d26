import { getVodSign } from "@/api/tc-vod"
import TcVod from "vod-js-sdk-v6"

const getSignature = async function () {
  try {
    const res = await getVodSign()
    if (!res.data || !res.data.signature) {
      throw new Error("获取签名失败")
    }
    return res.data.signature
  } catch (error) {
    console.log(error, "error")
  }
}

export const uploadVideo = (file, callback) => {
  const vod = new TcVod({
    getSignature: getSignature
  })
  console.log("vodSign", vod, file)
  const uploader = vod.upload({
    mediaFile: file
  })
  uploader.on("media_progress", (info) => {
    const percent = parseInt(info.percent) * 100
    console.log(percent, "that.percent")
  })
  console.log("uploader", uploader)
  uploader
    .done()
    .then(function (doneResult) {
      const params = {
        name: file.name,
        fileUrl: doneResult.video.url,
        size: file.size,
        fileId: doneResult.fileId
      }
      const fileList = []
      fileList.push(params)
      callback(fileList)
    })
    .catch(function (err) {
      console.log("上传失败", err)
    })
}

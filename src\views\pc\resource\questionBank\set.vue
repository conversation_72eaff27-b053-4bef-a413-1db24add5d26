<template>
     <div class="content-box  upload mt10" >
        <div class="select-form">
             <div class="flex_start title" @click="goCancel">
                <el-icon style="margin-right: 4px;"><ArrowLeft /></el-icon>
                题集篮
            </div>
            <el-form :inline="true" ref="formRef" :rules="rules" :model="formInline" class="demo-form-inline flex_between flex_wrap" label-width='50px' label-position='top' style="margin-top:24px">
                <el-form-item label="学段" style="width:45%" prop='stage'>
                   <el-select
                        v-model="formInline.stage"
                        placeholder="请选择学科"
                        clearable
                         @change="changeStage"
                    >
                       <el-option :label="item.title" :value="item.id" v-for="item in stages" :key="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="学科" style="width:45%" prop='subjectId'>
                    <el-select
                        v-model="formInline.subjectId"
                        placeholder="请选择学科"
                        clearable
                    >
                         <el-option :label="item.label" :value="item.value" v-for="item in subjects" :key="item.value" />
                    </el-select>
                </el-form-item>
                 <p class="line"></p>
                 
                  <el-form-item label="题集名称"  style="width:45%" prop='name'>
                    <el-input v-model="formInline.name" placeholder="请输入题集名称"></el-input>
                </el-form-item>
            </el-form>
        </div>
       <div class="question-list">
            <div class="question-item">
                <div  v-for="(item,index) in qdList" :key="item.id" >
                   <h3 class="question-type">{{item.questionCategoryName}}</h3>
                   <div class="flex_between_0 question-content" >
                   
                    <div class="flex_start_0 flex_1">
                      <p>{{index+1}}.</p>
                      <div class="html flex_1" v-html="item.questionContent"></div>
                    </div>
                     
                    <button class="flex_center del-btn" @click="delTj(item)" v-show="!questionSetId">
                        <el-icon><Delete /></el-icon>
                        删除
                    </button>
                 </div>
                </div>
                
            </div>
                <!-- <div class="question-item" v-show="qList3.length!=0">
                 <h3 class="question-type">解答题</h3>
                <div class="flex_between_0 question-content"  v-for="(item,index) in qList3" :key="item.id" >
                    <div class="flex_start_0 flex_1">
                      <p>{{index+1}}.</p>
                      <div class="html flex_1" v-html="item.questionContent"></div>
                    </div>
                     
                    <button class="flex_center del-btn" @click="delTj(item)" v-show="!questionSetId">
                        <el-icon><Delete /></el-icon>
                        删除
                    </button>
                 </div>
            </div>
                <div class="question-item" v-show="qList4.length!=0">
                 <h3 class="question-type">填空题</h3>
                 <div class="flex_between_0 question-content"  v-for="(item,index) in qList4" :key="item.id" >
                    <div class="flex_start_0 flex_1">
                      <p>{{index+1}}.</p>
                      <div class="html flex_1" v-html="item.questionContent"></div>
                    </div>
                     
                    <button class="flex_center del-btn" @click="delTj(item)" v-show="!questionSetId">
                        <el-icon><Delete /></el-icon>
                        删除
                    </button>
                 </div>
            </div>
                <div class="question-item" v-show="qList6.length!=0">
                 <h3 class="question-type">判断题</h3>
                <div class="flex_between_0 question-content"  v-for="(item,index) in qList6" :key="item.id" >
                    <div class="flex_start_0 flex_1">
                      <p>{{index+1}}.</p>
                      <div class="html flex_1" v-html="item.questionContent"></div>
                    </div>
                     
                    <button class="flex_center del-btn" @click="delTj(item)" v-show="!questionSetId">
                        <el-icon><Delete /></el-icon>
                        删除
                    </button>
                 </div>
            </div> -->
           

            <div class="flex_center choice-question" @click="choiceQuestion" v-show="!questionSetId">
                <img src="@/assets/pc/question-add.png" alt="">
                <p>继续选题</p>
            </div>
       </div>
        <div class="btn-list flex_end">
          
             <el-buton class="submit" @click="goSave">保存</el-buton>
        </div>
    </div>
</template>
<script setup>
import WangEditor from '@/views/pc/component/wangeditor.vue'

import {basketEntry,subjectList,basketDel,saveQuestionSet,questionSetDetail,saveQuestionEdit} from '@/api/pc/index.js'
import {ref,reactive, onMounted, onActivated} from 'vue'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {ElMessage,ElMessageBox} from 'element-plus'
import {storeToRefs} from 'pinia'
import {useRouter,useRoute} from 'vue-router'
let userInfo=useUserInfoStoreHook()
let {stages}=storeToRefs(userInfo)
let router=useRouter()
let route=useRoute()
let questionSetId=ref('')
let subjects=ref([])
let formInline=reactive({
    stage:'',
    subjectId:'',
    name:'',
})
//1 选择 3 解答 4填空 6判断
let qdList=ref([])
let qList1=ref([])
let qList3=ref([])
let qList4=ref([])
let qList6=ref([])

let qList50=ref([])
let qList51=ref([])
let qList52=ref([])
let qList53=ref([])
let qList54=ref([])
let qList55=ref([])
let formRef=ref()
let rules={
    stage:[{ required: true, message: '请选择学段', trigger: 'change' }],
    subjectId:[{ required: true, message: '请选择学科', trigger: 'change' }],
    name:[{ required: true, message: '请输入题集名', trigger: 'blur' }],
}
let choiceQuestion=()=>{
    router.push('/resource/question/index')
}
let goCancel=()=>{
    router.go(-1)
}
let goSave=()=>{
      formRef.value.validate((valid, fields) => {
            if (valid) {
                let ids=[]
                qdList.value.forEach(item=>{
                    ids.push(item.id)
                })
                //  qList3.value.forEach(item=>{
                //     ids.push(item.id)
                // })
                //  qList4.value.forEach(item=>{
                //     ids.push(item.id)
                // })
                // qList6.value.forEach(item=>{
                //     ids.push(item.id)
                // })

                // qList50.value.forEach(item=>{
                //     ids.push(item.id)
                // })
                // qList51.value.forEach(item=>{
                //     ids.push(item.id)
                // })
                // qList52.value.forEach(item=>{
                //     ids.push(item.id)
                // })
                // qList53.value.forEach(item=>{
                //     ids.push(item.id)
                // })
                // qList54.value.forEach(item=>{
                //     ids.push(item.id)
                // })
                //  qList55.value.forEach(item=>{
                //     ids.push(item.id)
                // })
                ElMessageBox.confirm(
                '确认保存该题集吗?',
                    '提示',
                    {
                        confirmButtonText: '确认',
                        cancelButtonText: '取消',
                        type: 'warning',
                    }
                )
                .then(async() => {
                    if(!questionSetId.value){
                        let res=await saveQuestionSet({
                            ...formInline,
                            questionIds:ids
                            }) 
                        if(res){
                            Object.assign(formInline,{
                                 stage:'',
                                 subjectId:'',
                                 name:'',
                             })
                             formRef.value.resetFields()
                                ElMessage.success('保存成功')
                                // getBasket()
                                router.go(-1)
                        }
                    }else{
                        let res1=await saveQuestionEdit({
                            questionSetId:questionSetId.value,
                            ...formInline,
                        })
                        if(res1){
                             Object.assign(formInline,{
                                 stage:'',
                                 subjectId:'',
                                 name:'',
                             })
                             formRef.value.resetFields()
                            ElMessage.success('保存成功')
                                // getBasket()
                            router.go(-1)
                        }
                    }
                  
                })
                .catch(() => {
            
                })
            } else {
             
            }
        })
 
}
let delTj=(row)=>{
     ElMessageBox.confirm(
           '确认从题集中删除该题目吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
            let res=await basketDel({
                questionIds:[row.id]
            }) 
           if(res){
                ElMessage.success('删除成功')
                getBasket()
           }
        })
        .catch(() => {
      
        })
}
let getBasket=async()=>{
    let res=await basketEntry()
    if(res){
        qdList.value=res.data
        //  let tempqList1=[]
        //  let tempqList3=[]
        //  let tempqList4=[]
        //  let tempqList6=[]
        //  res.data.forEach(item=>{
        //     if(item.questionCategory==1){
        //         tempqList1.push(item)
        //     }
        //     if(item.questionCategory==3){
        //         tempqList3.push(item)
        //     }
        //     if(item.questionCategory==4){
        //         tempqList4.push(item)
        //     } 
        //     if(item.questionCategory==6){
        //         tempqList6.push(item)
        //     }    
        // })
        //     qList1.value=tempqList1
        //     qList3.value=tempqList3
        //     qList4.value=tempqList4
        //     qList6.value=tempqList6
    }
}
let changeStage=async()=>{
    Object.assign(formInline,{
        subjectId:''
    })
    let res=await subjectList({
        stage:formInline.stage
    })
    if(res){
        subjects.value=res.data
    }
}
let getDetail=async()=>{
   let res=await questionSetDetail({
    questionSetId:questionSetId.value
   })
   if(res){
    Object.assign(formInline,{
        stage:res.data.stage.toString(),
        subjectId:res.data.subjectId,
        name:res.data.name
    })
      let res1=await subjectList({
           stage:formInline.stage
        })
        if(res){
            subjects.value=res1.data
        }
        qdList.value=res.data.questions
        // let tempqList1=[]
        //  let tempqList3=[]
        //  let tempqList4=[]
        //  let tempqList6=[]
        //  res.data.questions.forEach(item=>{
        //     if(item.questionCategory==1){
        //         tempqList1.push(item)
        //     }
        //     if(item.questionCategory==3){
        //         tempqList3.push(item)
        //     }
        //     if(item.questionCategory==4){
        //         tempqList4.push(item)
        //     } 
        //     if(item.questionCategory==6){
        //         tempqList6.push(item)
        //     }    
        // })
        //     qList1.value=tempqList1
        //     qList3.value=tempqList3
        //     qList4.value=tempqList4
        //     qList6.value=tempqList6
   }
}
onMounted(()=>{
    
   
})
onActivated(()=>{
 questionSetId.value=route.query.id
    if(questionSetId.value){
        getDetail()
    }else{
        getBasket()
    }
    
})
</script>
<style lang="scss" scoped>
 .select-form{
    border-radius: 8px;
    padding: 24px 24px 0 24px;
    background: #fff;
    .title{
        font-size: 20px;
        cursor: pointer;
        color: #2B2C33;
        font-weight: bold;
    }
    :deep(.el-form--inline .el-form-item){
        margin-right: 0;
        margin-bottom: 24px;
    }
    :deep(.el-form-item--label-top .el-form-item__label){
        font-size: 16px;
        color: #2B2C33;
        font-weight: bold;
    }
    :deep(.el-select__wrapper),:deep(.el-input__wrapper){
        height: 48px;
    }
    .line{
        width: 100%;
        height: 1px;
        background: #E2E3E6;
        margin:8px 0 30px 0;
    }
 }
.question-list{
    padding-bottom: 24px;
    background: #FFFFFF;
   border-radius: 8px;
   margin-top: 10px;
   .choice-question{
    font-size: 16px;
    color: #4260FD;
    background: #FAFBFF;
    border-radius: 12px;
    padding: 20px 0;
    margin:16px 24px 0 24px;
    cursor: pointer;
    img{
        width: 24px;
        margin-right: 8px;
    }
   }
  .question-item{
  
    padding: 24px 0 0 0;
    .question-type{
        font-size: 18px;
        color: #2B2C33;
        font-weight: bold;
       
        padding: 6px 24px 6px 24px;
    }
    .question-content{
        min-height: 168px;
        padding: 24px;
          &:nth-child(2n){
           background: #f9f9f9;
    }
    }
    .del-btn{
        padding: 6px 12px;
        background: #fff;
        border-radius: 100px;
        font-size: 14px;
        height: 32px;
        color: #6D6F75;
        cursor: pointer;
        border: 1px solid #E2E3E6;
    }
  }
}
 .btn-list{
    padding: 16px 0 24px 0;
  
    .submit{
        padding: 11px 74px;
         line-height: 26px;
        background: #508CFF;
        border-radius: 12px;
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        margin-left: 16px;
        cursor: pointer;
    }
 }
//  :deep(.html){
//     img{
//         max-width: 100%;
//     }
//  }
:deep(.html){
    img{
        max-width: 100%
     
      }
    .question{
        margin-bottom:16px;
      img{
        max-width: 100% !important;
        max-height: 100% !important;
      }
    
    }
     ol{
      list-style-type:upper-alpha;
      li{
        margin-bottom: 8px;
      }
      }
}
</style>
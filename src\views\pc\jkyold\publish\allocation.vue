<template>
    <div class="publish-result-main">
        <el-form :model="search_form" class="search_form">
            <el-form-item label="专家组">
                <el-select v-model="search_form.expertGroupId" :disabled="!detail.isAssignCapable" class="select_48" placeholder="请选择" @change="changeExtend">
                    <el-option v-for="item in expert_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select> 
            </el-form-item>
        </el-form>
        <p class="text_16_bold mt_24">专家名单</p>
        <el-table :data="expert_user_list" class="my-table" style="width: 100%;margin-top:8px" :header-cell-style='{"color":"#94959C",fontSize:"14px",background: "#F8F9FF"}'>
            <el-table-column type="index" label="序号" width="60"  /> 
            <el-table-column prop="name" label="人员名称" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="mobile" label="手机号" />
            <el-table-column prop="tenantTypeName" label="评审部分">
                <template #default='scope'>
                    <el-select v-model="scope.row.reviewSectionId" placeholder="请选择" :disabled="!detail.isAssignCapable" @change="changeExtend">
                        <el-option v-for="item in review_section_list" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </template>
            </el-table-column>
        </el-table> 
        <div class="flex_end mt_24">
            <el-button class="plain-btn btn_96_48" type="plain" @click="emit('close')">取消</el-button>
            <el-button class="primary-btn btn_96_48" v-if="detail.isAssignCapable" type="primary" @click="submitClick">确认</el-button>
        </div> 
    </div> 
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import { expertSelect, expertUser, reviewSection, review, reviewTemplate } from '@/api/pc/jky'
    import {ElMessage,ElLoading,ElMessageBox} from 'element-plus'
    const expert_list = ref([{}])
    const search_form = ref({expertGroupId:''})
    const expert_select = ref([])
    const expert_user_list = ref([])
    const review_section_list = ref([])
    const emit = defineEmits(['close'])
    const props = defineProps({
        id: '', // 默认值
    });

    const detail = ref({})

    const getExpertSelect = async () => {
        let res = await expertSelect()
        if(res){
            expert_select.value = res.data
            search_form.value.expertGroupId = res.data&&res.data.length>0 ? res.data[0].value : ''
            getTemplate()
        }
    }
    const getExpertUser = async () => {
        let res = await expertUser({expertGroupId:search_form.value.expertGroupId})
        if(res){
            expert_user_list.value = res.data
            expert_user_list.value.forEach(item => item.reviewSectionId = '')
            console.log(expert_user_list.value,'expert_user_list.value')
        }
    }
    const getReviewSection = async () => {
        let res = await reviewSection(props.id)
        if(res){
            review_section_list.value = res.data
        }
    }
    const getTemplate = async () => {
        let res = await reviewTemplate(props.id)
        if(res){
            
            detail.value = res.data
            
            if(!res.data.isAssignCapable){
                search_form.value.expertGroupId = res.data.expertGroupId
                expert_user_list.value = res.data.taskReviewTemplates.map(item => {
                    return {
                        id: item.reviewUserId,
                        name: item.reviewUserName,
                        mobile: item.reviewUserMobile,
                        reviewSectionId: item.reviewSectionId,
                        reviewSectionName: item.reviewSectionName
                    };
                });
            }else{
                getExpertUser()
            }
        }
    }
    const submitClick = async () => {
        let taskReviews = expert_user_list.value.map(item => ({
            reviewUserId: item.id,
            reviewSectionId: item.reviewSectionId
        }));
        let params = {
            taskMasterId: props.id,
            expertGroupId:search_form.value.expertGroupId,
            taskReviews:taskReviews
        }
        let res = await review(params)
        if(res){
            ElMessage.success('分配成功')
            emit('close')
        }
    }
    onMounted(() => {
        getExpertSelect()
        getReviewSection()
    })
</script>
<style lang="scss" scoped>
.publish-result-main{}
</style> 
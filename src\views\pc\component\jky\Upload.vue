<template>
        <el-upload
            :file-list="file_list"
            action="Fake Action"
            show-file-list="true"
            multiple
            drag
            :http-request="uploadFile"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
            :before-upload='beforeUpload'
        >
            <div class="upload-box text_16 flex_column_center">
                <img class="upload-icon" src="@/assets/pc/jky/upload-btn.png" alt="">
                <p>将文件拖到此处，或<span class="blue2">点击上传</span></p>
                <p class="text_12 grey2 mt_8">支持上传word、pdf文件，且大小不超过10MB</p>
            </div>
        </el-upload>
        <p class="text_14 orange2 mt_10">{{props.tip}}</p>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import {uploadFileFunc} from '@/utils/pc-upload.js'
    import {ElMessage,ElLoading,ElMessageBox} from 'element-plus'

    const props = defineProps({
        tip: '', // 默认值
    });

    const file_list = ref([])

    function getQueryParams(src,type) {
        const url = new URL(src);
        const params = url.searchParams.get(type);
        return params
    }

    const getExtensionString = (str) => {
        var index = str.lastIndexOf('.')
        // 如果找到了第一个"."并且它不在字符串的起始处或结尾处
        if (index != -1 && index != 0 && index + 1 != str.length) {
            return str.substr(index + 1)
        } else {
            return ''
        }
    } 

    const beforeUpload=(file)=>{
        console.log(file,'filebeforeUpload')
        let type = getExtensionString(file.name)
        let allowType=['doc','docx','pdf']
        if (!allowType.includes(type)) {
            ElMessage.warning('请上传以下格式的文件：' + allowType.join('、'));
            return false;
        }
        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
            ElMessage.warning('文件大小不能超过10MB');
            return false;
        }
        return true;
    }

    const uploadFile = (file) => {
        console.log(file,'filefilesfeferg')
        uploadFileFunc(file.file,(url) => {
            file_list.value.push({
                url:url,
                name:file.file.name,
                fileId:getQueryParams(url,'fileId')
            })
            console.log(file_list.value,'上传文件')

        })
    }
    onMounted(() => {
    })
</script>
<style lang="scss" scoped>
.upload-box{background: #F0F5FF;border-radius: 12px;border: 1px dashed #1F6CFF;padding: 16px 0;width: 100%;
    .upload-icon{width: 54px;margin-bottom: 8px;}
}
:deep(.el-upload){width: 100%;}
:deep(.el-upload-dragger){padding: 0;border-radius: 12px;border: none;}
</style>
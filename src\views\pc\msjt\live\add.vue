<template>
    <div class="msjt-live-add content-box">
        <div class="msjt-live-add-content">
            <p class="text_20_bold flex_start mr_40 pointer" style="display:inline-flex;" @click="router.back()"><el-icon><ArrowLeftBold /></el-icon>{{route.params.id == 0 ? '新增直播' : '查看'}}</p> 
            <div class="address-box" v-if="route.params.id != 0 && state == 3">
                <p>推流地址：{{address.pushUrl}}</p>
                <!-- <p>服务器：{{address.obsServer}}</p>
                <p>串流密码：{{address.key}}</p> -->
            </div>
            <el-form :model="add_form" ref="addRef" inline :rules="rules" class="search_form flex_start_1 flex_wrap">
                <el-form-item label="直播名称" prop="title" style="margin-right: 80px;" :title="route.params.id != 0 ? add_form.title : ''">
                    <el-input v-model="add_form.title" @input="changeUserName" :disabled="route.params.id != 0" class="input_48" placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="开播时间" prop="startTime" style="margin-right: 0;">
                    <el-date-picker v-model="add_form.startTime" type="datetime" :disabled="route.params.id != 0" class="my-date-picker" placeholder="选择日期" value-format="YYYY-MM-DD HH:mm:ss" />
                </el-form-item>
                <el-form-item label="结束时间" prop="endTime" style="margin-right: 80px;">
                    <el-date-picker v-model="add_form.endTime" :disabled="route.params.id != 0" type="datetime" class="my-date-picker" placeholder="选择日期" value-format="YYYY-MM-DD HH:mm:ss" />
                </el-form-item>
                <el-form-item label="直播主题" prop="topic" style="margin-right: 0;" :title="route.params.id != 0 ? add_form.topic : ''">
                    <el-input v-model="add_form.topic" :disabled="route.params.id != 0" @input="changeUserName" class="input_48" placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="可回放" style="margin-right: 80px;">
                    <el-radio-group v-model="add_form.canReplay" class="ml-4" :disabled="route.params.id != 0">
                        <el-radio label="开启" :value="true" size="large"></el-radio>
                        <el-radio label="关闭" :value="false" size="large"></el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="开启评论" style="margin-right: 0;">
                    <el-radio-group v-model="add_form.chatOpen" class="ml-4" :disabled="route.params.id != 0">
                        <el-radio label="开启" :value="true" size="large"></el-radio>
                        <el-radio label="关闭" :value="false" size="large"></el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="封面" prop="sealUrl" style="margin-right: 80px;">
                    <el-upload
                        :file-list="add_form.sealUrl"
                        action="Fake Action"
                        show-file-list="false"
                        list-type="picture-card"
                        multiple
                        :limit="1"
                        :http-request="uploadFile"
                        :on-preview="handlePictureCardPreview"
                        :on-remove="handleRemove"
                        :before-upload='beforeUpload'
                        :disabled="route.params.id != 0"
                        :class="add_form.sealUrl&&add_form.sealUrl.length > 0 ? 'hide-upload' : ''"
                    >
                        <div class="upload-seal flex_column_center_center text_14 blue">
                            <el-icon class="avatar-uploader-icon" size="24"><Plus /></el-icon>
                            <p>点击上传图片</p>
                        </div>
                        <p></p>
                    </el-upload>
                    <p class="text_12 grey2 mt_8">(图片建议为jpg、jpeg、png格式，建议尺寸为280*150px，大小为2M以内)</p>
                </el-form-item>
                <el-form-item label="直播范围" style="margin-right: 0;">
                    <el-radio-group v-model="add_form.open" class="ml-4" :disabled="route.params.id != 0">
                        <el-radio label="公开" :value="true" size="large"></el-radio>
                        <el-radio label="不公开" :value="false" size="large"></el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
        </div>
        <div class="flex_end mt_24" v-if="state == 1 && route.meta.model == 2 ">
            <el-button class="plain-btn btn_120_48" type="plain" @click="checkClick('2')">驳回</el-button>
            <el-button class="primary-btn btn_184_48" type="primary" @click="checkClick('3')">通过</el-button>
        </div>
        <div class="flex_end mt_24" v-else>
            <el-button class="plain-btn btn_120_48" type="plain" @click="router.back()">取消</el-button>
            <el-button v-if="route.params.id == 0" class="primary-btn btn_184_48" type="primary" @click="submitClick">提交</el-button>
        </div>
        
        <el-dialog v-model="preview_visible">
            <img w-full :src="add_form.sealUrl[0].url" alt="Preview Image" style="width: 100%;max-height:600px;overflow:hidden;" />
        </el-dialog>
    </div>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import { CirclePlusFilled, ArrowLeftBold } from '@element-plus/icons-vue'
    import { toRaw } from "@vue/reactivity"
    import {uploadFileFunc} from '@/utils/pc-upload.js'
    import { getExtensionString, getQueryParams } from '@/utils/index.js'
    import { applyAdd, applyDetail, approve, pushAddress } from '@/api/pc/msjt.js'
    import {ElMessage,ElLoading,ElMessageBox} from 'element-plus'
    import { useRouter, useRoute } from 'vue-router'
    const router = useRouter()
    const route = useRoute()
    const formObj = function(obj = {}){
        this.title = obj.title || ''
        this.startTime = obj.startTime || ''
        this.endTime = obj.endTime || ''
        this.canReplay = obj.canReplay
        this.chatOpen = obj.chatOpen
        this.open = obj.open
        this.topic = obj.topic || ''
        this.sealUrl = obj.sealUrl || []
    } 
    const radio1 = ref('开启')
    const addRef = ref()
    const preview_visible = ref(false)
    const state = ref(0)
    const address = ref({})
    const beforeUpload=(file)=>{
        let type = getExtensionString(file.name)
        let allowType=['jpg','jpeg','png']
        if(allowType.indexOf(type)>-1){
            // 文件大小限制（2MB）
            // const maxSize = 2 * 1024 * 1024;
            // if (file.size > maxSize) {
            //     ElMessage.warning('文件大小不能超过2MB');
            //     return false;
            // }
            return true
        }else{
            ElMessage.warning('请上传以下格式的文件'+allowType.join('，'))
            return false
        }
        
    }
    const uploadFile = (file) => {
        uploadFileFunc(file.file,(url) => {
            add_form.value.sealUrl.push({
                url:url,
                name:file.file.name,
                fileId:getQueryParams(url,'fileId')
            })
            console.log(toRaw(add_form.value.sealUrl),'上传文件')

        })
    }
    const handleRemove = () => {
        add_form.value.sealUrl = []
    }
    const handlePictureCardPreview = () => {
        preview_visible.value = true
    }
    const add_form = ref(new formObj({canReplay:true,chatOpen:true,open:true}))
    const rules = ref({
        title:[{required: true,message: '请输入直播名称',trigger: 'blur'}],
        startTime:[{required: true,message: '请选择开播时间',trigger: 'blur'},{
            validator: (rule, value, callback) => {
                const currentTime = new Date();
                if (value && new Date(value) < currentTime) {
                return callback(new Error('开播时间不能早于当前时间'));
                }
                const endTime = add_form.value.endTime;
                if (endTime && value && new Date(value) > new Date(endTime)) {
                    callback(new Error('开播时间不能晚于结束时间'));
                } else {
                    callback();
                }
            },
            trigger: 'blur'
        }],
        endTime:[{required: true,message: '请选择结束时间',trigger: 'blur'},{
            validator: (rule, value, callback) => {
              const startTime = add_form.value.startTime;
              if (startTime && value && new Date(value) < new Date(startTime)) {
                callback(new Error('结束时间不能早于开播时间'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
        }],
        topic:[{required: true,message: '请输入直播主题',trigger: 'blur'}],
        sealUrl:[{required: true,message: '请上传封面',trigger: 'blur'}]
    })

    const getDetail = async () => {
        let res = await applyDetail(route.params.id)
        if(res){
            add_form.value = new formObj(res.data)
            add_form.value.sealUrl = [{url:res.data.sealUrl}]
            state.value = res.data.state
            console.log(toRaw(add_form.value),'add_form.value')
            getAddress(res.data.liveId)
        }
    }
    const getAddress = async (liveId) => {
        let res = await pushAddress(liveId)
        if(res){
            address.value = res.data
        }
    }
    const submitClick = async () => {
        if (!addRef.value) return
        await addRef.value.validate(async(valid, fields) => {
            if (valid) {
                // 防止重复提交
                if (addRef.value && addRef.value.submitting) return;
                if (addRef.value) addRef.value.submitting = true;
                let params = JSON.parse(JSON.stringify(add_form.value))
                params.sealUrl = add_form.value.sealUrl[0].url
                let res = await applyAdd(params)
                if(res){
                    ElMessage.success('发布成功')
                    router.back()
                }
            } else {
                console.log('error submit!', fields)
            }
        })
    }
    const checkClick = async (state) => {
        ElMessageBox.confirm( `确认${state == 3 ? '通过' : '驳回'}吗?`, '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        ).then(async () => {
            // 防止重复提交
            if (addRef.value && addRef.value.submitting) return;
            if (addRef.value) addRef.value.submitting = true;
            let res = await approve(route.params.id,{state:state})
            if(res){
                ElMessage.success('审核成功')
                router.back()
            }
        })
    }
    onMounted(() => {
        console.log(route.meta,'routefdegfr')
        if(route.params.id != 0){
            getDetail()
        }
    })
</script>
<style lang="scss" scoped>
.msjt-live-add{padding-bottom: 20px;
    .msjt-live-add-content{border-radius: 8px;background: #FFFFFF;padding: 24px;margin-top:16px;}
    .search_form{margin-top: 24px;
        :deep(.el-form-item){width: calc(50% - 40px);}
    }
    .upload-seal{border-radius: 12px;width: 196px;height: 104px;}
    .hide-upload{
        :deep(.el-upload--picture-card){display: none;}
    }
    // border: 1px dashed #B4B6BE;
    :deep(.el-upload--picture-card), :deep(.el-upload-list--picture-card .el-upload-list__item){width: 196px;height: 104px;}
    :deep(.el-radio.el-radio--large .el-radio__inner){width: 24px;height: 24px;}
    :deep(.el-radio__inner:after){width: 10px;height: 10px;}
    :deep(.el-radio__input.is-checked .el-radio__inner){background: #23D486;border-color: #23D486;}
    :deep(.el-radio__input.is-checked+.el-radio__label), :deep(.el-radio__label){color: #2B2C33;}
    :deep(.el-upload--picture-card){background: #fff;border-radius: 12px;}
    :deep(.el-upload-list--picture-card .el-upload-list__item-thumbnail){object-fit: cover;}
    .address-box{background: #508CFF;color: #fff;margin: 24px 0;padding: 16px 16px 6px;border-radius: 8px;
        p{padding-bottom: 10px;}
    }
}
</style>
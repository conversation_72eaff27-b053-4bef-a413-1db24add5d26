<script setup>
import { onMounted, ref, computed } from "vue"
import personalSpace from "@/assets/mobile/tabbar/personal-space.png"
import personalSpaceActive from "@/assets/mobile/tabbar/personal-space-active.png"
import diarySquare from "@/assets/mobile/tabbar/diary-square.png"
import diarySquareActive from "@/assets/mobile/tabbar/diary-square-active.png"
import topicConversation from "@/assets/mobile/tabbar/topic-conversation.png"
import topicConversationActive from "@/assets/mobile/tabbar/topic-conversation-active.png"
import dataStatistic from "@/assets/mobile/tabbar/data-statistic.png"
import dataStatisticActive from "@/assets/mobile/tabbar/data-statistic-active.png"
import home from "@/assets/mobile/tabbar/home.png"
import homeActive from "@/assets/mobile/tabbar/home-active.png"

import { useUserStore } from "@/store/modules/user"

const userStore = useUserStore()

const isDirector = computed(() => userStore.roles?.includes("DIRECTOR"))
const isMember = computed(() => userStore.roles?.includes("MEMBER"))
const isTutor = computed(() => userStore.roles?.includes("TUTOR"))
const isViceDirector = computed(() => userStore.roles?.includes("VICE_DIRECTOR"))
const isLeader = computed(() => userStore.roles?.includes("LEADER"))

const active = ref(0)
const tabbarData = ref([
  {
    title: '首页',
    to: {
      name: "Home"
    },
    active: homeActive,
    inactive: home
  },
  {
    title: "个人空间",
    to: {
      name: "PersonalSpace"
    },
    active: personalSpaceActive,
    inactive: personalSpace
  },
  {
    title: "日记广场",
    to: {
      name: "DiarySquare"
    },
    active: diarySquareActive,
    inactive: diarySquare
  },
  {
    title: "主题交流",
    to: {
      name: "TopicConversation"
    },
    active: topicConversationActive,
    inactive: topicConversation
  },
  {
    title: "数据统计",
    to: {
      name: "DataStatistic"
    },
    active: dataStatisticActive,
    inactive: dataStatistic
  }
])

onMounted(() => {
  if (!isTutor.value && !isDirector.value && !isLeader.value && !isViceDirector.value) { 
    tabbarData.value = tabbarData.value.filter(item => item.title !== "数据统计")
  }
})
</script>

<template>
  <div class="tab-bar">
    <!-- <Teleport to="body"> -->
    <van-tabbar safe-area-inset-bottom v-model="active" :placeholder="true" :route="true" fixed>
      <van-tabbar-item v-for="(item, index) in tabbarData" :key="index" :to="item.to">
        {{ item.title }}
        <template #icon="props">
          <img :src="props.active ? item.active : item.inactive" alt="" />
        </template>
      </van-tabbar-item>
    </van-tabbar>
    <div class="tab-bar-shadow" />
    <!-- </Teleport> -->
  </div>
</template>

<style lang="scss" scoped>
.tab-bar {
  position: relative;
  z-index: 99;
  // height: var(--van-tabbar-height);
  background-color: #fff;

  .tab-bar-shadow {
    position: absolute;
    top: -12px;
    left: 0;
    width: 100%;
    height: 12px;
    background: linear-gradient(180deg, rgba(236, 238, 250, 0) 0%, #eceefa 100%);
  }
}

.van-tabbar--fixed {
  z-index: 99;
}

:deep(.van-tabbar-item--active) {
  --van-tabbar-item-active-color: #508CFF;
}
</style>

<template>
    <div class="flex_start_0 diary">
            <img src="@/assets/pc/teacher.png" alt="" class="head" />
            <div class="flex_1">
                   <div class="flex_between">
                    <div class="flex_start">
                        <div class="info">
                          <div class="flex_start ">
                              <p class="teacher">王老师</p>
                               <!-- <div class="flex_start stars">
                                  <div v-for="item in 5" :key="item" class="star-box">
                                        <img src="@/assets/pc/star.png" alt="" v-if="item>starnum">
                                        <img src="@/assets/pc/stared.png" alt="" v-else>
                                  </div>
                               </div> -->
                          </div>
                          <p class="date">2023-09-09</p>
                      </div>
                      <div class="recommend">设为推荐</div>
                    </div>
                    
                       <ul class="flex_start action-list">
                          <li class="flex_start">
                              <img src="@/assets/pc/eye.png" alt="">
                              <p>123</p>
                          </li>
                          <li class="flex_start">
                              <img src="@/assets/pc/comment-star.png" alt="">
                              <p>123</p>
                          </li>
                          <li class="flex_start">
                              <img src="@/assets/pc/comment.png" alt="">
                              <p>123</p>
                          </li>
                          <li class="flex_start">
                              <img src="@/assets/pc/thumb.png" alt="">
                              <p>123</p>
                          </li>
                       </ul>
                   </div>
                   <div class="rich">
                      <div :class="!isopen?'rich-html':'rich-open-html'" ref="richRef">
                          这周过的很快，转眼之间又到了周末。这周的 这周过的很快，转眼之间又到了周末。这周的 这周过的很快，转眼之间又到了周末。这周的
                          这周过的很快，转眼之间又到了周末。这周的 这周过的很快，转眼之间又到了周末。这周的 这周过的很快，转眼之间又到了周末。这周的
                          这周过的很快，转眼之间又到了周末。这周的 这周过的很快，转眼之间又到了周末。这周的 这周过的很快，转眼之间又到了周末。这周的
                          这周过的很快，转眼之间又到了周末。这周的 这周过的很快，转眼之间又到了周末。这周的 这周过的很快，转眼之间又到了周末。这周的
                          这周过的很快，转眼之间又到了周末。这周的 这周过的很快，转眼之间又到了周末。这周的 这周过的很快，转眼之间又到了周末。这周
                       </div>
                       <p class="has-more" v-show="hasmore" @click="isopen=!isopen">
                           {{ isopen?'收起':'全部' }}
                       </p>
                   </div>
                 
                   <div class="bottom">
                      <ul class="flex_start tag-list">
                          <li class="flex_start" v-for="item in 4" :key="item">
                              <img src="@/assets/pc/tag.png" alt="">
                              <p>教学心得</p>
                          </li>
                      </ul>
                   </div>
            </div>
    </div>
  </template>
  
  <script setup>
  import {onMounted, ref} from 'vue'
  let starnum=ref(3)
  let richRef=ref()
  let hasmore=ref(false)
  let isopen=ref(false)
  function checkLines() {
      // 获取文字容器元素
      const textElement = richRef.value;
      // 比较元素的 scrollHeight 和 clientHeight
      if (textElement.scrollHeight > textElement.clientHeight) {
          console.log('文字超过了四行');
          hasmore.value=true
      } else {
          console.log('文字未超过四行');
      }
  }
  onMounted(()=>{
      checkLines()
  })
  </script>
  
  <style lang="scss" scoped>
  .diary{
      margin-top: 10px;
      background: #fff;
      padding: 16px 19px;
      .head{
          width: 52px;
          height: 52px;
          margin-right: 10px;
      }
      .info{
          .teacher{
              font-size: 16px;
              color: #2B2C33;
              font-weight: bold;
              margin-right: 24px;
          }
          .stars{
              .star-box{
                  margin-right: 4px;
              }
              img{
                  width: 24px;
                  height: auto;
              }
          }
      }
      .date{
          font-size: 14px;
          color: #94959C;
          margin-top: 4px;
      }
      .action-list{
          li{
              font-size: 14px;
              color: #94959C;
              margin-right: 24px;
              cursor: pointer;
              &:last-child{
                  margin-right: 0;
              }
              img{
                  width: 14px;
                  height: auto;
                  margin-right: 4px;
              }
          }
      }
      .rich{
          position: relative;
          .rich-html{
          margin-top:8px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          font-size: 16px;
          color: #2B2C33;
        padding-right: 4ch;
        line-height: 24px;
      }
      .rich-open-html{
          margin-top:8px;
          line-height: 24px;
           font-size: 16px;
          color: #2B2C33;
      }
      .has-more{
          font-size: 16px;
          color: #496EEE;
          position: absolute;
          right: 0;
          bottom:0;
          cursor: pointer;
      }
      }
      .bottom{
          .tag-list{
              li{
                  padding: 4px 10px;
                  border-radius: 100px;
                  font-size: 16px;
                  border-radius: 19px;
                  border: 1px solid #E2E3E6;
                  color: #2B2C33;
                  margin: 10px 10px 0 0;cursor: pointer;
                  img{
                      width: 16px;
                      height: auto;
                      margin-right: 8px;
                      
                  }
              }
          }
      }
  }
  </style>
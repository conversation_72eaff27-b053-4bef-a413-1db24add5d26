<template>
    <div class="msjt-check-main">
        <div class="radius-box" style="margin-top:16px;">
            <div class="flex_start">
                <p class="text_20_bold">直播审核</p>
                <ul class="flex_start state-ul">
                    <li v-for="item in state_list" :class="item.id == search_form.state? 'active-state' : ''" :key="item.id" @click="changeState(item)">{{item.title}}</li>
                </ul>
            </div>
            <el-table :data="check_list" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F9F9F9"}'>
                <el-table-column prop="title" label="直播名称" show-overflow-tooltip></el-table-column> 
                <el-table-column prop="startTime" label="开播时间" />
                <el-table-column prop="topic" label="直播主题" />
                <el-table-column prop="createTime" label="申请时间" />
                <el-table-column prop="approveUserTitle" label="审核人" />
                <el-table-column prop="" label="审核状态">
                    <template #default='scope'> 
                        <p v-if="scope.row.state == 1" class="flex_start orange"><img src="@/assets/pc/jky/review-wait.png" class="review-icon" alt="">待审核 </p>
                        <p v-else-if="scope.row.state == 2" class="flex_start red2"><img src="@/assets/pc/jky/review-fail.png" class="review-icon" alt="">已驳回</p>
                        <p v-else-if="scope.row.state == 3" class="flex_start green"><img src="@/assets/pc/jky/review-success.png" class="review-icon" alt="">已通过</p>
                        <p v-else class="flex_start grey2"><img src="@/assets/pc/jky/review-sx.png" class="review-icon" alt="">已失效</p>

                    </template>
                </el-table-column>
                <el-table-column prop="address" label="操作" width="240">
                    <template #default='scope'>
                        <div class="flex_start" style="height:100%">
                            <el-button type="primary" link style="font-size:16px;font-weight:400;" @click="goDetail(scope.row)">查看 </el-button>
                            <p class="table-line"></p> 
                             <!-- v-if="scope.row.state == 1" -->
                            <el-button type="primary" link style="font-size:16px;font-weight:400;" :style="scope.row.canReplay ? 'color: #E72C4A' : 'color: #409EFF;'" @click="closeClick(scope.row)">{{scope.row.canReplay ? '关闭回放' : '开启回放'}}</el-button>
                            <p class="table-line"></p> 
                            <el-button type="primary" link style="font-size:16px;color: #E72C4A;font-weight:400;" @click="deleteClick(scope.row)">删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    background
                    layout="total,prev, pager, next"
                    :total="total"
                    class="mt-4"
                    :current-page='search_form.pageNum'
                    :page-size="search_form.pageSize"
                    @current-change='currentChange'
                />
        </div>
    </div>
</template> 
<script setup>
    import { reactive,ref,onBeforeMount,onMounted,onActivated} from 'vue'
    import { CirclePlusFilled } from '@element-plus/icons-vue'
    import { applyList, msjtClose, msjtOpen, msjtDel } from '@/api/pc/msjt'
    import {ElMessage,ElLoading,ElMessageBox} from 'element-plus'
    import {useRoute,useRouter} from 'vue-router'
    let route = useRoute()
    let router = useRouter()

    const formObj = function(obj = {}){
        this.state = obj.state || 0
        this.name = obj.name || ''
        this.pageNum = obj.pageNum || 1
        this.pageSize = obj.pageSize || 10
    }
    const search_form = ref(new formObj({}))
    const total = ref(0)
    const check_list = ref([])
    const state_list = ref([{id:0,title:'全部'},{id:1,title:'待审核'},{id:2,title:'已驳回'},{id:3,title:'已通过'},{id:4,title:'已失效'}])

    const getCheckList = async () => {
        let res = await applyList(search_form.value)
        if(res){
            check_list.value = res.data.list
            total.value = res.data.total
        }
    }

    const searchClick = () => {

    }
    const changeState = (item) => {
        search_form.value.state = item.id
        getCheckList()
    }
    const currentChange = (page) => {
        search_form.value.pageNum = page
        getCheckList()
    }
    const goDetail = (row) => {
        router.push(`/msjt/check/add/${row.id}`)
    }
    const closeClick = (row) => { // 关闭回放
        ElMessageBox.confirm( `确认${row.canReplay ? '关闭' : '开启'}回放吗?`, '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        ).then(async () => {
            let res = await (row.canReplay ?  msjtClose(row.id) : msjtOpen(row.id))
            if(res){
                ElMessage.success(row.canReplay ? '关闭成功' : '开启成功')
                getCheckList()
            }
        })
    }
    const deleteClick = (row) => { // 删除
        ElMessageBox.confirm( '确认删除吗?', '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        ).then(async () => {
            let res = await msjtDel(row.id)
            if(res){
                ElMessage.success('删除成功')
                getCheckList()
            }
        })
    }
    onActivated(() => {
        getCheckList()
    })
</script>
<style lang="scss" scoped>
.msjt-check-main{max-width: 1128px;margin:0 auto;font-family: PingFangSC, PingFang SC;
    .radius-box{border-radius: 12px;background: #fff;padding: 24px 24px 6px;margin: 10px 0;}
    .review-icon{width: 14px;height: 14px;margin-right: 8px;}
    .state-ul{border: 1px solid #508CFF;border-radius: 8px;background: #F0F5FF;font-weight: 400; font-size: 16px; color: #508CFF; line-height: 24px; text-align: left;margin-left:24px;overflow:hidden;
        li{padding: 8px 24px;cursor: pointer;}
        .active-state{background: #508CFF;color: #FFFFFF;font-weight: 600;}
    }
}
</style> 
<style lang="scss"> 
    .el-table th.el-table__cell.is-leaf{border: none!important;}
</style>
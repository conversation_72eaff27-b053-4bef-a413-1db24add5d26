<template>
    <div class="allocation-main">
        <ul class="flex_start tab-ul">
            <li v-for="item in tab_list" :key="item.id" :class="current_tab === item.id? 'active-tab' : ''" @click="current_tab = item.id">{{item.title}}</li>
        </ul>
        <div class="flex_start_0">
            <div>
                <ul class="group-ul">
                    <li v-for="(item,index) in group_list" :key="item.id" class="flex_start" :class="current_group === item.id? 'active-group' : ''" @click="current_group = item.id">
                        <p>第{{letters[index]}}组</p>
                        <el-icon class="ml_10"><ArrowRight /></el-icon>
                    </li>
                </ul>
                <div class="flex_center group-add-btn pointer" @click="addGroup">
                    <img src="@/assets/pc/jky/add.png" class="state-icon" alt="">
                </div>
            </div>
            <div class="select-pw">
                <p class="text_16_bold">选题价值</p>
                <div class="select-box flex_start_start mb_24 flex_wrap pointer" :class="current_category === 'xtjz'? 'active-select' : ''" @click="changeCategory('xtjz')">
                    <p v-if="currentGroupData.xtjz && currentGroupData.xtjz.length > 0" class="selected-user flex_center" v-for="item in currentGroupData.xtjz" :key="item.id">
                        <span class="mr_10">{{item.name}}</span>
                        <el-icon @click.stop="removeExpert(currentGroupData.xtjz, item.id)"><Close /></el-icon>
                    </p>
                    <p v-else>请选择</p>
                </div>
                <p class="text_16_bold">设计与论证</p>
                <div class="select-box flex_start_start mb_24 flex_wrap pointer" :class="current_category === 'sjylz'? 'active-select' : ''" @click="changeCategory('sjylz')">
                    <p v-if="currentGroupData.sjylz && currentGroupData.sjylz.length > 0" class="selected-user flex_center" v-for="item in currentGroupData.sjylz" :key="item.id">
                        <span class="mr_10">{{item.name}}</span>
                        <el-icon @click.stop="removeExpert(currentGroupData.sjylz, item.id)"><Close /></el-icon>
                    </p>
                    <p v-else>请选择</p>
                </div>
                <p class="text_16_bold">可行性分析</p>
                <div class="select-box flex_start_start flex_wrap pointer" :class="current_category === 'kxxfx'? 'active-select' : ''" @click="changeCategory('kxxfx')">
                    <p v-if="currentGroupData.kxxfx && currentGroupData.kxxfx.length > 0" class="selected-user flex_center" v-for="item in currentGroupData.kxxfx" :key="item.id">
                        <span class="mr_10">{{item.name}}</span>
                        <el-icon @click.stop="removeExpert(currentGroupData.kxxfx, item.id)"><Close /></el-icon>
                    </p>
                    <p v-else>请选择</p>
                </div>
                <el-icon v-if="group_list.length > 1" class="remove-group-btn red2" size="16" @click="removeGroup"><Delete /></el-icon>
            </div>
            <div class="half-box">
                <div class="flex_between">
                    <el-select class="select_40 mr_24" v-model="search_form.sch" placeholder="所在单位" @change="changeExtend">
                        <el-option v-for="item in expert_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                    <el-input v-model="search_form.name" @input="changeUserName" class="input_40" clearable placeholder="搜索评委姓名"></el-input>
                </div>
                <el-table :data="teacher_list" class="my-table" ref="multipleTableRef" stripe @selection-change="handleSelectionChange" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F8F9FF"}'>
                    <el-table-column type="selection" width="55" />
                    <el-table-column prop="name" label="评委姓名" show-overflow-tooltip></el-table-column> 
                    <el-table-column prop="sch" label="所在单位" show-overflow-tooltip></el-table-column> 
                </el-table>
                
            </div>
        </div>
        <el-pagination
            background
            layout="total,prev, pager, next"
            :total="total"
            class="mt-4"
            :current-page='search_form.pageNum'
            :page-size="search_form.pageSize"
            @current-change='currentChange'
        />
    </div>
</template>

<script setup>
import { reactive, ref, computed, onBeforeMount, onMounted, nextTick, watch } from 'vue'
import { toRaw } from "@vue/reactivity"
import { ArrowRight, Delete, Close } from '@element-plus/icons-vue'

// 表单对象构造函数
const formObj = function(obj = {}){
    this.sch = obj.sch || ''
    this.name = obj.name || ''
    this.pageNum = obj.pageNum || 1
    this.pageSize = obj.pageSize || 8
}

// 响应式数据
const search_form = ref(new formObj({}))
const total = ref(0)
const tab_list = ref([{id:1,title:'评委分组'},{id:2,title:'材料分配'}])
const current_tab = ref(1)
const letters = ['一','二','三','四','五','六','七','八','九','十','十一','十二','十三','十四','十五','十六','十七','十八','十九','二十']
const group_list = ref([{id:1,xtjz:[],sjylz:[],kxxfx:[]}])
const current_group = ref(1)
const current_category = ref('xtjz')
const teacher_list = ref([])
const expert_select = ref([
    {value: '山东中医药大学', label: '山东中医药大学'},
    {value: '其他院校', label: '其他院校'}
])

// 获取当前选中组的数据
const currentGroupData = computed(() => {
    return group_list.value.find(item => item.id === current_group.value) || {}
})

// 表格引用
const multipleTableRef = ref(null)

// 获取评委列表
const getTeacherList = async () => {
    try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 300))
        
        // 根据搜索条件过滤数据
        let filteredTeachers = [
            {id: 1, name:'赵一凡', sch:'山东中医药大学'},
            {id: 2, name:'骆明庆', sch:'山东中医药大学'},
            {id: 3, name:'刘晓燕', sch:'山东中医药大学'},
            {id: 4, name:'付逸兴', sch:'山东中医药大学'},
            {id: 5, name:'高涵', sch:'山东中医药大学'},
            {id: 6, name:'陈嘉阳', sch:'山东中医药大学'},
            {id: 7, name:'高涵1', sch:'山东中医药大学'},
            {id: 8, name:'高涵2', sch:'山东中医药大学'},
            {id: 9, name:'高涵3', sch:'山东中医药大学'},
            {id: 10, name:'高涵4', sch:'山东中医药大学'},
        ]
        
        // 应用搜索过滤
        if (search_form.value.name) {
            filteredTeachers = filteredTeachers.filter(teacher => 
                teacher.name.includes(search_form.value.name)
            )
        }
        
        if (search_form.value.sch) {
            filteredTeachers = filteredTeachers.filter(teacher => 
                teacher.sch === search_form.value.sch
            )
        }
        
        // 分页处理
        const startIndex = (search_form.value.pageNum - 1) * search_form.value.pageSize
        const endIndex = startIndex + search_form.value.pageSize
        
        teacher_list.value = filteredTeachers.slice(startIndex, endIndex)
        total.value = filteredTeachers.length
        
        // 恢复已选择的评委
        nextTick(() => {
            restoreSelectedTeachers()
        })
    } catch (error) {
        console.error('获取评委列表失败:', error)
        // 这里可以添加错误提示
    }
}

// 恢复已选择的评委
const restoreSelectedTeachers = () => {
    if (!multipleTableRef.value || !currentGroupData.value) return
    
    // 获取当前类别下已选择的评委ID
    const selectedIds = currentGroupData.value[current_category.value]?.map(item => item.id) || []
    
    // 清除所有选择
    multipleTableRef.value.clearSelection()
    
    // 重新选择已选择的评委
    if (selectedIds.length > 0) {
        teacher_list.value.forEach(teacher => {
            if (selectedIds.includes(teacher.id)) {
                multipleTableRef.value.toggleRowSelection(teacher, true)
            }
        })
    }
}

// 处理表格选择变化
const handleSelectionChange = (val) => {
    if (!currentGroupData.value) return
    
    // 更新当前类别下的评委列表
    currentGroupData.value[current_category.value] = val.map(item => ({
        id: item.id,
        name: item.name,
        sch: item.sch
    }))
    
    console.log(toRaw(val), '选中的评委')
}

// 页码变化处理
const currentChange = (page) => {
    search_form.value.pageNum = page
    getTeacherList()
}

// 更改类别
const changeCategory = (category) => {
    current_category.value = category
    // 恢复已选择的评委
    nextTick(() => {
        restoreSelectedTeachers()
    })
}

// 添加新分组
const addGroup = () => {
    const newId = Math.max(0, ...group_list.value.map(item => item.id)) + 1
    group_list.value.push({
        id: newId,
        xtjz: [],
        sjylz: [],
        kxxfx: []
    })
    current_group.value = newId
}

// 移除分组
const removeGroup = () => {
    if (group_list.value.length <= 1) return
    
    const index = group_list.value.findIndex(item => item.id === current_group.value)
    
    if (index !== -1) {
        group_list.value.splice(index, 1)
        
        // 如果删除的是最后一组，切换到上一组
        if (current_group.value > group_list.value.length) {
            current_group.value = group_list.value.length || 1
        }
    }
}

// 移除专家
const removeExpert = (list, id) => {
    const index = list.findIndex(item => item.id === id)
    if (index !== -1) {
        list.splice(index, 1)
        
        // 更新表格选择状态
        nextTick(() => {
            if (multipleTableRef.value) {
                const row = teacher_list.value.find(item => item.id === id)
                if (row) {
                    multipleTableRef.value.toggleRowSelection(row, false)
                }
            }
        })
    }
}

// 更改搜索条件
const changeExtend = () => {
    search_form.value.pageNum = 1
    getTeacherList()
}

// 更改用户名搜索
const changeUserName = () => {
    search_form.value.pageNum = 1
    getTeacherList()
}

// 生命周期钩子
onMounted(() => { 
    getTeacherList()
})

// 监听当前组或类别的变化，恢复已选择的评委
watch([current_group, current_category], () => {
    nextTick(() => {
        restoreSelectedTeachers()
    })
})
</script>

<style lang="scss" scoped>
.allocation-main{
    .tab-ul{
        border-bottom: 1px solid #E2E3E6;
        margin-bottom:20px;
        display:inline-flex;
        
        li{
            margin-right: 32px;
            color: #94959C;
            padding: 6px 0;
            cursor: pointer;
        }
        
        .active-tab{
            font-weight: bold;
            color: #508CFF;
            border-bottom: 2px solid #508CFF;
        }
    }
    
    .group-ul{
        width: 112px;
        
        li{
            font-weight: 400; 
            font-size: 16px; 
            color: #2B2C33; 
            line-height: 22px; 
            text-align: left;
            padding: 10px 18px;
            cursor: pointer;
        }
        
        .active-group{
            color: #508CFF;
            background: #DFEAFF;
            font-weight: bold;
        }
    }
    
    .group-add-btn{
        margin-top: 20px;
        
        img{
            width: 16px;
        }
    }
    
    .half-box{
        width: 50%;
        padding-left: 20px;
    }
    
    .select-pw{
        border: 1px solid #E2E3E6;
        border-radius: 0px 8px 8px 8px;
        padding: 16px 16px 46px;
        width: calc(50% - 112px);
        position:relative;
        
        .select-box{
            border: 1px solid #E2E3E6;
            border-radius: 8px;
            padding: 8px;
            height: 88px;
            margin-top: 8px;
            overflow-y: auto;
            
            .selected-user{
                background: #DFEAFF;
                border-radius: 8px;
                font-weight: 400;
                padding: 5px 16px;
                margin: 0 8px 8px 0; 
                font-size: 16px; 
                color: #508CFF; 
                line-height: 22px; 
                text-align: left;
                
                el-icon{
                    cursor: pointer;
                }
            }
        }
        
        .active-select{
            border: 1px solid #508CFF;
        }
        
        .remove-group-btn{
            position: absolute;
            bottom: 16px;
            right: 17px;
            cursor: pointer;
        }
    }
    
    .pointer{
        cursor: pointer;
    }
    
    .flex_start{
        display: flex;
        align-items: center;
    }
    
    .flex_start_start{
        display: flex;
        align-items: flex-start;
    }
    
    .flex_center{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .flex_between{
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .flex_wrap{
        flex-wrap: wrap;
    }
    
    .ml_10{
        margin-left: 10px;
    }
    
    .mb_24{
        margin-bottom: 24px;
    }
    
    .text_16_bold{
        font-size: 16px;
        font-weight: bold;
    }
    
    .red2{
        color: #F56C6C;
    }
    
    .mt-4{
        margin-top: 16px;
    }
}
</style>    
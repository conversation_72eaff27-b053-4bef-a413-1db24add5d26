import { getCosConfigApi } from "@/api/cos"
import COS from "cos-js-sdk-v5"

export const getCosConfig = async () => {
  try {
    const res = await getCosConfigApi()

    if (res) {
      console.log("获取cos配置成功", res)
      const { data } = res

      const config = data

      const cosInstance = new COS({
        SecretId: data.token.credentials.tmpSecretId,
        SecretKey: data.token.credentials.tmpSecretKey,
        XCosSecurityToken: data.token.credentials.sessionToken,
        StartTime: data.token.startTime,
        ExpiredTime: data.token.expiredTime
      })

      return { cosInstance, config }
    }
  } catch (err) {
    console.warn("获取cos配置失败", err)
    return
  }
}

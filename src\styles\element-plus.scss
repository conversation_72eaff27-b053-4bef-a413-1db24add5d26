// 自定义 Element Plus 样式

// 卡片
.el-card {
  background-color: var(--el-bg-color) !important;
}

// 分页
// 自定义翻页器
.el-pagination {
  justify-content: flex-end;
  margin-top: 16px;

  &__total {
    font-size: 12px;
    color: #94959C;
  }

  &.is-background {
    .btn-prev,
    .btn-next {
      border: 1px solid #dee0eb !important;
      border-radius: 8px;
      background-color: #fff !important;
    }

    .el-pager {
      .number {
        background-color: #fff !important;
        border: 1px solid #dee0eb !important;
        border-radius: 8px;
      }

      li.is-active {
        background-color: var(--blue1) !important;
      }
    }
  }

  // 参考 Bootstrap 的响应式设计 WIDTH = 768
  @media screen and (max-width: 768px) {
    .el-pagination__total,
    .el-pagination__sizes,
    .el-pagination__jump,
    .btn-prev,
    .btn-next {
      display: none !important;
    }
  }
}

// 自定义按钮
.blue-88-btn {
  width: 88px !important;
  height: 44px !important;
  background: linear-gradient(180deg, #3381ff 0%, #122ff0 100%);
  border-radius: 8px !important;
  color: #fff !important;
  font-size: 16px !important;
  line-height: 24px !important;

  .el-icon {
    margin-right: 5px;
  }
}

.blue-136-btn {
  width: 136px !important;
  height: 44px !important;
  background: linear-gradient(180deg, #3381ff 0%, #122ff0 100%);
  border-radius: 8px !important;
  color: #fff !important;
  font-size: 16px !important;
  line-height: 24px !important;

  .el-icon {
    margin-right: 5px;
  }
}

.plain-88-btn {
  width: 88px !important;
  height: 44px !important;
  background: #fff !important;
  border-radius: 8px !important;
  color: #61636e !important;
  font-size: 16px !important;
  line-height: 24px !important;

  .el-icon {
    margin-right: 5px;
  }
}

.add-112-btn {
  width: 112px !important;
  height: 44px !important;
  background: #fff !important;
  border: 1px solid #122ff0 !important;
  border-radius: 8px !important;
  color: #61636e !important;
  font-size: 14px !important;
  line-height: 20px !important;

  .el-icon {
    color: #122ff0 !important;
    font-size: 16px !important;
  }

  &:hover {
    background: #ebf2ff !important;
  }

  &:disabled {
    background: #fff !important;
    border: 1px solid #c1c3ce !important;
    color: #c1c3ce !important;
  }
}

.dialog-cancel-88-btn {
  width: 88px !important;
  height: 44px !important;
  background: #fff !important;
  border: 1px solid #122ff0 !important;
  border-radius: 8px !important;
  color: #61636e !important;
  font-size: 14px !important;
  line-height: 20px !important;

  .el-icon {
    color: #61636e !important;
    font-size: 16px !important;
  }

  &:hover {
    background: #ebf2ff !important;
  }

  &:disabled {
    background: #fff !important;
    border: 1px solid #c1c3ce !important;
    color: #c1c3ce !important;
  }
}

.cancel-112-btn {
  width: 112px !important;
  height: 44px !important;
  background: #fff !important;
  border: 1px solid #122ff0 !important;
  border-radius: 8px !important;
  color: #61636e !important;
  font-size: 14px !important;
  line-height: 20px !important;

  .el-icon {
    color: #61636e !important;
    font-size: 16px !important;
  }

  &:hover {
    background: #ebf2ff !important;
  }

  &:disabled {
    background: #fff !important;
    border: 1px solid #c1c3ce !important;
    color: #c1c3ce !important;
  }
}

.publish-88-btn {
  width: 88px !important;
  height: 32px !important;
  line-height: 32px !important;
  border-radius: 16px !important;
  border: 1px solid #122ff0 !important;
  color: #61636e !important;

  img {
    margin-right: 3px;
  }

  &:hover {
    background: #ebf2ff !important;
  }
}

.cancel-88-btn {
  width: 88px !important;
  height: 32px !important;
  line-height: 32px !important;
  border-radius: 16px !important;
  border: 1px solid #c1c3ce !important;
  color: #61636e !important;

  &:hover {
    background: rgba($color: #c1c3ce, $alpha: 0.1) !important;
  }
}

.delete-112-btn {
  width: 112px;
  height: 44px;
  background: #fff;
  border: 1px solid #ea4c42;
  border-radius: 8px;
  color: #61636e;
  font-size: 16px;
  line-height: 24px;

  &:hover {
    background: #ffeeed;
    color: #61636e;
    border: 1px solid #ea4c42;
  }
}

// 自定义顶部 flex 布局 表单
.custom-flex-top-form {
  display: flex;
  gap: 16px;
  justify-content: flex-start;
  align-items: flex-end;

  .el-form-item {
    &__label {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      color: #31333e;
    }
  }

  .el-input {
    &__wrapper {
      width: 284px;
      border-radius: 8px;
    }

    &__inner {
      height: 42px;
      line-height: 42px;
    }
  }
}

// 自定义表格
.custom-table {
  border-radius: 8px !important;

  .el-table__cell {
    padding: 16px 0 !important;
  }

  .el-table__header {
    .cell {
      color: #31333e !important;
      font-size: 16px !important;
      line-height: 24px !important;
      font-weight: 600 !important;
    }
  }

  .el-table__body {
    .cell {
      font-size: 16px !important;
      line-height: 24px !important;
    }

    .el-popper {
      max-width: 500px;
    }

    .cell {
      &.el-tooltip {
        div {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}

// 自定义确认框
.custom-confirm-box {
  padding: 20px;

  .el-message-box__header {
    padding-bottom: 20px;

    .el-message-box__title {
      font-size: 18px;
      line-height: 26px;
      font-weight: 600;
      color: #31333e;
    }

    .el-message-box__headerbtn {
      width: 56px;
      height: 56px;

      &:hover {
        color: var(--blue1) !important;
      }
    }
  }

  .el-message-box__container {
    .el-icon {
      display: none;
    }

    .el-message-box__message {
      font-size: 16px;
    }
  }

  .el-message-box__btns {
    display: flex;
    justify-content: center;
    padding-top: 30px;

    .confirm-btn {
      width: 136px;
      height: 44px;
      background: linear-gradient(180deg, #3381ff 0%, #122ff0 100%);
      border-radius: 8px;
      color: #fff;
      font-size: 16px;
      line-height: 24px;

      &:disabled {
        opacity: 0.3;
        background: linear-gradient(180deg, #3381ff 0%, #122ff0 100%);
        color: #fff;
      }

      &:hover {
        background: linear-gradient(180deg, #3381ff 0%, #122ff0 100%);
        color: #fff;
        opacity: 0.7;
      }
    }

    .cancel-btn {
      width: 88px;
      height: 44px;
      background: #fff;
      border-radius: 8px;
      border: 1px solid #122ff0;
      color: #61636e;
      font-size: 16px;
      line-height: 24px;

      &:disabled {
        opacity: 0.3;
        background: #fff;
        color: #c1c3ce;
      }

      &:hover {
        background: #fff;
        color: #c1c3ce;
        opacity: 0.7;
      }
    }
  }
}

// 自定义 form
.custom-form {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 172px;
  row-gap: 30px;

  .el-form-item {
    &__label {
      font-size: 16px;
      line-height: 24px;
      font-weight: 600;
      color: #31333e;
    }
  }

  .el-input {
    &__wrapper {
      width: 100%;
      height: 42px;
      line-height: 42px;
      border-radius: 8px;
    }

    &__inner {
      height: 42px;
      line-height: 42px;
      font-size: 16px;
    }
  }

  .el-radio {
    &__inner {
      width: 20px;
      height: 20px;
    }

    &__label {
      font-size: 16px;
      line-height: 24px;
      color: var(--grey1);
    }

    &.is-checked {
      .el-radio__inner {
        border-color: var(--green1) !important;
        background: #fff !important;

        &::after {
          width: 12px;
          height: 12px;
          background: var(--green1) !important;
        }
      }

      .el-radio__label {
        color: var(--grey1) !important;
      }
    }
  }

  .el-upload {
    width: 112px;
    height: 112px;
    background: #ffffff;
    border-radius: 8px;
  }
}

// 自定义弹窗
.custom-dialog {
  padding: 20px;
  padding-bottom: 32px;

  .el-dialog__header > .el-dialog__title {
    --el-text-color-primary: var(--grey1);
    font-weight: 600;
    font-size: 18px;
  }

  .el-dialog__headerbtn {
    width: 60px;
    height: 70px;
    font-size: 20px;
  }

  .el-dialog__body {
    .el-form-item__label {
      color: var(--grey1);
      font-size: 16px;
      font-weight: 600;
      line-height: 48px;

      .tip-container {
        position: relative;

        .tip {
          position: absolute;
          left: 80px;
          top: 0;
          width: 200px;
        }
      }
    }

    .el-form-item.is-required {
      .el-form-item__label {
        &::after {
          color: var(--el-color-danger);
          content: "*";
          margin-left: 4px;
        }

        &::before {
          display: none;
        }
      }
    }

    .download-form {
      .el-form-item__label {
        line-height: 24px;
      }
    }
  }

  .el-form-item {
    .el-input__wrapper {
      height: 48px;
      line-height: 48px;
      border-radius: 8px;
    }

    .el-input__inner {
      height: 46px;
      line-height: 46px;
      font-size: 16px;
    }

    .el-date-editor {
      width: 100%;
      height: 48px;
      border-radius: 8px;
    }

    .el-select {
      height: 48px;
      border-radius: 8px;

      &__wrapper {
        height: 48px;
        font-size: 16px;
        border-radius: 8px;
      }
    }

    .el-upload {
      width: 100%;

      .el-input__inner {
        cursor: pointer !important;
      }
    }

    .upload-has-file {
      position: relative;

      .el-input__wrapper {
        padding-right: 34px;
      }

      .upload-close-icon {
        position: absolute;
        right: 14px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
      }
    }
  }

  .confirm-btn {
    width: 136px;
    height: 44px;
    background: linear-gradient(180deg, #3381ff 0%, #122ff0 100%);
    border-radius: 8px;
    color: #fff;
    font-size: 16px;
    line-height: 24px;

    &:hover {
      background: linear-gradient(180deg, #3381ff 0%, #122ff0 100%);
      color: #fff;
      opacity: 0.7;
    }

    &:disabled {
      opacity: 0.3;
      background: linear-gradient(180deg, #3381ff 0%, #122ff0 100%);
      color: #fff;
    }
  }

  .cancel-btn {
    width: 88px;
    height: 44px;
    background: #fff;
    border-radius: 8px;
    border: 1px solid #122ff0;
    color: #61636e;
    font-size: 16px;
    line-height: 24px;
  }

  .export-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.el-form-item.is-required {
  .el-form-item__label {
    &::after {
      color: var(--el-color-danger);
      content: "*";
      margin-left: 4px;
    }

    &::before {
      display: none;
    }
  }
}

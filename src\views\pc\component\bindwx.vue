<template>
      <div>
         <el-dialog title="" v-model="props.bindwxVisibleShow" width="456px" :close-on-click-modal='false'>
            <div class="header">
                <img src="@/assets/pc/logo.png" alt="" class="header-bg">
                <img src="@/assets/pc/dialog-del.png" alt="" class="dialog-del" @click="goCancel"/>
            </div>  
             <div class="bind-wx flex_column flex_center">
                  <h3  v-show="status=='PENDING'||status=='EXPIRED'">绑定微信</h3>
                   <div class="qrcode" v-show="status=='PENDING'||status=='EXPIRED'">
                       <img :src="qrBase64" alt="" style="width:200px;height:200px">
                       <div class="expired flex_column flex_center" @click="reloadQR" v-show="status=='EXPIRED'">
                            <p style="font-size:18px">二维码已过期</p>
                            <el-icon style="font-size:20px;color:#508CFF;margin-top:24px"><RefreshRight /></el-icon>
                            <p style="font-size: 16px;color: #508CFF;">点击刷新</p>
                       </div>
                   </div>
                  <p class="tip"  v-show="status=='PENDING'||status=='EXPIRED'">
                      微信扫一扫，立即绑定
                  </p>

                  <div class="bindok flex_column flex_center" v-show="status=='OK'">
                        <img src="@/assets/pc/bindok.png" alt="" style="width:184px;height:auto" />
                        <div class="flex_center">
                             <img src="@/assets/pc/box-checked.png" alt="" style="width:20px;height:auto;margin-right:4px" />
                            <p style="font-size:15px">扫描成功，请在手机上授权是否绑定</p>
                        </div>
                        <button @click="goCancel">返回</button>
                  </div>
             </div>
         </el-dialog>
      </div>
</template>
<script setup>
import {onMounted, reactive, ref,defineProps,defineEmits, watch} from 'vue'
import {storeToRefs} from 'pinia'
// import { useUserStore } from "@/store/modules/user"
import {getQrCode,getBindStatus} from '@/api/pc/index.js'
import {useUserInfoStore} from "@/store/modules/pc"
import { ElMessage } from 'element-plus'
// let userStore=useUserStore()
let useUserInfo=useUserInfoStore()
let props=defineProps(['bindwxVisibleShow'])
let emits=defineEmits(['emitBindWx'])
// let { bindwxVisibleShow } = storeToRefs(useUserInfo)
// let bindwxVisibleShow=ref(true)
let dialogTableVisible=ref(false)
let loginRef=ref()
let qrBase64=ref('')
let bindkey=ref('')
let status=ref('PENDING')
let interval=''
watch(()=>props.bindwxVisibleShow,(newval)=>{
    if(newval){
       initQr() 
    }else{
        clearInterval(interval)
    }
})
let initQr=async()=>{
  let res=await getQrCode()
  if(res){
    bindkey.value=res.data.key
    qrBase64.value='data:image/png;base64,'+res.data.image
     interval=setInterval(()=>{
        getstatus()
    },3000)
  }
}
let goCancel=()=>{
   emits('emitBindWx',false)
}
let reloadQR=()=>{
     initQr() 
}
let getstatus=async()=>{
    let res=await getBindStatus({
        bindKey:bindkey.value
    })
    if(res){
         status.value=res.data
         if(status.value!='PENDING'){
              clearInterval(interval)
            if(status.value=='OK'){
              useUserInfo.getInfo()
           }
          
         }
        //  console.log(res.data,'res.data')
    }
}
onMounted(()=>{
   
//    
})
</script>
<style scoped lang='scss'>
.header{
    position: relative;
   .header-bg{
    width: 100%;
  }
  .dialog-del{
    position: absolute;
    top:24px;
    right: 24px;
    height: 16px;
    width: 16px;
    cursor: pointer;
  }
}
:deep(.el-dialog__header){
    display: none;
}
:deep(.el-overlay-dialog .el-dialog){
    padding: 0;
    border-radius: 7px;
}
:deep(.el-dialog__body){
    padding: 0;
    border-radius:8px ;
}
.bind-wx{
    padding: 24px 0 36px 0;
    h3{
        text-align: center;
        font-size: 20px;
        color: #2B2C33;
    }
    .qrcode{
        width: 200px;
        height: 200px;
        position: relative;
        .expired{
             width: 200px;
        height: 200px;
        position: absolute;
        top:0;
        left:0;
        cursor: pointer;
        background: #FFFFFF;
opacity: 0.9;
        }
    }
    .tip{
        font-size: 16px;
        color: #508CFF;
        text-align: center;
        margin-top: 16px;
    }
}
.bindok{
    margin-top: 34px;
    button{
        width: 184px;
height: 48px;
background: #508CFF;
color:#fff;
border-radius: 12px;
outline: none;
margin-top: 34px;
border:none;
cursor: pointer;
    }
}
</style>

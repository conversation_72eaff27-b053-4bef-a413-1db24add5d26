<template>
    <div class="editor-container">
        <div style="border: 1px solid #ccc">
    
      <Editor
        style="height: 96px; overflow-y: hidden;"
        v-model="valueHtml"
        :defaultConfig="editorConfig"
        :mode="mode"
        @onCreated="handleCreated"
         @onChange="onChange"
      />
     
    </div>
    </div>
</template>

<script setup>
  import '@wangeditor/editor/dist/css/style.css' // 引入 css
  import {uploadFileFunc} from '@/utils/pc-upload.js'
  import { onBeforeUnmount, ref, shallowRef, onMounted,defineProps, watch,defineEmits } from 'vue'
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
        let mode='default'
     
      let props=defineProps(['html'])
      let emits=defineEmits(['emitHtml'])
      // 内容 HTML
      const valueHtml = ref('')

      // 模拟 ajax 异步获取内容
      onMounted(() => {
        
      })
       watch(()=>props.html,(newval)=>{
        valueHtml.value=newval
       },{
        immediate:true
       })
     
      let onChange=(editor)=>{
        // console.log(editor.getHtml())
        if(editor.getHtml()=='<p><br></p>'){
           emits('emitHtml','')
        }else{
          emits('emitHtml',editor.getHtml())
        }
            
      } 
      const editorConfig = { 
          placeholder: '请输入内容...', 
         MENU_CONF: {},
       
       }
      
   let handleCreated=()=>{
    
   }

</script>

<style lang="scss" scoped>
.editor-container{
    margin-top: 16px;
}
:deep(.w-e-text-placeholder){
    top:8px
}
:deep(.w-e-text-container p){
//   top:10px
padding:0;
margin: 0;
line-height: 20px;
}
:deep(.w-e-text-container){
  font-size: 16px;
  padding: 6px;
}
:deep(.w-e-modal button){
  line-height: 16px;
}
:deep(.w-e-modal){
  height: 260px;
  overflow: auto;
}
</style>
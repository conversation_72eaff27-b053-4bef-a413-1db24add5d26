<script setup>
import { ref, watch, onMounted } from "vue"
import { EchartsUI, useEcharts } from "@/components/Echart"

const chartRef = ref()
const { renderEcharts } = useEcharts(chartRef)

const props = defineProps({
  chartData: {
    type: Object,
    default: () => ({
      data: [],
      color: [],
      pieColor: []
    })
  }
})

watch(
  () => props.chartData,
  () => {
    paintChart()
  },
  { deep: true }
)

onMounted(() => {
  paintChart()
})

// 直接创建渐变色对象，与pieColor对应
const createGradient = (index) => {
  const gradients = [
    { // 自我学习进步 - 粉红色渐变
      type: 'linear',
      x: 0, y: 0, x2: 1, y2: 0,
      colorStops: [
        { offset: 0, color: '#F45487' },
        { offset: 1, color: '#DD5DFF' }
      ]
    },
    { // 互动交流进步 - 橙色渐变
      type: 'linear',
      x: 0, y: 0, x2: 0, y2: 1,
      colorStops: [
        { offset: 0, color: '#FFA952' },
        { offset: 1, color: '#FFE700' }
      ]
    },
    { // 阶段成长进步 - 蓝色渐变
      type: 'linear',
      x: 0, y: 0, x2: 1, y2: 0,
      colorStops: [
        { offset: 0, color: '#72DCF8' },
        { offset: 1, color: '#5AE0C7' }
      ]
    },
    { // 行知成果进步 - 绿色渐变
      type: 'linear',
      x: 0, y: 0, x2: 0, y2: 1,
      colorStops: [
        { offset: 0, color: '#ECFF4A' },
        { offset: 1, color: '#A9F457' }
      ]
    },
    { // 退步 - 蓝紫色渐变
      type: 'linear',
      x: 0, y: 0, x2: 0, y2: 1,
      colorStops: [
        { offset: 0, color: '#5674F5' },
        { offset: 1, color: '#7C92FF' }
      ]
    }
  ];
  
  return gradients[index % gradients.length];
}

const paintChart = () => {
  const data = props.chartData.data
  
  // 计算总数
  const total = data.reduce((sum, item) => sum + item.points, 0)
  
  // 使用传入的color属性作为文字颜色
  const textColors = props.chartData.color || [
    '#EB58B8', // 自我学习进步
    '#FD9623', // 互动交流进步
    '#35C0D8', // 阶段成长进步
    '#78C613', // 行知成果进步
    '#6C86FB'  // 退步
  ]
  
  // 计算百分比并添加样式
  const seriesData = data.map((item, index) => {
    const percentage = Math.round((item.points / total) * 100)
    
    return {
      value: item.points,
      name: item.eventGroupLabel,
      percentage: percentage,
      itemStyle: {
        color: createGradient(index),
        borderWidth: 2,
        borderColor: '#fff'
      }
    }
  })

  // 创建富文本配置对象
  const richObj = {};
  // 为每个数据项创建一个富文本样式
  seriesData.forEach((item, index) => {
    const color = textColors[index % textColors.length];
    // 名称样式
    richObj[`name${index}`] = {
      fontSize: 12,
      lineHeight: 18,
      color: color
    };
    // 值样式
    richObj[`value${index}`] = {
      fontSize: 12,
      lineHeight: 18,
      color: color
    };
  });

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      show: false
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        data: seriesData,
        label: {
          show: true,
          position: 'outside',
          formatter: (params) => {
            // 使用索引确保每个标签都有正确的颜色
            const idx = params.dataIndex;
            return `{name${idx}|${params.name}}\n{value${idx}|${params.value} | ${params.percent}%}`;
          },
          rich: richObj
        },
        emphasis: {
          label: {
            show: true,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          smooth: true,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        }
      }
    ]
  }

  // 通过此方式单独设置每个项的连接线颜色
  option.series[0].data = option.series[0].data.map((item, index) => {
    const color = textColors[index % textColors.length];
    item.labelLine = {
      lineStyle: {
        color: color
      }
    };
    return item;
  });

  renderEcharts(option)
}
</script>

<template>
  <EchartsUI ref="chartRef" :height="'228px'" />
</template>

<style scoped></style>

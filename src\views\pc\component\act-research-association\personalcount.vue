<template>
    <div class="count">
        <p class="title">个人日记统计</p>
       
        <ul class="flex_between count-list">
            <li class="flex_center flex_column flex_1">
                <h4>{{countForm.totalRecords}}</h4>
                <p>日记数</p>
            </li>
            <li class="flex_center flex_column flex_1" v-if="roles.indexOf('DIRECTOR')<0">
                <h4 class="star-num">{{countForm.totalScore}}</h4>
                <p>得星数</p>
            </li>
            <li class="flex_center flex_column flex_1" v-if="roles.indexOf('DIRECTOR')<0">
                <h4>{{countForm.totalEvaluations}}</h4>
                <p>评价数</p>
            </li>
            <li class="flex_center flex_column flex_1" v-if="roles.indexOf('DIRECTOR')<0">
                <h4>{{countForm.totalComments}}</h4>
                <p>评论数</p>
            </li>
            <li class="flex_center flex_column flex_1">
                <h4 class="thumnb-num">{{countForm.totalLikes}}</h4>
                <p>点赞数</p>
            </li>
            <li class="flex_center flex_column flex_1">
                
                <h4>{{countForm.totalViews}}</h4>
                <p>阅读数</p>
            </li>
        </ul>
    </div>
</template>
<script setup>
import {defineProps} from 'vue'
let props=defineProps(['countForm'])
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'

let userInfoObj=useUserInfoStoreHook()
let {roles}=storeToRefs(userInfoObj)
</script>
<style lang="scss" scoped>
.count{
    padding: 18px 18px 20px 18px;
    background: #FFFFFF;
border-radius: 4px;
    .title{
        font-size: 16px;
        color: #2B2C33;
    }
    .count-list{
        margin-top: 25px;
        cursor: pointer;
        li{
            // padding-right: 46px;
            // padding-left: 46px;
            border-right: 1px solid #E2E3E4;
            &:last-child{
                padding-right: 0;
                border-right: none;
            }
            &:first-child{
                padding-left: 0;
            }
           h4{
                font-size: 22px;
                color: #508CFF;
                font-weight: bold;
            }
            .star-num{
                color: #FFA833;
            }
            .thumnb-num{
                color: #FF6767;
            }
            p{
                font-size: 16px;
                color: #6D6F75;
                margin-top: 8px;
            }
        }
    }
}
</style>
<template>
  <div class="bckWhite" style="padding-top:20px">
      <div  class="content-box  question">
          <el-carousel height="300px" style="width:100%">
            <el-carousel-item v-for="item in 1" :key="item">
                <img src="@/assets/pc/homepage/banner1.png" alt="" style="width:100%;height:100%">
            </el-carousel-item>
          </el-carousel>
          <ul class="flex_start flex_wrap statistic">
             <li class="total-1">
                <p class="label">资源总量</p>
                <p class="value">{{statisticForm.numberOfResource}}</p>
             </li>
            <li class="total-2">
                <p class="label">学校总数</p>
                <p class="value">{{statisticForm.numberOfSchool}}</p>
             </li>
             <li class="total-3">
                <p class="label">学生总数</p>
                <p class="value">{{statisticForm.numberOfStudent}}</p>
             </li>
              <li class="total-4">
                <p class="label">教师总数</p>
                <p class="value">{{statisticForm.numberOfTeacher}}</p>
             </li>
             <li class="total-5">
                <p class="label">互动课堂开课数</p>
                <p class="value">{{statisticForm.numberOfOpenLive}}</p>
             </li>
          </ul>
      </div>
  </div>
    <div class="resource-recommend">
     <div class="content-box">
          <div class="title flex_between">
            <div class="flex_start left">
              <img src="@/assets/pc/homepage/week-class.png" alt="">
              <p>本周课表</p>
            </div>
             <!-- <p class="more">更多</p> -->
          </div>
          <div>
            <el-row class="week">
                <el-col :span="1"></el-col>
                <el-col :span="23">
                    <el-row>
                            <el-col :span="2"><div class="flex_center"> 课时</div></el-col>
                            <el-col :span="3" v-for="item in dateList" :key="item"><div class="flex_center">{{item}}</div></el-col>
                    </el-row>
                </el-col>
            </el-row>
            <el-row style="margin-top:8px">
                <el-col :span="1" class="afternoon flex_center">
                    <div class="flex_center">
                        <p>上</p>
                         <p style="margin-top:8px">午</p>
                    </div>
                </el-col>
                
                <el-col :span="23" >
                    <el-row v-for="(item,index) in lastFourList" :key="index" class="day">
                            <el-col :span="2"><div class="flex_center"> {{index+1}}</div></el-col>
                            <el-col :span="3" v-for="(citem,cindex) in item.dataList" :key="cindex" class="flex_center flex_column">
                                <div class="flex_center" style="text-align:center" v-for="(gcitem,gcindex) in citem.timetableList" :key="gcindex"> 
                                    {{gcitem.title}}
                                </div>
                            </el-col>
                    </el-row>
                </el-col>
            </el-row>
           <el-row style="margin-top:8px">
                <el-col :span="1" class="afternoon flex_center">
                    <div class="flex_center">
                        <p>下</p>
                         <p style="margin-top:8px">午</p>
                    </div>
                </el-col>
                
                 <el-col :span="23" >
                    <el-row v-for="(item,index) in nextFourList" :key="index" class="day">
                            <el-col :span="2"><div class="flex_center"> {{index+1}}</div></el-col>
                            <el-col :span="3" v-for="(citem,cindex) in item.dataList" :key="cindex" class="flex_center flex_column">
                               <div class="flex_center" :style="{'text-align':'center','margin-bottom':citem.timetableList.length-1==gcindex?'0px':'8px'}" v-for="(gcitem,gcindex) in citem.timetableList" :key="gcindex"> 
                                    {{gcitem.title}}
                                </div>
                            </el-col>
                    </el-row>
                </el-col>
            </el-row>
          </div>
     </div>
  </div>
  <div class="resource-recommend resource-recommend-active">
     <div class="content-box">
          <div class="title flex_between">
            <div class="flex_start left">
              <img src="@/assets/pc/homepage/res-recommend.png" alt="">
              <p>资源推荐</p>
            </div>
             <p class="more"  @click="goMore">更多</p>
          </div>
          <div class="res-table flex_between_0">
              <div class="hot">
                  <div class="module-name flex_between">
                      <div class="left flex_start">
                        <p>热门资源</p>
                         <img src="@/assets/pc/homepage/hot.png" alt="">
                      </div>
                      <div class="right flex_start" @click="goMore">
                         <p>查看更多</p>
                         <img src="@/assets/pc/homepage/see-more.png" alt="">
                      </div>
                  </div>
                  <swiper
                    v-bind="swiperOptions"
                    class="scroll-container">
                    <swiper-slide  v-for="item in hotList" :key="item.resourceId">
                        <div class="flex_between res-list-item" @click="goHot(item.resourceId)">
                            <p class="file-name">{{item.resourceName}}</p>
                            <p class="file-date">{{item.createTime}}</p>
                        </div>
                    </swiper-slide>
                  
                  </swiper>
                    <el-empty description="暂无数据" style="background:none;height:200px" v-show="hotList.length==0" :image-size="60" >

                  </el-empty>
              </div>
              <div class="hot new">
                 <div class="module-name flex_between">
                      <div class="left flex_start">
                        <p>新资源信息</p>
                         <img src="@/assets/pc/homepage/new.png" alt="">
                      </div>
                      <div class="right flex_start"  @click="goMore">
                         <p>查看更多</p>
                         <img src="@/assets/pc/homepage/see-more.png" alt="">
                      </div>
                  </div>
                  <swiper
                   
                    v-bind="swiperOptions"
                    class="scroll-container">
                    <swiper-slide  v-for="item in newList" :key="item.resourceId">
                        <div class="flex_between res-list-item"  @click="goMore">
                            <p class="file-name">{{item.resourceName}}</p>
                            <p class="file-date">{{item.createTime}}</p>
                        </div>
                    </swiper-slide>
                  </swiper>
                  <el-empty description="暂无数据" style="background:none;height:200px" v-show="newList.length==0" :image-size="60">

                  </el-empty>
              </div>
          </div>
     </div>
  </div>
  <div class="resource-recommend">
     <div class="content-box">
          <div class="title flex_between">
            <div class="flex_start left">
              <img src="@/assets/pc/homepage/res-recommend.png" alt="">
              <p>资源中心</p>
            </div>
             <p class="more" @click="goMore">更多</p>
          </div>
          <div class="res-center flex_start flex_wrap">
              <div class="item" v-for="item in recommendList" :key="item.resourceId" @click="goHot(item.resourceId)">
                  <img :src="item.coverUrl" alt="" class="cover"/>
                  <div class="bottom">
                      <p class="res-title">{{item.resourceName}}</p>
                      <div class="flex_between">
                         <p class="time">{{item.createTime}}</p>
                         <div class="flex_start view">
                             <img src="@/assets/pc/homepage/player.png" alt="" />
                             <p>{{item.views}}</p>
                         </div>
                      </div>
                  </div>
              </div>
          </div>
     </div>
  </div>
  <div class="footer flex_center">
        <a href="https://mskzkt.jse.edu.cn/cloudCourse/index/pc/">江苏省名师空中课堂</a>
        <a href="https://cxjdhd.jse.edu.cn/#/index">江苏省城乡结对互动课堂</a>
        <a href="https://basic.smartedu.cn/">国家中小学智慧教育平台</a>
        <a href="https://jyjs.ntjy.net/">慧学南通</a>
  </div>
</template>
<script setup>

import {onMounted, ref} from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import {getWeekday} from '@/utils/index.js'
import {useRouter} from 'vue-router'
import { Autoplay } from 'swiper/modules'
import {getHomePageProfileApi,getHomePageRecommendResource,getHomePageHotResource,getHomePageNewResource,getHomeTimeAble} from '@/api/pc/home.js'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'
let userInfoStore=useUserInfoStoreHook()
let {userInfo,loginVisibleShow}=storeToRefs(userInfoStore)
import 'swiper/css'
const swiperOptions = {
  modules: [Autoplay],
  direction: 'vertical',
  loop: true,
  autoplay: {
    delay: 0,
    disableOnInteraction: false,
    pauseOnMouseEnter: true
  },
  speed: 2500,
  slidesPerView: 5,
  spaceBetween: 16,
  allowTouchMove: false,
  simulateTouch: false,
  cssMode: false
}
let router=useRouter()
let statisticForm=ref({})
let hotList=ref([])
let newList=ref([])
let recommendList=ref([])
let dateList=ref([])
let lastFourList=ref([])
let nextFourList=ref([])
let getStatistic=async()=>{
    let res=await getHomePageProfileApi()
    if(res){
        //62
       statisticForm.value=res.data
    }
}
let goHot=(resourceId)=>{
   
    if(!userInfo.value.user){
        loginVisibleShow.value=true
        return
    }
  router.push('/resource/teach/preview?id='+resourceId)
}
let goMore=()=>{
     if(!userInfo.value.user){
        loginVisibleShow.value=true
        return
    }
    router.push('/resource/teach/index')
}
let getNew=async()=>{
    let res=await getHomePageHotResource({
        topK:10
    })
    if(res){
        newList.value=res.data
    }
}
let getRecommend=async()=>{
    let res=await getHomePageRecommendResource()
    if(res){
        recommendList.value=res.data
    }
}
let getHot=async()=>{
    let res=await getHomePageNewResource({
        topK:10
    })
    if(res){
        hotList.value=res.data
    }
}
let getTime=async()=>{
    let res=await getHomeTimeAble()
    if(res){
        let date=[]
        res.data[0].dataList.forEach(item=>{
            date.push(item.classDate+getWeekday(item.classDate))
        })
        dateList.value=date
        lastFourList.value=res.data.slice(0,4)
        nextFourList.value=res.data.slice(4)
    }
}
onMounted(()=>{
  getStatistic()
  getNew()
  getHot()
  getRecommend()
  getTime()
})
</script>
<style setup lang='scss'>
.bckWhite{
    background: #fff;
}
.week{
    background: linear-gradient( 270deg, #4EB8FF 0%, #1B6FFF 100%);
    border-radius: 8px 8px 0px 0px;
    padding: 8px 0;
    color: #fff;
    font-size: 16px;
}
.afternoon{
    border-radius: 8px;
    border: 1px solid #C0E4FE;
    font-weight: 500;
    font-size: 18px;
    color: #4260FD;
    
   div{
      width: 100%;
       writing-mode: vertical-rl;
      text-orientation: upright; /* 竖直方向 */
   }
}
.day{
    border: 1px solid #C0E4FE;
    border-radius: 8px;
    padding: 6px 0;
    font-size: 16px;
color: #2B2C33;
   margin-bottom: 4px;
   margin-left: 8px;
     &:nth-child(even){
       background: #F2F9FF;
    }
  .el-col{
    border-right: 1px solid #7BC9FD;
    &:last-child{
        border:none
    }
  
   }
   &:last-child{
    margin-bottom: 0;
   }
}
.statistic{
    margin-top: 20px;
    li{
        margin-right: 2%;
        width: 18.4%;
        background: #EDE8FF url(@/assets/pc/homepage/resource.png) no-repeat 90% center;
        background-size:70px 50px;
        padding: 12px 0 12px 24px;
        border-radius: 8px;
        .label{
            font-size: 14px;
            color: #5D598E;
            line-height: 20px;
        }
        .value{
           
            font-size: 24px;
            color: #5D598E;
            line-height: 32px;
        }
      &:last-child{
          margin-right: 0;
      }
    }
    .total-2{
            background: #FFE8E8 url(@/assets/pc/homepage/total.png) no-repeat 90% center;
             background-size:70px 50px;
            
    }
     .total-3{
            background: #FFF5E8 url(@/assets/pc/homepage/student.png) no-repeat 90% center;
             background-size:70px 50px; 
    }
     .total-4{
            background: #E8F0FF url(@/assets/pc/homepage/teacher.png) no-repeat 90% center;
             background-size:70px 50px;
   
    }
   .total-5{
            background: #E0F6EF url(@/assets/pc/homepage/exchange.png) no-repeat 90% center;
            background-size:70px 50px;
           
    }
}

.resource-recommend{
   background: #fff;
    padding: 30px 0;
    .title{
        border-bottom: 1px solid #E2E3E6;
        padding-bottom: 6px;
       .left{
            img{
                width: 34px;
                height: auto;
                margin-right: 6px;
            }
            p{
                font-size: 24px;
                color: #2B2C33;
                line-height: 40px;
            }
       }
       .more{
            cursor: pointer;
            font-size: 14px;
            color: #496EEE;
       }
    }
    .res-table{
        .hot{
            width: 48%;
            padding: 17px 20px 24px 20px;
            margin-top: 20px;
            border-radius: 6px;
            border: 1px solid #55B3FF;
            background: linear-gradient( 180deg, #E3F3FF 0%, rgba(255,255,255,0) 100%);
            border-radius: 6px;
           
            .module-name{
                .left{
                   p{
                    font-weight: bold;
                    font-size: 18px;
                    color: #2B2C33;
                    margin-right: 6px;
                   }
                   img{
                    width: 34px;
                    height: auto;
                   }
                }
                .right{
                    p{
                        font-size: 14px;
                      color: #1552AE;
                      margin-right: 6px;
                    }
                    img{
                        width: 13px;
                        height: auto;
                    }
                }
            }
            .res-list-item{
                    // margin-bottom: 20px;
                    &:last-child{
                        margin-bottom: 0;
                    }
                   .file-name{
                    font-size: 16px;
                    color: #2B2C33;
                    cursor: pointer;
                    &:hover{
                        color: #0055FF;
                    }
                   }
                   .file-date{
                     font-size: 12px;
                     color: #94959C;
                   }
                }
            .scroll-container {
                width: 100%;
                height: 190px;
                 margin-top: 16px;
                :deep(.swiper-slide) {
                    height: 100% !important;
                    transition-timing-function: linear !important; // 使用线性过渡效果
                }

                :deep(.swiper-wrapper) {
                    transition-timing-function: linear !important; // 使用线性过渡效果
                }
                }
        }
        .new{
            background: linear-gradient( 180deg, #E2FFF8 0%, rgba(255,255,255,0) 100%);
             border: 1px solid #53CDA0;
        }
    }
    .res-center{
        .item{
           margin-top: 20px;
           overflow: hidden;
           cursor: pointer;
           &:nth-child(4n+4){
            margin-right: 0;
           }
           width: 24%;
           margin-right: 1.333%;
           border-radius: 12px;
           border:1px solid #E2E3E6;
            &:hover{
                // box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.15);
                box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.12);
                transform: translate(0, -5px);
                transition-delay: 0 !important;
                transition: all 300ms;
            }
           .cover{
            width: 100%;
            height: 148px;
            
           }
           .bottom{
             padding: 12px 16px;
             .res-title{
                font-size: 16px;
                color: #2B2C33;
                margin-bottom: 8px;
                width: 100%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
             }
             .time{
                font-size: 12px;
                color: #94959C;
                line-height: 18px;
             }
             .view{
                font-size: 12px;
                color: #94959C;
                p{
                    line-height: 12px;
                }
                img{
                    width: 12px;
                    height: auto;
                    margin-right: 4px;
                }
             }
           }
        }
    }
}
.resource-recommend-active{
 background: linear-gradient( 180deg, #F1F7FC 0%, #FFFFFF 100%);

}
.footer{
    padding: 54px 0;
    background: url(@/assets/pc/homepage/footer.png) no-repeat;
    color: #fff;
    a{
        margin-right: 84px;
        cursor: pointer;
        text-decoration: underline;
        font-size: 16px;
        
        &:last-child{
            margin-right: 0;
        }
    }
}

</style>
<template>
   <div>
          <div class="video" id="video" style="width:100%;"> </div>
          <div class="detail">
                <h3 class="title"> {{row.title}}</h3>
                <p class="time">{{row.createTime?row.createTime.slice(0,10):''}}</p>
                <div class="flex_start time">
                    <p style="width:50%">播放量：{{row.viewCount}}</p>
                    <p  style="width:50%">分享次数：{{row.shareCount}}</p>
                </div>
                 <div class="flex_start time">
                    <p style="width:50%">资源作者：{{row.createTitle}}</p>
                    <p  style="width:50%">作者单位：{{row.schoolTitle}}</p>
                </div>
                <!-- <div class="desc">
                       <p class="desc-title">资源简介</p>
                       <p class="desc-content"></p>
                </div> -->
          </div>
          <div class="form" v-if="!row.isEvaluate&&userStore.userInfo.globalRoles&&(userStore.userInfo.globalRoles.indexOf('EXPERT')>-1||userStore.userInfo.globalRoles.indexOf('DIRECTOR')>-1)">
              <el-form :model="commentForm"  label-position="top" ref="evaluateFormRef" :rules="commentRules">
                    <el-form-item label='教学目标诊断' prop='targetAccurate'>
                        <div class="flex_start item">
                            <p class="flex_start">目标明确性</p>
                            <el-rate v-model="commentForm.targetAccurate" size="large" />
                        </div>
                      <div class="flex_start item">
                            <p class="flex_start">目标达成度</p>
                            <el-rate v-model="commentForm.targetAchievement" size="large" />
                        </div>
                    </el-form-item>
                    <el-form-item label='教学内容诊断' prop='contentAccurate'>
                        <div class="flex_start item">
                            <p class="flex_start">内容准确性</p>
                            <el-rate v-model="commentForm.contentAccurate" size="large" />
                        </div>
                      <div class="flex_start item">
                            <p class="flex_start">内容逻辑性</p>
                            <el-rate v-model="commentForm.contentLogic" size="large" />
                        </div>
                        <div class="flex_start item">
                            <p class="flex_start">内容趣味性</p>
                            <el-rate v-model="commentForm.contentInteresting" size="large" />
                        </div>
                    </el-form-item>
                    <el-form-item label='教学方法诊断' prop='methodDiversity'>
                        <div class="flex_start item">
                            <p class="flex_start">方法多样性</p>
                            <el-rate v-model="commentForm.methodDiversity" size="large" />
                        </div>
                      <div class="flex_start item">
                            <p class="flex_start">方法有效性</p>
                            <el-rate v-model="commentForm.methodEffective" size="large" />
                        </div>
                        <div class="flex_start item">
                            <p class="flex_start">学生参与度</p>
                            <el-rate v-model="commentForm.studentEngagement" size="large" />
                        </div>
                    </el-form-item>
                    <el-form-item label='教学效果诊断' prop='homeworkCompletion'>
                        <div class="flex_start item">
                            <p class="flex_start">作业完成情况</p>
                            <el-rate v-model="commentForm.homeworkCompletion" size="large" />
                        </div>
                      <div class="flex_start item">
                            <p class="flex_start">考试成绩</p>
                            <el-rate v-model="commentForm.examResults" size="large" />
                        </div>
                    </el-form-item>
                     <el-form-item label='总体评价'>
                        <el-input placeholder="请输入" v-model="commentForm.evaluate" type="textarea" resize="none" rows="6"></el-input>
                    </el-form-item>
                </el-form>
                <div class="flex_center">
                   
                    <button class="submit" @click="goSumit">提交评价</button>
                </div>
                
          </div>
         
          <van-list class="comment-list" v-if="comments.length>0"  v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="load">
              <div :class="['comment-item',item.evaluate?'flex_start_0':'flex_start']" v-for="item in comments" :key="item.id">
                       <img :src="item.userAvatarUrl || DefaultAvatar" alt="" class="teacher-logo">
                       <div class="flex_1">
                           <div class="top-line flex_between">
                               <div class="flex_start person-info">
                                  <h3>{{item.userName}}</h3>
                                  <p>{{item.createTime}}</p>
                               </div>
                                <el-rate v-model="item.finalScore" size="large" disabled />
                           </div>
                           <p class="comment-content">{{item.evaluate}}</p>
                       </div>
              </div>
          </van-list>
   </div>
</template>
<script setup>
import Player, { Events } from 'xgplayer'; // 引入西瓜视频模块
import 'xgplayer/dist/index.min.css'; // 引入西瓜视频样式
import {useRoute} from 'vue-router'
import emptyVideo from '@/assets/pc/emptyvideo.png'
import {jyfxDetail,jyfxEvaluate,jyfxEvaluates} from '@/api/pc/wisdom.js'
// import Vue3AudioPlayer from 'vue3-audio-player';
import DefaultAvatar from "@/assets/layouts/default-avatar.png"
// import 'vue3-audio-player/dist/style.css'
import { showConfirmDialog } from 'vant';
import {onMounted, ref,defineProps,reactive} from 'vue'
import { useUserStore } from "@/store/modules/user"
import { List,showSuccessToast} from 'vant';
const userStore = useUserStore()
let player=''
let route=useRoute()
let row=ref({})
let resourceId=''
let loading=ref(false)
let finished=ref(false)
let config={
    id: 'video', // 占位dom元素
    width: '100%', 
    height: '100%', // 视频宽高尺寸
    url: '', // 视频源
    poster: "", // 视频封面
    autoplay: false, // 是否自动播放，不自动播放，浏览器有限制规则
    autoplayMuted: false, // 是否自动播放（静音播放）
    videoInit: true, // 是否默认初始化video，默认初始化，默认true
    playsinline: true, // 是否启用内联播放模式，仅移动端生效
    defaultPlaybackRate: 1, // 默认播放速度（可选：0.5/0.75/1/1.5/2等）
    volume: 0.72, // 播放音量（可选：0 ~ 1）
    loop: false, // 是否循环播放，默认不循环播放
    startTime: 0, // 点播模式下，初始起播时间
    videoAttributes: {}, // video扩展属性，暂且不配置
    lang: 'zh-cn', // 播放器初始显示语言，设置为中文
    fluid: true, // 是否流式布局（宽高优先于流失布局，默认16:9）注掉上方宽高看效果
    fitVideoSize: 'fixed', // 保持容器宽/高，不做适配，按照容器来
    videoFillMode: 'auto', // 宽高不够自动底色填充（fill拉伸填充等...）
    seekedStatus: 'play', // 跳转后继续播放
   
    thumbnail: null, // 进度条预览图配置，普通业务用不到
    marginControls: false, // 是否开启画面和控制栏分离模式，不开启空间多一些
    domEventType: 'default', // 响应的事件类型，不用指定，用默认的即可
  
    icons: {}, // 使用默认的icon图标
    i18n: [], // 使用默认的中文
    // 自定义一些颜色
    // commonStyle: {
    //     progressColor: 'green', // 整个进度条颜色
    //     playedColor: 'chocolate', // 已播放的进度条颜色
    //     volumeColor: 'pink', // 音量大小竖向滑块颜色
    // },
    controls: true, // 是否使用底部控制栏，默认使用
    miniprogress: true, // 是否使用mini进度条（当底部控制栏隐藏时生效）
    screenShot: false, // 关闭截图功能
    rotate: true, // 是否使用视频旋转插件，默认不使用
    download: false, // 是否使用下载按钮，一般不用，一般自定义控制
    pip: false, // 使用使用画中画模式，默认不用
    mini: false, // 是否使用小屏幕控件
    cssFullscreen: true, // 是否使用网页样式全屏按钮开关
    playbackRate: [0.5, 1, 1.5, 2, 3], //传入倍速可选数组
    playbackRate: true, //false，禁用倍速播放（即控制栏不显示）
    keyShortcut: false, // 是否开启快捷键模式
   
}
const init = () => {
    player = new Player({
        ...config
    });
    if(player){
          player.on(Events.PLAY, (ev) => {
        console.log('-播放开始-', ev);
        })
        player.on(Events.PAUSE, (ev) => {
            console.log('-播放结束-', ev);
        })
        player.on('loadedmetadata', (ev) => {
            console.log('-媒体数据加载好了-', ev);
        })
        player.on(Events.SEEKED, (ev) => {
            console.log('-跳着播放-', ev);
        })
    }
   
    // 等各种监听事件
}
let getDetail=async()=>{
    let res=await jyfxDetail({
        resourceId
    })
    if(res){
        row.value=res.data
        config.url=row.value.fileUrl
        config.poster=row.value.sealImg?row.value.sealImg:emptyVideo
        // config.poster='http://localhost:8080/src/assets/pc/emptyvideo.png'
        init()
    }
}
let evaluateFormRef=ref()
let commentForm=reactive({
    targetAccurate:0,
    targetAchievement:0,

    contentAccurate:0,
    contentLogic:0,
    contentInteresting:0,

    methodDiversity:0,
    methodEffective:0,
    studentEngagement:0,

    homeworkCompletion:0,
    examResults:0,
    evaluate:'',
})
let comments=ref([])
let commentTotal=ref(0)
let commentSearch=reactive({
    pageSize:10,
    pageNum:1
})
let load=()=>{
   
 if (comments.value?.length >= commentTotal.value) {
                finished.value = true
            } else {
                // commentSearch.pageNum++
                // alert(11)
                Object.assign(commentSearch,{
                    pageNum:commentSearch.pageNum+1
                })
                getComments()
            }
}
let getComments=async()=>{
   loading.value=true
      
        let res=await jyfxEvaluates({
            resourceId,
            ...commentSearch
        })
        if(res){
            commentTotal.value=res.data.total
            comments.value=comments.value.concat([...res.data.list])
            
        }
        loading.value=false
  
}
let checkedTargetAccurate=(rule,value,callBack)=>{
    if(commentForm.targetAchievement==0||commentForm.targetAccurate==0){
       callBack(new Error('填写完整教学目标诊断'))
    }else{
        callBack()
    }
}
let checkedContentAccurate=(rule,value,callBack)=>{
 if(commentForm.contentAccurate==0||commentForm.contentLogic==0||commentForm.contentInteresting==0){
       callBack(new Error('填写完整教学内容诊断'))
    }else{
        callBack()
    }
}
let checkedMethodDiversity=(rule,value,callBack)=>{
if(commentForm.methodDiversity==0||commentForm.methodEffective==0||commentForm.studentEngagement==0){
       callBack(new Error('填写完整教学方法诊断'))
    }else{
        callBack()
    }
}
let checkedHomeworkCompletion=(rule,value,callBack)=>{
if(commentForm.homeworkCompletion==0||commentForm.examResults==0){
       callBack(new Error('填写完整教学效果诊断'))
    }else{
        callBack()
    }
}
let commentRules={
  targetAccurate:[{ required: true, message: '请选择',trigger: 'change'},{ validator: checkedTargetAccurate, trigger: 'change' }],
  contentAccurate:[{ required: true, message: '请选择',trigger: 'change'},{ validator: checkedContentAccurate, trigger: 'change' }],
  methodDiversity:[{ required: true, message: '请选择',trigger: 'change'},{ validator: checkedMethodDiversity, trigger: 'change' }],
  homeworkCompletion:[{ required: true, message: '请选择',trigger: 'change'},{ validator: checkedHomeworkCompletion, trigger: 'change' }],
}
let goSumit=()=>{
   evaluateFormRef.value.validate(async(valid) => {
    if (valid) {

        showConfirmDialog({
            title: '提示',
            message:
                '确认提交评价吗',
            })
            .then(async() => {
                    let res=await jyfxEvaluate({
                                resourceId,
                                ...commentForm
                        })
                            if(res){
                                showSuccessToast('评价成功');
                                // ElMessage.success('评价成功')
                                row.value.isEvaluate=true
                                  Object.assign(commentSearch,{
                                        pageNum:1
                                    })
                                    comments.value=[]
                                getComments()
                            }
                    })
        .catch(() => {
            // on cancel
        });
      
    
    } else {
      console.log('error submit!')
    }
  })
}
onMounted(()=>{
    resourceId=route.query.id
    getDetail()
    getComments()
    // init()
    //
})
</script>

<style lang='scss' scoped>
#video{
    height: 300px;
}
.detail{
    padding: 20px 15px;
    background: #FFF;
    .title{
        font-size: 16px;
        color: #2B2C33;
        line-height: 24px;
        font-weight: bold;
    }
    .time{
        font-size: 12px;
        color: #6D6F75;
        line-height: 18px;
        margin-top: 8px;
    }
    .desc{
      padding: 16px;
      background: #F9F9F9;
        border-radius: 12px;
        margin-top: 20px;
        .desc-title{
            font-size: 12px;
          color: #6D6F75;
        }
        .desc-content{
            font-size: 12px;
            color: #2B2C33;
            line-height: 18px;
            margin-top: 5px;
        }
    }
   
}
 .form{
        background: #FFF;
        padding: 16px;
        margin-top: 16px;
        :deep(.el-form-item__label){
            font-size: 16px;
            color: #2B2C33;
            font-weight: bold;
        }
        .item{
        width: 100%;
        p{
            margin: 0 16px;
            width: 100px;
            &::before{
                content:'';
                display:block;
                width: 6px;margin-right: 8px;
                height: 6px;background: #508CFF;border-radius: 200px;
            }
        }
    }
    .submit{
        font-size: 16px;
        color: #fff;
        line-height: 16px;
        padding: 10px 60px;
        background: #508CFF;
        border-radius: 12px;
        margin: 24px auto 0 auto;
        border:none
    }
    }
.comment-list{
    padding: 24px 15px 0  15px;
    // height: 200px;
    // overflow:auto;
    background: #FFF;
    margin-top: 24px;
    .comment-item{
        padding-bottom: 24px;
        &:last-child{
            margin-bottom: 0;
        }
        .teacher-logo{
            width: 32px;
            height: 32px;
            margin-right: 6px;
            border-radius: 200px;
        }
        :deep(.el-rate--large){
            height: auto;
        }
        .top-line{
           .person-info{
            h3{
                font-size: 14px;
                color: #2B2C33;
                font-weight: bold;
                margin-right: 8px;
            }
            p{
                font-size: 12px;
                color: #94959C;
            }
           }
        }
        .comment-content{
            font-size: 14px;
            color: #2B2C33;
            margin-top: 6px;
        }
    }
}
:deep(.el-rate .el-rate__icon){
    font-size: 24px;
}
</style>
<template>
    <div class="accept-detail">
        <ul class="detail-ul text_16">
            <li class="flex_start_0">
                <p class="detail-label">课题</p>
                <p class="flex_1">{{detail.taskTitle}}</p>
            </li>
            <li class="flex_start_0">
                <p class="detail-label">备注</p>
                <p class="flex_1">{{detail.taskContent}}</p>
            </li>
            <li class="flex_start_0">
                <p class="detail-label">材料清单</p>
                <div class="flex_1">
                    <p v-for="item in detail.reviewSections" :key="item" class="file-name" @click="previewClick(item)">{{item.fileName}}</p>
                </div>
            </li>
        </ul>
        <div class="flex_end">
            <el-button class="plain-btn btn_96_48" type="plain" @click="emit('close')">返回</el-button>
        </div>
    </div>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import { schTaskExamine } from '@/api/pc/jky'
    const props = defineProps({
        id: '', // 默认值
    });
    const emit = defineEmits(['close'])
    const detail = ref({})

    const getDetail = async () => {
        let res = await schTaskExamine(props.id)
        if(res){
            detail.value = res.data
        }
    }
    const previewClick = (item) => {
        window.open(item.filePreviewUrl)
    }
    onMounted(() => {
        getDetail()
    })
</script>
<style lang="scss" scoped> 
.detail-ul{font-family: SourceHanSansSC, SourceHanSansSC;
    li{color: #2B2C33;margin-bottom:16px;
        .detail-label{color: #94959C;width: 64px;margin-right: 24px;}
        .file-name{color: #1F6CFF;margin-bottom: 16px;cursor: pointer;}
    }
}
</style>
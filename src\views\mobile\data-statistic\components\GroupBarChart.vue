<script setup>
import { ref, watch, onMounted } from "vue"
import { EchartsUI, useEcharts } from "@/components/Echart"

const chartRef = ref()
const { renderEcharts } = useEcharts(chartRef)

const props = defineProps({
  chartData: {
    type: Object,
    default: () => ({
      data: [],
      color: []
    })
  }
})

watch(
  () => props.chartData,
  () => {
    paintChart()
  },
  { deep: true }
)

onMounted(() => {
  paintChart()
})

const paintChart = () => {
  const data = props.chartData.data
  const yAxisData = data.map(item => item.name)
  const xAxisData = data.map(item => item.value)
  renderEcharts({
    backgroundColor: "#fff",
    color: props.chartData.color,
    xAxis: {
      type: "value",
      axisTick: {
        show: false,
      },
      splitLine: {     //网格线
        show: false
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#8DA2B5",
        },
      },
      axisLabel: {
        color: "# ",
        fontSize: 12,
      },
      data: xAxisData,
    },
    yAxis: {
      type: "category",
      realtimeSort: true,
      data: yAxisData,
      axisLine: {
        show: true,
        lineStyle: {
          color: "#8DA2B5",
        },
      },
      splitLine: {     //网格线
        show: false
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: "#8DA2B5",
      },
    },
    grid: {
      bottom: "1%",
      top: "1%",
      left: "1%",
      right: "8%",
      containLabel: true,
      borderColor: "#8DA2B5",
    },
    series: [
      {
        name: "assist",
        type: "bar",
        stack: "1",
        itemStyle: {
          normal: {
            barBorderColor: "rgba(0,0,0,0)",
            color: "rgba(0,0,0,0)",
          },
          emphasis: {
            barBorderColor: "rgba(0,0,0,0)",
            color: "rgba(0,0,0,0)",
          },
        },
        tooltip: {
          trigger: "none",
        },
        data: [],
      }, //设置两个柱状图进行重叠，第一层柱子设置透明度,由此来实现柱子与坐标轴之间的距离  stack:''设置重叠效果
      {
        type: "bar",
        stack: "1",
        barWidth: 12,
        barBorderRadius: 30,
        itemStyle: {
          barBorderRadius: [0, 8, 8, 0],
          color: function (params) {
            return props.chartData.color[params.dataIndex % 4]
          }
        },
        data: xAxisData
      },
    ],
  })
}
</script>

<template>
  <EchartsUI ref="chartRef" :height="'178px'" />
</template>

<style scoped></style>

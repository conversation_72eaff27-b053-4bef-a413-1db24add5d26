import { loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path, { resolve } from "path"
import postcssPxToViewport from "postcss-px-to-viewport"
// import commonjs from '@rollup/plugin-commonjs';

// https://vite.dev/config/
export default ({ mode }) => {
  const viteEnv = loadEnv(mode, process.cwd())
  const { VITE_PUBLIC_PATH } = viteEnv
  return {
    base: VITE_PUBLIC_PATH,
    resolve: {
      alias: {
        "@": resolve(__dirname, "./src")
      }
    },
    server: {
      host: true,
      port: 8080,
      // open: true,
      cors: true,
      strictPort: false,
      /* 预热常用文件，提高初始页面加载速度 */
      warmup: {
        clientFiles: ['./src/layout/**/*.vue']
      }
    },
    build: {
      /** 单个 chunk 文件的大小超过 2048KB 时发出警告 */
      chunkSizeWarningLimit: 2048,
      /** 禁用 gzip 压缩大小报告 */
      reportCompressedSize: false,
      /** 打包后静态资源目录 */
      assetsDir: "static",
      rollupOptions: {
        output: {
          /**
           * 分块策略
           * 1. 注意这些包名必须存在，否则打包会报错
           * 2. 如果你不想自定义 chunk 分割策略，可以直接移除这段配置
           */
          manualChunks: {
            vue: ["vue", "vue-router", "pinia"],
            element: ["element-plus", "@element-plus/icons-vue"],
            vant: ["vant"]
          }
        }
      }
    },
    css: {
      postcss: {
        plugins: [
          postcssPxToViewport({
            unitToConvert: "px", // 要转化的单位
            viewportWidth: 375, // UI设计稿的宽度
            unitPrecision: 6, // 转换后的精度，即小数点位数
            propList: ["*"], // 指定转换的css属性的单位，*代表全部css属性的单位都进行转换
            viewportUnit: "vw", // 指定需要转换成的视窗单位，默认vw
            fontViewportUnit: "vw", // 指定字体需要转换成的视窗单位，默认vw
            selectorBlackList: ["ignore-"], // 指定不转换为视窗单位的类名，
            minPixelValue: 1, // 默认值1，小于或等于1px则不进行转换
            mediaQuery: false, // 是否在媒体查询的css代码中也进行转换，默认false
            replace: true, // 是否转换后直接更换属性值
            // exclude: [/node_modules/], // 设置忽略文件，用正则做目录名匹配
            // exclude: [],
            include: [/mobile/, /Tabbar/], //如果设置了include，那将只有匹配到的文件才会被转换
            landscape: false, // 是否处理横屏情况
            landscapeUnit: "vw", // 横屏时使用的单位
            landscapeWidth: 1125
          }),
          postcssPxToViewport({
            unitToConvert: "px", // 要转化的单位
            viewportWidth: 375, // UI设计稿的宽度
            unitPrecision: 6, // 转换后的精度，即小数点位数
            propList: ["*"], // 指定转换的css属性的单位，*代表全部css属性的单位都进行转换
            viewportUnit: "vw", // 指定需要转换成的视窗单位，默认vw
            fontViewportUnit: "vw", // 指定字体需要转换成的视窗单位，默认vw
            selectorBlackList: ["ignore-"], // 指定不转换为视窗单位的类名，
            minPixelValue: 1, // 默认值1，小于或等于1px则不进行转换
            mediaQuery: false, // 是否在媒体查询的css代码中也进行转换，默认false
            replace: true, // 是否转换后直接更换属性值
            // exclude: [/node_modules/], // 设置忽略文件，用正则做目录名匹配
            // exclude: [],
            include: [/vant/], //如果设置了include，那将只有匹配到的文件才会被转换
            landscape: false, // 是否处理横屏情况
            landscapeUnit: "vw", // 横屏时使用的单位
            landscapeWidth: 1125
          })
        ]
      },
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/css/variable.scss";`
        }
      }
    },
    /** 混淆器 */
    esbuild:
      mode === "development" || mode === "staging"
        ? undefined
        : {
            /** 打包时移除 console.log */
            pure: ["console.log"],
            /** 打包时移除 debugger */
            drop: ["debugger"],
            /** 打包时移除所有注释 */
            legalComments: "none"
          },
    /** Vite 插件 */
    plugins: [
      vue()
    ],
  }
}

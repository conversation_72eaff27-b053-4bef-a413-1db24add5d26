import{aE as nr,ag as x0,z as je,A as Oe,B as le,Q as y0,I as yt,aC as ir,r as Ae,l as ar,h as or,aq as sr,H as cr,K as q0,u as Te,J as Xe,R as n0,P as I0,a6 as Fu,m as lr,n as fr,O as dr,E as hr,D as pr}from"./vue-CelzWiMA.js";import{_ as mr}from"./cleanall-Da4K8AGg.js";import{c as kt,a as k0,e as ce}from"./element-BwwoSxMX.js";import{_ as br}from"./logo-CxUTONFc.js";import{_ as wt}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{l as c0,o as vr,c as gr}from"./index-DCRFd5B4.js";import"./vant-C4eZMtet.js";const xr="/zs/static/ai-header-q6DnsuJI.png",yr="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAkCAYAAACXOioTAAAAAXNSR0IArs4c6QAAA7JJREFUSEvtV11oHFUU/s5MNon/WtD6otgY0M69M4SdTWts1diobaHii1DEB/FVopXaF6mCoMUHTbEW3/VBEfFFqPhbodQamuRu6OzcsUpM8QcstVhaLW262TlyZ2fC1sxudpMnsedp5t57vu+enznnDKENEWJoFdPcs0T0GBj9iQphhpk/Ie7Zr/X4n0vB0FIHHLe0lZjfB3BTk7NnmOjJqDL1WSuslkSO468nC0cA2EtcqMYxNkSROtrsXFOi/v7+np7eG8oAnFSZwXiPiA+ad2YaAeGpxIl1ieYuni3OzMzM5ZE1JZLS38XAG6lSDczbtC5/3ggiRHELiA5kFjOwKwrVWEdEjix9R+Ch5PbA21GoduQBONLfR8Bz9XM0HoVT93ZEJKRvMilJgJish7+vTH6dB7DWHXzI4virdO+MDtWqTok4U7DIurNSmZzNA3Ddwb6Y45+yPR2q3HA0jZGQ/gKRbdl9QTBxIo/I89atqcW1hUtcIcIV1/0Ps07K0isMHmVgLArV65kLGpNhuR+sI/0XCdhhaiYJ6Z8EsBpAtbcHNyulzhqylZaggYGBG6vz9ikABQC/kZDFIwDVCyHzFq3LX5jHlRZVIYqbQZRUewYfMha9BaBemRl7tVYvmMeVtgkh/DEQdqbV/00Swn8QhG+y6mtb1duCIDi/ksbned41tbjwa1b92eL7TVE1Vv0IpEMHeI8Oyy8l7ltmKxey+BpAu9PLH9ehWptUbyFKT4D4g6ybWmSNVCqThxKyDocT1x18IObYtPtkziDw9jAsf7TQJoTrHwRjU0r2F5i2aj1lBhO0O24JUdoAYjMNXZfifKlDtblOmIrnebfUaoWjINyRLlVBeLm3G3uVUtW8XpSt+b5fuHgJO8F4NU1nszXbXYjvmZ6e/uMyIvPiuv7dMcOk9+0NwL+YmaHLogNBMPVDI6Hnle6aj3lbOjM06vxcs+1Hjh+bMLFPZFGHlXL9asb8xwA25lhxDgzzgRvNWwFcv+gM47BtVx8PgsB8rAvSrJWTlKWnGbwHSADbkd+ZeHdUKb9bH5wul5aT6vDwcNfp0+ceZVjbAV4HYM2/9E8ANBETf3h1Nz5tFcslZ+9GYCH9dwA8k/r8+TBU+9oxNTdGrRSFKO4H0WhyhnlU67Ihbks6s+g/SeQ4zrWWddU4A305PukG0JWuXwIwn5Nds3F8YSiKor9bprcQxU0gSn5Nli3MI1qXs46QwCyKkeP4G8nC4WWTmDyJcV8UqW8bMf4BCEEJz/Kgr0AAAAAASUVORK5CYII=",kr="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAkCAYAAAD7PHgWAAAAAXNSR0IArs4c6QAABINJREFUWEfNWE1vG1UUPXdcUYS6K0UCafJH+B38BARCwt2g0u6yqhB21CQISNM4INGqqF8boBBBK1i3ohJIdMGKQkUXlYAoizaZdw/c9zHzxnaJ8x1vxnFsz/G555577pPugKdBvKvKu4TMffg6voIIcUgecnKZj1VxnAqAgAL3UWGm43Dxg648PWic0h2wAtFRBWgAlQAFVDwiMF8UWPj4LfnroIB6gCQ6nj0DOQRUlWtCWXIVZgfvyG/7DVS6y3R0LGisGXlKNM8Do/Y3FJWqXqUW/U9OyY/7BdQYdCCKVGJfZhfKnMrtQUZmDawqbyml99lpWdlroNYkThXFcInzUntGffkju/7qJfEzKf0XXsTlxTdkYy/AegZJFC0AWbO0WDT2PLvWUAaaQbfEQ1JmRbF4aVpWdxOoAVQSkjNYd3IEGiwolNzrtAbavBY/v0pycV2KuRvT8sduAK0B1gwaVm3sZlSHSZuh42vt1hr1r2+QvOy0mLl+Vn7aCVAPEISM3CgHGbs7sNhoMTVPi90MqL1XiBU66V17X25tB6g1iarGEjsaVq8zK2U07NpqkuaCBnPPjFPIeXlC7Rrf40vvCIXco0r/n2O48v20VJOCNYDMDTq/cSp16Oi2T/pm8XKIjdIucf0DGouqP/9AUZx7/gmWrn4ka5sBle7AlB9LF1kZ7tzQAMkLR/3R/z8xOka/0ZJqm4rf/zfJhY0nxfzKovz5LKCmQatrVsZ2+WoWo60ke0lMG/vtDm9KHxovb6TcR2v21wW4qFUx8/V5+WUYqLy9zF+hKEkcZdSgv2a24gFEXeY2k3RWT5k0x13UZBqTdQWszNl3J1mEK6lyU1V6316QHxJQ8U9IeXMBJ+QIyqLClANKUZSqKB0xFZ7zZap0msRj7DS6HOcCqbTDFcp9tmE5eqwHyzsgeq+WnesB4ASP166wc+x3vKIuAAYNvJagTNkPIVFCcSJpse2fErJmuHlrCjWvW/tL1HJoPFC+3DJAACUcSjpMGUBSDJhnm8RLWZ5sd/JwWorNNDylArCkY/miVeIjgiljwkpM1RJaWGkjS1ZidFpNknSa0nimqXSjcZMoj3TjzZ53HNG7PehcC03iaEwc3SQPjhh2q0nags/YC65gu0Sj0zpkNIavVv0xTdLYTOP+E+TBOGVqrWRBIkwSeyQzD5Nl1Eejha0DuMSq6I+1mdaoyyL/hHmwFv/W86QEoy6K+ZXZ/zdqH7d2mAdjoM2qkOkytxl1fCDA7HNPiwsTjrpdzYP15EiBot5piHtuO2EhBdZdzoPRx/yMXrG14MZ78t0EdjvyltGlqWUVcXnaeh7cAPl5pUV/x4G1tTTtPA+uErsc+RPAZq1sp5Exea7ZSZo8+FBV5gri/B4sTepAafbioTznl/Zn58F9WTvj2Uwa4hPkQfI2nPQ+PSPfbEf4W/mMHX1UYcYCm+TBiuT+H32cjACTX42ET8Xaf2lloA7nDuTwyACqMZicv9lLHgFyKI7fHoM4np0P3heVQ3SAucQzBE6BuKvEoTsC/hfP8ukpSd3wdwAAAABJRU5ErkJggg==",Ru={};function wr(e){let u=Ru[e];if(u)return u;u=Ru[e]=[];for(let t=0;t<128;t++){const o=String.fromCharCode(t);u.push(o)}for(let t=0;t<e.length;t++){const o=e.charCodeAt(t);u[o]="%"+("0"+o.toString(16).toUpperCase()).slice(-2)}return u}function Ye(e,u){typeof u!="string"&&(u=Ye.defaultChars);const t=wr(u);return e.replace(/(%[a-f0-9]{2})+/gi,function(o){let n="";for(let r=0,c=o.length;r<c;r+=3){const i=parseInt(o.slice(r+1,r+3),16);if(i<128){n+=t[i];continue}if((i&224)===192&&r+3<c){const s=parseInt(o.slice(r+4,r+6),16);if((s&192)===128){const f=i<<6&1984|s&63;f<128?n+="��":n+=String.fromCharCode(f),r+=3;continue}}if((i&240)===224&&r+6<c){const s=parseInt(o.slice(r+4,r+6),16),f=parseInt(o.slice(r+7,r+9),16);if((s&192)===128&&(f&192)===128){const h=i<<12&61440|s<<6&4032|f&63;h<2048||h>=55296&&h<=57343?n+="���":n+=String.fromCharCode(h),r+=6;continue}}if((i&248)===240&&r+9<c){const s=parseInt(o.slice(r+4,r+6),16),f=parseInt(o.slice(r+7,r+9),16),h=parseInt(o.slice(r+10,r+12),16);if((s&192)===128&&(f&192)===128&&(h&192)===128){let v=i<<18&1835008|s<<12&258048|f<<6&4032|h&63;v<65536||v>1114111?n+="����":(v-=65536,n+=String.fromCharCode(55296+(v>>10),56320+(v&1023))),r+=9;continue}}n+="�"}return n})}Ye.defaultChars=";/?:@&=+$,#";Ye.componentChars="";const Tu={};function _r(e){let u=Tu[e];if(u)return u;u=Tu[e]=[];for(let t=0;t<128;t++){const o=String.fromCharCode(t);/^[0-9a-z]$/i.test(o)?u.push(o):u.push("%"+("0"+t.toString(16).toUpperCase()).slice(-2))}for(let t=0;t<e.length;t++)u[e.charCodeAt(t)]=e[t];return u}function l0(e,u,t){typeof u!="string"&&(t=u,u=l0.defaultChars),typeof t>"u"&&(t=!0);const o=_r(u);let n="";for(let r=0,c=e.length;r<c;r++){const i=e.charCodeAt(r);if(t&&i===37&&r+2<c&&/^[0-9a-f]{2}$/i.test(e.slice(r+1,r+3))){n+=e.slice(r,r+3),r+=2;continue}if(i<128){n+=o[i];continue}if(i>=55296&&i<=57343){if(i>=55296&&i<=56319&&r+1<c){const s=e.charCodeAt(r+1);if(s>=56320&&s<=57343){n+=encodeURIComponent(e[r]+e[r+1]),r++;continue}}n+="%EF%BF%BD";continue}n+=encodeURIComponent(e[r])}return n}l0.defaultChars=";/?:@&=+$,-_.!~*'()#";l0.componentChars="-_.!~*'()";function xu(e){let u="";return u+=e.protocol||"",u+=e.slashes?"//":"",u+=e.auth?e.auth+"@":"",e.hostname&&e.hostname.indexOf(":")!==-1?u+="["+e.hostname+"]":u+=e.hostname||"",u+=e.port?":"+e.port:"",u+=e.pathname||"",u+=e.search||"",u+=e.hash||"",u}function w0(){this.protocol=null,this.slashes=null,this.auth=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.pathname=null}const Cr=/^([a-z0-9.+-]+:)/i,Ar=/:[0-9]*$/,Dr=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,Er=["<",">",'"',"`"," ","\r",`
`,"	"],Sr=["{","}","|","\\","^","`"].concat(Er),Fr=["'"].concat(Sr),Mu=["%","/","?",";","#"].concat(Fr),zu=["/","?","#"],Rr=255,Bu=/^[+a-z0-9A-Z_-]{0,63}$/,Tr=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,Lu={javascript:!0,"javascript:":!0},qu={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};function yu(e,u){if(e&&e instanceof w0)return e;const t=new w0;return t.parse(e,u),t}w0.prototype.parse=function(e,u){let t,o,n,r=e;if(r=r.trim(),!u&&e.split("#").length===1){const f=Dr.exec(r);if(f)return this.pathname=f[1],f[2]&&(this.search=f[2]),this}let c=Cr.exec(r);if(c&&(c=c[0],t=c.toLowerCase(),this.protocol=c,r=r.substr(c.length)),(u||c||r.match(/^\/\/[^@\/]+@[^@\/]+/))&&(n=r.substr(0,2)==="//",n&&!(c&&Lu[c])&&(r=r.substr(2),this.slashes=!0)),!Lu[c]&&(n||c&&!qu[c])){let f=-1;for(let a=0;a<zu.length;a++)o=r.indexOf(zu[a]),o!==-1&&(f===-1||o<f)&&(f=o);let h,v;f===-1?v=r.lastIndexOf("@"):v=r.lastIndexOf("@",f),v!==-1&&(h=r.slice(0,v),r=r.slice(v+1),this.auth=h),f=-1;for(let a=0;a<Mu.length;a++)o=r.indexOf(Mu[a]),o!==-1&&(f===-1||o<f)&&(f=o);f===-1&&(f=r.length),r[f-1]===":"&&f--;const _=r.slice(0,f);r=r.slice(f),this.parseHost(_),this.hostname=this.hostname||"";const A=this.hostname[0]==="["&&this.hostname[this.hostname.length-1]==="]";if(!A){const a=this.hostname.split(/\./);for(let b=0,m=a.length;b<m;b++){const S=a[b];if(S&&!S.match(Bu)){let p="";for(let d=0,w=S.length;d<w;d++)S.charCodeAt(d)>127?p+="x":p+=S[d];if(!p.match(Bu)){const d=a.slice(0,b),w=a.slice(b+1),y=S.match(Tr);y&&(d.push(y[1]),w.unshift(y[2])),w.length&&(r=w.join(".")+r),this.hostname=d.join(".");break}}}}this.hostname.length>Rr&&(this.hostname=""),A&&(this.hostname=this.hostname.substr(1,this.hostname.length-2))}const i=r.indexOf("#");i!==-1&&(this.hash=r.substr(i),r=r.slice(0,i));const s=r.indexOf("?");return s!==-1&&(this.search=r.substr(s),r=r.slice(0,s)),r&&(this.pathname=r),qu[t]&&this.hostname&&!this.pathname&&(this.pathname=""),this};w0.prototype.parseHost=function(e){let u=Ar.exec(e);u&&(u=u[0],u!==":"&&(this.port=u.substr(1)),e=e.substr(0,e.length-u.length)),e&&(this.hostname=e)};const Mr=Object.freeze(Object.defineProperty({__proto__:null,decode:Ye,encode:l0,format:xu,parse:yu},Symbol.toStringTag,{value:"Module"})),_t=/[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,Ct=/[\0-\x1F\x7F-\x9F]/,zr=/[\xAD\u0600-\u0605\u061C\u06DD\u070F\u0890\u0891\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD80D[\uDC30-\uDC3F]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/,ku=/[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDEAD\uDF55-\uDF59\uDF86-\uDF89]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5A\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDEB9\uDF3C-\uDF3E]|\uD806[\uDC3B\uDD44-\uDD46\uDDE2\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2\uDF00-\uDF09]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8\uDF43-\uDF4F\uDFFF]|\uD809[\uDC70-\uDC74]|\uD80B[\uDFF1\uDFF2]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A\uDFE2]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/,At=/[\$\+<->\^`\|~\xA2-\xA6\xA8\xA9\xAC\xAE-\xB1\xB4\xB8\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0384\u0385\u03F6\u0482\u058D-\u058F\u0606-\u0608\u060B\u060E\u060F\u06DE\u06E9\u06FD\u06FE\u07F6\u07FE\u07FF\u0888\u09F2\u09F3\u09FA\u09FB\u0AF1\u0B70\u0BF3-\u0BFA\u0C7F\u0D4F\u0D79\u0E3F\u0F01-\u0F03\u0F13\u0F15-\u0F17\u0F1A-\u0F1F\u0F34\u0F36\u0F38\u0FBE-\u0FC5\u0FC7-\u0FCC\u0FCE\u0FCF\u0FD5-\u0FD8\u109E\u109F\u1390-\u1399\u166D\u17DB\u1940\u19DE-\u19FF\u1B61-\u1B6A\u1B74-\u1B7C\u1FBD\u1FBF-\u1FC1\u1FCD-\u1FCF\u1FDD-\u1FDF\u1FED-\u1FEF\u1FFD\u1FFE\u2044\u2052\u207A-\u207C\u208A-\u208C\u20A0-\u20C0\u2100\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F\u218A\u218B\u2190-\u2307\u230C-\u2328\u232B-\u2426\u2440-\u244A\u249C-\u24E9\u2500-\u2767\u2794-\u27C4\u27C7-\u27E5\u27F0-\u2982\u2999-\u29D7\u29DC-\u29FB\u29FE-\u2B73\u2B76-\u2B95\u2B97-\u2BFF\u2CE5-\u2CEA\u2E50\u2E51\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFF\u3004\u3012\u3013\u3020\u3036\u3037\u303E\u303F\u309B\u309C\u3190\u3191\u3196-\u319F\u31C0-\u31E3\u31EF\u3200-\u321E\u322A-\u3247\u3250\u3260-\u327F\u328A-\u32B0\u32C0-\u33FF\u4DC0-\u4DFF\uA490-\uA4C6\uA700-\uA716\uA720\uA721\uA789\uA78A\uA828-\uA82B\uA836-\uA839\uAA77-\uAA79\uAB5B\uAB6A\uAB6B\uFB29\uFBB2-\uFBC2\uFD40-\uFD4F\uFDCF\uFDFC-\uFDFF\uFE62\uFE64-\uFE66\uFE69\uFF04\uFF0B\uFF1C-\uFF1E\uFF3E\uFF40\uFF5C\uFF5E\uFFE0-\uFFE6\uFFE8-\uFFEE\uFFFC\uFFFD]|\uD800[\uDD37-\uDD3F\uDD79-\uDD89\uDD8C-\uDD8E\uDD90-\uDD9C\uDDA0\uDDD0-\uDDFC]|\uD802[\uDC77\uDC78\uDEC8]|\uD805\uDF3F|\uD807[\uDFD5-\uDFF1]|\uD81A[\uDF3C-\uDF3F\uDF45]|\uD82F\uDC9C|\uD833[\uDF50-\uDFC3]|\uD834[\uDC00-\uDCF5\uDD00-\uDD26\uDD29-\uDD64\uDD6A-\uDD6C\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDDEA\uDE00-\uDE41\uDE45\uDF00-\uDF56]|\uD835[\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85\uDE86]|\uD838[\uDD4F\uDEFF]|\uD83B[\uDCAC\uDCB0\uDD2E\uDEF0\uDEF1]|\uD83C[\uDC00-\uDC2B\uDC30-\uDC93\uDCA0-\uDCAE\uDCB1-\uDCBF\uDCC1-\uDCCF\uDCD1-\uDCF5\uDD0D-\uDDAD\uDDE6-\uDE02\uDE10-\uDE3B\uDE40-\uDE48\uDE50\uDE51\uDE60-\uDE65\uDF00-\uDFFF]|\uD83D[\uDC00-\uDED7\uDEDC-\uDEEC\uDEF0-\uDEFC\uDF00-\uDF76\uDF7B-\uDFD9\uDFE0-\uDFEB\uDFF0]|\uD83E[\uDC00-\uDC0B\uDC10-\uDC47\uDC50-\uDC59\uDC60-\uDC87\uDC90-\uDCAD\uDCB0\uDCB1\uDD00-\uDE53\uDE60-\uDE6D\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC5\uDECE-\uDEDB\uDEE0-\uDEE8\uDEF0-\uDEF8\uDF00-\uDF92\uDF94-\uDFCA]/,Dt=/[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/,Br=Object.freeze(Object.defineProperty({__proto__:null,Any:_t,Cc:Ct,Cf:zr,P:ku,S:At,Z:Dt},Symbol.toStringTag,{value:"Module"})),Lr=new Uint16Array('ᵁ<Õıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms¦³¹ÈÏlig耻Æ䃆P耻&䀦cute耻Á䃁reve;䄂Āiyx}rc耻Â䃂;䐐r;쀀𝔄rave耻À䃀pha;䎑acr;䄀d;橓Āgp¡on;䄄f;쀀𝔸plyFunction;恡ing耻Å䃅Ācs¾Ãr;쀀𝒜ign;扔ilde耻Ã䃃ml耻Ä䃄ЀaceforsuåûþėĜĢħĪĀcrêòkslash;或Ŷöø;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀𝔅pf;쀀𝔹eve;䋘còēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻©䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻Ç䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷òſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀𝒞pĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀𝔇Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀𝔻ƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegraìȹoɴ͹\0\0ͻ»͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔eåˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀𝒟rok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻Ð䃐cute耻É䃉ƀaiyӒӗӜron;䄚rc耻Ê䃊;䐭ot;䄖r;쀀𝔈rave耻È䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀𝔼silon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻Ë䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀𝔉lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀𝔽All;戀riertrf;愱cò׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀𝔊;拙pf;쀀𝔾eater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀𝒢;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅòکrok;䄦mpńېۘownHumðįqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻Í䃍Āiyܓܘrc耻Î䃎;䐘ot;䄰r;愑rave耻Ì䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lieóϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀𝕀a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻Ï䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀𝔍pf;쀀𝕁ǣ߇\0ߌr;쀀𝒥rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀𝔎pf;쀀𝕂cr;쀀𝒦րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ightáΜs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀𝔏Ā;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊightáοightáϊf;쀀𝕃erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂòࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀𝔐nusPlus;戓pf;쀀𝕄cò੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘ë૙eryThiî૙tedĀGL૸ଆreaterGreateòٳessLesóੈLine;䀊r;쀀𝔑ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀𝒩ilde耻Ñ䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻Ó䃓Āiy෎ීrc耻Ô䃔;䐞blac;䅐r;쀀𝔒rave耻Ò䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀𝕆enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀𝒪ash耻Ø䃘iŬื฼de耻Õ䃕es;樷ml耻Ö䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀𝔓i;䎦;䎠usMinus;䂱Āipຢອncareplanåڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀𝒫;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀𝔔pf;愚cr;쀀𝒬؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻®䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r»ཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀𝔖ortȀDLRUᄪᄴᄾᅉownArrow»ОeftArrow»࢚ightArrow»࿝pArrow;憑gma;䎣allCircle;战pf;쀀𝕊ɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀𝒮ar;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Tháྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et»ሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻Þ䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀𝔗Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀𝕋ipleDot;惛Āctዖዛr;쀀𝒯rok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻Ú䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻Û䃛;䐣blac;䅰r;쀀𝔘rave耻Ù䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀𝕌ЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥ownáϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀𝒰ilde;䅨ml耻Ü䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀𝔙pf;쀀𝕍cr;쀀𝒱dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀𝔚pf;쀀𝕎cr;쀀𝒲Ȁfiosᓋᓐᓒᓘr;쀀𝔛;䎞pf;쀀𝕏cr;쀀𝒳ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻Ý䃝Āiyᔉᔍrc;䅶;䐫r;쀀𝔜pf;쀀𝕐cr;쀀𝒴ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidtè૙a;䎖r;愨pf;愤cr;쀀𝒵௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻á䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻â䃢te肻´̆;䐰lig耻æ䃦Ā;r²ᖺ;쀀𝔞rave耻à䃠ĀepᗊᗖĀfpᗏᗔsym;愵èᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e»ᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢»¹arr;捼Āgpᙣᙧon;䄅f;쀀𝕒΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒñᚃing耻å䃥ƀctyᚡᚦᚨr;쀀𝒶;䀪mpĀ;e዁ᚯñʈilde耻ã䃣ml耻ä䃤Āciᛂᛈoninôɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e»ᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰séᜌnoõēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀𝔟g΀costuvwឍឝឳេ៕៛៞ƀaiuបពរðݠrc;旯p»፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄eåᑄåᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀𝕓Ā;tᏋᡣom»Ꮜtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻¦䂦Ȁceioᥑᥖᥚᥠr;쀀𝒷mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t»᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁îړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻ç䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻¸ƭptyv;榲t脀¢;eᨭᨮ䂢räƲr;쀀𝔠ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark»ᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟»ཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it»᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;qÇÆɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁îᅠeĀmx᫱᫶ent»᫩eóɍǧ᫾\0ᬇĀ;dኻᬂot;橭nôɆƀfryᬐᬔᬗ;쀀𝕔oäɔ脀©;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀𝒸Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒreã᭳uã᭵ee;拎edge;拏en耻¤䂤earrowĀlrᯮ᯳eft»ᮀight»ᮽeäᯝĀciᰁᰇoninôǷnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍rò΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸òᄳhĀ;vᱚᱛ怐»ऊūᱡᱧarow;椏aã̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻°䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀𝔡arĀlrᲳᲵ»ࣜ»သʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀÷;o᳧ᳰntimes;拇nø᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀𝕕ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedgåúnƀadhᄮᵝᵧownarrowóᲃarpoonĀlrᵲᵶefôᲴighôᲶŢᵿᶅkaro÷གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀𝒹;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃ròЩaòྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴoôᲉĀcsḎḔute耻é䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻ê䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀𝔢ƀ;rsṐṑṗ檚ave耻è䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et»ẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀𝕖ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on»ớ;䏵ȀcsuvỪỳἋἣĀioữḱrc»Ḯɩỹ\0\0ỻíՈantĀglἂἆtr»ṝess»Ṻƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯oô͒ĀahὉὋ;䎷耻ð䃰Āmrὓὗl耻ë䃫o;悬ƀcipὡὤὧl;䀡sôծĀeoὬὴctatioîՙnentialåչৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotseñṄy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀𝔣lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀𝕗ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻½䂽;慓耻¼䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻¾䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀𝒻ࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lanô٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀𝔤Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox»ℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀𝕘Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎proø₞r;楸qĀlqؿ↖lesó₈ií٫Āen↣↭rtneqq;쀀≩︀Å↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽ròΠȀilmr⇐⇔⇗⇛rsðᒄf»․ilôکĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it»∊lip;怦con;抹r;쀀𝔥sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀𝕙bar;怕ƀclt≯≴≸r;쀀𝒽asè⇴rok;䄧Ābp⊂⊇ull;恃hen»ᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻í䃭ƀ;iyݱ⊰⊵rc耻î䃮;䐸Ācx⊼⊿y;䐵cl耻¡䂡ĀfrΟ⋉;쀀𝔦rave耻ì䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓inåގarôܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝doô⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙eróᕣã⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀𝕚a;䎹uest耻¿䂿Āci⎊⎏r;쀀𝒾nʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻ï䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀𝔧ath;䈷pf;쀀𝕛ǣ⏬\0⏱r;쀀𝒿rcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀𝔨reen;䄸cy;䑅cy;䑜pf;쀀𝕜cr;쀀𝓀஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼rò৆òΕail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴raîࡌbda;䎻gƀ;dlࢎⓁⓃ;榑åࢎ;檅uo耻«䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝ë≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼ìࢰâ┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□aé⓶arpoonĀdu▯▴own»њp»०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoonó྘quigarro÷⇰hreetimes;拋ƀ;qs▋ও◺lanôবʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋pproøⓆot;拖qĀgq♃♅ôউgtò⒌ôছiíলƀilr♕࣡♚sht;楼;쀀𝔩Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖rò◁orneòᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che»⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox»⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽rëࣁgƀlmr⛿✍✔eftĀar০✇ightá৲apsto;柼ightá৽parrowĀlr✥✩efô⓭ight;憬ƀafl✶✹✽r;榅;쀀𝕝us;樭imes;樴š❋❏st;戗áፎƀ;ef❗❘᠀旊nge»❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇ròࢨorneòᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀𝓁mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹reå◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀Å⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻¯䂯Āet⡗⡙;時Ā;e⡞⡟朠se»⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻owîҌefôएðᏑker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle»ᘦr;쀀𝔪o;愧ƀcdn⢯⢴⣉ro耻µ䂵Ȁ;acdᑤ⢽⣀⣄sôᚧir;櫰ot肻·Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛ò−ðઁĀdp⣩⣮els;抧f;쀀𝕞Āct⣸⣽r;쀀𝓂pos»ᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la»˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉roø඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻 ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸uiöୣĀei⩊⩎ar;椨í஘istĀ;s஠டr;쀀𝔫ȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lanô௢ií௪Ā;rஶ⪁»ஷƀAap⪊⪍⪑rò⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹rò⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro÷⫁ightarro÷⪐ƀ;qs఻⪺⫪lanôౕĀ;sౕ⫴»శiíౝĀ;rవ⫾iĀ;eచథiäඐĀpt⬌⬑f;쀀𝕟膀¬;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lleì୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳uåಥĀ;cಘ⭸Ā;eಒ⭽ñಘȀAait⮈⮋⮝⮧rò⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow»⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉uå൅;쀀𝓃ortɭ⬅\0\0⯖ará⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭å೸åഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗñസȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇìௗlde耻ñ䃱çృiangleĀlrⱒⱜeftĀ;eచⱚñదightĀ;eೋⱥñ೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻ó䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻ô䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀𝔬ͯ⵹\0\0⵼\0ⶂn;䋛ave耻ò䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨrò᪀Āir⶝ⶠr;榾oss;榻nå๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀𝕠ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨rò᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f»ⷿ耻ª䂪耻º䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧ò⸁ash耻ø䃸l;折iŬⸯ⸴de耻õ䃵esĀ;aǛ⸺s;樶ml耻ö䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀¶;l⹭⹮䂶leìЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀𝔭ƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕maô੶ne;明ƀ;tv⺿⻀⻈䏀chfork»´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎ö⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻±ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀𝕡nd耻£䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷uå໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾pproø⽃urlyeñ໙ñ໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨iíໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺ð⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴ï໻rel;抰Āci⿀⿅r;쀀𝓅;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀𝔮pf;쀀𝕢rime;恗cr;쀀𝓆ƀaeo⿸〉〓tĀei⿾々rnionóڰnt;樖stĀ;e【】䀿ñἙô༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがròႳòϝail;検aròᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕iãᅮmptyv;榳gȀ;del࿑らるろ;榒;榥å࿑uo耻»䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞ë≝ð✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶aló༞ƀabrョリヮrò៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗ì࿲âヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜnåႻarôྩt;断ƀilrㅩဣㅮsht;楽;쀀𝔯ĀaoㅷㆆrĀduㅽㅿ»ѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭaéトarpoonĀduㆻㆿowîㅾp»႒eftĀah㇊㇐rrowó࿪arpoonóՑightarrows;應quigarro÷ニhreetimes;拌g;䋚ingdotseñἲƀahm㈍㈐㈓rò࿪aòՑ;怏oustĀ;a㈞㈟掱che»㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾rëဃƀafl㉇㉊㉎r;榆;쀀𝕣us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒arò㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀𝓇Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠reåㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛quï➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡uåᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓iíሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒ë∨Ā;oਸ਼਴t耻§䂧i;䀻war;椩mĀin㍩ðnuóñt;朶rĀ;o㍶⁕쀀𝔰Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜iäᑤaraì⹯耻­䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲aròᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetmé㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀𝕤aĀdr㑍ЂesĀ;u㑔㑕晠it»㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍ñᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝ñᆮƀ;afᅻ㒦ְrť㒫ֱ»ᅼaròᅈȀcemt㒹㒾㓂㓅r;쀀𝓈tmîñiì㐕aræᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psiloîỠhé⺯s»⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦pproø㋺urlyeñᇾñᇳƀaes㖂㖈㌛pproø㌚qñ㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻¹䂹耻²䂲耻³䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨ë∮Ā;oਫ਩war;椪lig耻ß䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄rë๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀𝔱Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮pproø዁im»ኬsðኞĀas㚺㚮ð዁rn耻þ䃾Ǭ̟㛆⋧es膀×;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀á⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀𝕥rk;櫚á㍢rime;怴ƀaip㜏㜒㝤dåቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own»ᶻeftĀ;e⠀㜾ñम;扜ightĀ;e㊪㝋ñၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀𝓉;䑆cy;䑛rok;䅧Āio㞋㞎xô᝷headĀlr㞗㞠eftarro÷ࡏightarrow»ཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶ròϭar;楣Ācr㟜㟢ute耻ú䃺òᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻û䃻;䑃ƀabh㠃㠆㠋ròᎭlac;䅱aòᏃĀir㠓㠘sht;楾;쀀𝔲rave耻ù䃹š㠧㠱rĀlr㠬㠮»ॗ»ႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r»㡆op;挏ri;旸Āal㡖㡚cr;䅫肻¨͉Āgp㡢㡦on;䅳f;쀀𝕦̀adhlsuᅋ㡸㡽፲㢑㢠ownáᎳarpoonĀlr㢈㢌efô㠭ighô㠯iƀ;hl㢙㢚㢜䏅»ᏺon»㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r»㢽op;挎ng;䅯ri;旹cr;쀀𝓊ƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨»᠓Āam㣯㣲rò㢨l耻ü䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠ròϷarĀ;v㤦㤧櫨;櫩asèϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖appá␕othinçẖƀhir㓫⻈㥙opô⾵Ā;hᎷ㥢ïㆍĀiu㥩㥭gmá㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟etá㚜iangleĀlr㦪㦯eft»थight»ၑy;䐲ash»ံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨaòᑩr;쀀𝔳tré㦮suĀbp㧯㧱»ജ»൙pf;쀀𝕧roð໻tré㦴Ācu㨆㨋r;쀀𝓋Ābp㨐㨘nĀEe㦀㨖»㥾nĀEe㦒㨞»㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀𝔴pf;쀀𝕨Ā;eᑹ㩦atèᑹcr;쀀𝓌ૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tré៑r;쀀𝔵ĀAa㪔㪗ròσrò৶;䎾ĀAa㪡㪤ròθrò৫að✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀𝕩imåឲĀAa㫇㫊ròώròਁĀcq㫒ីr;쀀𝓍Āpt៖㫜ré។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻ý䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻¥䂥r;쀀𝔶cy;䑗pf;쀀𝕪cr;쀀𝓎Ācm㬦㬩y;䑎l耻ÿ䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡træᕟa;䎶r;쀀𝔷cy;䐶grarr;懝pf;쀀𝕫cr;쀀𝓏Ājn㮅㮇;怍j;怌'.split("").map(e=>e.charCodeAt(0))),qr=new Uint16Array("Ȁaglq	\x1Bɭ\0\0p;䀦os;䀧t;䀾t;䀼uot;䀢".split("").map(e=>e.charCodeAt(0)));var P0;const Ir=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),Pr=(P0=String.fromCodePoint)!==null&&P0!==void 0?P0:function(e){let u="";return e>65535&&(e-=65536,u+=String.fromCharCode(e>>>10&1023|55296),e=56320|e&1023),u+=String.fromCharCode(e),u};function Nr(e){var u;return e>=55296&&e<=57343||e>1114111?65533:(u=Ir.get(e))!==null&&u!==void 0?u:e}var pe;(function(e){e[e.NUM=35]="NUM",e[e.SEMI=59]="SEMI",e[e.EQUALS=61]="EQUALS",e[e.ZERO=48]="ZERO",e[e.NINE=57]="NINE",e[e.LOWER_A=97]="LOWER_A",e[e.LOWER_F=102]="LOWER_F",e[e.LOWER_X=120]="LOWER_X",e[e.LOWER_Z=122]="LOWER_Z",e[e.UPPER_A=65]="UPPER_A",e[e.UPPER_F=70]="UPPER_F",e[e.UPPER_Z=90]="UPPER_Z"})(pe||(pe={}));const Or=32;var Ge;(function(e){e[e.VALUE_LENGTH=49152]="VALUE_LENGTH",e[e.BRANCH_LENGTH=16256]="BRANCH_LENGTH",e[e.JUMP_TABLE=127]="JUMP_TABLE"})(Ge||(Ge={}));function bu(e){return e>=pe.ZERO&&e<=pe.NINE}function Ur(e){return e>=pe.UPPER_A&&e<=pe.UPPER_F||e>=pe.LOWER_A&&e<=pe.LOWER_F}function jr(e){return e>=pe.UPPER_A&&e<=pe.UPPER_Z||e>=pe.LOWER_A&&e<=pe.LOWER_Z||bu(e)}function Hr(e){return e===pe.EQUALS||jr(e)}var he;(function(e){e[e.EntityStart=0]="EntityStart",e[e.NumericStart=1]="NumericStart",e[e.NumericDecimal=2]="NumericDecimal",e[e.NumericHex=3]="NumericHex",e[e.NamedEntity=4]="NamedEntity"})(he||(he={}));var Ve;(function(e){e[e.Legacy=0]="Legacy",e[e.Strict=1]="Strict",e[e.Attribute=2]="Attribute"})(Ve||(Ve={}));class Vr{constructor(u,t,o){this.decodeTree=u,this.emitCodePoint=t,this.errors=o,this.state=he.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=Ve.Strict}startEntity(u){this.decodeMode=u,this.state=he.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(u,t){switch(this.state){case he.EntityStart:return u.charCodeAt(t)===pe.NUM?(this.state=he.NumericStart,this.consumed+=1,this.stateNumericStart(u,t+1)):(this.state=he.NamedEntity,this.stateNamedEntity(u,t));case he.NumericStart:return this.stateNumericStart(u,t);case he.NumericDecimal:return this.stateNumericDecimal(u,t);case he.NumericHex:return this.stateNumericHex(u,t);case he.NamedEntity:return this.stateNamedEntity(u,t)}}stateNumericStart(u,t){return t>=u.length?-1:(u.charCodeAt(t)|Or)===pe.LOWER_X?(this.state=he.NumericHex,this.consumed+=1,this.stateNumericHex(u,t+1)):(this.state=he.NumericDecimal,this.stateNumericDecimal(u,t))}addToNumericResult(u,t,o,n){if(t!==o){const r=o-t;this.result=this.result*Math.pow(n,r)+parseInt(u.substr(t,r),n),this.consumed+=r}}stateNumericHex(u,t){const o=t;for(;t<u.length;){const n=u.charCodeAt(t);if(bu(n)||Ur(n))t+=1;else return this.addToNumericResult(u,o,t,16),this.emitNumericEntity(n,3)}return this.addToNumericResult(u,o,t,16),-1}stateNumericDecimal(u,t){const o=t;for(;t<u.length;){const n=u.charCodeAt(t);if(bu(n))t+=1;else return this.addToNumericResult(u,o,t,10),this.emitNumericEntity(n,2)}return this.addToNumericResult(u,o,t,10),-1}emitNumericEntity(u,t){var o;if(this.consumed<=t)return(o=this.errors)===null||o===void 0||o.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(u===pe.SEMI)this.consumed+=1;else if(this.decodeMode===Ve.Strict)return 0;return this.emitCodePoint(Nr(this.result),this.consumed),this.errors&&(u!==pe.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(u,t){const{decodeTree:o}=this;let n=o[this.treeIndex],r=(n&Ge.VALUE_LENGTH)>>14;for(;t<u.length;t++,this.excess++){const c=u.charCodeAt(t);if(this.treeIndex=Gr(o,n,this.treeIndex+Math.max(1,r),c),this.treeIndex<0)return this.result===0||this.decodeMode===Ve.Attribute&&(r===0||Hr(c))?0:this.emitNotTerminatedNamedEntity();if(n=o[this.treeIndex],r=(n&Ge.VALUE_LENGTH)>>14,r!==0){if(c===pe.SEMI)return this.emitNamedEntityData(this.treeIndex,r,this.consumed+this.excess);this.decodeMode!==Ve.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return-1}emitNotTerminatedNamedEntity(){var u;const{result:t,decodeTree:o}=this,n=(o[t]&Ge.VALUE_LENGTH)>>14;return this.emitNamedEntityData(t,n,this.consumed),(u=this.errors)===null||u===void 0||u.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(u,t,o){const{decodeTree:n}=this;return this.emitCodePoint(t===1?n[u]&~Ge.VALUE_LENGTH:n[u+1],o),t===3&&this.emitCodePoint(n[u+2],o),o}end(){var u;switch(this.state){case he.NamedEntity:return this.result!==0&&(this.decodeMode!==Ve.Attribute||this.result===this.treeIndex)?this.emitNotTerminatedNamedEntity():0;case he.NumericDecimal:return this.emitNumericEntity(0,2);case he.NumericHex:return this.emitNumericEntity(0,3);case he.NumericStart:return(u=this.errors)===null||u===void 0||u.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case he.EntityStart:return 0}}}function Et(e){let u="";const t=new Vr(e,o=>u+=Pr(o));return function(n,r){let c=0,i=0;for(;(i=n.indexOf("&",i))>=0;){u+=n.slice(c,i),t.startEntity(r);const f=t.write(n,i+1);if(f<0){c=i+t.end();break}c=i+f,i=f===0?c+1:c}const s=u+n.slice(c);return u="",s}}function Gr(e,u,t,o){const n=(u&Ge.BRANCH_LENGTH)>>7,r=u&Ge.JUMP_TABLE;if(n===0)return r!==0&&o===r?t:-1;if(r){const s=o-r;return s<0||s>=n?-1:e[t+s]-1}let c=t,i=c+n-1;for(;c<=i;){const s=c+i>>>1,f=e[s];if(f<o)c=s+1;else if(f>o)i=s-1;else return e[s+n]}return-1}const Wr=Et(Lr);Et(qr);function St(e,u=Ve.Legacy){return Wr(e,u)}function Zr(e){return Object.prototype.toString.call(e)}function wu(e){return Zr(e)==="[object String]"}const $r=Object.prototype.hasOwnProperty;function Xr(e,u){return $r.call(e,u)}function A0(e){return Array.prototype.slice.call(arguments,1).forEach(function(t){if(t){if(typeof t!="object")throw new TypeError(t+"must be object");Object.keys(t).forEach(function(o){e[o]=t[o]})}}),e}function Ft(e,u,t){return[].concat(e.slice(0,u),t,e.slice(u+1))}function _u(e){return!(e>=55296&&e<=57343||e>=64976&&e<=65007||(e&65535)===65535||(e&65535)===65534||e>=0&&e<=8||e===11||e>=14&&e<=31||e>=127&&e<=159||e>1114111)}function _0(e){if(e>65535){e-=65536;const u=55296+(e>>10),t=56320+(e&1023);return String.fromCharCode(u,t)}return String.fromCharCode(e)}const Rt=/\\([!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~])/g,Jr=/&([a-z#][a-z0-9]{1,31});/gi,Kr=new RegExp(Rt.source+"|"+Jr.source,"gi"),Qr=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))$/i;function Yr(e,u){if(u.charCodeAt(0)===35&&Qr.test(u)){const o=u[1].toLowerCase()==="x"?parseInt(u.slice(2),16):parseInt(u.slice(1),10);return _u(o)?_0(o):e}const t=St(e);return t!==e?t:e}function en(e){return e.indexOf("\\")<0?e:e.replace(Rt,"$1")}function e0(e){return e.indexOf("\\")<0&&e.indexOf("&")<0?e:e.replace(Kr,function(u,t,o){return t||Yr(u,o)})}const un=/[&<>"]/,tn=/[&<>"]/g,rn={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function nn(e){return rn[e]}function We(e){return un.test(e)?e.replace(tn,nn):e}const an=/[.?*+^$[\]\\(){}|-]/g;function on(e){return e.replace(an,"\\$&")}function se(e){switch(e){case 9:case 32:return!0}return!1}function i0(e){if(e>=8192&&e<=8202)return!0;switch(e){case 9:case 10:case 11:case 12:case 13:case 32:case 160:case 5760:case 8239:case 8287:case 12288:return!0}return!1}function a0(e){return ku.test(e)||At.test(e)}function o0(e){switch(e){case 33:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:case 58:case 59:case 60:case 61:case 62:case 63:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 124:case 125:case 126:return!0;default:return!1}}function D0(e){return e=e.trim().replace(/\s+/g," "),"ẞ".toLowerCase()==="Ṿ"&&(e=e.replace(/ẞ/g,"ß")),e.toLowerCase().toUpperCase()}const sn={mdurl:Mr,ucmicro:Br},cn=Object.freeze(Object.defineProperty({__proto__:null,arrayReplaceAt:Ft,assign:A0,escapeHtml:We,escapeRE:on,fromCodePoint:_0,has:Xr,isMdAsciiPunct:o0,isPunctChar:a0,isSpace:se,isString:wu,isValidEntityCode:_u,isWhiteSpace:i0,lib:sn,normalizeReference:D0,unescapeAll:e0,unescapeMd:en},Symbol.toStringTag,{value:"Module"}));function ln(e,u,t){let o,n,r,c;const i=e.posMax,s=e.pos;for(e.pos=u+1,o=1;e.pos<i;){if(r=e.src.charCodeAt(e.pos),r===93&&(o--,o===0)){n=!0;break}if(c=e.pos,e.md.inline.skipToken(e),r===91){if(c===e.pos-1)o++;else if(t)return e.pos=s,-1}}let f=-1;return n&&(f=e.pos),e.pos=s,f}function fn(e,u,t){let o,n=u;const r={ok:!1,pos:0,str:""};if(e.charCodeAt(n)===60){for(n++;n<t;){if(o=e.charCodeAt(n),o===10||o===60)return r;if(o===62)return r.pos=n+1,r.str=e0(e.slice(u+1,n)),r.ok=!0,r;if(o===92&&n+1<t){n+=2;continue}n++}return r}let c=0;for(;n<t&&(o=e.charCodeAt(n),!(o===32||o<32||o===127));){if(o===92&&n+1<t){if(e.charCodeAt(n+1)===32)break;n+=2;continue}if(o===40&&(c++,c>32))return r;if(o===41){if(c===0)break;c--}n++}return u===n||c!==0||(r.str=e0(e.slice(u,n)),r.pos=n,r.ok=!0),r}function dn(e,u,t,o){let n,r=u;const c={ok:!1,can_continue:!1,pos:0,str:"",marker:0};if(o)c.str=o.str,c.marker=o.marker;else{if(r>=t)return c;let i=e.charCodeAt(r);if(i!==34&&i!==39&&i!==40)return c;u++,r++,i===40&&(i=41),c.marker=i}for(;r<t;){if(n=e.charCodeAt(r),n===c.marker)return c.pos=r+1,c.str+=e0(e.slice(u,r)),c.ok=!0,c;if(n===40&&c.marker===41)return c;n===92&&r+1<t&&r++,r++}return c.can_continue=!0,c.str+=e0(e.slice(u,r)),c}const hn=Object.freeze(Object.defineProperty({__proto__:null,parseLinkDestination:fn,parseLinkLabel:ln,parseLinkTitle:dn},Symbol.toStringTag,{value:"Module"})),Be={};Be.code_inline=function(e,u,t,o,n){const r=e[u];return"<code"+n.renderAttrs(r)+">"+We(r.content)+"</code>"};Be.code_block=function(e,u,t,o,n){const r=e[u];return"<pre"+n.renderAttrs(r)+"><code>"+We(e[u].content)+`</code></pre>
`};Be.fence=function(e,u,t,o,n){const r=e[u],c=r.info?e0(r.info).trim():"";let i="",s="";if(c){const h=c.split(/(\s+)/g);i=h[0],s=h.slice(2).join("")}let f;if(t.highlight?f=t.highlight(r.content,i,s)||We(r.content):f=We(r.content),f.indexOf("<pre")===0)return f+`
`;if(c){const h=r.attrIndex("class"),v=r.attrs?r.attrs.slice():[];h<0?v.push(["class",t.langPrefix+i]):(v[h]=v[h].slice(),v[h][1]+=" "+t.langPrefix+i);const _={attrs:v};return`<pre><code${n.renderAttrs(_)}>${f}</code></pre>
`}return`<pre><code${n.renderAttrs(r)}>${f}</code></pre>
`};Be.image=function(e,u,t,o,n){const r=e[u];return r.attrs[r.attrIndex("alt")][1]=n.renderInlineAsText(r.children,t,o),n.renderToken(e,u,t)};Be.hardbreak=function(e,u,t){return t.xhtmlOut?`<br />
`:`<br>
`};Be.softbreak=function(e,u,t){return t.breaks?t.xhtmlOut?`<br />
`:`<br>
`:`
`};Be.text=function(e,u){return We(e[u].content)};Be.html_block=function(e,u){return e[u].content};Be.html_inline=function(e,u){return e[u].content};function u0(){this.rules=A0({},Be)}u0.prototype.renderAttrs=function(u){let t,o,n;if(!u.attrs)return"";for(n="",t=0,o=u.attrs.length;t<o;t++)n+=" "+We(u.attrs[t][0])+'="'+We(u.attrs[t][1])+'"';return n};u0.prototype.renderToken=function(u,t,o){const n=u[t];let r="";if(n.hidden)return"";n.block&&n.nesting!==-1&&t&&u[t-1].hidden&&(r+=`
`),r+=(n.nesting===-1?"</":"<")+n.tag,r+=this.renderAttrs(n),n.nesting===0&&o.xhtmlOut&&(r+=" /");let c=!1;if(n.block&&(c=!0,n.nesting===1&&t+1<u.length)){const i=u[t+1];(i.type==="inline"||i.hidden||i.nesting===-1&&i.tag===n.tag)&&(c=!1)}return r+=c?`>
`:">",r};u0.prototype.renderInline=function(e,u,t){let o="";const n=this.rules;for(let r=0,c=e.length;r<c;r++){const i=e[r].type;typeof n[i]<"u"?o+=n[i](e,r,u,t,this):o+=this.renderToken(e,r,u)}return o};u0.prototype.renderInlineAsText=function(e,u,t){let o="";for(let n=0,r=e.length;n<r;n++)switch(e[n].type){case"text":o+=e[n].content;break;case"image":o+=this.renderInlineAsText(e[n].children,u,t);break;case"html_inline":case"html_block":o+=e[n].content;break;case"softbreak":case"hardbreak":o+=`
`;break}return o};u0.prototype.render=function(e,u,t){let o="";const n=this.rules;for(let r=0,c=e.length;r<c;r++){const i=e[r].type;i==="inline"?o+=this.renderInline(e[r].children,u,t):typeof n[i]<"u"?o+=n[i](e,r,u,t,this):o+=this.renderToken(e,r,u,t)}return o};function xe(){this.__rules__=[],this.__cache__=null}xe.prototype.__find__=function(e){for(let u=0;u<this.__rules__.length;u++)if(this.__rules__[u].name===e)return u;return-1};xe.prototype.__compile__=function(){const e=this,u=[""];e.__rules__.forEach(function(t){t.enabled&&t.alt.forEach(function(o){u.indexOf(o)<0&&u.push(o)})}),e.__cache__={},u.forEach(function(t){e.__cache__[t]=[],e.__rules__.forEach(function(o){o.enabled&&(t&&o.alt.indexOf(t)<0||e.__cache__[t].push(o.fn))})})};xe.prototype.at=function(e,u,t){const o=this.__find__(e),n=t||{};if(o===-1)throw new Error("Parser rule not found: "+e);this.__rules__[o].fn=u,this.__rules__[o].alt=n.alt||[],this.__cache__=null};xe.prototype.before=function(e,u,t,o){const n=this.__find__(e),r=o||{};if(n===-1)throw new Error("Parser rule not found: "+e);this.__rules__.splice(n,0,{name:u,enabled:!0,fn:t,alt:r.alt||[]}),this.__cache__=null};xe.prototype.after=function(e,u,t,o){const n=this.__find__(e),r=o||{};if(n===-1)throw new Error("Parser rule not found: "+e);this.__rules__.splice(n+1,0,{name:u,enabled:!0,fn:t,alt:r.alt||[]}),this.__cache__=null};xe.prototype.push=function(e,u,t){const o=t||{};this.__rules__.push({name:e,enabled:!0,fn:u,alt:o.alt||[]}),this.__cache__=null};xe.prototype.enable=function(e,u){Array.isArray(e)||(e=[e]);const t=[];return e.forEach(function(o){const n=this.__find__(o);if(n<0){if(u)return;throw new Error("Rules manager: invalid rule name "+o)}this.__rules__[n].enabled=!0,t.push(o)},this),this.__cache__=null,t};xe.prototype.enableOnly=function(e,u){Array.isArray(e)||(e=[e]),this.__rules__.forEach(function(t){t.enabled=!1}),this.enable(e,u)};xe.prototype.disable=function(e,u){Array.isArray(e)||(e=[e]);const t=[];return e.forEach(function(o){const n=this.__find__(o);if(n<0){if(u)return;throw new Error("Rules manager: invalid rule name "+o)}this.__rules__[n].enabled=!1,t.push(o)},this),this.__cache__=null,t};xe.prototype.getRules=function(e){return this.__cache__===null&&this.__compile__(),this.__cache__[e]||[]};function Fe(e,u,t){this.type=e,this.tag=u,this.attrs=null,this.map=null,this.nesting=t,this.level=0,this.children=null,this.content="",this.markup="",this.info="",this.meta=null,this.block=!1,this.hidden=!1}Fe.prototype.attrIndex=function(u){if(!this.attrs)return-1;const t=this.attrs;for(let o=0,n=t.length;o<n;o++)if(t[o][0]===u)return o;return-1};Fe.prototype.attrPush=function(u){this.attrs?this.attrs.push(u):this.attrs=[u]};Fe.prototype.attrSet=function(u,t){const o=this.attrIndex(u),n=[u,t];o<0?this.attrPush(n):this.attrs[o]=n};Fe.prototype.attrGet=function(u){const t=this.attrIndex(u);let o=null;return t>=0&&(o=this.attrs[t][1]),o};Fe.prototype.attrJoin=function(u,t){const o=this.attrIndex(u);o<0?this.attrPush([u,t]):this.attrs[o][1]=this.attrs[o][1]+" "+t};function Tt(e,u,t){this.src=e,this.env=t,this.tokens=[],this.inlineMode=!1,this.md=u}Tt.prototype.Token=Fe;const pn=/\r\n?|\n/g,mn=/\0/g;function bn(e){let u;u=e.src.replace(pn,`
`),u=u.replace(mn,"�"),e.src=u}function vn(e){let u;e.inlineMode?(u=new e.Token("inline","",0),u.content=e.src,u.map=[0,1],u.children=[],e.tokens.push(u)):e.md.block.parse(e.src,e.md,e.env,e.tokens)}function gn(e){const u=e.tokens;for(let t=0,o=u.length;t<o;t++){const n=u[t];n.type==="inline"&&e.md.inline.parse(n.content,e.md,e.env,n.children)}}function xn(e){return/^<a[>\s]/i.test(e)}function yn(e){return/^<\/a\s*>/i.test(e)}function kn(e){const u=e.tokens;if(e.md.options.linkify)for(let t=0,o=u.length;t<o;t++){if(u[t].type!=="inline"||!e.md.linkify.pretest(u[t].content))continue;let n=u[t].children,r=0;for(let c=n.length-1;c>=0;c--){const i=n[c];if(i.type==="link_close"){for(c--;n[c].level!==i.level&&n[c].type!=="link_open";)c--;continue}if(i.type==="html_inline"&&(xn(i.content)&&r>0&&r--,yn(i.content)&&r++),!(r>0)&&i.type==="text"&&e.md.linkify.test(i.content)){const s=i.content;let f=e.md.linkify.match(s);const h=[];let v=i.level,_=0;f.length>0&&f[0].index===0&&c>0&&n[c-1].type==="text_special"&&(f=f.slice(1));for(let A=0;A<f.length;A++){const a=f[A].url,b=e.md.normalizeLink(a);if(!e.md.validateLink(b))continue;let m=f[A].text;f[A].schema?f[A].schema==="mailto:"&&!/^mailto:/i.test(m)?m=e.md.normalizeLinkText("mailto:"+m).replace(/^mailto:/,""):m=e.md.normalizeLinkText(m):m=e.md.normalizeLinkText("http://"+m).replace(/^http:\/\//,"");const S=f[A].index;if(S>_){const y=new e.Token("text","",0);y.content=s.slice(_,S),y.level=v,h.push(y)}const p=new e.Token("link_open","a",1);p.attrs=[["href",b]],p.level=v++,p.markup="linkify",p.info="auto",h.push(p);const d=new e.Token("text","",0);d.content=m,d.level=v,h.push(d);const w=new e.Token("link_close","a",-1);w.level=--v,w.markup="linkify",w.info="auto",h.push(w),_=f[A].lastIndex}if(_<s.length){const A=new e.Token("text","",0);A.content=s.slice(_),A.level=v,h.push(A)}u[t].children=n=Ft(n,c,h)}}}}const Mt=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,wn=/\((c|tm|r)\)/i,_n=/\((c|tm|r)\)/ig,Cn={c:"©",r:"®",tm:"™"};function An(e,u){return Cn[u.toLowerCase()]}function Dn(e){let u=0;for(let t=e.length-1;t>=0;t--){const o=e[t];o.type==="text"&&!u&&(o.content=o.content.replace(_n,An)),o.type==="link_open"&&o.info==="auto"&&u--,o.type==="link_close"&&o.info==="auto"&&u++}}function En(e){let u=0;for(let t=e.length-1;t>=0;t--){const o=e[t];o.type==="text"&&!u&&Mt.test(o.content)&&(o.content=o.content.replace(/\+-/g,"±").replace(/\.{2,}/g,"…").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---(?=[^-]|$)/mg,"$1—").replace(/(^|\s)--(?=\s|$)/mg,"$1–").replace(/(^|[^-\s])--(?=[^-\s]|$)/mg,"$1–")),o.type==="link_open"&&o.info==="auto"&&u--,o.type==="link_close"&&o.info==="auto"&&u++}}function Sn(e){let u;if(e.md.options.typographer)for(u=e.tokens.length-1;u>=0;u--)e.tokens[u].type==="inline"&&(wn.test(e.tokens[u].content)&&Dn(e.tokens[u].children),Mt.test(e.tokens[u].content)&&En(e.tokens[u].children))}const Fn=/['"]/,Iu=/['"]/g,Pu="’";function v0(e,u,t){return e.slice(0,u)+t+e.slice(u+1)}function Rn(e,u){let t;const o=[];for(let n=0;n<e.length;n++){const r=e[n],c=e[n].level;for(t=o.length-1;t>=0&&!(o[t].level<=c);t--);if(o.length=t+1,r.type!=="text")continue;let i=r.content,s=0,f=i.length;e:for(;s<f;){Iu.lastIndex=s;const h=Iu.exec(i);if(!h)break;let v=!0,_=!0;s=h.index+1;const A=h[0]==="'";let a=32;if(h.index-1>=0)a=i.charCodeAt(h.index-1);else for(t=n-1;t>=0&&!(e[t].type==="softbreak"||e[t].type==="hardbreak");t--)if(e[t].content){a=e[t].content.charCodeAt(e[t].content.length-1);break}let b=32;if(s<f)b=i.charCodeAt(s);else for(t=n+1;t<e.length&&!(e[t].type==="softbreak"||e[t].type==="hardbreak");t++)if(e[t].content){b=e[t].content.charCodeAt(0);break}const m=o0(a)||a0(String.fromCharCode(a)),S=o0(b)||a0(String.fromCharCode(b)),p=i0(a),d=i0(b);if(d?v=!1:S&&(p||m||(v=!1)),p?_=!1:m&&(d||S||(_=!1)),b===34&&h[0]==='"'&&a>=48&&a<=57&&(_=v=!1),v&&_&&(v=m,_=S),!v&&!_){A&&(r.content=v0(r.content,h.index,Pu));continue}if(_)for(t=o.length-1;t>=0;t--){let w=o[t];if(o[t].level<c)break;if(w.single===A&&o[t].level===c){w=o[t];let y,k;A?(y=u.md.options.quotes[2],k=u.md.options.quotes[3]):(y=u.md.options.quotes[0],k=u.md.options.quotes[1]),r.content=v0(r.content,h.index,k),e[w.token].content=v0(e[w.token].content,w.pos,y),s+=k.length-1,w.token===n&&(s+=y.length-1),i=r.content,f=i.length,o.length=t;continue e}}v?o.push({token:n,pos:h.index,single:A,level:c}):_&&A&&(r.content=v0(r.content,h.index,Pu))}}}function Tn(e){if(e.md.options.typographer)for(let u=e.tokens.length-1;u>=0;u--)e.tokens[u].type!=="inline"||!Fn.test(e.tokens[u].content)||Rn(e.tokens[u].children,e)}function Mn(e){let u,t;const o=e.tokens,n=o.length;for(let r=0;r<n;r++){if(o[r].type!=="inline")continue;const c=o[r].children,i=c.length;for(u=0;u<i;u++)c[u].type==="text_special"&&(c[u].type="text");for(u=t=0;u<i;u++)c[u].type==="text"&&u+1<i&&c[u+1].type==="text"?c[u+1].content=c[u].content+c[u+1].content:(u!==t&&(c[t]=c[u]),t++);u!==t&&(c.length=t)}}const N0=[["normalize",bn],["block",vn],["inline",gn],["linkify",kn],["replacements",Sn],["smartquotes",Tn],["text_join",Mn]];function Cu(){this.ruler=new xe;for(let e=0;e<N0.length;e++)this.ruler.push(N0[e][0],N0[e][1])}Cu.prototype.process=function(e){const u=this.ruler.getRules("");for(let t=0,o=u.length;t<o;t++)u[t](e)};Cu.prototype.State=Tt;function Le(e,u,t,o){this.src=e,this.md=u,this.env=t,this.tokens=o,this.bMarks=[],this.eMarks=[],this.tShift=[],this.sCount=[],this.bsCount=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.ddIndent=-1,this.listIndent=-1,this.parentType="root",this.level=0;const n=this.src;for(let r=0,c=0,i=0,s=0,f=n.length,h=!1;c<f;c++){const v=n.charCodeAt(c);if(!h)if(se(v)){i++,v===9?s+=4-s%4:s++;continue}else h=!0;(v===10||c===f-1)&&(v!==10&&c++,this.bMarks.push(r),this.eMarks.push(c),this.tShift.push(i),this.sCount.push(s),this.bsCount.push(0),h=!1,i=0,s=0,r=c+1)}this.bMarks.push(n.length),this.eMarks.push(n.length),this.tShift.push(0),this.sCount.push(0),this.bsCount.push(0),this.lineMax=this.bMarks.length-1}Le.prototype.push=function(e,u,t){const o=new Fe(e,u,t);return o.block=!0,t<0&&this.level--,o.level=this.level,t>0&&this.level++,this.tokens.push(o),o};Le.prototype.isEmpty=function(u){return this.bMarks[u]+this.tShift[u]>=this.eMarks[u]};Le.prototype.skipEmptyLines=function(u){for(let t=this.lineMax;u<t&&!(this.bMarks[u]+this.tShift[u]<this.eMarks[u]);u++);return u};Le.prototype.skipSpaces=function(u){for(let t=this.src.length;u<t;u++){const o=this.src.charCodeAt(u);if(!se(o))break}return u};Le.prototype.skipSpacesBack=function(u,t){if(u<=t)return u;for(;u>t;)if(!se(this.src.charCodeAt(--u)))return u+1;return u};Le.prototype.skipChars=function(u,t){for(let o=this.src.length;u<o&&this.src.charCodeAt(u)===t;u++);return u};Le.prototype.skipCharsBack=function(u,t,o){if(u<=o)return u;for(;u>o;)if(t!==this.src.charCodeAt(--u))return u+1;return u};Le.prototype.getLines=function(u,t,o,n){if(u>=t)return"";const r=new Array(t-u);for(let c=0,i=u;i<t;i++,c++){let s=0;const f=this.bMarks[i];let h=f,v;for(i+1<t||n?v=this.eMarks[i]+1:v=this.eMarks[i];h<v&&s<o;){const _=this.src.charCodeAt(h);if(se(_))_===9?s+=4-(s+this.bsCount[i])%4:s++;else if(h-f<this.tShift[i])s++;else break;h++}s>o?r[c]=new Array(s-o+1).join(" ")+this.src.slice(h,v):r[c]=this.src.slice(h,v)}return r.join("")};Le.prototype.Token=Fe;const zn=65536;function O0(e,u){const t=e.bMarks[u]+e.tShift[u],o=e.eMarks[u];return e.src.slice(t,o)}function Nu(e){const u=[],t=e.length;let o=0,n=e.charCodeAt(o),r=!1,c=0,i="";for(;o<t;)n===124&&(r?(i+=e.substring(c,o-1),c=o):(u.push(i+e.substring(c,o)),i="",c=o+1)),r=n===92,o++,n=e.charCodeAt(o);return u.push(i+e.substring(c)),u}function Bn(e,u,t,o){if(u+2>t)return!1;let n=u+1;if(e.sCount[n]<e.blkIndent||e.sCount[n]-e.blkIndent>=4)return!1;let r=e.bMarks[n]+e.tShift[n];if(r>=e.eMarks[n])return!1;const c=e.src.charCodeAt(r++);if(c!==124&&c!==45&&c!==58||r>=e.eMarks[n])return!1;const i=e.src.charCodeAt(r++);if(i!==124&&i!==45&&i!==58&&!se(i)||c===45&&se(i))return!1;for(;r<e.eMarks[n];){const w=e.src.charCodeAt(r);if(w!==124&&w!==45&&w!==58&&!se(w))return!1;r++}let s=O0(e,u+1),f=s.split("|");const h=[];for(let w=0;w<f.length;w++){const y=f[w].trim();if(!y){if(w===0||w===f.length-1)continue;return!1}if(!/^:?-+:?$/.test(y))return!1;y.charCodeAt(y.length-1)===58?h.push(y.charCodeAt(0)===58?"center":"right"):y.charCodeAt(0)===58?h.push("left"):h.push("")}if(s=O0(e,u).trim(),s.indexOf("|")===-1||e.sCount[u]-e.blkIndent>=4)return!1;f=Nu(s),f.length&&f[0]===""&&f.shift(),f.length&&f[f.length-1]===""&&f.pop();const v=f.length;if(v===0||v!==h.length)return!1;if(o)return!0;const _=e.parentType;e.parentType="table";const A=e.md.block.ruler.getRules("blockquote"),a=e.push("table_open","table",1),b=[u,0];a.map=b;const m=e.push("thead_open","thead",1);m.map=[u,u+1];const S=e.push("tr_open","tr",1);S.map=[u,u+1];for(let w=0;w<f.length;w++){const y=e.push("th_open","th",1);h[w]&&(y.attrs=[["style","text-align:"+h[w]]]);const k=e.push("inline","",0);k.content=f[w].trim(),k.children=[],e.push("th_close","th",-1)}e.push("tr_close","tr",-1),e.push("thead_close","thead",-1);let p,d=0;for(n=u+2;n<t&&!(e.sCount[n]<e.blkIndent);n++){let w=!1;for(let k=0,M=A.length;k<M;k++)if(A[k](e,n,t,!0)){w=!0;break}if(w||(s=O0(e,n).trim(),!s)||e.sCount[n]-e.blkIndent>=4||(f=Nu(s),f.length&&f[0]===""&&f.shift(),f.length&&f[f.length-1]===""&&f.pop(),d+=v-f.length,d>zn))break;if(n===u+2){const k=e.push("tbody_open","tbody",1);k.map=p=[u+2,0]}const y=e.push("tr_open","tr",1);y.map=[n,n+1];for(let k=0;k<v;k++){const M=e.push("td_open","td",1);h[k]&&(M.attrs=[["style","text-align:"+h[k]]]);const L=e.push("inline","",0);L.content=f[k]?f[k].trim():"",L.children=[],e.push("td_close","td",-1)}e.push("tr_close","tr",-1)}return p&&(e.push("tbody_close","tbody",-1),p[1]=n),e.push("table_close","table",-1),b[1]=n,e.parentType=_,e.line=n,!0}function Ln(e,u,t){if(e.sCount[u]-e.blkIndent<4)return!1;let o=u+1,n=o;for(;o<t;){if(e.isEmpty(o)){o++;continue}if(e.sCount[o]-e.blkIndent>=4){o++,n=o;continue}break}e.line=n;const r=e.push("code_block","code",0);return r.content=e.getLines(u,n,4+e.blkIndent,!1)+`
`,r.map=[u,e.line],!0}function qn(e,u,t,o){let n=e.bMarks[u]+e.tShift[u],r=e.eMarks[u];if(e.sCount[u]-e.blkIndent>=4||n+3>r)return!1;const c=e.src.charCodeAt(n);if(c!==126&&c!==96)return!1;let i=n;n=e.skipChars(n,c);let s=n-i;if(s<3)return!1;const f=e.src.slice(i,n),h=e.src.slice(n,r);if(c===96&&h.indexOf(String.fromCharCode(c))>=0)return!1;if(o)return!0;let v=u,_=!1;for(;v++,!(v>=t||(n=i=e.bMarks[v]+e.tShift[v],r=e.eMarks[v],n<r&&e.sCount[v]<e.blkIndent));)if(e.src.charCodeAt(n)===c&&!(e.sCount[v]-e.blkIndent>=4)&&(n=e.skipChars(n,c),!(n-i<s)&&(n=e.skipSpaces(n),!(n<r)))){_=!0;break}s=e.sCount[u],e.line=v+(_?1:0);const A=e.push("fence","code",0);return A.info=h,A.content=e.getLines(u+1,v,s,!0),A.markup=f,A.map=[u,e.line],!0}function In(e,u,t,o){let n=e.bMarks[u]+e.tShift[u],r=e.eMarks[u];const c=e.lineMax;if(e.sCount[u]-e.blkIndent>=4||e.src.charCodeAt(n)!==62)return!1;if(o)return!0;const i=[],s=[],f=[],h=[],v=e.md.block.ruler.getRules("blockquote"),_=e.parentType;e.parentType="blockquote";let A=!1,a;for(a=u;a<t;a++){const d=e.sCount[a]<e.blkIndent;if(n=e.bMarks[a]+e.tShift[a],r=e.eMarks[a],n>=r)break;if(e.src.charCodeAt(n++)===62&&!d){let y=e.sCount[a]+1,k,M;e.src.charCodeAt(n)===32?(n++,y++,M=!1,k=!0):e.src.charCodeAt(n)===9?(k=!0,(e.bsCount[a]+y)%4===3?(n++,y++,M=!1):M=!0):k=!1;let L=y;for(i.push(e.bMarks[a]),e.bMarks[a]=n;n<r;){const q=e.src.charCodeAt(n);if(se(q))q===9?L+=4-(L+e.bsCount[a]+(M?1:0))%4:L++;else break;n++}A=n>=r,s.push(e.bsCount[a]),e.bsCount[a]=e.sCount[a]+1+(k?1:0),f.push(e.sCount[a]),e.sCount[a]=L-y,h.push(e.tShift[a]),e.tShift[a]=n-e.bMarks[a];continue}if(A)break;let w=!1;for(let y=0,k=v.length;y<k;y++)if(v[y](e,a,t,!0)){w=!0;break}if(w){e.lineMax=a,e.blkIndent!==0&&(i.push(e.bMarks[a]),s.push(e.bsCount[a]),h.push(e.tShift[a]),f.push(e.sCount[a]),e.sCount[a]-=e.blkIndent);break}i.push(e.bMarks[a]),s.push(e.bsCount[a]),h.push(e.tShift[a]),f.push(e.sCount[a]),e.sCount[a]=-1}const b=e.blkIndent;e.blkIndent=0;const m=e.push("blockquote_open","blockquote",1);m.markup=">";const S=[u,0];m.map=S,e.md.block.tokenize(e,u,a);const p=e.push("blockquote_close","blockquote",-1);p.markup=">",e.lineMax=c,e.parentType=_,S[1]=e.line;for(let d=0;d<h.length;d++)e.bMarks[d+u]=i[d],e.tShift[d+u]=h[d],e.sCount[d+u]=f[d],e.bsCount[d+u]=s[d];return e.blkIndent=b,!0}function Pn(e,u,t,o){const n=e.eMarks[u];if(e.sCount[u]-e.blkIndent>=4)return!1;let r=e.bMarks[u]+e.tShift[u];const c=e.src.charCodeAt(r++);if(c!==42&&c!==45&&c!==95)return!1;let i=1;for(;r<n;){const f=e.src.charCodeAt(r++);if(f!==c&&!se(f))return!1;f===c&&i++}if(i<3)return!1;if(o)return!0;e.line=u+1;const s=e.push("hr","hr",0);return s.map=[u,e.line],s.markup=Array(i+1).join(String.fromCharCode(c)),!0}function Ou(e,u){const t=e.eMarks[u];let o=e.bMarks[u]+e.tShift[u];const n=e.src.charCodeAt(o++);if(n!==42&&n!==45&&n!==43)return-1;if(o<t){const r=e.src.charCodeAt(o);if(!se(r))return-1}return o}function Uu(e,u){const t=e.bMarks[u]+e.tShift[u],o=e.eMarks[u];let n=t;if(n+1>=o)return-1;let r=e.src.charCodeAt(n++);if(r<48||r>57)return-1;for(;;){if(n>=o)return-1;if(r=e.src.charCodeAt(n++),r>=48&&r<=57){if(n-t>=10)return-1;continue}if(r===41||r===46)break;return-1}return n<o&&(r=e.src.charCodeAt(n),!se(r))?-1:n}function Nn(e,u){const t=e.level+2;for(let o=u+2,n=e.tokens.length-2;o<n;o++)e.tokens[o].level===t&&e.tokens[o].type==="paragraph_open"&&(e.tokens[o+2].hidden=!0,e.tokens[o].hidden=!0,o+=2)}function On(e,u,t,o){let n,r,c,i,s=u,f=!0;if(e.sCount[s]-e.blkIndent>=4||e.listIndent>=0&&e.sCount[s]-e.listIndent>=4&&e.sCount[s]<e.blkIndent)return!1;let h=!1;o&&e.parentType==="paragraph"&&e.sCount[s]>=e.blkIndent&&(h=!0);let v,_,A;if((A=Uu(e,s))>=0){if(v=!0,c=e.bMarks[s]+e.tShift[s],_=Number(e.src.slice(c,A-1)),h&&_!==1)return!1}else if((A=Ou(e,s))>=0)v=!1;else return!1;if(h&&e.skipSpaces(A)>=e.eMarks[s])return!1;if(o)return!0;const a=e.src.charCodeAt(A-1),b=e.tokens.length;v?(i=e.push("ordered_list_open","ol",1),_!==1&&(i.attrs=[["start",_]])):i=e.push("bullet_list_open","ul",1);const m=[s,0];i.map=m,i.markup=String.fromCharCode(a);let S=!1;const p=e.md.block.ruler.getRules("list"),d=e.parentType;for(e.parentType="list";s<t;){r=A,n=e.eMarks[s];const w=e.sCount[s]+A-(e.bMarks[s]+e.tShift[s]);let y=w;for(;r<n;){const T=e.src.charCodeAt(r);if(T===9)y+=4-(y+e.bsCount[s])%4;else if(T===32)y++;else break;r++}const k=r;let M;k>=n?M=1:M=y-w,M>4&&(M=1);const L=w+M;i=e.push("list_item_open","li",1),i.markup=String.fromCharCode(a);const q=[s,0];i.map=q,v&&(i.info=e.src.slice(c,A-1));const D=e.tight,Z=e.tShift[s],G=e.sCount[s],Q=e.listIndent;if(e.listIndent=e.blkIndent,e.blkIndent=L,e.tight=!0,e.tShift[s]=k-e.bMarks[s],e.sCount[s]=y,k>=n&&e.isEmpty(s+1)?e.line=Math.min(e.line+2,t):e.md.block.tokenize(e,s,t,!0),(!e.tight||S)&&(f=!1),S=e.line-s>1&&e.isEmpty(e.line-1),e.blkIndent=e.listIndent,e.listIndent=Q,e.tShift[s]=Z,e.sCount[s]=G,e.tight=D,i=e.push("list_item_close","li",-1),i.markup=String.fromCharCode(a),s=e.line,q[1]=s,s>=t||e.sCount[s]<e.blkIndent||e.sCount[s]-e.blkIndent>=4)break;let X=!1;for(let T=0,N=p.length;T<N;T++)if(p[T](e,s,t,!0)){X=!0;break}if(X)break;if(v){if(A=Uu(e,s),A<0)break;c=e.bMarks[s]+e.tShift[s]}else if(A=Ou(e,s),A<0)break;if(a!==e.src.charCodeAt(A-1))break}return v?i=e.push("ordered_list_close","ol",-1):i=e.push("bullet_list_close","ul",-1),i.markup=String.fromCharCode(a),m[1]=s,e.line=s,e.parentType=d,f&&Nn(e,b),!0}function Un(e,u,t,o){let n=e.bMarks[u]+e.tShift[u],r=e.eMarks[u],c=u+1;if(e.sCount[u]-e.blkIndent>=4||e.src.charCodeAt(n)!==91)return!1;function i(p){const d=e.lineMax;if(p>=d||e.isEmpty(p))return null;let w=!1;if(e.sCount[p]-e.blkIndent>3&&(w=!0),e.sCount[p]<0&&(w=!0),!w){const M=e.md.block.ruler.getRules("reference"),L=e.parentType;e.parentType="reference";let q=!1;for(let D=0,Z=M.length;D<Z;D++)if(M[D](e,p,d,!0)){q=!0;break}if(e.parentType=L,q)return null}const y=e.bMarks[p]+e.tShift[p],k=e.eMarks[p];return e.src.slice(y,k+1)}let s=e.src.slice(n,r+1);r=s.length;let f=-1;for(n=1;n<r;n++){const p=s.charCodeAt(n);if(p===91)return!1;if(p===93){f=n;break}else if(p===10){const d=i(c);d!==null&&(s+=d,r=s.length,c++)}else if(p===92&&(n++,n<r&&s.charCodeAt(n)===10)){const d=i(c);d!==null&&(s+=d,r=s.length,c++)}}if(f<0||s.charCodeAt(f+1)!==58)return!1;for(n=f+2;n<r;n++){const p=s.charCodeAt(n);if(p===10){const d=i(c);d!==null&&(s+=d,r=s.length,c++)}else if(!se(p))break}const h=e.md.helpers.parseLinkDestination(s,n,r);if(!h.ok)return!1;const v=e.md.normalizeLink(h.str);if(!e.md.validateLink(v))return!1;n=h.pos;const _=n,A=c,a=n;for(;n<r;n++){const p=s.charCodeAt(n);if(p===10){const d=i(c);d!==null&&(s+=d,r=s.length,c++)}else if(!se(p))break}let b=e.md.helpers.parseLinkTitle(s,n,r);for(;b.can_continue;){const p=i(c);if(p===null)break;s+=p,n=r,r=s.length,c++,b=e.md.helpers.parseLinkTitle(s,n,r,b)}let m;for(n<r&&a!==n&&b.ok?(m=b.str,n=b.pos):(m="",n=_,c=A);n<r;){const p=s.charCodeAt(n);if(!se(p))break;n++}if(n<r&&s.charCodeAt(n)!==10&&m)for(m="",n=_,c=A;n<r;){const p=s.charCodeAt(n);if(!se(p))break;n++}if(n<r&&s.charCodeAt(n)!==10)return!1;const S=D0(s.slice(1,f));return S?(o||(typeof e.env.references>"u"&&(e.env.references={}),typeof e.env.references[S]>"u"&&(e.env.references[S]={title:m,href:v}),e.line=c),!0):!1}const jn=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],Hn="[a-zA-Z_:][a-zA-Z0-9:._-]*",Vn="[^\"'=<>`\\x00-\\x20]+",Gn="'[^']*'",Wn='"[^"]*"',Zn="(?:"+Vn+"|"+Gn+"|"+Wn+")",$n="(?:\\s+"+Hn+"(?:\\s*=\\s*"+Zn+")?)",zt="<[A-Za-z][A-Za-z0-9\\-]*"+$n+"*\\s*\\/?>",Bt="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",Xn="<!---?>|<!--(?:[^-]|-[^-]|--[^>])*-->",Jn="<[?][\\s\\S]*?[?]>",Kn="<![A-Za-z][^>]*>",Qn="<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",Yn=new RegExp("^(?:"+zt+"|"+Bt+"|"+Xn+"|"+Jn+"|"+Kn+"|"+Qn+")"),ei=new RegExp("^(?:"+zt+"|"+Bt+")"),Je=[[/^<(script|pre|style|textarea)(?=(\s|>|$))/i,/<\/(script|pre|style|textarea)>/i,!0],[/^<!--/,/-->/,!0],[/^<\?/,/\?>/,!0],[/^<![A-Z]/,/>/,!0],[/^<!\[CDATA\[/,/\]\]>/,!0],[new RegExp("^</?("+jn.join("|")+")(?=(\\s|/?>|$))","i"),/^$/,!0],[new RegExp(ei.source+"\\s*$"),/^$/,!1]];function ui(e,u,t,o){let n=e.bMarks[u]+e.tShift[u],r=e.eMarks[u];if(e.sCount[u]-e.blkIndent>=4||!e.md.options.html||e.src.charCodeAt(n)!==60)return!1;let c=e.src.slice(n,r),i=0;for(;i<Je.length&&!Je[i][0].test(c);i++);if(i===Je.length)return!1;if(o)return Je[i][2];let s=u+1;if(!Je[i][1].test(c)){for(;s<t&&!(e.sCount[s]<e.blkIndent);s++)if(n=e.bMarks[s]+e.tShift[s],r=e.eMarks[s],c=e.src.slice(n,r),Je[i][1].test(c)){c.length!==0&&s++;break}}e.line=s;const f=e.push("html_block","",0);return f.map=[u,s],f.content=e.getLines(u,s,e.blkIndent,!0),!0}function ti(e,u,t,o){let n=e.bMarks[u]+e.tShift[u],r=e.eMarks[u];if(e.sCount[u]-e.blkIndent>=4)return!1;let c=e.src.charCodeAt(n);if(c!==35||n>=r)return!1;let i=1;for(c=e.src.charCodeAt(++n);c===35&&n<r&&i<=6;)i++,c=e.src.charCodeAt(++n);if(i>6||n<r&&!se(c))return!1;if(o)return!0;r=e.skipSpacesBack(r,n);const s=e.skipCharsBack(r,35,n);s>n&&se(e.src.charCodeAt(s-1))&&(r=s),e.line=u+1;const f=e.push("heading_open","h"+String(i),1);f.markup="########".slice(0,i),f.map=[u,e.line];const h=e.push("inline","",0);h.content=e.src.slice(n,r).trim(),h.map=[u,e.line],h.children=[];const v=e.push("heading_close","h"+String(i),-1);return v.markup="########".slice(0,i),!0}function ri(e,u,t){const o=e.md.block.ruler.getRules("paragraph");if(e.sCount[u]-e.blkIndent>=4)return!1;const n=e.parentType;e.parentType="paragraph";let r=0,c,i=u+1;for(;i<t&&!e.isEmpty(i);i++){if(e.sCount[i]-e.blkIndent>3)continue;if(e.sCount[i]>=e.blkIndent){let A=e.bMarks[i]+e.tShift[i];const a=e.eMarks[i];if(A<a&&(c=e.src.charCodeAt(A),(c===45||c===61)&&(A=e.skipChars(A,c),A=e.skipSpaces(A),A>=a))){r=c===61?1:2;break}}if(e.sCount[i]<0)continue;let _=!1;for(let A=0,a=o.length;A<a;A++)if(o[A](e,i,t,!0)){_=!0;break}if(_)break}if(!r)return!1;const s=e.getLines(u,i,e.blkIndent,!1).trim();e.line=i+1;const f=e.push("heading_open","h"+String(r),1);f.markup=String.fromCharCode(c),f.map=[u,e.line];const h=e.push("inline","",0);h.content=s,h.map=[u,e.line-1],h.children=[];const v=e.push("heading_close","h"+String(r),-1);return v.markup=String.fromCharCode(c),e.parentType=n,!0}function ni(e,u,t){const o=e.md.block.ruler.getRules("paragraph"),n=e.parentType;let r=u+1;for(e.parentType="paragraph";r<t&&!e.isEmpty(r);r++){if(e.sCount[r]-e.blkIndent>3||e.sCount[r]<0)continue;let f=!1;for(let h=0,v=o.length;h<v;h++)if(o[h](e,r,t,!0)){f=!0;break}if(f)break}const c=e.getLines(u,r,e.blkIndent,!1).trim();e.line=r;const i=e.push("paragraph_open","p",1);i.map=[u,e.line];const s=e.push("inline","",0);return s.content=c,s.map=[u,e.line],s.children=[],e.push("paragraph_close","p",-1),e.parentType=n,!0}const g0=[["table",Bn,["paragraph","reference"]],["code",Ln],["fence",qn,["paragraph","reference","blockquote","list"]],["blockquote",In,["paragraph","reference","blockquote","list"]],["hr",Pn,["paragraph","reference","blockquote","list"]],["list",On,["paragraph","reference","blockquote"]],["reference",Un],["html_block",ui,["paragraph","reference","blockquote"]],["heading",ti,["paragraph","reference","blockquote"]],["lheading",ri],["paragraph",ni]];function E0(){this.ruler=new xe;for(let e=0;e<g0.length;e++)this.ruler.push(g0[e][0],g0[e][1],{alt:(g0[e][2]||[]).slice()})}E0.prototype.tokenize=function(e,u,t){const o=this.ruler.getRules(""),n=o.length,r=e.md.options.maxNesting;let c=u,i=!1;for(;c<t&&(e.line=c=e.skipEmptyLines(c),!(c>=t||e.sCount[c]<e.blkIndent));){if(e.level>=r){e.line=t;break}const s=e.line;let f=!1;for(let h=0;h<n;h++)if(f=o[h](e,c,t,!1),f){if(s>=e.line)throw new Error("block rule didn't increment state.line");break}if(!f)throw new Error("none of the block rules matched");e.tight=!i,e.isEmpty(e.line-1)&&(i=!0),c=e.line,c<t&&e.isEmpty(c)&&(i=!0,c++,e.line=c)}};E0.prototype.parse=function(e,u,t,o){if(!e)return;const n=new this.State(e,u,t,o);this.tokenize(n,n.line,n.lineMax)};E0.prototype.State=Le;function f0(e,u,t,o){this.src=e,this.env=t,this.md=u,this.tokens=o,this.tokens_meta=Array(o.length),this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache={},this.delimiters=[],this._prev_delimiters=[],this.backticks={},this.backticksScanned=!1,this.linkLevel=0}f0.prototype.pushPending=function(){const e=new Fe("text","",0);return e.content=this.pending,e.level=this.pendingLevel,this.tokens.push(e),this.pending="",e};f0.prototype.push=function(e,u,t){this.pending&&this.pushPending();const o=new Fe(e,u,t);let n=null;return t<0&&(this.level--,this.delimiters=this._prev_delimiters.pop()),o.level=this.level,t>0&&(this.level++,this._prev_delimiters.push(this.delimiters),this.delimiters=[],n={delimiters:this.delimiters}),this.pendingLevel=this.level,this.tokens.push(o),this.tokens_meta.push(n),o};f0.prototype.scanDelims=function(e,u){const t=this.posMax,o=this.src.charCodeAt(e),n=e>0?this.src.charCodeAt(e-1):32;let r=e;for(;r<t&&this.src.charCodeAt(r)===o;)r++;const c=r-e,i=r<t?this.src.charCodeAt(r):32,s=o0(n)||a0(String.fromCharCode(n)),f=o0(i)||a0(String.fromCharCode(i)),h=i0(n),v=i0(i),_=!v&&(!f||h||s),A=!h&&(!s||v||f);return{can_open:_&&(u||!A||s),can_close:A&&(u||!_||f),length:c}};f0.prototype.Token=Fe;function ii(e){switch(e){case 10:case 33:case 35:case 36:case 37:case 38:case 42:case 43:case 45:case 58:case 60:case 61:case 62:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 125:case 126:return!0;default:return!1}}function ai(e,u){let t=e.pos;for(;t<e.posMax&&!ii(e.src.charCodeAt(t));)t++;return t===e.pos?!1:(u||(e.pending+=e.src.slice(e.pos,t)),e.pos=t,!0)}const oi=/(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i;function si(e,u){if(!e.md.options.linkify||e.linkLevel>0)return!1;const t=e.pos,o=e.posMax;if(t+3>o||e.src.charCodeAt(t)!==58||e.src.charCodeAt(t+1)!==47||e.src.charCodeAt(t+2)!==47)return!1;const n=e.pending.match(oi);if(!n)return!1;const r=n[1],c=e.md.linkify.matchAtStart(e.src.slice(t-r.length));if(!c)return!1;let i=c.url;if(i.length<=r.length)return!1;i=i.replace(/\*+$/,"");const s=e.md.normalizeLink(i);if(!e.md.validateLink(s))return!1;if(!u){e.pending=e.pending.slice(0,-r.length);const f=e.push("link_open","a",1);f.attrs=[["href",s]],f.markup="linkify",f.info="auto";const h=e.push("text","",0);h.content=e.md.normalizeLinkText(i);const v=e.push("link_close","a",-1);v.markup="linkify",v.info="auto"}return e.pos+=i.length-r.length,!0}function ci(e,u){let t=e.pos;if(e.src.charCodeAt(t)!==10)return!1;const o=e.pending.length-1,n=e.posMax;if(!u)if(o>=0&&e.pending.charCodeAt(o)===32)if(o>=1&&e.pending.charCodeAt(o-1)===32){let r=o-1;for(;r>=1&&e.pending.charCodeAt(r-1)===32;)r--;e.pending=e.pending.slice(0,r),e.push("hardbreak","br",0)}else e.pending=e.pending.slice(0,-1),e.push("softbreak","br",0);else e.push("softbreak","br",0);for(t++;t<n&&se(e.src.charCodeAt(t));)t++;return e.pos=t,!0}const Au=[];for(let e=0;e<256;e++)Au.push(0);"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach(function(e){Au[e.charCodeAt(0)]=1});function li(e,u){let t=e.pos;const o=e.posMax;if(e.src.charCodeAt(t)!==92||(t++,t>=o))return!1;let n=e.src.charCodeAt(t);if(n===10){for(u||e.push("hardbreak","br",0),t++;t<o&&(n=e.src.charCodeAt(t),!!se(n));)t++;return e.pos=t,!0}let r=e.src[t];if(n>=55296&&n<=56319&&t+1<o){const i=e.src.charCodeAt(t+1);i>=56320&&i<=57343&&(r+=e.src[t+1],t++)}const c="\\"+r;if(!u){const i=e.push("text_special","",0);n<256&&Au[n]!==0?i.content=r:i.content=c,i.markup=c,i.info="escape"}return e.pos=t+1,!0}function fi(e,u){let t=e.pos;if(e.src.charCodeAt(t)!==96)return!1;const n=t;t++;const r=e.posMax;for(;t<r&&e.src.charCodeAt(t)===96;)t++;const c=e.src.slice(n,t),i=c.length;if(e.backticksScanned&&(e.backticks[i]||0)<=n)return u||(e.pending+=c),e.pos+=i,!0;let s=t,f;for(;(f=e.src.indexOf("`",s))!==-1;){for(s=f+1;s<r&&e.src.charCodeAt(s)===96;)s++;const h=s-f;if(h===i){if(!u){const v=e.push("code_inline","code",0);v.markup=c,v.content=e.src.slice(t,f).replace(/\n/g," ").replace(/^ (.+) $/,"$1")}return e.pos=s,!0}e.backticks[h]=f}return e.backticksScanned=!0,u||(e.pending+=c),e.pos+=i,!0}function di(e,u){const t=e.pos,o=e.src.charCodeAt(t);if(u||o!==126)return!1;const n=e.scanDelims(e.pos,!0);let r=n.length;const c=String.fromCharCode(o);if(r<2)return!1;let i;r%2&&(i=e.push("text","",0),i.content=c,r--);for(let s=0;s<r;s+=2)i=e.push("text","",0),i.content=c+c,e.delimiters.push({marker:o,length:0,token:e.tokens.length-1,end:-1,open:n.can_open,close:n.can_close});return e.pos+=n.length,!0}function ju(e,u){let t;const o=[],n=u.length;for(let r=0;r<n;r++){const c=u[r];if(c.marker!==126||c.end===-1)continue;const i=u[c.end];t=e.tokens[c.token],t.type="s_open",t.tag="s",t.nesting=1,t.markup="~~",t.content="",t=e.tokens[i.token],t.type="s_close",t.tag="s",t.nesting=-1,t.markup="~~",t.content="",e.tokens[i.token-1].type==="text"&&e.tokens[i.token-1].content==="~"&&o.push(i.token-1)}for(;o.length;){const r=o.pop();let c=r+1;for(;c<e.tokens.length&&e.tokens[c].type==="s_close";)c++;c--,r!==c&&(t=e.tokens[c],e.tokens[c]=e.tokens[r],e.tokens[r]=t)}}function hi(e){const u=e.tokens_meta,t=e.tokens_meta.length;ju(e,e.delimiters);for(let o=0;o<t;o++)u[o]&&u[o].delimiters&&ju(e,u[o].delimiters)}const Lt={tokenize:di,postProcess:hi};function pi(e,u){const t=e.pos,o=e.src.charCodeAt(t);if(u||o!==95&&o!==42)return!1;const n=e.scanDelims(e.pos,o===42);for(let r=0;r<n.length;r++){const c=e.push("text","",0);c.content=String.fromCharCode(o),e.delimiters.push({marker:o,length:n.length,token:e.tokens.length-1,end:-1,open:n.can_open,close:n.can_close})}return e.pos+=n.length,!0}function Hu(e,u){const t=u.length;for(let o=t-1;o>=0;o--){const n=u[o];if(n.marker!==95&&n.marker!==42||n.end===-1)continue;const r=u[n.end],c=o>0&&u[o-1].end===n.end+1&&u[o-1].marker===n.marker&&u[o-1].token===n.token-1&&u[n.end+1].token===r.token+1,i=String.fromCharCode(n.marker),s=e.tokens[n.token];s.type=c?"strong_open":"em_open",s.tag=c?"strong":"em",s.nesting=1,s.markup=c?i+i:i,s.content="";const f=e.tokens[r.token];f.type=c?"strong_close":"em_close",f.tag=c?"strong":"em",f.nesting=-1,f.markup=c?i+i:i,f.content="",c&&(e.tokens[u[o-1].token].content="",e.tokens[u[n.end+1].token].content="",o--)}}function mi(e){const u=e.tokens_meta,t=e.tokens_meta.length;Hu(e,e.delimiters);for(let o=0;o<t;o++)u[o]&&u[o].delimiters&&Hu(e,u[o].delimiters)}const qt={tokenize:pi,postProcess:mi};function bi(e,u){let t,o,n,r,c="",i="",s=e.pos,f=!0;if(e.src.charCodeAt(e.pos)!==91)return!1;const h=e.pos,v=e.posMax,_=e.pos+1,A=e.md.helpers.parseLinkLabel(e,e.pos,!0);if(A<0)return!1;let a=A+1;if(a<v&&e.src.charCodeAt(a)===40){for(f=!1,a++;a<v&&(t=e.src.charCodeAt(a),!(!se(t)&&t!==10));a++);if(a>=v)return!1;if(s=a,n=e.md.helpers.parseLinkDestination(e.src,a,e.posMax),n.ok){for(c=e.md.normalizeLink(n.str),e.md.validateLink(c)?a=n.pos:c="",s=a;a<v&&(t=e.src.charCodeAt(a),!(!se(t)&&t!==10));a++);if(n=e.md.helpers.parseLinkTitle(e.src,a,e.posMax),a<v&&s!==a&&n.ok)for(i=n.str,a=n.pos;a<v&&(t=e.src.charCodeAt(a),!(!se(t)&&t!==10));a++);}(a>=v||e.src.charCodeAt(a)!==41)&&(f=!0),a++}if(f){if(typeof e.env.references>"u")return!1;if(a<v&&e.src.charCodeAt(a)===91?(s=a+1,a=e.md.helpers.parseLinkLabel(e,a),a>=0?o=e.src.slice(s,a++):a=A+1):a=A+1,o||(o=e.src.slice(_,A)),r=e.env.references[D0(o)],!r)return e.pos=h,!1;c=r.href,i=r.title}if(!u){e.pos=_,e.posMax=A;const b=e.push("link_open","a",1),m=[["href",c]];b.attrs=m,i&&m.push(["title",i]),e.linkLevel++,e.md.inline.tokenize(e),e.linkLevel--,e.push("link_close","a",-1)}return e.pos=a,e.posMax=v,!0}function vi(e,u){let t,o,n,r,c,i,s,f,h="";const v=e.pos,_=e.posMax;if(e.src.charCodeAt(e.pos)!==33||e.src.charCodeAt(e.pos+1)!==91)return!1;const A=e.pos+2,a=e.md.helpers.parseLinkLabel(e,e.pos+1,!1);if(a<0)return!1;if(r=a+1,r<_&&e.src.charCodeAt(r)===40){for(r++;r<_&&(t=e.src.charCodeAt(r),!(!se(t)&&t!==10));r++);if(r>=_)return!1;for(f=r,i=e.md.helpers.parseLinkDestination(e.src,r,e.posMax),i.ok&&(h=e.md.normalizeLink(i.str),e.md.validateLink(h)?r=i.pos:h=""),f=r;r<_&&(t=e.src.charCodeAt(r),!(!se(t)&&t!==10));r++);if(i=e.md.helpers.parseLinkTitle(e.src,r,e.posMax),r<_&&f!==r&&i.ok)for(s=i.str,r=i.pos;r<_&&(t=e.src.charCodeAt(r),!(!se(t)&&t!==10));r++);else s="";if(r>=_||e.src.charCodeAt(r)!==41)return e.pos=v,!1;r++}else{if(typeof e.env.references>"u")return!1;if(r<_&&e.src.charCodeAt(r)===91?(f=r+1,r=e.md.helpers.parseLinkLabel(e,r),r>=0?n=e.src.slice(f,r++):r=a+1):r=a+1,n||(n=e.src.slice(A,a)),c=e.env.references[D0(n)],!c)return e.pos=v,!1;h=c.href,s=c.title}if(!u){o=e.src.slice(A,a);const b=[];e.md.inline.parse(o,e.md,e.env,b);const m=e.push("image","img",0),S=[["src",h],["alt",""]];m.attrs=S,m.children=b,m.content=o,s&&S.push(["title",s])}return e.pos=r,e.posMax=_,!0}const gi=/^([a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/,xi=/^([a-zA-Z][a-zA-Z0-9+.-]{1,31}):([^<>\x00-\x20]*)$/;function yi(e,u){let t=e.pos;if(e.src.charCodeAt(t)!==60)return!1;const o=e.pos,n=e.posMax;for(;;){if(++t>=n)return!1;const c=e.src.charCodeAt(t);if(c===60)return!1;if(c===62)break}const r=e.src.slice(o+1,t);if(xi.test(r)){const c=e.md.normalizeLink(r);if(!e.md.validateLink(c))return!1;if(!u){const i=e.push("link_open","a",1);i.attrs=[["href",c]],i.markup="autolink",i.info="auto";const s=e.push("text","",0);s.content=e.md.normalizeLinkText(r);const f=e.push("link_close","a",-1);f.markup="autolink",f.info="auto"}return e.pos+=r.length+2,!0}if(gi.test(r)){const c=e.md.normalizeLink("mailto:"+r);if(!e.md.validateLink(c))return!1;if(!u){const i=e.push("link_open","a",1);i.attrs=[["href",c]],i.markup="autolink",i.info="auto";const s=e.push("text","",0);s.content=e.md.normalizeLinkText(r);const f=e.push("link_close","a",-1);f.markup="autolink",f.info="auto"}return e.pos+=r.length+2,!0}return!1}function ki(e){return/^<a[>\s]/i.test(e)}function wi(e){return/^<\/a\s*>/i.test(e)}function _i(e){const u=e|32;return u>=97&&u<=122}function Ci(e,u){if(!e.md.options.html)return!1;const t=e.posMax,o=e.pos;if(e.src.charCodeAt(o)!==60||o+2>=t)return!1;const n=e.src.charCodeAt(o+1);if(n!==33&&n!==63&&n!==47&&!_i(n))return!1;const r=e.src.slice(o).match(Yn);if(!r)return!1;if(!u){const c=e.push("html_inline","",0);c.content=r[0],ki(c.content)&&e.linkLevel++,wi(c.content)&&e.linkLevel--}return e.pos+=r[0].length,!0}const Ai=/^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,Di=/^&([a-z][a-z0-9]{1,31});/i;function Ei(e,u){const t=e.pos,o=e.posMax;if(e.src.charCodeAt(t)!==38||t+1>=o)return!1;if(e.src.charCodeAt(t+1)===35){const r=e.src.slice(t).match(Ai);if(r){if(!u){const c=r[1][0].toLowerCase()==="x"?parseInt(r[1].slice(1),16):parseInt(r[1],10),i=e.push("text_special","",0);i.content=_u(c)?_0(c):_0(65533),i.markup=r[0],i.info="entity"}return e.pos+=r[0].length,!0}}else{const r=e.src.slice(t).match(Di);if(r){const c=St(r[0]);if(c!==r[0]){if(!u){const i=e.push("text_special","",0);i.content=c,i.markup=r[0],i.info="entity"}return e.pos+=r[0].length,!0}}}return!1}function Vu(e){const u={},t=e.length;if(!t)return;let o=0,n=-2;const r=[];for(let c=0;c<t;c++){const i=e[c];if(r.push(0),(e[o].marker!==i.marker||n!==i.token-1)&&(o=c),n=i.token,i.length=i.length||0,!i.close)continue;u.hasOwnProperty(i.marker)||(u[i.marker]=[-1,-1,-1,-1,-1,-1]);const s=u[i.marker][(i.open?3:0)+i.length%3];let f=o-r[o]-1,h=f;for(;f>s;f-=r[f]+1){const v=e[f];if(v.marker===i.marker&&v.open&&v.end<0){let _=!1;if((v.close||i.open)&&(v.length+i.length)%3===0&&(v.length%3!==0||i.length%3!==0)&&(_=!0),!_){const A=f>0&&!e[f-1].open?r[f-1]+1:0;r[c]=c-f+A,r[f]=A,i.open=!1,v.end=c,v.close=!1,h=-1,n=-2;break}}}h!==-1&&(u[i.marker][(i.open?3:0)+(i.length||0)%3]=h)}}function Si(e){const u=e.tokens_meta,t=e.tokens_meta.length;Vu(e.delimiters);for(let o=0;o<t;o++)u[o]&&u[o].delimiters&&Vu(u[o].delimiters)}function Fi(e){let u,t,o=0;const n=e.tokens,r=e.tokens.length;for(u=t=0;u<r;u++)n[u].nesting<0&&o--,n[u].level=o,n[u].nesting>0&&o++,n[u].type==="text"&&u+1<r&&n[u+1].type==="text"?n[u+1].content=n[u].content+n[u+1].content:(u!==t&&(n[t]=n[u]),t++);u!==t&&(n.length=t)}const U0=[["text",ai],["linkify",si],["newline",ci],["escape",li],["backticks",fi],["strikethrough",Lt.tokenize],["emphasis",qt.tokenize],["link",bi],["image",vi],["autolink",yi],["html_inline",Ci],["entity",Ei]],j0=[["balance_pairs",Si],["strikethrough",Lt.postProcess],["emphasis",qt.postProcess],["fragments_join",Fi]];function d0(){this.ruler=new xe;for(let e=0;e<U0.length;e++)this.ruler.push(U0[e][0],U0[e][1]);this.ruler2=new xe;for(let e=0;e<j0.length;e++)this.ruler2.push(j0[e][0],j0[e][1])}d0.prototype.skipToken=function(e){const u=e.pos,t=this.ruler.getRules(""),o=t.length,n=e.md.options.maxNesting,r=e.cache;if(typeof r[u]<"u"){e.pos=r[u];return}let c=!1;if(e.level<n){for(let i=0;i<o;i++)if(e.level++,c=t[i](e,!0),e.level--,c){if(u>=e.pos)throw new Error("inline rule didn't increment state.pos");break}}else e.pos=e.posMax;c||e.pos++,r[u]=e.pos};d0.prototype.tokenize=function(e){const u=this.ruler.getRules(""),t=u.length,o=e.posMax,n=e.md.options.maxNesting;for(;e.pos<o;){const r=e.pos;let c=!1;if(e.level<n){for(let i=0;i<t;i++)if(c=u[i](e,!1),c){if(r>=e.pos)throw new Error("inline rule didn't increment state.pos");break}}if(c){if(e.pos>=o)break;continue}e.pending+=e.src[e.pos++]}e.pending&&e.pushPending()};d0.prototype.parse=function(e,u,t,o){const n=new this.State(e,u,t,o);this.tokenize(n);const r=this.ruler2.getRules(""),c=r.length;for(let i=0;i<c;i++)r[i](n)};d0.prototype.State=f0;function Ri(e){const u={};e=e||{},u.src_Any=_t.source,u.src_Cc=Ct.source,u.src_Z=Dt.source,u.src_P=ku.source,u.src_ZPCc=[u.src_Z,u.src_P,u.src_Cc].join("|"),u.src_ZCc=[u.src_Z,u.src_Cc].join("|");const t="[><｜]";return u.src_pseudo_letter="(?:(?!"+t+"|"+u.src_ZPCc+")"+u.src_Any+")",u.src_ip4="(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)",u.src_auth="(?:(?:(?!"+u.src_ZCc+"|[@/\\[\\]()]).)+@)?",u.src_port="(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?",u.src_host_terminator="(?=$|"+t+"|"+u.src_ZPCc+")(?!"+(e["---"]?"-(?!--)|":"-|")+"_|:\\d|\\.-|\\.(?!$|"+u.src_ZPCc+"))",u.src_path="(?:[/?#](?:(?!"+u.src_ZCc+"|"+t+`|[()[\\]{}.,"'?!\\-;]).|\\[(?:(?!`+u.src_ZCc+"|\\]).)*\\]|\\((?:(?!"+u.src_ZCc+"|[)]).)*\\)|\\{(?:(?!"+u.src_ZCc+'|[}]).)*\\}|\\"(?:(?!'+u.src_ZCc+`|["]).)+\\"|\\'(?:(?!`+u.src_ZCc+"|[']).)+\\'|\\'(?="+u.src_pseudo_letter+"|[-])|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!"+u.src_ZCc+"|[.]|$)|"+(e["---"]?"\\-(?!--(?:[^-]|$))(?:-*)|":"\\-+|")+",(?!"+u.src_ZCc+"|$)|;(?!"+u.src_ZCc+"|$)|\\!+(?!"+u.src_ZCc+"|[!]|$)|\\?(?!"+u.src_ZCc+"|[?]|$))+|\\/)?",u.src_email_name='[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*',u.src_xn="xn--[a-z0-9\\-]{1,59}",u.src_domain_root="(?:"+u.src_xn+"|"+u.src_pseudo_letter+"{1,63})",u.src_domain="(?:"+u.src_xn+"|(?:"+u.src_pseudo_letter+")|(?:"+u.src_pseudo_letter+"(?:-|"+u.src_pseudo_letter+"){0,61}"+u.src_pseudo_letter+"))",u.src_host="(?:(?:(?:(?:"+u.src_domain+")\\.)*"+u.src_domain+"))",u.tpl_host_fuzzy="(?:"+u.src_ip4+"|(?:(?:(?:"+u.src_domain+")\\.)+(?:%TLDS%)))",u.tpl_host_no_ip_fuzzy="(?:(?:(?:"+u.src_domain+")\\.)+(?:%TLDS%))",u.src_host_strict=u.src_host+u.src_host_terminator,u.tpl_host_fuzzy_strict=u.tpl_host_fuzzy+u.src_host_terminator,u.src_host_port_strict=u.src_host+u.src_port+u.src_host_terminator,u.tpl_host_port_fuzzy_strict=u.tpl_host_fuzzy+u.src_port+u.src_host_terminator,u.tpl_host_port_no_ip_fuzzy_strict=u.tpl_host_no_ip_fuzzy+u.src_port+u.src_host_terminator,u.tpl_host_fuzzy_test="localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:"+u.src_ZPCc+"|>|$))",u.tpl_email_fuzzy="(^|"+t+'|"|\\(|'+u.src_ZCc+")("+u.src_email_name+"@"+u.tpl_host_fuzzy_strict+")",u.tpl_link_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+u.src_ZPCc+"))((?![$+<=>^`|｜])"+u.tpl_host_port_fuzzy_strict+u.src_path+")",u.tpl_link_no_ip_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+u.src_ZPCc+"))((?![$+<=>^`|｜])"+u.tpl_host_port_no_ip_fuzzy_strict+u.src_path+")",u}function vu(e){return Array.prototype.slice.call(arguments,1).forEach(function(t){t&&Object.keys(t).forEach(function(o){e[o]=t[o]})}),e}function S0(e){return Object.prototype.toString.call(e)}function Ti(e){return S0(e)==="[object String]"}function Mi(e){return S0(e)==="[object Object]"}function zi(e){return S0(e)==="[object RegExp]"}function Gu(e){return S0(e)==="[object Function]"}function Bi(e){return e.replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}const It={fuzzyLink:!0,fuzzyEmail:!0,fuzzyIP:!1};function Li(e){return Object.keys(e||{}).reduce(function(u,t){return u||It.hasOwnProperty(t)},!1)}const qi={"http:":{validate:function(e,u,t){const o=e.slice(u);return t.re.http||(t.re.http=new RegExp("^\\/\\/"+t.re.src_auth+t.re.src_host_port_strict+t.re.src_path,"i")),t.re.http.test(o)?o.match(t.re.http)[0].length:0}},"https:":"http:","ftp:":"http:","//":{validate:function(e,u,t){const o=e.slice(u);return t.re.no_http||(t.re.no_http=new RegExp("^"+t.re.src_auth+"(?:localhost|(?:(?:"+t.re.src_domain+")\\.)+"+t.re.src_domain_root+")"+t.re.src_port+t.re.src_host_terminator+t.re.src_path,"i")),t.re.no_http.test(o)?u>=3&&e[u-3]===":"||u>=3&&e[u-3]==="/"?0:o.match(t.re.no_http)[0].length:0}},"mailto:":{validate:function(e,u,t){const o=e.slice(u);return t.re.mailto||(t.re.mailto=new RegExp("^"+t.re.src_email_name+"@"+t.re.src_host_strict,"i")),t.re.mailto.test(o)?o.match(t.re.mailto)[0].length:0}}},Ii="a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]",Pi="biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф".split("|");function Ni(e){e.__index__=-1,e.__text_cache__=""}function Oi(e){return function(u,t){const o=u.slice(t);return e.test(o)?o.match(e)[0].length:0}}function Wu(){return function(e,u){u.normalize(e)}}function C0(e){const u=e.re=Ri(e.__opts__),t=e.__tlds__.slice();e.onCompile(),e.__tlds_replaced__||t.push(Ii),t.push(u.src_xn),u.src_tlds=t.join("|");function o(i){return i.replace("%TLDS%",u.src_tlds)}u.email_fuzzy=RegExp(o(u.tpl_email_fuzzy),"i"),u.link_fuzzy=RegExp(o(u.tpl_link_fuzzy),"i"),u.link_no_ip_fuzzy=RegExp(o(u.tpl_link_no_ip_fuzzy),"i"),u.host_fuzzy_test=RegExp(o(u.tpl_host_fuzzy_test),"i");const n=[];e.__compiled__={};function r(i,s){throw new Error('(LinkifyIt) Invalid schema "'+i+'": '+s)}Object.keys(e.__schemas__).forEach(function(i){const s=e.__schemas__[i];if(s===null)return;const f={validate:null,link:null};if(e.__compiled__[i]=f,Mi(s)){zi(s.validate)?f.validate=Oi(s.validate):Gu(s.validate)?f.validate=s.validate:r(i,s),Gu(s.normalize)?f.normalize=s.normalize:s.normalize?r(i,s):f.normalize=Wu();return}if(Ti(s)){n.push(i);return}r(i,s)}),n.forEach(function(i){e.__compiled__[e.__schemas__[i]]&&(e.__compiled__[i].validate=e.__compiled__[e.__schemas__[i]].validate,e.__compiled__[i].normalize=e.__compiled__[e.__schemas__[i]].normalize)}),e.__compiled__[""]={validate:null,normalize:Wu()};const c=Object.keys(e.__compiled__).filter(function(i){return i.length>0&&e.__compiled__[i]}).map(Bi).join("|");e.re.schema_test=RegExp("(^|(?!_)(?:[><｜]|"+u.src_ZPCc+"))("+c+")","i"),e.re.schema_search=RegExp("(^|(?!_)(?:[><｜]|"+u.src_ZPCc+"))("+c+")","ig"),e.re.schema_at_start=RegExp("^"+e.re.schema_search.source,"i"),e.re.pretest=RegExp("("+e.re.schema_test.source+")|("+e.re.host_fuzzy_test.source+")|@","i"),Ni(e)}function Ui(e,u){const t=e.__index__,o=e.__last_index__,n=e.__text_cache__.slice(t,o);this.schema=e.__schema__.toLowerCase(),this.index=t+u,this.lastIndex=o+u,this.raw=n,this.text=n,this.url=n}function gu(e,u){const t=new Ui(e,u);return e.__compiled__[t.schema].normalize(t,e),t}function ke(e,u){if(!(this instanceof ke))return new ke(e,u);u||Li(e)&&(u=e,e={}),this.__opts__=vu({},It,u),this.__index__=-1,this.__last_index__=-1,this.__schema__="",this.__text_cache__="",this.__schemas__=vu({},qi,e),this.__compiled__={},this.__tlds__=Pi,this.__tlds_replaced__=!1,this.re={},C0(this)}ke.prototype.add=function(u,t){return this.__schemas__[u]=t,C0(this),this};ke.prototype.set=function(u){return this.__opts__=vu(this.__opts__,u),this};ke.prototype.test=function(u){if(this.__text_cache__=u,this.__index__=-1,!u.length)return!1;let t,o,n,r,c,i,s,f,h;if(this.re.schema_test.test(u)){for(s=this.re.schema_search,s.lastIndex=0;(t=s.exec(u))!==null;)if(r=this.testSchemaAt(u,t[2],s.lastIndex),r){this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+r;break}}return this.__opts__.fuzzyLink&&this.__compiled__["http:"]&&(f=u.search(this.re.host_fuzzy_test),f>=0&&(this.__index__<0||f<this.__index__)&&(o=u.match(this.__opts__.fuzzyIP?this.re.link_fuzzy:this.re.link_no_ip_fuzzy))!==null&&(c=o.index+o[1].length,(this.__index__<0||c<this.__index__)&&(this.__schema__="",this.__index__=c,this.__last_index__=o.index+o[0].length))),this.__opts__.fuzzyEmail&&this.__compiled__["mailto:"]&&(h=u.indexOf("@"),h>=0&&(n=u.match(this.re.email_fuzzy))!==null&&(c=n.index+n[1].length,i=n.index+n[0].length,(this.__index__<0||c<this.__index__||c===this.__index__&&i>this.__last_index__)&&(this.__schema__="mailto:",this.__index__=c,this.__last_index__=i))),this.__index__>=0};ke.prototype.pretest=function(u){return this.re.pretest.test(u)};ke.prototype.testSchemaAt=function(u,t,o){return this.__compiled__[t.toLowerCase()]?this.__compiled__[t.toLowerCase()].validate(u,o,this):0};ke.prototype.match=function(u){const t=[];let o=0;this.__index__>=0&&this.__text_cache__===u&&(t.push(gu(this,o)),o=this.__last_index__);let n=o?u.slice(o):u;for(;this.test(n);)t.push(gu(this,o)),n=n.slice(this.__last_index__),o+=this.__last_index__;return t.length?t:null};ke.prototype.matchAtStart=function(u){if(this.__text_cache__=u,this.__index__=-1,!u.length)return null;const t=this.re.schema_at_start.exec(u);if(!t)return null;const o=this.testSchemaAt(u,t[2],t[0].length);return o?(this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+o,gu(this,0)):null};ke.prototype.tlds=function(u,t){return u=Array.isArray(u)?u:[u],t?(this.__tlds__=this.__tlds__.concat(u).sort().filter(function(o,n,r){return o!==r[n-1]}).reverse(),C0(this),this):(this.__tlds__=u.slice(),this.__tlds_replaced__=!0,C0(this),this)};ke.prototype.normalize=function(u){u.schema||(u.url="http://"+u.url),u.schema==="mailto:"&&!/^mailto:/i.test(u.url)&&(u.url="mailto:"+u.url)};ke.prototype.onCompile=function(){};const Qe=2147483647,Me=36,Du=1,s0=26,ji=38,Hi=700,Pt=72,Nt=128,Ot="-",Vi=/^xn--/,Gi=/[^\0-\x7F]/,Wi=/[\x2E\u3002\uFF0E\uFF61]/g,Zi={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},H0=Me-Du,ze=Math.floor,V0=String.fromCharCode;function He(e){throw new RangeError(Zi[e])}function $i(e,u){const t=[];let o=e.length;for(;o--;)t[o]=u(e[o]);return t}function Ut(e,u){const t=e.split("@");let o="";t.length>1&&(o=t[0]+"@",e=t[1]),e=e.replace(Wi,".");const n=e.split("."),r=$i(n,u).join(".");return o+r}function jt(e){const u=[];let t=0;const o=e.length;for(;t<o;){const n=e.charCodeAt(t++);if(n>=55296&&n<=56319&&t<o){const r=e.charCodeAt(t++);(r&64512)==56320?u.push(((n&1023)<<10)+(r&1023)+65536):(u.push(n),t--)}else u.push(n)}return u}const Xi=e=>String.fromCodePoint(...e),Ji=function(e){return e>=48&&e<58?26+(e-48):e>=65&&e<91?e-65:e>=97&&e<123?e-97:Me},Zu=function(e,u){return e+22+75*(e<26)-((u!=0)<<5)},Ht=function(e,u,t){let o=0;for(e=t?ze(e/Hi):e>>1,e+=ze(e/u);e>H0*s0>>1;o+=Me)e=ze(e/H0);return ze(o+(H0+1)*e/(e+ji))},Vt=function(e){const u=[],t=e.length;let o=0,n=Nt,r=Pt,c=e.lastIndexOf(Ot);c<0&&(c=0);for(let i=0;i<c;++i)e.charCodeAt(i)>=128&&He("not-basic"),u.push(e.charCodeAt(i));for(let i=c>0?c+1:0;i<t;){const s=o;for(let h=1,v=Me;;v+=Me){i>=t&&He("invalid-input");const _=Ji(e.charCodeAt(i++));_>=Me&&He("invalid-input"),_>ze((Qe-o)/h)&&He("overflow"),o+=_*h;const A=v<=r?Du:v>=r+s0?s0:v-r;if(_<A)break;const a=Me-A;h>ze(Qe/a)&&He("overflow"),h*=a}const f=u.length+1;r=Ht(o-s,f,s==0),ze(o/f)>Qe-n&&He("overflow"),n+=ze(o/f),o%=f,u.splice(o++,0,n)}return String.fromCodePoint(...u)},Gt=function(e){const u=[];e=jt(e);const t=e.length;let o=Nt,n=0,r=Pt;for(const s of e)s<128&&u.push(V0(s));const c=u.length;let i=c;for(c&&u.push(Ot);i<t;){let s=Qe;for(const h of e)h>=o&&h<s&&(s=h);const f=i+1;s-o>ze((Qe-n)/f)&&He("overflow"),n+=(s-o)*f,o=s;for(const h of e)if(h<o&&++n>Qe&&He("overflow"),h===o){let v=n;for(let _=Me;;_+=Me){const A=_<=r?Du:_>=r+s0?s0:_-r;if(v<A)break;const a=v-A,b=Me-A;u.push(V0(Zu(A+a%b,0))),v=ze(a/b)}u.push(V0(Zu(v,0))),r=Ht(n,f,i===c),n=0,++i}++n,++o}return u.join("")},Ki=function(e){return Ut(e,function(u){return Vi.test(u)?Vt(u.slice(4).toLowerCase()):u})},Qi=function(e){return Ut(e,function(u){return Gi.test(u)?"xn--"+Gt(u):u})},Wt={version:"2.3.1",ucs2:{decode:jt,encode:Xi},decode:Vt,encode:Gt,toASCII:Qi,toUnicode:Ki},Yi={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:100},components:{core:{},block:{},inline:{}}},ea={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["paragraph"]},inline:{rules:["text"],rules2:["balance_pairs","fragments_join"]}}},ua={options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["blockquote","code","fence","heading","hr","html_block","lheading","list","reference","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","html_inline","image","link","newline","text"],rules2:["balance_pairs","emphasis","fragments_join"]}}},ta={default:Yi,zero:ea,commonmark:ua},ra=/^(vbscript|javascript|file|data):/,na=/^data:image\/(gif|png|jpeg|webp);/;function ia(e){const u=e.trim().toLowerCase();return ra.test(u)?na.test(u):!0}const Zt=["http:","https:","mailto:"];function aa(e){const u=yu(e,!0);if(u.hostname&&(!u.protocol||Zt.indexOf(u.protocol)>=0))try{u.hostname=Wt.toASCII(u.hostname)}catch{}return l0(xu(u))}function oa(e){const u=yu(e,!0);if(u.hostname&&(!u.protocol||Zt.indexOf(u.protocol)>=0))try{u.hostname=Wt.toUnicode(u.hostname)}catch{}return Ye(xu(u),Ye.defaultChars+"%")}function De(e,u){if(!(this instanceof De))return new De(e,u);u||wu(e)||(u=e||{},e="default"),this.inline=new d0,this.block=new E0,this.core=new Cu,this.renderer=new u0,this.linkify=new ke,this.validateLink=ia,this.normalizeLink=aa,this.normalizeLinkText=oa,this.utils=cn,this.helpers=A0({},hn),this.options={},this.configure(e),u&&this.set(u)}De.prototype.set=function(e){return A0(this.options,e),this};De.prototype.configure=function(e){const u=this;if(wu(e)){const t=e;if(e=ta[t],!e)throw new Error('Wrong `markdown-it` preset "'+t+'", check name')}if(!e)throw new Error("Wrong `markdown-it` preset, can't be empty");return e.options&&u.set(e.options),e.components&&Object.keys(e.components).forEach(function(t){e.components[t].rules&&u[t].ruler.enableOnly(e.components[t].rules),e.components[t].rules2&&u[t].ruler2.enableOnly(e.components[t].rules2)}),this};De.prototype.enable=function(e,u){let t=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach(function(n){t=t.concat(this[n].ruler.enable(e,!0))},this),t=t.concat(this.inline.ruler2.enable(e,!0));const o=e.filter(function(n){return t.indexOf(n)<0});if(o.length&&!u)throw new Error("MarkdownIt. Failed to enable unknown rule(s): "+o);return this};De.prototype.disable=function(e,u){let t=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach(function(n){t=t.concat(this[n].ruler.disable(e,!0))},this),t=t.concat(this.inline.ruler2.disable(e,!0));const o=e.filter(function(n){return t.indexOf(n)<0});if(o.length&&!u)throw new Error("MarkdownIt. Failed to disable unknown rule(s): "+o);return this};De.prototype.use=function(e){const u=[this].concat(Array.prototype.slice.call(arguments,1));return e.apply(e,u),this};De.prototype.parse=function(e,u){if(typeof e!="string")throw new Error("Input data should be a String");const t=new this.core.State(e,this,u);return this.core.process(t),t.tokens};De.prototype.render=function(e,u){return u=u||{},this.renderer.render(this.parse(e,u),this.options,u)};De.prototype.parseInline=function(e,u){const t=new this.core.State(e,this,u);return t.inlineMode=!0,this.core.process(t),t.tokens};De.prototype.renderInline=function(e,u){return u=u||{},this.renderer.render(this.parseInline(e,u),this.options,u)};var G0,$u;function Ze(){if($u)return G0;$u=1;function e(u,t,o){var n="KaTeX parse error: "+u;if(t!==void 0&&o!==void 0){n+=" at position "+o+": ";var r=t._input;r=r.slice(0,o)+"̲"+r.slice(o);var c=Math.max(0,o-15),i=o+15;n+=r.slice(c,i)}var s=new Error(n);return s.name="ParseError",s.__proto__=e.prototype,s.position=o,s}return e.prototype.__proto__=Error.prototype,G0=e,G0}var W0,Xu;function $t(){if(Xu)return W0;Xu=1;function e(t,o){return t===void 0?o:t}function u(t){t=t||{},this.displayMode=e(t.displayMode,!1),this.throwOnError=e(t.throwOnError,!0),this.errorColor=e(t.errorColor,"#cc0000")}return W0=u,W0}var Z0,Ju;function F0(){if(Ju)return Z0;Ju=1;function e(S,p,d,w){this.id=S,this.size=p,this.cramped=w,this.sizeMultiplier=d}e.prototype.sup=function(){return v[_[this.id]]},e.prototype.sub=function(){return v[A[this.id]]},e.prototype.fracNum=function(){return v[a[this.id]]},e.prototype.fracDen=function(){return v[b[this.id]]},e.prototype.cramp=function(){return v[m[this.id]]},e.prototype.cls=function(){return f[this.size]+(this.cramped?" cramped":" uncramped")},e.prototype.reset=function(){return h[this.size]};var u=0,t=1,o=2,n=3,r=4,c=5,i=6,s=7,f=["displaystyle textstyle","textstyle","scriptstyle","scriptscriptstyle"],h=["reset-textstyle","reset-textstyle","reset-scriptstyle","reset-scriptscriptstyle"],v=[new e(u,0,1,!1),new e(t,0,1,!0),new e(o,1,1,!1),new e(n,1,1,!0),new e(r,2,.7,!1),new e(c,2,.7,!0),new e(i,3,.5,!1),new e(s,3,.5,!0)],_=[r,c,r,c,i,s,i,s],A=[c,c,c,c,s,s,s,s],a=[o,n,r,c,i,s,i,s],b=[n,n,c,c,s,s,s,s],m=[t,t,n,n,c,c,s,s];return Z0={DISPLAY:v[u],TEXT:v[o],SCRIPT:v[r],SCRIPTSCRIPT:v[i]},Z0}var $0,Ku;function Ue(){if(Ku)return $0;Ku=1;var e=Array.prototype.indexOf,u=function(A,a){if(A==null)return-1;if(e&&A.indexOf===e)return A.indexOf(a);for(var b=0,m=A.length;b<m;b++)if(A[b]===a)return b;return-1},t=function(A,a){return u(A,a)!==-1},o=function(A,a){return A===void 0?a:A},n=/([A-Z])/g,r=function(A){return A.replace(n,"-$1").toLowerCase()},c={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},i=/[&><"']/g;function s(A){return c[A]}function f(A){return(""+A).replace(i,s)}var h;if(typeof document<"u"){var v=document.createElement("span");"textContent"in v?h=function(A,a){A.textContent=a}:h=function(A,a){A.innerText=a}}function _(A){h(A,"")}return $0={contains:t,deflt:o,escape:f,hyphenate:r,indexOf:u,setTextContent:h,clearNode:_},$0}var X0,Qu;function Xt(){if(Qu)return X0;Qu=1;var e=Ue(),u=function(r){r=r.slice();for(var c=r.length-1;c>=0;c--)r[c]||r.splice(c,1);return r.join(" ")};function t(r,c,i,s,f,h){this.classes=r||[],this.children=c||[],this.height=i||0,this.depth=s||0,this.maxFontSize=f||0,this.style=h||{},this.attributes={}}t.prototype.setAttribute=function(r,c){this.attributes[r]=c},t.prototype.toNode=function(){var r=document.createElement("span");r.className=u(this.classes);for(var c in this.style)Object.prototype.hasOwnProperty.call(this.style,c)&&(r.style[c]=this.style[c]);for(var i in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,i)&&r.setAttribute(i,this.attributes[i]);for(var s=0;s<this.children.length;s++)r.appendChild(this.children[s].toNode());return r},t.prototype.toMarkup=function(){var r="<span";this.classes.length&&(r+=' class="',r+=e.escape(u(this.classes)),r+='"');var c="";for(var i in this.style)this.style.hasOwnProperty(i)&&(c+=e.hyphenate(i)+":"+this.style[i]+";");c&&(r+=' style="'+e.escape(c)+'"');for(var s in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,s)&&(r+=" "+s+'="',r+=e.escape(this.attributes[s]),r+='"');r+=">";for(var f=0;f<this.children.length;f++)r+=this.children[f].toMarkup();return r+="</span>",r};function o(r,c,i,s){this.children=r||[],this.height=c||0,this.depth=i||0,this.maxFontSize=s||0}o.prototype.toNode=function(){for(var r=document.createDocumentFragment(),c=0;c<this.children.length;c++)r.appendChild(this.children[c].toNode());return r},o.prototype.toMarkup=function(){for(var r="",c=0;c<this.children.length;c++)r+=this.children[c].toMarkup();return r};function n(r,c,i,s,f,h,v){this.value=r||"",this.height=c||0,this.depth=i||0,this.italic=s||0,this.skew=f||0,this.classes=h||[],this.style=v||{},this.maxFontSize=0}return n.prototype.toNode=function(){var r=document.createTextNode(this.value),c=null;this.italic>0&&(c=document.createElement("span"),c.style.marginRight=this.italic+"em"),this.classes.length>0&&(c=c||document.createElement("span"),c.className=u(this.classes));for(var i in this.style)this.style.hasOwnProperty(i)&&(c=c||document.createElement("span"),c.style[i]=this.style[i]);return c?(c.appendChild(r),c):r},n.prototype.toMarkup=function(){var r=!1,c="<span";this.classes.length&&(r=!0,c+=' class="',c+=e.escape(u(this.classes)),c+='"');var i="";this.italic>0&&(i+="margin-right:"+this.italic+"em;");for(var s in this.style)this.style.hasOwnProperty(s)&&(i+=e.hyphenate(s)+":"+this.style[s]+";");i&&(r=!0,c+=' style="'+e.escape(i)+'"');var f=e.escape(this.value);return r?(c+=">",c+=f,c+="</span>",c):f},X0={span:t,documentFragment:o,symbolNode:n},X0}var J0,Yu;function sa(){return Yu||(Yu=1,J0={"AMS-Regular":{65:[0,.68889,0,0],66:[0,.68889,0,0],67:[0,.68889,0,0],68:[0,.68889,0,0],69:[0,.68889,0,0],70:[0,.68889,0,0],71:[0,.68889,0,0],72:[0,.68889,0,0],73:[0,.68889,0,0],74:[.16667,.68889,0,0],75:[0,.68889,0,0],76:[0,.68889,0,0],77:[0,.68889,0,0],78:[0,.68889,0,0],79:[.16667,.68889,0,0],80:[0,.68889,0,0],81:[.16667,.68889,0,0],82:[0,.68889,0,0],83:[0,.68889,0,0],84:[0,.68889,0,0],85:[0,.68889,0,0],86:[0,.68889,0,0],87:[0,.68889,0,0],88:[0,.68889,0,0],89:[0,.68889,0,0],90:[0,.68889,0,0],107:[0,.68889,0,0],165:[0,.675,.025,0],174:[.15559,.69224,0,0],240:[0,.68889,0,0],295:[0,.68889,0,0],710:[0,.825,0,0],732:[0,.9,0,0],770:[0,.825,0,0],771:[0,.9,0,0],989:[.08167,.58167,0,0],1008:[0,.43056,.04028,0],8245:[0,.54986,0,0],8463:[0,.68889,0,0],8487:[0,.68889,0,0],8498:[0,.68889,0,0],8502:[0,.68889,0,0],8503:[0,.68889,0,0],8504:[0,.68889,0,0],8513:[0,.68889,0,0],8592:[-.03598,.46402,0,0],8594:[-.03598,.46402,0,0],8602:[-.13313,.36687,0,0],8603:[-.13313,.36687,0,0],8606:[.01354,.52239,0,0],8608:[.01354,.52239,0,0],8610:[.01354,.52239,0,0],8611:[.01354,.52239,0,0],8619:[0,.54986,0,0],8620:[0,.54986,0,0],8621:[-.13313,.37788,0,0],8622:[-.13313,.36687,0,0],8624:[0,.69224,0,0],8625:[0,.69224,0,0],8630:[0,.43056,0,0],8631:[0,.43056,0,0],8634:[.08198,.58198,0,0],8635:[.08198,.58198,0,0],8638:[.19444,.69224,0,0],8639:[.19444,.69224,0,0],8642:[.19444,.69224,0,0],8643:[.19444,.69224,0,0],8644:[.1808,.675,0,0],8646:[.1808,.675,0,0],8647:[.1808,.675,0,0],8648:[.19444,.69224,0,0],8649:[.1808,.675,0,0],8650:[.19444,.69224,0,0],8651:[.01354,.52239,0,0],8652:[.01354,.52239,0,0],8653:[-.13313,.36687,0,0],8654:[-.13313,.36687,0,0],8655:[-.13313,.36687,0,0],8666:[.13667,.63667,0,0],8667:[.13667,.63667,0,0],8669:[-.13313,.37788,0,0],8672:[-.064,.437,0,0],8674:[-.064,.437,0,0],8705:[0,.825,0,0],8708:[0,.68889,0,0],8709:[.08167,.58167,0,0],8717:[0,.43056,0,0],8722:[-.03598,.46402,0,0],8724:[.08198,.69224,0,0],8726:[.08167,.58167,0,0],8733:[0,.69224,0,0],8736:[0,.69224,0,0],8737:[0,.69224,0,0],8738:[.03517,.52239,0,0],8739:[.08167,.58167,0,0],8740:[.25142,.74111,0,0],8741:[.08167,.58167,0,0],8742:[.25142,.74111,0,0],8756:[0,.69224,0,0],8757:[0,.69224,0,0],8764:[-.13313,.36687,0,0],8765:[-.13313,.37788,0,0],8769:[-.13313,.36687,0,0],8770:[-.03625,.46375,0,0],8774:[.30274,.79383,0,0],8776:[-.01688,.48312,0,0],8778:[.08167,.58167,0,0],8782:[.06062,.54986,0,0],8783:[.06062,.54986,0,0],8785:[.08198,.58198,0,0],8786:[.08198,.58198,0,0],8787:[.08198,.58198,0,0],8790:[0,.69224,0,0],8791:[.22958,.72958,0,0],8796:[.08198,.91667,0,0],8806:[.25583,.75583,0,0],8807:[.25583,.75583,0,0],8808:[.25142,.75726,0,0],8809:[.25142,.75726,0,0],8812:[.25583,.75583,0,0],8814:[.20576,.70576,0,0],8815:[.20576,.70576,0,0],8816:[.30274,.79383,0,0],8817:[.30274,.79383,0,0],8818:[.22958,.72958,0,0],8819:[.22958,.72958,0,0],8822:[.1808,.675,0,0],8823:[.1808,.675,0,0],8828:[.13667,.63667,0,0],8829:[.13667,.63667,0,0],8830:[.22958,.72958,0,0],8831:[.22958,.72958,0,0],8832:[.20576,.70576,0,0],8833:[.20576,.70576,0,0],8840:[.30274,.79383,0,0],8841:[.30274,.79383,0,0],8842:[.13597,.63597,0,0],8843:[.13597,.63597,0,0],8847:[.03517,.54986,0,0],8848:[.03517,.54986,0,0],8858:[.08198,.58198,0,0],8859:[.08198,.58198,0,0],8861:[.08198,.58198,0,0],8862:[0,.675,0,0],8863:[0,.675,0,0],8864:[0,.675,0,0],8865:[0,.675,0,0],8872:[0,.69224,0,0],8873:[0,.69224,0,0],8874:[0,.69224,0,0],8876:[0,.68889,0,0],8877:[0,.68889,0,0],8878:[0,.68889,0,0],8879:[0,.68889,0,0],8882:[.03517,.54986,0,0],8883:[.03517,.54986,0,0],8884:[.13667,.63667,0,0],8885:[.13667,.63667,0,0],8888:[0,.54986,0,0],8890:[.19444,.43056,0,0],8891:[.19444,.69224,0,0],8892:[.19444,.69224,0,0],8901:[0,.54986,0,0],8903:[.08167,.58167,0,0],8905:[.08167,.58167,0,0],8906:[.08167,.58167,0,0],8907:[0,.69224,0,0],8908:[0,.69224,0,0],8909:[-.03598,.46402,0,0],8910:[0,.54986,0,0],8911:[0,.54986,0,0],8912:[.03517,.54986,0,0],8913:[.03517,.54986,0,0],8914:[0,.54986,0,0],8915:[0,.54986,0,0],8916:[0,.69224,0,0],8918:[.0391,.5391,0,0],8919:[.0391,.5391,0,0],8920:[.03517,.54986,0,0],8921:[.03517,.54986,0,0],8922:[.38569,.88569,0,0],8923:[.38569,.88569,0,0],8926:[.13667,.63667,0,0],8927:[.13667,.63667,0,0],8928:[.30274,.79383,0,0],8929:[.30274,.79383,0,0],8934:[.23222,.74111,0,0],8935:[.23222,.74111,0,0],8936:[.23222,.74111,0,0],8937:[.23222,.74111,0,0],8938:[.20576,.70576,0,0],8939:[.20576,.70576,0,0],8940:[.30274,.79383,0,0],8941:[.30274,.79383,0,0],8994:[.19444,.69224,0,0],8995:[.19444,.69224,0,0],9416:[.15559,.69224,0,0],9484:[0,.69224,0,0],9488:[0,.69224,0,0],9492:[0,.37788,0,0],9496:[0,.37788,0,0],9585:[.19444,.68889,0,0],9586:[.19444,.74111,0,0],9632:[0,.675,0,0],9633:[0,.675,0,0],9650:[0,.54986,0,0],9651:[0,.54986,0,0],9654:[.03517,.54986,0,0],9660:[0,.54986,0,0],9661:[0,.54986,0,0],9664:[.03517,.54986,0,0],9674:[.11111,.69224,0,0],9733:[.19444,.69224,0,0],10003:[0,.69224,0,0],10016:[0,.69224,0,0],10731:[.11111,.69224,0,0],10846:[.19444,.75583,0,0],10877:[.13667,.63667,0,0],10878:[.13667,.63667,0,0],10885:[.25583,.75583,0,0],10886:[.25583,.75583,0,0],10887:[.13597,.63597,0,0],10888:[.13597,.63597,0,0],10889:[.26167,.75726,0,0],10890:[.26167,.75726,0,0],10891:[.48256,.98256,0,0],10892:[.48256,.98256,0,0],10901:[.13667,.63667,0,0],10902:[.13667,.63667,0,0],10933:[.25142,.75726,0,0],10934:[.25142,.75726,0,0],10935:[.26167,.75726,0,0],10936:[.26167,.75726,0,0],10937:[.26167,.75726,0,0],10938:[.26167,.75726,0,0],10949:[.25583,.75583,0,0],10950:[.25583,.75583,0,0],10955:[.28481,.79383,0,0],10956:[.28481,.79383,0,0],57350:[.08167,.58167,0,0],57351:[.08167,.58167,0,0],57352:[.08167,.58167,0,0],57353:[0,.43056,.04028,0],57356:[.25142,.75726,0,0],57357:[.25142,.75726,0,0],57358:[.41951,.91951,0,0],57359:[.30274,.79383,0,0],57360:[.30274,.79383,0,0],57361:[.41951,.91951,0,0],57366:[.25142,.75726,0,0],57367:[.25142,.75726,0,0],57368:[.25142,.75726,0,0],57369:[.25142,.75726,0,0],57370:[.13597,.63597,0,0],57371:[.13597,.63597,0,0]},"Caligraphic-Regular":{48:[0,.43056,0,0],49:[0,.43056,0,0],50:[0,.43056,0,0],51:[.19444,.43056,0,0],52:[.19444,.43056,0,0],53:[.19444,.43056,0,0],54:[0,.64444,0,0],55:[.19444,.43056,0,0],56:[0,.64444,0,0],57:[.19444,.43056,0,0],65:[0,.68333,0,.19445],66:[0,.68333,.03041,.13889],67:[0,.68333,.05834,.13889],68:[0,.68333,.02778,.08334],69:[0,.68333,.08944,.11111],70:[0,.68333,.09931,.11111],71:[.09722,.68333,.0593,.11111],72:[0,.68333,.00965,.11111],73:[0,.68333,.07382,0],74:[.09722,.68333,.18472,.16667],75:[0,.68333,.01445,.05556],76:[0,.68333,0,.13889],77:[0,.68333,0,.13889],78:[0,.68333,.14736,.08334],79:[0,.68333,.02778,.11111],80:[0,.68333,.08222,.08334],81:[.09722,.68333,0,.11111],82:[0,.68333,0,.08334],83:[0,.68333,.075,.13889],84:[0,.68333,.25417,0],85:[0,.68333,.09931,.08334],86:[0,.68333,.08222,0],87:[0,.68333,.08222,.08334],88:[0,.68333,.14643,.13889],89:[.09722,.68333,.08222,.08334],90:[0,.68333,.07944,.13889]},"Fraktur-Regular":{33:[0,.69141,0,0],34:[0,.69141,0,0],38:[0,.69141,0,0],39:[0,.69141,0,0],40:[.24982,.74947,0,0],41:[.24982,.74947,0,0],42:[0,.62119,0,0],43:[.08319,.58283,0,0],44:[0,.10803,0,0],45:[.08319,.58283,0,0],46:[0,.10803,0,0],47:[.24982,.74947,0,0],48:[0,.47534,0,0],49:[0,.47534,0,0],50:[0,.47534,0,0],51:[.18906,.47534,0,0],52:[.18906,.47534,0,0],53:[.18906,.47534,0,0],54:[0,.69141,0,0],55:[.18906,.47534,0,0],56:[0,.69141,0,0],57:[.18906,.47534,0,0],58:[0,.47534,0,0],59:[.12604,.47534,0,0],61:[-.13099,.36866,0,0],63:[0,.69141,0,0],65:[0,.69141,0,0],66:[0,.69141,0,0],67:[0,.69141,0,0],68:[0,.69141,0,0],69:[0,.69141,0,0],70:[.12604,.69141,0,0],71:[0,.69141,0,0],72:[.06302,.69141,0,0],73:[0,.69141,0,0],74:[.12604,.69141,0,0],75:[0,.69141,0,0],76:[0,.69141,0,0],77:[0,.69141,0,0],78:[0,.69141,0,0],79:[0,.69141,0,0],80:[.18906,.69141,0,0],81:[.03781,.69141,0,0],82:[0,.69141,0,0],83:[0,.69141,0,0],84:[0,.69141,0,0],85:[0,.69141,0,0],86:[0,.69141,0,0],87:[0,.69141,0,0],88:[0,.69141,0,0],89:[.18906,.69141,0,0],90:[.12604,.69141,0,0],91:[.24982,.74947,0,0],93:[.24982,.74947,0,0],94:[0,.69141,0,0],97:[0,.47534,0,0],98:[0,.69141,0,0],99:[0,.47534,0,0],100:[0,.62119,0,0],101:[0,.47534,0,0],102:[.18906,.69141,0,0],103:[.18906,.47534,0,0],104:[.18906,.69141,0,0],105:[0,.69141,0,0],106:[0,.69141,0,0],107:[0,.69141,0,0],108:[0,.69141,0,0],109:[0,.47534,0,0],110:[0,.47534,0,0],111:[0,.47534,0,0],112:[.18906,.52396,0,0],113:[.18906,.47534,0,0],114:[0,.47534,0,0],115:[0,.47534,0,0],116:[0,.62119,0,0],117:[0,.47534,0,0],118:[0,.52396,0,0],119:[0,.52396,0,0],120:[.18906,.47534,0,0],121:[.18906,.47534,0,0],122:[.18906,.47534,0,0],8216:[0,.69141,0,0],8217:[0,.69141,0,0],58112:[0,.62119,0,0],58113:[0,.62119,0,0],58114:[.18906,.69141,0,0],58115:[.18906,.69141,0,0],58116:[.18906,.47534,0,0],58117:[0,.69141,0,0],58118:[0,.62119,0,0],58119:[0,.47534,0,0]},"Main-Bold":{33:[0,.69444,0,0],34:[0,.69444,0,0],35:[.19444,.69444,0,0],36:[.05556,.75,0,0],37:[.05556,.75,0,0],38:[0,.69444,0,0],39:[0,.69444,0,0],40:[.25,.75,0,0],41:[.25,.75,0,0],42:[0,.75,0,0],43:[.13333,.63333,0,0],44:[.19444,.15556,0,0],45:[0,.44444,0,0],46:[0,.15556,0,0],47:[.25,.75,0,0],48:[0,.64444,0,0],49:[0,.64444,0,0],50:[0,.64444,0,0],51:[0,.64444,0,0],52:[0,.64444,0,0],53:[0,.64444,0,0],54:[0,.64444,0,0],55:[0,.64444,0,0],56:[0,.64444,0,0],57:[0,.64444,0,0],58:[0,.44444,0,0],59:[.19444,.44444,0,0],60:[.08556,.58556,0,0],61:[-.10889,.39111,0,0],62:[.08556,.58556,0,0],63:[0,.69444,0,0],64:[0,.69444,0,0],65:[0,.68611,0,0],66:[0,.68611,0,0],67:[0,.68611,0,0],68:[0,.68611,0,0],69:[0,.68611,0,0],70:[0,.68611,0,0],71:[0,.68611,0,0],72:[0,.68611,0,0],73:[0,.68611,0,0],74:[0,.68611,0,0],75:[0,.68611,0,0],76:[0,.68611,0,0],77:[0,.68611,0,0],78:[0,.68611,0,0],79:[0,.68611,0,0],80:[0,.68611,0,0],81:[.19444,.68611,0,0],82:[0,.68611,0,0],83:[0,.68611,0,0],84:[0,.68611,0,0],85:[0,.68611,0,0],86:[0,.68611,.01597,0],87:[0,.68611,.01597,0],88:[0,.68611,0,0],89:[0,.68611,.02875,0],90:[0,.68611,0,0],91:[.25,.75,0,0],92:[.25,.75,0,0],93:[.25,.75,0,0],94:[0,.69444,0,0],95:[.31,.13444,.03194,0],96:[0,.69444,0,0],97:[0,.44444,0,0],98:[0,.69444,0,0],99:[0,.44444,0,0],100:[0,.69444,0,0],101:[0,.44444,0,0],102:[0,.69444,.10903,0],103:[.19444,.44444,.01597,0],104:[0,.69444,0,0],105:[0,.69444,0,0],106:[.19444,.69444,0,0],107:[0,.69444,0,0],108:[0,.69444,0,0],109:[0,.44444,0,0],110:[0,.44444,0,0],111:[0,.44444,0,0],112:[.19444,.44444,0,0],113:[.19444,.44444,0,0],114:[0,.44444,0,0],115:[0,.44444,0,0],116:[0,.63492,0,0],117:[0,.44444,0,0],118:[0,.44444,.01597,0],119:[0,.44444,.01597,0],120:[0,.44444,0,0],121:[.19444,.44444,.01597,0],122:[0,.44444,0,0],123:[.25,.75,0,0],124:[.25,.75,0,0],125:[.25,.75,0,0],126:[.35,.34444,0,0],168:[0,.69444,0,0],172:[0,.44444,0,0],175:[0,.59611,0,0],176:[0,.69444,0,0],177:[.13333,.63333,0,0],180:[0,.69444,0,0],215:[.13333,.63333,0,0],247:[.13333,.63333,0,0],305:[0,.44444,0,0],567:[.19444,.44444,0,0],710:[0,.69444,0,0],711:[0,.63194,0,0],713:[0,.59611,0,0],714:[0,.69444,0,0],715:[0,.69444,0,0],728:[0,.69444,0,0],729:[0,.69444,0,0],730:[0,.69444,0,0],732:[0,.69444,0,0],768:[0,.69444,0,0],769:[0,.69444,0,0],770:[0,.69444,0,0],771:[0,.69444,0,0],772:[0,.59611,0,0],774:[0,.69444,0,0],775:[0,.69444,0,0],776:[0,.69444,0,0],778:[0,.69444,0,0],779:[0,.69444,0,0],780:[0,.63194,0,0],824:[.19444,.69444,0,0],915:[0,.68611,0,0],916:[0,.68611,0,0],920:[0,.68611,0,0],923:[0,.68611,0,0],926:[0,.68611,0,0],928:[0,.68611,0,0],931:[0,.68611,0,0],933:[0,.68611,0,0],934:[0,.68611,0,0],936:[0,.68611,0,0],937:[0,.68611,0,0],8211:[0,.44444,.03194,0],8212:[0,.44444,.03194,0],8216:[0,.69444,0,0],8217:[0,.69444,0,0],8220:[0,.69444,0,0],8221:[0,.69444,0,0],8224:[.19444,.69444,0,0],8225:[.19444,.69444,0,0],8242:[0,.55556,0,0],8407:[0,.72444,.15486,0],8463:[0,.69444,0,0],8465:[0,.69444,0,0],8467:[0,.69444,0,0],8472:[.19444,.44444,0,0],8476:[0,.69444,0,0],8501:[0,.69444,0,0],8592:[-.10889,.39111,0,0],8593:[.19444,.69444,0,0],8594:[-.10889,.39111,0,0],8595:[.19444,.69444,0,0],8596:[-.10889,.39111,0,0],8597:[.25,.75,0,0],8598:[.19444,.69444,0,0],8599:[.19444,.69444,0,0],8600:[.19444,.69444,0,0],8601:[.19444,.69444,0,0],8636:[-.10889,.39111,0,0],8637:[-.10889,.39111,0,0],8640:[-.10889,.39111,0,0],8641:[-.10889,.39111,0,0],8656:[-.10889,.39111,0,0],8657:[.19444,.69444,0,0],8658:[-.10889,.39111,0,0],8659:[.19444,.69444,0,0],8660:[-.10889,.39111,0,0],8661:[.25,.75,0,0],8704:[0,.69444,0,0],8706:[0,.69444,.06389,0],8707:[0,.69444,0,0],8709:[.05556,.75,0,0],8711:[0,.68611,0,0],8712:[.08556,.58556,0,0],8715:[.08556,.58556,0,0],8722:[.13333,.63333,0,0],8723:[.13333,.63333,0,0],8725:[.25,.75,0,0],8726:[.25,.75,0,0],8727:[-.02778,.47222,0,0],8728:[-.02639,.47361,0,0],8729:[-.02639,.47361,0,0],8730:[.18,.82,0,0],8733:[0,.44444,0,0],8734:[0,.44444,0,0],8736:[0,.69224,0,0],8739:[.25,.75,0,0],8741:[.25,.75,0,0],8743:[0,.55556,0,0],8744:[0,.55556,0,0],8745:[0,.55556,0,0],8746:[0,.55556,0,0],8747:[.19444,.69444,.12778,0],8764:[-.10889,.39111,0,0],8768:[.19444,.69444,0,0],8771:[.00222,.50222,0,0],8776:[.02444,.52444,0,0],8781:[.00222,.50222,0,0],8801:[.00222,.50222,0,0],8804:[.19667,.69667,0,0],8805:[.19667,.69667,0,0],8810:[.08556,.58556,0,0],8811:[.08556,.58556,0,0],8826:[.08556,.58556,0,0],8827:[.08556,.58556,0,0],8834:[.08556,.58556,0,0],8835:[.08556,.58556,0,0],8838:[.19667,.69667,0,0],8839:[.19667,.69667,0,0],8846:[0,.55556,0,0],8849:[.19667,.69667,0,0],8850:[.19667,.69667,0,0],8851:[0,.55556,0,0],8852:[0,.55556,0,0],8853:[.13333,.63333,0,0],8854:[.13333,.63333,0,0],8855:[.13333,.63333,0,0],8856:[.13333,.63333,0,0],8857:[.13333,.63333,0,0],8866:[0,.69444,0,0],8867:[0,.69444,0,0],8868:[0,.69444,0,0],8869:[0,.69444,0,0],8900:[-.02639,.47361,0,0],8901:[-.02639,.47361,0,0],8902:[-.02778,.47222,0,0],8968:[.25,.75,0,0],8969:[.25,.75,0,0],8970:[.25,.75,0,0],8971:[.25,.75,0,0],8994:[-.13889,.36111,0,0],8995:[-.13889,.36111,0,0],9651:[.19444,.69444,0,0],9657:[-.02778,.47222,0,0],9661:[.19444,.69444,0,0],9667:[-.02778,.47222,0,0],9711:[.19444,.69444,0,0],9824:[.12963,.69444,0,0],9825:[.12963,.69444,0,0],9826:[.12963,.69444,0,0],9827:[.12963,.69444,0,0],9837:[0,.75,0,0],9838:[.19444,.69444,0,0],9839:[.19444,.69444,0,0],10216:[.25,.75,0,0],10217:[.25,.75,0,0],10815:[0,.68611,0,0],10927:[.19667,.69667,0,0],10928:[.19667,.69667,0,0]},"Main-Italic":{33:[0,.69444,.12417,0],34:[0,.69444,.06961,0],35:[.19444,.69444,.06616,0],37:[.05556,.75,.13639,0],38:[0,.69444,.09694,0],39:[0,.69444,.12417,0],40:[.25,.75,.16194,0],41:[.25,.75,.03694,0],42:[0,.75,.14917,0],43:[.05667,.56167,.03694,0],44:[.19444,.10556,0,0],45:[0,.43056,.02826,0],46:[0,.10556,0,0],47:[.25,.75,.16194,0],48:[0,.64444,.13556,0],49:[0,.64444,.13556,0],50:[0,.64444,.13556,0],51:[0,.64444,.13556,0],52:[.19444,.64444,.13556,0],53:[0,.64444,.13556,0],54:[0,.64444,.13556,0],55:[.19444,.64444,.13556,0],56:[0,.64444,.13556,0],57:[0,.64444,.13556,0],58:[0,.43056,.0582,0],59:[.19444,.43056,.0582,0],61:[-.13313,.36687,.06616,0],63:[0,.69444,.1225,0],64:[0,.69444,.09597,0],65:[0,.68333,0,0],66:[0,.68333,.10257,0],67:[0,.68333,.14528,0],68:[0,.68333,.09403,0],69:[0,.68333,.12028,0],70:[0,.68333,.13305,0],71:[0,.68333,.08722,0],72:[0,.68333,.16389,0],73:[0,.68333,.15806,0],74:[0,.68333,.14028,0],75:[0,.68333,.14528,0],76:[0,.68333,0,0],77:[0,.68333,.16389,0],78:[0,.68333,.16389,0],79:[0,.68333,.09403,0],80:[0,.68333,.10257,0],81:[.19444,.68333,.09403,0],82:[0,.68333,.03868,0],83:[0,.68333,.11972,0],84:[0,.68333,.13305,0],85:[0,.68333,.16389,0],86:[0,.68333,.18361,0],87:[0,.68333,.18361,0],88:[0,.68333,.15806,0],89:[0,.68333,.19383,0],90:[0,.68333,.14528,0],91:[.25,.75,.1875,0],93:[.25,.75,.10528,0],94:[0,.69444,.06646,0],95:[.31,.12056,.09208,0],97:[0,.43056,.07671,0],98:[0,.69444,.06312,0],99:[0,.43056,.05653,0],100:[0,.69444,.10333,0],101:[0,.43056,.07514,0],102:[.19444,.69444,.21194,0],103:[.19444,.43056,.08847,0],104:[0,.69444,.07671,0],105:[0,.65536,.1019,0],106:[.19444,.65536,.14467,0],107:[0,.69444,.10764,0],108:[0,.69444,.10333,0],109:[0,.43056,.07671,0],110:[0,.43056,.07671,0],111:[0,.43056,.06312,0],112:[.19444,.43056,.06312,0],113:[.19444,.43056,.08847,0],114:[0,.43056,.10764,0],115:[0,.43056,.08208,0],116:[0,.61508,.09486,0],117:[0,.43056,.07671,0],118:[0,.43056,.10764,0],119:[0,.43056,.10764,0],120:[0,.43056,.12042,0],121:[.19444,.43056,.08847,0],122:[0,.43056,.12292,0],126:[.35,.31786,.11585,0],163:[0,.69444,0,0],305:[0,.43056,0,.02778],567:[.19444,.43056,0,.08334],768:[0,.69444,0,0],769:[0,.69444,.09694,0],770:[0,.69444,.06646,0],771:[0,.66786,.11585,0],772:[0,.56167,.10333,0],774:[0,.69444,.10806,0],775:[0,.66786,.11752,0],776:[0,.66786,.10474,0],778:[0,.69444,0,0],779:[0,.69444,.1225,0],780:[0,.62847,.08295,0],915:[0,.68333,.13305,0],916:[0,.68333,0,0],920:[0,.68333,.09403,0],923:[0,.68333,0,0],926:[0,.68333,.15294,0],928:[0,.68333,.16389,0],931:[0,.68333,.12028,0],933:[0,.68333,.11111,0],934:[0,.68333,.05986,0],936:[0,.68333,.11111,0],937:[0,.68333,.10257,0],8211:[0,.43056,.09208,0],8212:[0,.43056,.09208,0],8216:[0,.69444,.12417,0],8217:[0,.69444,.12417,0],8220:[0,.69444,.1685,0],8221:[0,.69444,.06961,0],8463:[0,.68889,0,0]},"Main-Regular":{32:[0,0,0,0],33:[0,.69444,0,0],34:[0,.69444,0,0],35:[.19444,.69444,0,0],36:[.05556,.75,0,0],37:[.05556,.75,0,0],38:[0,.69444,0,0],39:[0,.69444,0,0],40:[.25,.75,0,0],41:[.25,.75,0,0],42:[0,.75,0,0],43:[.08333,.58333,0,0],44:[.19444,.10556,0,0],45:[0,.43056,0,0],46:[0,.10556,0,0],47:[.25,.75,0,0],48:[0,.64444,0,0],49:[0,.64444,0,0],50:[0,.64444,0,0],51:[0,.64444,0,0],52:[0,.64444,0,0],53:[0,.64444,0,0],54:[0,.64444,0,0],55:[0,.64444,0,0],56:[0,.64444,0,0],57:[0,.64444,0,0],58:[0,.43056,0,0],59:[.19444,.43056,0,0],60:[.0391,.5391,0,0],61:[-.13313,.36687,0,0],62:[.0391,.5391,0,0],63:[0,.69444,0,0],64:[0,.69444,0,0],65:[0,.68333,0,0],66:[0,.68333,0,0],67:[0,.68333,0,0],68:[0,.68333,0,0],69:[0,.68333,0,0],70:[0,.68333,0,0],71:[0,.68333,0,0],72:[0,.68333,0,0],73:[0,.68333,0,0],74:[0,.68333,0,0],75:[0,.68333,0,0],76:[0,.68333,0,0],77:[0,.68333,0,0],78:[0,.68333,0,0],79:[0,.68333,0,0],80:[0,.68333,0,0],81:[.19444,.68333,0,0],82:[0,.68333,0,0],83:[0,.68333,0,0],84:[0,.68333,0,0],85:[0,.68333,0,0],86:[0,.68333,.01389,0],87:[0,.68333,.01389,0],88:[0,.68333,0,0],89:[0,.68333,.025,0],90:[0,.68333,0,0],91:[.25,.75,0,0],92:[.25,.75,0,0],93:[.25,.75,0,0],94:[0,.69444,0,0],95:[.31,.12056,.02778,0],96:[0,.69444,0,0],97:[0,.43056,0,0],98:[0,.69444,0,0],99:[0,.43056,0,0],100:[0,.69444,0,0],101:[0,.43056,0,0],102:[0,.69444,.07778,0],103:[.19444,.43056,.01389,0],104:[0,.69444,0,0],105:[0,.66786,0,0],106:[.19444,.66786,0,0],107:[0,.69444,0,0],108:[0,.69444,0,0],109:[0,.43056,0,0],110:[0,.43056,0,0],111:[0,.43056,0,0],112:[.19444,.43056,0,0],113:[.19444,.43056,0,0],114:[0,.43056,0,0],115:[0,.43056,0,0],116:[0,.61508,0,0],117:[0,.43056,0,0],118:[0,.43056,.01389,0],119:[0,.43056,.01389,0],120:[0,.43056,0,0],121:[.19444,.43056,.01389,0],122:[0,.43056,0,0],123:[.25,.75,0,0],124:[.25,.75,0,0],125:[.25,.75,0,0],126:[.35,.31786,0,0],160:[0,0,0,0],168:[0,.66786,0,0],172:[0,.43056,0,0],175:[0,.56778,0,0],176:[0,.69444,0,0],177:[.08333,.58333,0,0],180:[0,.69444,0,0],215:[.08333,.58333,0,0],247:[.08333,.58333,0,0],305:[0,.43056,0,0],567:[.19444,.43056,0,0],710:[0,.69444,0,0],711:[0,.62847,0,0],713:[0,.56778,0,0],714:[0,.69444,0,0],715:[0,.69444,0,0],728:[0,.69444,0,0],729:[0,.66786,0,0],730:[0,.69444,0,0],732:[0,.66786,0,0],768:[0,.69444,0,0],769:[0,.69444,0,0],770:[0,.69444,0,0],771:[0,.66786,0,0],772:[0,.56778,0,0],774:[0,.69444,0,0],775:[0,.66786,0,0],776:[0,.66786,0,0],778:[0,.69444,0,0],779:[0,.69444,0,0],780:[0,.62847,0,0],824:[.19444,.69444,0,0],915:[0,.68333,0,0],916:[0,.68333,0,0],920:[0,.68333,0,0],923:[0,.68333,0,0],926:[0,.68333,0,0],928:[0,.68333,0,0],931:[0,.68333,0,0],933:[0,.68333,0,0],934:[0,.68333,0,0],936:[0,.68333,0,0],937:[0,.68333,0,0],8211:[0,.43056,.02778,0],8212:[0,.43056,.02778,0],8216:[0,.69444,0,0],8217:[0,.69444,0,0],8220:[0,.69444,0,0],8221:[0,.69444,0,0],8224:[.19444,.69444,0,0],8225:[.19444,.69444,0,0],8230:[0,.12,0,0],8242:[0,.55556,0,0],8407:[0,.71444,.15382,0],8463:[0,.68889,0,0],8465:[0,.69444,0,0],8467:[0,.69444,0,.11111],8472:[.19444,.43056,0,.11111],8476:[0,.69444,0,0],8501:[0,.69444,0,0],8592:[-.13313,.36687,0,0],8593:[.19444,.69444,0,0],8594:[-.13313,.36687,0,0],8595:[.19444,.69444,0,0],8596:[-.13313,.36687,0,0],8597:[.25,.75,0,0],8598:[.19444,.69444,0,0],8599:[.19444,.69444,0,0],8600:[.19444,.69444,0,0],8601:[.19444,.69444,0,0],8614:[.011,.511,0,0],8617:[.011,.511,0,0],8618:[.011,.511,0,0],8636:[-.13313,.36687,0,0],8637:[-.13313,.36687,0,0],8640:[-.13313,.36687,0,0],8641:[-.13313,.36687,0,0],8652:[.011,.671,0,0],8656:[-.13313,.36687,0,0],8657:[.19444,.69444,0,0],8658:[-.13313,.36687,0,0],8659:[.19444,.69444,0,0],8660:[-.13313,.36687,0,0],8661:[.25,.75,0,0],8704:[0,.69444,0,0],8706:[0,.69444,.05556,.08334],8707:[0,.69444,0,0],8709:[.05556,.75,0,0],8711:[0,.68333,0,0],8712:[.0391,.5391,0,0],8715:[.0391,.5391,0,0],8722:[.08333,.58333,0,0],8723:[.08333,.58333,0,0],8725:[.25,.75,0,0],8726:[.25,.75,0,0],8727:[-.03472,.46528,0,0],8728:[-.05555,.44445,0,0],8729:[-.05555,.44445,0,0],8730:[.2,.8,0,0],8733:[0,.43056,0,0],8734:[0,.43056,0,0],8736:[0,.69224,0,0],8739:[.25,.75,0,0],8741:[.25,.75,0,0],8743:[0,.55556,0,0],8744:[0,.55556,0,0],8745:[0,.55556,0,0],8746:[0,.55556,0,0],8747:[.19444,.69444,.11111,0],8764:[-.13313,.36687,0,0],8768:[.19444,.69444,0,0],8771:[-.03625,.46375,0,0],8773:[-.022,.589,0,0],8776:[-.01688,.48312,0,0],8781:[-.03625,.46375,0,0],8784:[-.133,.67,0,0],8800:[.215,.716,0,0],8801:[-.03625,.46375,0,0],8804:[.13597,.63597,0,0],8805:[.13597,.63597,0,0],8810:[.0391,.5391,0,0],8811:[.0391,.5391,0,0],8826:[.0391,.5391,0,0],8827:[.0391,.5391,0,0],8834:[.0391,.5391,0,0],8835:[.0391,.5391,0,0],8838:[.13597,.63597,0,0],8839:[.13597,.63597,0,0],8846:[0,.55556,0,0],8849:[.13597,.63597,0,0],8850:[.13597,.63597,0,0],8851:[0,.55556,0,0],8852:[0,.55556,0,0],8853:[.08333,.58333,0,0],8854:[.08333,.58333,0,0],8855:[.08333,.58333,0,0],8856:[.08333,.58333,0,0],8857:[.08333,.58333,0,0],8866:[0,.69444,0,0],8867:[0,.69444,0,0],8868:[0,.69444,0,0],8869:[0,.69444,0,0],8872:[.249,.75,0,0],8900:[-.05555,.44445,0,0],8901:[-.05555,.44445,0,0],8902:[-.03472,.46528,0,0],8904:[.005,.505,0,0],8942:[.03,.9,0,0],8943:[-.19,.31,0,0],8945:[-.1,.82,0,0],8968:[.25,.75,0,0],8969:[.25,.75,0,0],8970:[.25,.75,0,0],8971:[.25,.75,0,0],8994:[-.14236,.35764,0,0],8995:[-.14236,.35764,0,0],9136:[.244,.744,0,0],9137:[.244,.744,0,0],9651:[.19444,.69444,0,0],9657:[-.03472,.46528,0,0],9661:[.19444,.69444,0,0],9667:[-.03472,.46528,0,0],9711:[.19444,.69444,0,0],9824:[.12963,.69444,0,0],9825:[.12963,.69444,0,0],9826:[.12963,.69444,0,0],9827:[.12963,.69444,0,0],9837:[0,.75,0,0],9838:[.19444,.69444,0,0],9839:[.19444,.69444,0,0],10216:[.25,.75,0,0],10217:[.25,.75,0,0],10222:[.244,.744,0,0],10223:[.244,.744,0,0],10229:[.011,.511,0,0],10230:[.011,.511,0,0],10231:[.011,.511,0,0],10232:[.024,.525,0,0],10233:[.024,.525,0,0],10234:[.024,.525,0,0],10236:[.011,.511,0,0],10815:[0,.68333,0,0],10927:[.13597,.63597,0,0],10928:[.13597,.63597,0,0]},"Math-BoldItalic":{47:[.19444,.69444,0,0],65:[0,.68611,0,0],66:[0,.68611,.04835,0],67:[0,.68611,.06979,0],68:[0,.68611,.03194,0],69:[0,.68611,.05451,0],70:[0,.68611,.15972,0],71:[0,.68611,0,0],72:[0,.68611,.08229,0],73:[0,.68611,.07778,0],74:[0,.68611,.10069,0],75:[0,.68611,.06979,0],76:[0,.68611,0,0],77:[0,.68611,.11424,0],78:[0,.68611,.11424,0],79:[0,.68611,.03194,0],80:[0,.68611,.15972,0],81:[.19444,.68611,0,0],82:[0,.68611,.00421,0],83:[0,.68611,.05382,0],84:[0,.68611,.15972,0],85:[0,.68611,.11424,0],86:[0,.68611,.25555,0],87:[0,.68611,.15972,0],88:[0,.68611,.07778,0],89:[0,.68611,.25555,0],90:[0,.68611,.06979,0],97:[0,.44444,0,0],98:[0,.69444,0,0],99:[0,.44444,0,0],100:[0,.69444,0,0],101:[0,.44444,0,0],102:[.19444,.69444,.11042,0],103:[.19444,.44444,.03704,0],104:[0,.69444,0,0],105:[0,.69326,0,0],106:[.19444,.69326,.0622,0],107:[0,.69444,.01852,0],108:[0,.69444,.0088,0],109:[0,.44444,0,0],110:[0,.44444,0,0],111:[0,.44444,0,0],112:[.19444,.44444,0,0],113:[.19444,.44444,.03704,0],114:[0,.44444,.03194,0],115:[0,.44444,0,0],116:[0,.63492,0,0],117:[0,.44444,0,0],118:[0,.44444,.03704,0],119:[0,.44444,.02778,0],120:[0,.44444,0,0],121:[.19444,.44444,.03704,0],122:[0,.44444,.04213,0],915:[0,.68611,.15972,0],916:[0,.68611,0,0],920:[0,.68611,.03194,0],923:[0,.68611,0,0],926:[0,.68611,.07458,0],928:[0,.68611,.08229,0],931:[0,.68611,.05451,0],933:[0,.68611,.15972,0],934:[0,.68611,0,0],936:[0,.68611,.11653,0],937:[0,.68611,.04835,0],945:[0,.44444,0,0],946:[.19444,.69444,.03403,0],947:[.19444,.44444,.06389,0],948:[0,.69444,.03819,0],949:[0,.44444,0,0],950:[.19444,.69444,.06215,0],951:[.19444,.44444,.03704,0],952:[0,.69444,.03194,0],953:[0,.44444,0,0],954:[0,.44444,0,0],955:[0,.69444,0,0],956:[.19444,.44444,0,0],957:[0,.44444,.06898,0],958:[.19444,.69444,.03021,0],959:[0,.44444,0,0],960:[0,.44444,.03704,0],961:[.19444,.44444,0,0],962:[.09722,.44444,.07917,0],963:[0,.44444,.03704,0],964:[0,.44444,.13472,0],965:[0,.44444,.03704,0],966:[.19444,.44444,0,0],967:[.19444,.44444,0,0],968:[.19444,.69444,.03704,0],969:[0,.44444,.03704,0],977:[0,.69444,0,0],981:[.19444,.69444,0,0],982:[0,.44444,.03194,0],1009:[.19444,.44444,0,0],1013:[0,.44444,0,0]},"Math-Italic":{47:[.19444,.69444,0,0],65:[0,.68333,0,.13889],66:[0,.68333,.05017,.08334],67:[0,.68333,.07153,.08334],68:[0,.68333,.02778,.05556],69:[0,.68333,.05764,.08334],70:[0,.68333,.13889,.08334],71:[0,.68333,0,.08334],72:[0,.68333,.08125,.05556],73:[0,.68333,.07847,.11111],74:[0,.68333,.09618,.16667],75:[0,.68333,.07153,.05556],76:[0,.68333,0,.02778],77:[0,.68333,.10903,.08334],78:[0,.68333,.10903,.08334],79:[0,.68333,.02778,.08334],80:[0,.68333,.13889,.08334],81:[.19444,.68333,0,.08334],82:[0,.68333,.00773,.08334],83:[0,.68333,.05764,.08334],84:[0,.68333,.13889,.08334],85:[0,.68333,.10903,.02778],86:[0,.68333,.22222,0],87:[0,.68333,.13889,0],88:[0,.68333,.07847,.08334],89:[0,.68333,.22222,0],90:[0,.68333,.07153,.08334],97:[0,.43056,0,0],98:[0,.69444,0,0],99:[0,.43056,0,.05556],100:[0,.69444,0,.16667],101:[0,.43056,0,.05556],102:[.19444,.69444,.10764,.16667],103:[.19444,.43056,.03588,.02778],104:[0,.69444,0,0],105:[0,.65952,0,0],106:[.19444,.65952,.05724,0],107:[0,.69444,.03148,0],108:[0,.69444,.01968,.08334],109:[0,.43056,0,0],110:[0,.43056,0,0],111:[0,.43056,0,.05556],112:[.19444,.43056,0,.08334],113:[.19444,.43056,.03588,.08334],114:[0,.43056,.02778,.05556],115:[0,.43056,0,.05556],116:[0,.61508,0,.08334],117:[0,.43056,0,.02778],118:[0,.43056,.03588,.02778],119:[0,.43056,.02691,.08334],120:[0,.43056,0,.02778],121:[.19444,.43056,.03588,.05556],122:[0,.43056,.04398,.05556],915:[0,.68333,.13889,.08334],916:[0,.68333,0,.16667],920:[0,.68333,.02778,.08334],923:[0,.68333,0,.16667],926:[0,.68333,.07569,.08334],928:[0,.68333,.08125,.05556],931:[0,.68333,.05764,.08334],933:[0,.68333,.13889,.05556],934:[0,.68333,0,.08334],936:[0,.68333,.11,.05556],937:[0,.68333,.05017,.08334],945:[0,.43056,.0037,.02778],946:[.19444,.69444,.05278,.08334],947:[.19444,.43056,.05556,0],948:[0,.69444,.03785,.05556],949:[0,.43056,0,.08334],950:[.19444,.69444,.07378,.08334],951:[.19444,.43056,.03588,.05556],952:[0,.69444,.02778,.08334],953:[0,.43056,0,.05556],954:[0,.43056,0,0],955:[0,.69444,0,0],956:[.19444,.43056,0,.02778],957:[0,.43056,.06366,.02778],958:[.19444,.69444,.04601,.11111],959:[0,.43056,0,.05556],960:[0,.43056,.03588,0],961:[.19444,.43056,0,.08334],962:[.09722,.43056,.07986,.08334],963:[0,.43056,.03588,0],964:[0,.43056,.1132,.02778],965:[0,.43056,.03588,.02778],966:[.19444,.43056,0,.08334],967:[.19444,.43056,0,.05556],968:[.19444,.69444,.03588,.11111],969:[0,.43056,.03588,0],977:[0,.69444,0,.08334],981:[.19444,.69444,0,.08334],982:[0,.43056,.02778,0],1009:[.19444,.43056,0,.08334],1013:[0,.43056,0,.05556]},"Math-Regular":{65:[0,.68333,0,.13889],66:[0,.68333,.05017,.08334],67:[0,.68333,.07153,.08334],68:[0,.68333,.02778,.05556],69:[0,.68333,.05764,.08334],70:[0,.68333,.13889,.08334],71:[0,.68333,0,.08334],72:[0,.68333,.08125,.05556],73:[0,.68333,.07847,.11111],74:[0,.68333,.09618,.16667],75:[0,.68333,.07153,.05556],76:[0,.68333,0,.02778],77:[0,.68333,.10903,.08334],78:[0,.68333,.10903,.08334],79:[0,.68333,.02778,.08334],80:[0,.68333,.13889,.08334],81:[.19444,.68333,0,.08334],82:[0,.68333,.00773,.08334],83:[0,.68333,.05764,.08334],84:[0,.68333,.13889,.08334],85:[0,.68333,.10903,.02778],86:[0,.68333,.22222,0],87:[0,.68333,.13889,0],88:[0,.68333,.07847,.08334],89:[0,.68333,.22222,0],90:[0,.68333,.07153,.08334],97:[0,.43056,0,0],98:[0,.69444,0,0],99:[0,.43056,0,.05556],100:[0,.69444,0,.16667],101:[0,.43056,0,.05556],102:[.19444,.69444,.10764,.16667],103:[.19444,.43056,.03588,.02778],104:[0,.69444,0,0],105:[0,.65952,0,0],106:[.19444,.65952,.05724,0],107:[0,.69444,.03148,0],108:[0,.69444,.01968,.08334],109:[0,.43056,0,0],110:[0,.43056,0,0],111:[0,.43056,0,.05556],112:[.19444,.43056,0,.08334],113:[.19444,.43056,.03588,.08334],114:[0,.43056,.02778,.05556],115:[0,.43056,0,.05556],116:[0,.61508,0,.08334],117:[0,.43056,0,.02778],118:[0,.43056,.03588,.02778],119:[0,.43056,.02691,.08334],120:[0,.43056,0,.02778],121:[.19444,.43056,.03588,.05556],122:[0,.43056,.04398,.05556],915:[0,.68333,.13889,.08334],916:[0,.68333,0,.16667],920:[0,.68333,.02778,.08334],923:[0,.68333,0,.16667],926:[0,.68333,.07569,.08334],928:[0,.68333,.08125,.05556],931:[0,.68333,.05764,.08334],933:[0,.68333,.13889,.05556],934:[0,.68333,0,.08334],936:[0,.68333,.11,.05556],937:[0,.68333,.05017,.08334],945:[0,.43056,.0037,.02778],946:[.19444,.69444,.05278,.08334],947:[.19444,.43056,.05556,0],948:[0,.69444,.03785,.05556],949:[0,.43056,0,.08334],950:[.19444,.69444,.07378,.08334],951:[.19444,.43056,.03588,.05556],952:[0,.69444,.02778,.08334],953:[0,.43056,0,.05556],954:[0,.43056,0,0],955:[0,.69444,0,0],956:[.19444,.43056,0,.02778],957:[0,.43056,.06366,.02778],958:[.19444,.69444,.04601,.11111],959:[0,.43056,0,.05556],960:[0,.43056,.03588,0],961:[.19444,.43056,0,.08334],962:[.09722,.43056,.07986,.08334],963:[0,.43056,.03588,0],964:[0,.43056,.1132,.02778],965:[0,.43056,.03588,.02778],966:[.19444,.43056,0,.08334],967:[.19444,.43056,0,.05556],968:[.19444,.69444,.03588,.11111],969:[0,.43056,.03588,0],977:[0,.69444,0,.08334],981:[.19444,.69444,0,.08334],982:[0,.43056,.02778,0],1009:[.19444,.43056,0,.08334],1013:[0,.43056,0,.05556]},"SansSerif-Regular":{33:[0,.69444,0,0],34:[0,.69444,0,0],35:[.19444,.69444,0,0],36:[.05556,.75,0,0],37:[.05556,.75,0,0],38:[0,.69444,0,0],39:[0,.69444,0,0],40:[.25,.75,0,0],41:[.25,.75,0,0],42:[0,.75,0,0],43:[.08333,.58333,0,0],44:[.125,.08333,0,0],45:[0,.44444,0,0],46:[0,.08333,0,0],47:[.25,.75,0,0],48:[0,.65556,0,0],49:[0,.65556,0,0],50:[0,.65556,0,0],51:[0,.65556,0,0],52:[0,.65556,0,0],53:[0,.65556,0,0],54:[0,.65556,0,0],55:[0,.65556,0,0],56:[0,.65556,0,0],57:[0,.65556,0,0],58:[0,.44444,0,0],59:[.125,.44444,0,0],61:[-.13,.37,0,0],63:[0,.69444,0,0],64:[0,.69444,0,0],65:[0,.69444,0,0],66:[0,.69444,0,0],67:[0,.69444,0,0],68:[0,.69444,0,0],69:[0,.69444,0,0],70:[0,.69444,0,0],71:[0,.69444,0,0],72:[0,.69444,0,0],73:[0,.69444,0,0],74:[0,.69444,0,0],75:[0,.69444,0,0],76:[0,.69444,0,0],77:[0,.69444,0,0],78:[0,.69444,0,0],79:[0,.69444,0,0],80:[0,.69444,0,0],81:[.125,.69444,0,0],82:[0,.69444,0,0],83:[0,.69444,0,0],84:[0,.69444,0,0],85:[0,.69444,0,0],86:[0,.69444,.01389,0],87:[0,.69444,.01389,0],88:[0,.69444,0,0],89:[0,.69444,.025,0],90:[0,.69444,0,0],91:[.25,.75,0,0],93:[.25,.75,0,0],94:[0,.69444,0,0],95:[.35,.09444,.02778,0],97:[0,.44444,0,0],98:[0,.69444,0,0],99:[0,.44444,0,0],100:[0,.69444,0,0],101:[0,.44444,0,0],102:[0,.69444,.06944,0],103:[.19444,.44444,.01389,0],104:[0,.69444,0,0],105:[0,.67937,0,0],106:[.19444,.67937,0,0],107:[0,.69444,0,0],108:[0,.69444,0,0],109:[0,.44444,0,0],110:[0,.44444,0,0],111:[0,.44444,0,0],112:[.19444,.44444,0,0],113:[.19444,.44444,0,0],114:[0,.44444,.01389,0],115:[0,.44444,0,0],116:[0,.57143,0,0],117:[0,.44444,0,0],118:[0,.44444,.01389,0],119:[0,.44444,.01389,0],120:[0,.44444,0,0],121:[.19444,.44444,.01389,0],122:[0,.44444,0,0],126:[.35,.32659,0,0],305:[0,.44444,0,0],567:[.19444,.44444,0,0],768:[0,.69444,0,0],769:[0,.69444,0,0],770:[0,.69444,0,0],771:[0,.67659,0,0],772:[0,.60889,0,0],774:[0,.69444,0,0],775:[0,.67937,0,0],776:[0,.67937,0,0],778:[0,.69444,0,0],779:[0,.69444,0,0],780:[0,.63194,0,0],915:[0,.69444,0,0],916:[0,.69444,0,0],920:[0,.69444,0,0],923:[0,.69444,0,0],926:[0,.69444,0,0],928:[0,.69444,0,0],931:[0,.69444,0,0],933:[0,.69444,0,0],934:[0,.69444,0,0],936:[0,.69444,0,0],937:[0,.69444,0,0],8211:[0,.44444,.02778,0],8212:[0,.44444,.02778,0],8216:[0,.69444,0,0],8217:[0,.69444,0,0],8220:[0,.69444,0,0],8221:[0,.69444,0,0]},"Script-Regular":{65:[0,.7,.22925,0],66:[0,.7,.04087,0],67:[0,.7,.1689,0],68:[0,.7,.09371,0],69:[0,.7,.18583,0],70:[0,.7,.13634,0],71:[0,.7,.17322,0],72:[0,.7,.29694,0],73:[0,.7,.19189,0],74:[.27778,.7,.19189,0],75:[0,.7,.31259,0],76:[0,.7,.19189,0],77:[0,.7,.15981,0],78:[0,.7,.3525,0],79:[0,.7,.08078,0],80:[0,.7,.08078,0],81:[0,.7,.03305,0],82:[0,.7,.06259,0],83:[0,.7,.19189,0],84:[0,.7,.29087,0],85:[0,.7,.25815,0],86:[0,.7,.27523,0],87:[0,.7,.27523,0],88:[0,.7,.26006,0],89:[0,.7,.2939,0],90:[0,.7,.24037,0]},"Size1-Regular":{40:[.35001,.85,0,0],41:[.35001,.85,0,0],47:[.35001,.85,0,0],91:[.35001,.85,0,0],92:[.35001,.85,0,0],93:[.35001,.85,0,0],123:[.35001,.85,0,0],125:[.35001,.85,0,0],710:[0,.72222,0,0],732:[0,.72222,0,0],770:[0,.72222,0,0],771:[0,.72222,0,0],8214:[-99e-5,.601,0,0],8593:[1e-5,.6,0,0],8595:[1e-5,.6,0,0],8657:[1e-5,.6,0,0],8659:[1e-5,.6,0,0],8719:[.25001,.75,0,0],8720:[.25001,.75,0,0],8721:[.25001,.75,0,0],8730:[.35001,.85,0,0],8739:[-.00599,.606,0,0],8741:[-.00599,.606,0,0],8747:[.30612,.805,.19445,0],8748:[.306,.805,.19445,0],8749:[.306,.805,.19445,0],8750:[.30612,.805,.19445,0],8896:[.25001,.75,0,0],8897:[.25001,.75,0,0],8898:[.25001,.75,0,0],8899:[.25001,.75,0,0],8968:[.35001,.85,0,0],8969:[.35001,.85,0,0],8970:[.35001,.85,0,0],8971:[.35001,.85,0,0],9168:[-99e-5,.601,0,0],10216:[.35001,.85,0,0],10217:[.35001,.85,0,0],10752:[.25001,.75,0,0],10753:[.25001,.75,0,0],10754:[.25001,.75,0,0],10756:[.25001,.75,0,0],10758:[.25001,.75,0,0]},"Size2-Regular":{40:[.65002,1.15,0,0],41:[.65002,1.15,0,0],47:[.65002,1.15,0,0],91:[.65002,1.15,0,0],92:[.65002,1.15,0,0],93:[.65002,1.15,0,0],123:[.65002,1.15,0,0],125:[.65002,1.15,0,0],710:[0,.75,0,0],732:[0,.75,0,0],770:[0,.75,0,0],771:[0,.75,0,0],8719:[.55001,1.05,0,0],8720:[.55001,1.05,0,0],8721:[.55001,1.05,0,0],8730:[.65002,1.15,0,0],8747:[.86225,1.36,.44445,0],8748:[.862,1.36,.44445,0],8749:[.862,1.36,.44445,0],8750:[.86225,1.36,.44445,0],8896:[.55001,1.05,0,0],8897:[.55001,1.05,0,0],8898:[.55001,1.05,0,0],8899:[.55001,1.05,0,0],8968:[.65002,1.15,0,0],8969:[.65002,1.15,0,0],8970:[.65002,1.15,0,0],8971:[.65002,1.15,0,0],10216:[.65002,1.15,0,0],10217:[.65002,1.15,0,0],10752:[.55001,1.05,0,0],10753:[.55001,1.05,0,0],10754:[.55001,1.05,0,0],10756:[.55001,1.05,0,0],10758:[.55001,1.05,0,0]},"Size3-Regular":{40:[.95003,1.45,0,0],41:[.95003,1.45,0,0],47:[.95003,1.45,0,0],91:[.95003,1.45,0,0],92:[.95003,1.45,0,0],93:[.95003,1.45,0,0],123:[.95003,1.45,0,0],125:[.95003,1.45,0,0],710:[0,.75,0,0],732:[0,.75,0,0],770:[0,.75,0,0],771:[0,.75,0,0],8730:[.95003,1.45,0,0],8968:[.95003,1.45,0,0],8969:[.95003,1.45,0,0],8970:[.95003,1.45,0,0],8971:[.95003,1.45,0,0],10216:[.95003,1.45,0,0],10217:[.95003,1.45,0,0]},"Size4-Regular":{40:[1.25003,1.75,0,0],41:[1.25003,1.75,0,0],47:[1.25003,1.75,0,0],91:[1.25003,1.75,0,0],92:[1.25003,1.75,0,0],93:[1.25003,1.75,0,0],123:[1.25003,1.75,0,0],125:[1.25003,1.75,0,0],710:[0,.825,0,0],732:[0,.825,0,0],770:[0,.825,0,0],771:[0,.825,0,0],8730:[1.25003,1.75,0,0],8968:[1.25003,1.75,0,0],8969:[1.25003,1.75,0,0],8970:[1.25003,1.75,0,0],8971:[1.25003,1.75,0,0],9115:[.64502,1.155,0,0],9116:[1e-5,.6,0,0],9117:[.64502,1.155,0,0],9118:[.64502,1.155,0,0],9119:[1e-5,.6,0,0],9120:[.64502,1.155,0,0],9121:[.64502,1.155,0,0],9122:[-99e-5,.601,0,0],9123:[.64502,1.155,0,0],9124:[.64502,1.155,0,0],9125:[-99e-5,.601,0,0],9126:[.64502,1.155,0,0],9127:[1e-5,.9,0,0],9128:[.65002,1.15,0,0],9129:[.90001,0,0,0],9130:[0,.3,0,0],9131:[1e-5,.9,0,0],9132:[.65002,1.15,0,0],9133:[.90001,0,0,0],9143:[.88502,.915,0,0],10216:[1.25003,1.75,0,0],10217:[1.25003,1.75,0,0],57344:[-.00499,.605,0,0],57345:[-.00499,.605,0,0],57680:[0,.12,0,0],57681:[0,.12,0,0],57682:[0,.12,0,0],57683:[0,.12,0,0]},"Typewriter-Regular":{33:[0,.61111,0,0],34:[0,.61111,0,0],35:[0,.61111,0,0],36:[.08333,.69444,0,0],37:[.08333,.69444,0,0],38:[0,.61111,0,0],39:[0,.61111,0,0],40:[.08333,.69444,0,0],41:[.08333,.69444,0,0],42:[0,.52083,0,0],43:[-.08056,.53055,0,0],44:[.13889,.125,0,0],45:[-.08056,.53055,0,0],46:[0,.125,0,0],47:[.08333,.69444,0,0],48:[0,.61111,0,0],49:[0,.61111,0,0],50:[0,.61111,0,0],51:[0,.61111,0,0],52:[0,.61111,0,0],53:[0,.61111,0,0],54:[0,.61111,0,0],55:[0,.61111,0,0],56:[0,.61111,0,0],57:[0,.61111,0,0],58:[0,.43056,0,0],59:[.13889,.43056,0,0],60:[-.05556,.55556,0,0],61:[-.19549,.41562,0,0],62:[-.05556,.55556,0,0],63:[0,.61111,0,0],64:[0,.61111,0,0],65:[0,.61111,0,0],66:[0,.61111,0,0],67:[0,.61111,0,0],68:[0,.61111,0,0],69:[0,.61111,0,0],70:[0,.61111,0,0],71:[0,.61111,0,0],72:[0,.61111,0,0],73:[0,.61111,0,0],74:[0,.61111,0,0],75:[0,.61111,0,0],76:[0,.61111,0,0],77:[0,.61111,0,0],78:[0,.61111,0,0],79:[0,.61111,0,0],80:[0,.61111,0,0],81:[.13889,.61111,0,0],82:[0,.61111,0,0],83:[0,.61111,0,0],84:[0,.61111,0,0],85:[0,.61111,0,0],86:[0,.61111,0,0],87:[0,.61111,0,0],88:[0,.61111,0,0],89:[0,.61111,0,0],90:[0,.61111,0,0],91:[.08333,.69444,0,0],92:[.08333,.69444,0,0],93:[.08333,.69444,0,0],94:[0,.61111,0,0],95:[.09514,0,0,0],96:[0,.61111,0,0],97:[0,.43056,0,0],98:[0,.61111,0,0],99:[0,.43056,0,0],100:[0,.61111,0,0],101:[0,.43056,0,0],102:[0,.61111,0,0],103:[.22222,.43056,0,0],104:[0,.61111,0,0],105:[0,.61111,0,0],106:[.22222,.61111,0,0],107:[0,.61111,0,0],108:[0,.61111,0,0],109:[0,.43056,0,0],110:[0,.43056,0,0],111:[0,.43056,0,0],112:[.22222,.43056,0,0],113:[.22222,.43056,0,0],114:[0,.43056,0,0],115:[0,.43056,0,0],116:[0,.55358,0,0],117:[0,.43056,0,0],118:[0,.43056,0,0],119:[0,.43056,0,0],120:[0,.43056,0,0],121:[.22222,.43056,0,0],122:[0,.43056,0,0],123:[.08333,.69444,0,0],124:[.08333,.69444,0,0],125:[.08333,.69444,0,0],126:[0,.61111,0,0],127:[0,.61111,0,0],305:[0,.43056,0,0],567:[.22222,.43056,0,0],768:[0,.61111,0,0],769:[0,.61111,0,0],770:[0,.61111,0,0],771:[0,.61111,0,0],772:[0,.56555,0,0],774:[0,.61111,0,0],776:[0,.61111,0,0],778:[0,.61111,0,0],780:[0,.56597,0,0],915:[0,.61111,0,0],916:[0,.61111,0,0],920:[0,.61111,0,0],923:[0,.61111,0,0],926:[0,.61111,0,0],928:[0,.61111,0,0],931:[0,.61111,0,0],933:[0,.61111,0,0],934:[0,.61111,0,0],936:[0,.61111,0,0],937:[0,.61111,0,0],2018:[0,.61111,0,0],2019:[0,.61111,0,0],8242:[0,.61111,0,0]}}),J0}var K0,et;function h0(){if(et)return K0;et=1;var e=F0(),u=.431,t=1,o=.677,n=.394,r=.444,c=.686,i=.345,s=.413,f=.363,h=.289,v=.15,_=.247,A=.386,a=.05,b=2.39,m=1.01,S=.81,p=.71,d=.25,w=.04,y=.111,k=.166,M=.2,L=.6,q=.1,D=10,Z=2/D,G={xHeight:u,quad:t,num1:o,num2:n,num3:r,denom1:c,denom2:i,sup1:s,sup2:f,sup3:h,sub1:v,sub2:_,supDrop:A,subDrop:a,axisHeight:d,defaultRuleThickness:w,bigOpSpacing1:y,bigOpSpacing2:k,bigOpSpacing3:M,bigOpSpacing4:L,bigOpSpacing5:q,ptPerEm:D,emPerEx:u/t,doubleRuleSep:Z,delim1:b,getDelim2:function(T){if(T.size===e.TEXT.size)return m;if(T.size===e.SCRIPT.size)return S;if(T.size===e.SCRIPTSCRIPT.size)return p;throw new Error("Unexpected style size: "+T.size)}},Q=sa(),X=function(T,N){var H=Q[N][T.charCodeAt(0)];if(H)return{depth:H[0],height:H[1],italic:H[2],skew:H[3],width:H[4]}};return K0={metrics:G,getCharacterMetrics:X},K0}var Q0={exports:{}},ut;function R0(){return ut||(ut=1,function(e){e.exports={math:{},text:{}};function u(k,M,L,q,D){e.exports[k][D]={font:M,group:L,replace:q}}var t="math",o="text",n="main",r="ams",c="accent",i="bin",s="close",f="inner",h="mathord",v="op",_="open",A="punct",a="rel",b="spacing",m="textord";u(t,n,a,"≡","\\equiv"),u(t,n,a,"≺","\\prec"),u(t,n,a,"≻","\\succ"),u(t,n,a,"∼","\\sim"),u(t,n,a,"⊥","\\perp"),u(t,n,a,"⪯","\\preceq"),u(t,n,a,"⪰","\\succeq"),u(t,n,a,"≃","\\simeq"),u(t,n,a,"∣","\\mid"),u(t,n,a,"≪","\\ll"),u(t,n,a,"≫","\\gg"),u(t,n,a,"≍","\\asymp"),u(t,n,a,"∥","\\parallel"),u(t,n,a,"⋈","\\bowtie"),u(t,n,a,"⌣","\\smile"),u(t,n,a,"⊑","\\sqsubseteq"),u(t,n,a,"⊒","\\sqsupseteq"),u(t,n,a,"≐","\\doteq"),u(t,n,a,"⌢","\\frown"),u(t,n,a,"∋","\\ni"),u(t,n,a,"∝","\\propto"),u(t,n,a,"⊢","\\vdash"),u(t,n,a,"⊣","\\dashv"),u(t,n,a,"∋","\\owns"),u(t,n,A,".","\\ldotp"),u(t,n,A,"⋅","\\cdotp"),u(t,n,m,"#","\\#"),u(t,n,m,"&","\\&"),u(t,n,m,"ℵ","\\aleph"),u(t,n,m,"∀","\\forall"),u(t,n,m,"ℏ","\\hbar"),u(t,n,m,"∃","\\exists"),u(t,n,m,"∇","\\nabla"),u(t,n,m,"♭","\\flat"),u(t,n,m,"ℓ","\\ell"),u(t,n,m,"♮","\\natural"),u(t,n,m,"♣","\\clubsuit"),u(t,n,m,"℘","\\wp"),u(t,n,m,"♯","\\sharp"),u(t,n,m,"♢","\\diamondsuit"),u(t,n,m,"ℜ","\\Re"),u(t,n,m,"♡","\\heartsuit"),u(t,n,m,"ℑ","\\Im"),u(t,n,m,"♠","\\spadesuit"),u(t,n,m,"†","\\dag"),u(t,n,m,"‡","\\ddag"),u(t,n,s,"⎱","\\rmoustache"),u(t,n,_,"⎰","\\lmoustache"),u(t,n,s,"⟯","\\rgroup"),u(t,n,_,"⟮","\\lgroup"),u(t,n,i,"∓","\\mp"),u(t,n,i,"⊖","\\ominus"),u(t,n,i,"⊎","\\uplus"),u(t,n,i,"⊓","\\sqcap"),u(t,n,i,"∗","\\ast"),u(t,n,i,"⊔","\\sqcup"),u(t,n,i,"◯","\\bigcirc"),u(t,n,i,"∙","\\bullet"),u(t,n,i,"‡","\\ddagger"),u(t,n,i,"≀","\\wr"),u(t,n,i,"⨿","\\amalg"),u(t,n,a,"⟵","\\longleftarrow"),u(t,n,a,"⇐","\\Leftarrow"),u(t,n,a,"⟸","\\Longleftarrow"),u(t,n,a,"⟶","\\longrightarrow"),u(t,n,a,"⇒","\\Rightarrow"),u(t,n,a,"⟹","\\Longrightarrow"),u(t,n,a,"↔","\\leftrightarrow"),u(t,n,a,"⟷","\\longleftrightarrow"),u(t,n,a,"⇔","\\Leftrightarrow"),u(t,n,a,"⟺","\\Longleftrightarrow"),u(t,n,a,"↦","\\mapsto"),u(t,n,a,"⟼","\\longmapsto"),u(t,n,a,"↗","\\nearrow"),u(t,n,a,"↩","\\hookleftarrow"),u(t,n,a,"↪","\\hookrightarrow"),u(t,n,a,"↘","\\searrow"),u(t,n,a,"↼","\\leftharpoonup"),u(t,n,a,"⇀","\\rightharpoonup"),u(t,n,a,"↙","\\swarrow"),u(t,n,a,"↽","\\leftharpoondown"),u(t,n,a,"⇁","\\rightharpoondown"),u(t,n,a,"↖","\\nwarrow"),u(t,n,a,"⇌","\\rightleftharpoons"),u(t,r,a,"≮","\\nless"),u(t,r,a,"","\\nleqslant"),u(t,r,a,"","\\nleqq"),u(t,r,a,"⪇","\\lneq"),u(t,r,a,"≨","\\lneqq"),u(t,r,a,"","\\lvertneqq"),u(t,r,a,"⋦","\\lnsim"),u(t,r,a,"⪉","\\lnapprox"),u(t,r,a,"⊀","\\nprec"),u(t,r,a,"⋠","\\npreceq"),u(t,r,a,"⋨","\\precnsim"),u(t,r,a,"⪹","\\precnapprox"),u(t,r,a,"≁","\\nsim"),u(t,r,a,"","\\nshortmid"),u(t,r,a,"∤","\\nmid"),u(t,r,a,"⊬","\\nvdash"),u(t,r,a,"⊭","\\nvDash"),u(t,r,a,"⋪","\\ntriangleleft"),u(t,r,a,"⋬","\\ntrianglelefteq"),u(t,r,a,"⊊","\\subsetneq"),u(t,r,a,"","\\varsubsetneq"),u(t,r,a,"⫋","\\subsetneqq"),u(t,r,a,"","\\varsubsetneqq"),u(t,r,a,"≯","\\ngtr"),u(t,r,a,"","\\ngeqslant"),u(t,r,a,"","\\ngeqq"),u(t,r,a,"⪈","\\gneq"),u(t,r,a,"≩","\\gneqq"),u(t,r,a,"","\\gvertneqq"),u(t,r,a,"⋧","\\gnsim"),u(t,r,a,"⪊","\\gnapprox"),u(t,r,a,"⊁","\\nsucc"),u(t,r,a,"⋡","\\nsucceq"),u(t,r,a,"⋩","\\succnsim"),u(t,r,a,"⪺","\\succnapprox"),u(t,r,a,"≆","\\ncong"),u(t,r,a,"","\\nshortparallel"),u(t,r,a,"∦","\\nparallel"),u(t,r,a,"⊯","\\nVDash"),u(t,r,a,"⋫","\\ntriangleright"),u(t,r,a,"⋭","\\ntrianglerighteq"),u(t,r,a,"","\\nsupseteqq"),u(t,r,a,"⊋","\\supsetneq"),u(t,r,a,"","\\varsupsetneq"),u(t,r,a,"⫌","\\supsetneqq"),u(t,r,a,"","\\varsupsetneqq"),u(t,r,a,"⊮","\\nVdash"),u(t,r,a,"⪵","\\precneqq"),u(t,r,a,"⪶","\\succneqq"),u(t,r,a,"","\\nsubseteqq"),u(t,r,i,"⊴","\\unlhd"),u(t,r,i,"⊵","\\unrhd"),u(t,r,a,"↚","\\nleftarrow"),u(t,r,a,"↛","\\nrightarrow"),u(t,r,a,"⇍","\\nLeftarrow"),u(t,r,a,"⇏","\\nRightarrow"),u(t,r,a,"↮","\\nleftrightarrow"),u(t,r,a,"⇎","\\nLeftrightarrow"),u(t,r,a,"△","\\vartriangle"),u(t,r,m,"ℏ","\\hslash"),u(t,r,m,"▽","\\triangledown"),u(t,r,m,"◊","\\lozenge"),u(t,r,m,"Ⓢ","\\circledS"),u(t,r,m,"®","\\circledR"),u(t,r,m,"∡","\\measuredangle"),u(t,r,m,"∄","\\nexists"),u(t,r,m,"℧","\\mho"),u(t,r,m,"Ⅎ","\\Finv"),u(t,r,m,"⅁","\\Game"),u(t,r,m,"k","\\Bbbk"),u(t,r,m,"‵","\\backprime"),u(t,r,m,"▲","\\blacktriangle"),u(t,r,m,"▼","\\blacktriangledown"),u(t,r,m,"■","\\blacksquare"),u(t,r,m,"⧫","\\blacklozenge"),u(t,r,m,"★","\\bigstar"),u(t,r,m,"∢","\\sphericalangle"),u(t,r,m,"∁","\\complement"),u(t,r,m,"ð","\\eth"),u(t,r,m,"╱","\\diagup"),u(t,r,m,"╲","\\diagdown"),u(t,r,m,"□","\\square"),u(t,r,m,"□","\\Box"),u(t,r,m,"◊","\\Diamond"),u(t,r,m,"¥","\\yen"),u(t,r,m,"✓","\\checkmark"),u(t,r,m,"ℶ","\\beth"),u(t,r,m,"ℸ","\\daleth"),u(t,r,m,"ℷ","\\gimel"),u(t,r,m,"ϝ","\\digamma"),u(t,r,m,"ϰ","\\varkappa"),u(t,r,_,"┌","\\ulcorner"),u(t,r,s,"┐","\\urcorner"),u(t,r,_,"└","\\llcorner"),u(t,r,s,"┘","\\lrcorner"),u(t,r,a,"≦","\\leqq"),u(t,r,a,"⩽","\\leqslant"),u(t,r,a,"⪕","\\eqslantless"),u(t,r,a,"≲","\\lesssim"),u(t,r,a,"⪅","\\lessapprox"),u(t,r,a,"≊","\\approxeq"),u(t,r,i,"⋖","\\lessdot"),u(t,r,a,"⋘","\\lll"),u(t,r,a,"≶","\\lessgtr"),u(t,r,a,"⋚","\\lesseqgtr"),u(t,r,a,"⪋","\\lesseqqgtr"),u(t,r,a,"≑","\\doteqdot"),u(t,r,a,"≓","\\risingdotseq"),u(t,r,a,"≒","\\fallingdotseq"),u(t,r,a,"∽","\\backsim"),u(t,r,a,"⋍","\\backsimeq"),u(t,r,a,"⫅","\\subseteqq"),u(t,r,a,"⋐","\\Subset"),u(t,r,a,"⊏","\\sqsubset"),u(t,r,a,"≼","\\preccurlyeq"),u(t,r,a,"⋞","\\curlyeqprec"),u(t,r,a,"≾","\\precsim"),u(t,r,a,"⪷","\\precapprox"),u(t,r,a,"⊲","\\vartriangleleft"),u(t,r,a,"⊴","\\trianglelefteq"),u(t,r,a,"⊨","\\vDash"),u(t,r,a,"⊪","\\Vvdash"),u(t,r,a,"⌣","\\smallsmile"),u(t,r,a,"⌢","\\smallfrown"),u(t,r,a,"≏","\\bumpeq"),u(t,r,a,"≎","\\Bumpeq"),u(t,r,a,"≧","\\geqq"),u(t,r,a,"⩾","\\geqslant"),u(t,r,a,"⪖","\\eqslantgtr"),u(t,r,a,"≳","\\gtrsim"),u(t,r,a,"⪆","\\gtrapprox"),u(t,r,i,"⋗","\\gtrdot"),u(t,r,a,"⋙","\\ggg"),u(t,r,a,"≷","\\gtrless"),u(t,r,a,"⋛","\\gtreqless"),u(t,r,a,"⪌","\\gtreqqless"),u(t,r,a,"≖","\\eqcirc"),u(t,r,a,"≗","\\circeq"),u(t,r,a,"≜","\\triangleq"),u(t,r,a,"∼","\\thicksim"),u(t,r,a,"≈","\\thickapprox"),u(t,r,a,"⫆","\\supseteqq"),u(t,r,a,"⋑","\\Supset"),u(t,r,a,"⊐","\\sqsupset"),u(t,r,a,"≽","\\succcurlyeq"),u(t,r,a,"⋟","\\curlyeqsucc"),u(t,r,a,"≿","\\succsim"),u(t,r,a,"⪸","\\succapprox"),u(t,r,a,"⊳","\\vartriangleright"),u(t,r,a,"⊵","\\trianglerighteq"),u(t,r,a,"⊩","\\Vdash"),u(t,r,a,"∣","\\shortmid"),u(t,r,a,"∥","\\shortparallel"),u(t,r,a,"≬","\\between"),u(t,r,a,"⋔","\\pitchfork"),u(t,r,a,"∝","\\varpropto"),u(t,r,a,"◀","\\blacktriangleleft"),u(t,r,a,"∴","\\therefore"),u(t,r,a,"∍","\\backepsilon"),u(t,r,a,"▶","\\blacktriangleright"),u(t,r,a,"∵","\\because"),u(t,r,a,"⋘","\\llless"),u(t,r,a,"⋙","\\gggtr"),u(t,r,i,"⊲","\\lhd"),u(t,r,i,"⊳","\\rhd"),u(t,r,a,"≂","\\eqsim"),u(t,n,a,"⋈","\\Join"),u(t,r,a,"≑","\\Doteq"),u(t,r,i,"∔","\\dotplus"),u(t,r,i,"∖","\\smallsetminus"),u(t,r,i,"⋒","\\Cap"),u(t,r,i,"⋓","\\Cup"),u(t,r,i,"⩞","\\doublebarwedge"),u(t,r,i,"⊟","\\boxminus"),u(t,r,i,"⊞","\\boxplus"),u(t,r,i,"⋇","\\divideontimes"),u(t,r,i,"⋉","\\ltimes"),u(t,r,i,"⋊","\\rtimes"),u(t,r,i,"⋋","\\leftthreetimes"),u(t,r,i,"⋌","\\rightthreetimes"),u(t,r,i,"⋏","\\curlywedge"),u(t,r,i,"⋎","\\curlyvee"),u(t,r,i,"⊝","\\circleddash"),u(t,r,i,"⊛","\\circledast"),u(t,r,i,"⋅","\\centerdot"),u(t,r,i,"⊺","\\intercal"),u(t,r,i,"⋒","\\doublecap"),u(t,r,i,"⋓","\\doublecup"),u(t,r,i,"⊠","\\boxtimes"),u(t,r,a,"⇢","\\dashrightarrow"),u(t,r,a,"⇠","\\dashleftarrow"),u(t,r,a,"⇇","\\leftleftarrows"),u(t,r,a,"⇆","\\leftrightarrows"),u(t,r,a,"⇚","\\Lleftarrow"),u(t,r,a,"↞","\\twoheadleftarrow"),u(t,r,a,"↢","\\leftarrowtail"),u(t,r,a,"↫","\\looparrowleft"),u(t,r,a,"⇋","\\leftrightharpoons"),u(t,r,a,"↶","\\curvearrowleft"),u(t,r,a,"↺","\\circlearrowleft"),u(t,r,a,"↰","\\Lsh"),u(t,r,a,"⇈","\\upuparrows"),u(t,r,a,"↿","\\upharpoonleft"),u(t,r,a,"⇃","\\downharpoonleft"),u(t,r,a,"⊸","\\multimap"),u(t,r,a,"↭","\\leftrightsquigarrow"),u(t,r,a,"⇉","\\rightrightarrows"),u(t,r,a,"⇄","\\rightleftarrows"),u(t,r,a,"↠","\\twoheadrightarrow"),u(t,r,a,"↣","\\rightarrowtail"),u(t,r,a,"↬","\\looparrowright"),u(t,r,a,"↷","\\curvearrowright"),u(t,r,a,"↻","\\circlearrowright"),u(t,r,a,"↱","\\Rsh"),u(t,r,a,"⇊","\\downdownarrows"),u(t,r,a,"↾","\\upharpoonright"),u(t,r,a,"⇂","\\downharpoonright"),u(t,r,a,"⇝","\\rightsquigarrow"),u(t,r,a,"⇝","\\leadsto"),u(t,r,a,"⇛","\\Rrightarrow"),u(t,r,a,"↾","\\restriction"),u(t,n,m,"‘","`"),u(t,n,m,"$","\\$"),u(t,n,m,"%","\\%"),u(t,n,m,"_","\\_"),u(t,n,m,"∠","\\angle"),u(t,n,m,"∞","\\infty"),u(t,n,m,"′","\\prime"),u(t,n,m,"△","\\triangle"),u(t,n,m,"Γ","\\Gamma"),u(t,n,m,"Δ","\\Delta"),u(t,n,m,"Θ","\\Theta"),u(t,n,m,"Λ","\\Lambda"),u(t,n,m,"Ξ","\\Xi"),u(t,n,m,"Π","\\Pi"),u(t,n,m,"Σ","\\Sigma"),u(t,n,m,"Υ","\\Upsilon"),u(t,n,m,"Φ","\\Phi"),u(t,n,m,"Ψ","\\Psi"),u(t,n,m,"Ω","\\Omega"),u(t,n,m,"¬","\\neg"),u(t,n,m,"¬","\\lnot"),u(t,n,m,"⊤","\\top"),u(t,n,m,"⊥","\\bot"),u(t,n,m,"∅","\\emptyset"),u(t,r,m,"∅","\\varnothing"),u(t,n,h,"α","\\alpha"),u(t,n,h,"β","\\beta"),u(t,n,h,"γ","\\gamma"),u(t,n,h,"δ","\\delta"),u(t,n,h,"ϵ","\\epsilon"),u(t,n,h,"ζ","\\zeta"),u(t,n,h,"η","\\eta"),u(t,n,h,"θ","\\theta"),u(t,n,h,"ι","\\iota"),u(t,n,h,"κ","\\kappa"),u(t,n,h,"λ","\\lambda"),u(t,n,h,"μ","\\mu"),u(t,n,h,"ν","\\nu"),u(t,n,h,"ξ","\\xi"),u(t,n,h,"o","\\omicron"),u(t,n,h,"π","\\pi"),u(t,n,h,"ρ","\\rho"),u(t,n,h,"σ","\\sigma"),u(t,n,h,"τ","\\tau"),u(t,n,h,"υ","\\upsilon"),u(t,n,h,"ϕ","\\phi"),u(t,n,h,"χ","\\chi"),u(t,n,h,"ψ","\\psi"),u(t,n,h,"ω","\\omega"),u(t,n,h,"ε","\\varepsilon"),u(t,n,h,"ϑ","\\vartheta"),u(t,n,h,"ϖ","\\varpi"),u(t,n,h,"ϱ","\\varrho"),u(t,n,h,"ς","\\varsigma"),u(t,n,h,"φ","\\varphi"),u(t,n,i,"∗","*"),u(t,n,i,"+","+"),u(t,n,i,"−","-"),u(t,n,i,"⋅","\\cdot"),u(t,n,i,"∘","\\circ"),u(t,n,i,"÷","\\div"),u(t,n,i,"±","\\pm"),u(t,n,i,"×","\\times"),u(t,n,i,"∩","\\cap"),u(t,n,i,"∪","\\cup"),u(t,n,i,"∖","\\setminus"),u(t,n,i,"∧","\\land"),u(t,n,i,"∨","\\lor"),u(t,n,i,"∧","\\wedge"),u(t,n,i,"∨","\\vee"),u(t,n,m,"√","\\surd"),u(t,n,_,"(","("),u(t,n,_,"[","["),u(t,n,_,"⟨","\\langle"),u(t,n,_,"∣","\\lvert"),u(t,n,_,"∥","\\lVert"),u(t,n,s,")",")"),u(t,n,s,"]","]"),u(t,n,s,"?","?"),u(t,n,s,"!","!"),u(t,n,s,"⟩","\\rangle"),u(t,n,s,"∣","\\rvert"),u(t,n,s,"∥","\\rVert"),u(t,n,a,"=","="),u(t,n,a,"<","<"),u(t,n,a,">",">"),u(t,n,a,":",":"),u(t,n,a,"≈","\\approx"),u(t,n,a,"≅","\\cong"),u(t,n,a,"≥","\\ge"),u(t,n,a,"≥","\\geq"),u(t,n,a,"←","\\gets"),u(t,n,a,">","\\gt"),u(t,n,a,"∈","\\in"),u(t,n,a,"∉","\\notin"),u(t,n,a,"⊂","\\subset"),u(t,n,a,"⊃","\\supset"),u(t,n,a,"⊆","\\subseteq"),u(t,n,a,"⊇","\\supseteq"),u(t,r,a,"⊈","\\nsubseteq"),u(t,r,a,"⊉","\\nsupseteq"),u(t,n,a,"⊨","\\models"),u(t,n,a,"←","\\leftarrow"),u(t,n,a,"≤","\\le"),u(t,n,a,"≤","\\leq"),u(t,n,a,"<","\\lt"),u(t,n,a,"≠","\\ne"),u(t,n,a,"≠","\\neq"),u(t,n,a,"→","\\rightarrow"),u(t,n,a,"→","\\to"),u(t,r,a,"≱","\\ngeq"),u(t,r,a,"≰","\\nleq"),u(t,n,b,null,"\\!"),u(t,n,b," ","\\ "),u(t,n,b," ","~"),u(t,n,b,null,"\\,"),u(t,n,b,null,"\\:"),u(t,n,b,null,"\\;"),u(t,n,b,null,"\\enspace"),u(t,n,b,null,"\\qquad"),u(t,n,b,null,"\\quad"),u(t,n,b," ","\\space"),u(t,n,A,",",","),u(t,n,A,";",";"),u(t,n,A,":","\\colon"),u(t,r,i,"⊼","\\barwedge"),u(t,r,i,"⊻","\\veebar"),u(t,n,i,"⊙","\\odot"),u(t,n,i,"⊕","\\oplus"),u(t,n,i,"⊗","\\otimes"),u(t,n,m,"∂","\\partial"),u(t,n,i,"⊘","\\oslash"),u(t,r,i,"⊚","\\circledcirc"),u(t,r,i,"⊡","\\boxdot"),u(t,n,i,"△","\\bigtriangleup"),u(t,n,i,"▽","\\bigtriangledown"),u(t,n,i,"†","\\dagger"),u(t,n,i,"⋄","\\diamond"),u(t,n,i,"⋆","\\star"),u(t,n,i,"◃","\\triangleleft"),u(t,n,i,"▹","\\triangleright"),u(t,n,_,"{","\\{"),u(t,n,s,"}","\\}"),u(t,n,_,"{","\\lbrace"),u(t,n,s,"}","\\rbrace"),u(t,n,_,"[","\\lbrack"),u(t,n,s,"]","\\rbrack"),u(t,n,_,"⌊","\\lfloor"),u(t,n,s,"⌋","\\rfloor"),u(t,n,_,"⌈","\\lceil"),u(t,n,s,"⌉","\\rceil"),u(t,n,m,"\\","\\backslash"),u(t,n,m,"∣","|"),u(t,n,m,"∣","\\vert"),u(t,n,m,"∥","\\|"),u(t,n,m,"∥","\\Vert"),u(t,n,a,"↑","\\uparrow"),u(t,n,a,"⇑","\\Uparrow"),u(t,n,a,"↓","\\downarrow"),u(t,n,a,"⇓","\\Downarrow"),u(t,n,a,"↕","\\updownarrow"),u(t,n,a,"⇕","\\Updownarrow"),u(t,t,v,"∐","\\coprod"),u(t,t,v,"⋁","\\bigvee"),u(t,t,v,"⋀","\\bigwedge"),u(t,t,v,"⨄","\\biguplus"),u(t,t,v,"⋂","\\bigcap"),u(t,t,v,"⋃","\\bigcup"),u(t,t,v,"∫","\\int"),u(t,t,v,"∫","\\intop"),u(t,t,v,"∬","\\iint"),u(t,t,v,"∭","\\iiint"),u(t,t,v,"∏","\\prod"),u(t,t,v,"∑","\\sum"),u(t,t,v,"⨂","\\bigotimes"),u(t,t,v,"⨁","\\bigoplus"),u(t,t,v,"⨀","\\bigodot"),u(t,t,v,"∮","\\oint"),u(t,t,v,"⨆","\\bigsqcup"),u(t,t,v,"∫","\\smallint"),u(t,n,f,"…","\\ldots"),u(t,n,f,"⋯","\\cdots"),u(t,n,f,"⋱","\\ddots"),u(t,n,m,"⋮","\\vdots"),u(t,n,c,"´","\\acute"),u(t,n,c,"`","\\grave"),u(t,n,c,"¨","\\ddot"),u(t,n,c,"~","\\tilde"),u(t,n,c,"¯","\\bar"),u(t,n,c,"˘","\\breve"),u(t,n,c,"ˇ","\\check"),u(t,n,c,"^","\\hat"),u(t,n,c,"⃗","\\vec"),u(t,n,c,"˙","\\dot"),u(t,n,h,"ı","\\imath"),u(t,n,h,"ȷ","\\jmath"),u(o,n,b," ","\\ "),u(o,n,b," "," "),u(o,n,b," ","~");var S,p,d='0123456789/@."';for(S=0;S<d.length;S++)p=d.charAt(S),u(t,n,m,p,p);var w="0123456789`!@*()-=+[]'\";:?/.,";for(S=0;S<w.length;S++)p=w.charAt(S),u(o,n,m,p,p);var y="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";for(S=0;S<y.length;S++)p=y.charAt(S),u(t,n,h,p,p),u(o,n,m,p,p)}(Q0)),Q0.exports}var Y0,tt;function T0(){if(tt)return Y0;tt=1;var e=Xt(),u=h0(),t=R0(),o=Ue(),n=["\\Gamma","\\Delta","\\Theta","\\Lambda","\\Xi","\\Pi","\\Sigma","\\Upsilon","\\Phi","\\Psi","\\Omega"],r=["ı","ȷ"],c=function(d,w,y,k,M){t[y][d]&&t[y][d].replace&&(d=t[y][d].replace);var L=u.getCharacterMetrics(d,w),q;return L?q=new e.symbolNode(d,L.height,L.depth,L.italic,L.skew,M):(typeof console<"u"&&console.warn("No character metrics for '"+d+"' in style '"+w+"'"),q=new e.symbolNode(d,0,0,0,0,M)),k&&(q.style.color=k),q},i=function(d,w,y,k){return d==="\\"||t[w][d].font==="main"?c(d,"Main-Regular",w,y,k):c(d,"AMS-Regular",w,y,k.concat(["amsrm"]))},s=function(d,w,y,k,M){if(M==="mathord")return f(d,w,y,k);if(M==="textord")return c(d,"Main-Regular",w,y,k.concat(["mathrm"]));throw new Error("unexpected type: "+M+" in mathDefault")},f=function(d,w,y,k){return/[0-9]/.test(d.charAt(0))||o.contains(r,d)||o.contains(n,d)?c(d,"Main-Italic",w,y,k.concat(["mainit"])):c(d,"Math-Italic",w,y,k.concat(["mathit"]))},h=function(d,w,y){var k=d.mode,M=d.value;t[k][M]&&t[k][M].replace&&(M=t[k][M].replace);var L=["mord"],q=w.getColor(),D=w.font;if(D){if(D==="mathit"||o.contains(r,M))return f(M,k,q,L);var Z=p[D].fontName;return u.getCharacterMetrics(M,Z)?c(M,Z,k,q,L.concat([D])):s(M,k,q,L,y)}else return s(M,k,q,L,y)},v=function(d){var w=0,y=0,k=0;if(d.children)for(var M=0;M<d.children.length;M++)d.children[M].height>w&&(w=d.children[M].height),d.children[M].depth>y&&(y=d.children[M].depth),d.children[M].maxFontSize>k&&(k=d.children[M].maxFontSize);d.height=w,d.depth=y,d.maxFontSize=k},_=function(d,w,y){var k=new e.span(d,w);return v(k),y&&(k.style.color=y),k},A=function(d){var w=new e.documentFragment(d);return v(w),w},a=function(d,w){var y=_([],[new e.symbolNode("​")]);y.style.fontSize=w/d.style.sizeMultiplier+"em";var k=_(["fontsize-ensurer","reset-"+d.size,"size5"],[y]);return k},b=function(d,w,y,k){var M,L,q;if(w==="individualShift"){var D=d;for(d=[D[0]],M=-D[0].shift-D[0].elem.depth,L=M,q=1;q<D.length;q++){var Z=-D[q].shift-L-D[q].elem.depth,G=Z-(D[q-1].elem.height+D[q-1].elem.depth);L=L+Z,d.push({type:"kern",size:G}),d.push(D[q])}}else if(w==="top"){var Q=y;for(q=0;q<d.length;q++)d[q].type==="kern"?Q-=d[q].size:Q-=d[q].elem.height+d[q].elem.depth;M=Q}else w==="bottom"?M=-y:w==="shift"?M=-d[0].elem.depth-y:w==="firstBaseline"?M=-d[0].elem.depth:M=0;var X=0;for(q=0;q<d.length;q++)d[q].type==="elem"&&(X=Math.max(X,d[q].elem.maxFontSize));var T=a(k,X),N=[];for(L=M,q=0;q<d.length;q++)if(d[q].type==="kern")L+=d[q].size;else{var H=d[q].elem,ee=-H.depth-L;L+=H.height+H.depth;var te=_([],[T,H]);te.height-=ee,te.depth+=ee,te.style.top=ee+"em",N.push(te)}var g=_(["baseline-fix"],[T,new e.symbolNode("​")]);N.push(g);var l=_(["vlist"],N);return l.height=Math.max(L,l.height),l.depth=Math.max(-M,l.depth),l},m={size1:.5,size2:.7,size3:.8,size4:.9,size5:1,size6:1.2,size7:1.44,size8:1.73,size9:2.07,size10:2.49},S={"\\qquad":{size:"2em",className:"qquad"},"\\quad":{size:"1em",className:"quad"},"\\enspace":{size:"0.5em",className:"enspace"},"\\;":{size:"0.277778em",className:"thickspace"},"\\:":{size:"0.22222em",className:"mediumspace"},"\\,":{size:"0.16667em",className:"thinspace"},"\\!":{size:"-0.16667em",className:"negativethinspace"}},p={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}};return Y0={fontMap:p,makeSymbol:c,mathsym:i,makeSpan:_,makeFragment:A,makeVList:b,makeOrd:h,sizingMultiplier:m,spacingFunctions:S},Y0}var eu,rt;function ca(){if(rt)return eu;rt=1;var e=Ze(),u=F0(),t=T0(),o=h0(),n=R0(),r=Ue(),c=t.makeSpan,i=function(D,Z){return n.math[D]&&n.math[D].replace?o.getCharacterMetrics(n.math[D].replace,Z):o.getCharacterMetrics(D,Z)},s=function(D,Z,G){return t.makeSymbol(D,"Size"+Z+"-Regular",G)},f=function(D,Z,G){var Q=c(["style-wrap",G.style.reset(),Z.cls()],[D]),X=Z.sizeMultiplier/G.style.sizeMultiplier;return Q.height*=X,Q.depth*=X,Q.maxFontSize=Z.sizeMultiplier,Q},h=function(D,Z,G,Q,X){var T=t.makeSymbol(D,"Main-Regular",X),N=f(T,Z,Q);if(G){var H=(1-Q.style.sizeMultiplier/Z.sizeMultiplier)*o.metrics.axisHeight;N.style.top=H+"em",N.height-=H,N.depth+=H}return N},v=function(D,Z,G,Q,X){var T=s(D,Z,X),N=f(c(["delimsizing","size"+Z],[T],Q.getColor()),u.TEXT,Q);if(G){var H=(1-Q.style.sizeMultiplier)*o.metrics.axisHeight;N.style.top=H+"em",N.height-=H,N.depth+=H}return N},_=function(D,Z,G){var Q;Z==="Size1-Regular"?Q="delim-size1":Z==="Size4-Regular"&&(Q="delim-size4");var X=c(["delimsizinginner",Q],[c([],[t.makeSymbol(D,Z,G)])]);return{type:"elem",elem:X}},A=function(D,Z,G,Q,X){var T,N,H,ee;T=H=ee=D,N=null;var te="Size1-Regular";D==="\\uparrow"?H=ee="⏐":D==="\\Uparrow"?H=ee="‖":D==="\\downarrow"?T=H="⏐":D==="\\Downarrow"?T=H="‖":D==="\\updownarrow"?(T="\\uparrow",H="⏐",ee="\\downarrow"):D==="\\Updownarrow"?(T="\\Uparrow",H="‖",ee="\\Downarrow"):D==="["||D==="\\lbrack"?(T="⎡",H="⎢",ee="⎣",te="Size4-Regular"):D==="]"||D==="\\rbrack"?(T="⎤",H="⎥",ee="⎦",te="Size4-Regular"):D==="\\lfloor"?(H=T="⎢",ee="⎣",te="Size4-Regular"):D==="\\lceil"?(T="⎡",H=ee="⎢",te="Size4-Regular"):D==="\\rfloor"?(H=T="⎥",ee="⎦",te="Size4-Regular"):D==="\\rceil"?(T="⎤",H=ee="⎥",te="Size4-Regular"):D==="("?(T="⎛",H="⎜",ee="⎝",te="Size4-Regular"):D===")"?(T="⎞",H="⎟",ee="⎠",te="Size4-Regular"):D==="\\{"||D==="\\lbrace"?(T="⎧",N="⎨",ee="⎩",H="⎪",te="Size4-Regular"):D==="\\}"||D==="\\rbrace"?(T="⎫",N="⎬",ee="⎭",H="⎪",te="Size4-Regular"):D==="\\lgroup"?(T="⎧",ee="⎩",H="⎪",te="Size4-Regular"):D==="\\rgroup"?(T="⎫",ee="⎭",H="⎪",te="Size4-Regular"):D==="\\lmoustache"?(T="⎧",ee="⎭",H="⎪",te="Size4-Regular"):D==="\\rmoustache"?(T="⎫",ee="⎩",H="⎪",te="Size4-Regular"):D==="\\surd"&&(T="",ee="⎷",H="",te="Size4-Regular");var g=i(T,te),l=g.height+g.depth,x=i(H,te),E=x.height+x.depth,R=i(ee,te),z=R.height+R.depth,I=0,C=1;if(N!==null){var j=i(N,te);I=j.height+j.depth,C=2}var $=l+z+I,U=Math.ceil((Z-$)/(C*E)),J=$+U*C*E,V=o.metrics.axisHeight;G&&(V*=Q.style.sizeMultiplier);var W=J/2-V,O=[];O.push(_(ee,te,X));var Y;if(N===null)for(Y=0;Y<U;Y++)O.push(_(H,te,X));else{for(Y=0;Y<U;Y++)O.push(_(H,te,X));for(O.push(_(N,te,X)),Y=0;Y<U;Y++)O.push(_(H,te,X))}O.push(_(T,te,X));var ie=t.makeVList(O,"bottom",W,Q);return f(c(["delimsizing","mult"],[ie],Q.getColor()),u.TEXT,Q)},a=["(",")","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","\\lceil","\\rceil","\\surd"],b=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","\\lmoustache","\\rmoustache"],m=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],S=[0,1.2,1.8,2.4,3],p=function(D,Z,G,Q){if(D==="<"||D==="\\lt"?D="\\langle":(D===">"||D==="\\gt")&&(D="\\rangle"),r.contains(a,D)||r.contains(m,D))return v(D,Z,!1,G,Q);if(r.contains(b,D))return A(D,S[Z],!1,G,Q);throw new e("Illegal delimiter: '"+D+"'")},d=[{type:"small",style:u.SCRIPTSCRIPT},{type:"small",style:u.SCRIPT},{type:"small",style:u.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],w=[{type:"small",style:u.SCRIPTSCRIPT},{type:"small",style:u.SCRIPT},{type:"small",style:u.TEXT},{type:"stack"}],y=[{type:"small",style:u.SCRIPTSCRIPT},{type:"small",style:u.SCRIPT},{type:"small",style:u.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],k=function(D){if(D.type==="small")return"Main-Regular";if(D.type==="large")return"Size"+D.size+"-Regular";if(D.type==="stack")return"Size4-Regular"},M=function(D,Z,G,Q){for(var X=Math.min(2,3-Q.style.size),T=X;T<G.length&&G[T].type!=="stack";T++){var N=i(D,k(G[T])),H=N.height+N.depth;if(G[T].type==="small"&&(H*=G[T].style.sizeMultiplier),H>Z)return G[T]}return G[G.length-1]},L=function(D,Z,G,Q,X){D==="<"||D==="\\lt"?D="\\langle":(D===">"||D==="\\gt")&&(D="\\rangle");var T;r.contains(m,D)?T=d:r.contains(a,D)?T=y:T=w;var N=M(D,Z,T,Q);if(N.type==="small")return h(D,N.style,G,Q,X);if(N.type==="large")return v(D,N.size,G,Q,X);if(N.type==="stack")return A(D,Z,G,Q,X)},q=function(D,Z,G,Q,X){var T=o.metrics.axisHeight*Q.style.sizeMultiplier,N=901,H=5/o.metrics.ptPerEm,ee=Math.max(Z-T,G+T),te=Math.max(ee/500*N,2*ee-H);return L(D,te,!0,Q,X)};return eu={sizedDelim:p,customSizedDelim:L,leftRightDelim:q},eu}var uu,nt;function la(){if(nt)return uu;nt=1;var e=Ze(),u=F0(),t=T0(),o=ca(),n=Xt(),r=h0(),c=Ue(),i=t.makeSpan,s=function(p,d,w){for(var y=[],k=0;k<p.length;k++){var M=p[k];y.push(m(M,d,w)),w=M}return y},f={mathord:"mord",textord:"mord",bin:"mbin",rel:"mrel",text:"mord",open:"mopen",close:"mclose",inner:"minner",genfrac:"mord",array:"mord",spacing:"mord",punct:"mpunct",ordgroup:"mord",op:"mop",katex:"mord",overline:"mord",underline:"mord",rule:"mord",leftright:"minner",sqrt:"mord",accent:"mord"},h=function(p){return p==null?f.mathord:p.type==="supsub"?h(p.value.base):p.type==="llap"||p.type==="rlap"?h(p.value):p.type==="color"||p.type==="sizing"||p.type==="styling"?h(p.value.value):p.type==="delimsizing"?f[p.value.delimType]:f[p.type]},v=function(p,d){return p?p.type==="op"?p.value.limits&&(d.style.size===u.DISPLAY.size||p.value.alwaysHandleSupSub):p.type==="accent"?A(p.value.base):null:!1},_=function(p){return p?p.type==="ordgroup"?p.value.length===1?_(p.value[0]):p:p.type==="color"&&p.value.value.length===1?_(p.value.value[0]):p:!1},A=function(p){var d=_(p);return d.type==="mathord"||d.type==="textord"||d.type==="bin"||d.type==="rel"||d.type==="inner"||d.type==="open"||d.type==="close"||d.type==="punct"},a=function(p){return i(["sizing","reset-"+p.size,"size5",p.style.reset(),u.TEXT.cls(),"nulldelimiter"])},b={};b.mathord=function(p,d,w){return t.makeOrd(p,d,"mathord")},b.textord=function(p,d,w){return t.makeOrd(p,d,"textord")},b.bin=function(p,d,w){for(var y="mbin",k=w;k&&k.type==="color";){var M=k.value.value;k=M[M.length-1]}return(!w||c.contains(["mbin","mopen","mrel","mop","mpunct"],h(k)))&&(p.type="textord",y="mord"),t.mathsym(p.value,p.mode,d.getColor(),[y])},b.rel=function(p,d,w){return t.mathsym(p.value,p.mode,d.getColor(),["mrel"])},b.open=function(p,d,w){return t.mathsym(p.value,p.mode,d.getColor(),["mopen"])},b.close=function(p,d,w){return t.mathsym(p.value,p.mode,d.getColor(),["mclose"])},b.inner=function(p,d,w){return t.mathsym(p.value,p.mode,d.getColor(),["minner"])},b.punct=function(p,d,w){return t.mathsym(p.value,p.mode,d.getColor(),["mpunct"])},b.ordgroup=function(p,d,w){return i(["mord",d.style.cls()],s(p.value,d.reset()))},b.text=function(p,d,w){return i(["text","mord",d.style.cls()],s(p.value.body,d.reset()))},b.color=function(p,d,w){var y=s(p.value.value,d.withColor(p.value.color),w);return new t.makeFragment(y)},b.supsub=function(p,d,w){if(v(p.value.base,d))return b[p.value.base.type](p,d,w);var y=m(p.value.base,d.reset()),k,M,L,q;p.value.sup&&(L=m(p.value.sup,d.withStyle(d.style.sup())),k=i([d.style.reset(),d.style.sup().cls()],[L])),p.value.sub&&(q=m(p.value.sub,d.withStyle(d.style.sub())),M=i([d.style.reset(),d.style.sub().cls()],[q]));var D,Z;A(p.value.base)?(D=0,Z=0):(D=y.height-r.metrics.supDrop,Z=y.depth+r.metrics.subDrop);var G;d.style===u.DISPLAY?G=r.metrics.sup1:d.style.cramped?G=r.metrics.sup3:G=r.metrics.sup2;var Q=u.TEXT.sizeMultiplier*d.style.sizeMultiplier,X=.5/r.metrics.ptPerEm/Q+"em",T;if(!p.value.sup)Z=Math.max(Z,r.metrics.sub1,q.height-.8*r.metrics.xHeight),T=t.makeVList([{type:"elem",elem:M}],"shift",Z,d),T.children[0].style.marginRight=X,y instanceof n.symbolNode&&(T.children[0].style.marginLeft=-y.italic+"em");else if(!p.value.sub)D=Math.max(D,G,L.depth+.25*r.metrics.xHeight),T=t.makeVList([{type:"elem",elem:k}],"shift",-D,d),T.children[0].style.marginRight=X;else{D=Math.max(D,G,L.depth+.25*r.metrics.xHeight),Z=Math.max(Z,r.metrics.sub2);var N=r.metrics.defaultRuleThickness;if(D-L.depth-(q.height-Z)<4*N){Z=4*N-(D-L.depth)+q.height;var H=.8*r.metrics.xHeight-(D-L.depth);H>0&&(D+=H,Z-=H)}T=t.makeVList([{type:"elem",elem:M,shift:Z},{type:"elem",elem:k,shift:-D}],"individualShift",null,d),y instanceof n.symbolNode&&(T.children[0].style.marginLeft=-y.italic+"em"),T.children[0].style.marginRight=X,T.children[1].style.marginRight=X}return i([h(p.value.base)],[y,T])},b.genfrac=function(p,d,w){var y=d.style;p.value.size==="display"?y=u.DISPLAY:p.value.size==="text"&&(y=u.TEXT);var k=y.fracNum(),M=y.fracDen(),L=m(p.value.numer,d.withStyle(k)),q=i([y.reset(),k.cls()],[L]),D=m(p.value.denom,d.withStyle(M)),Z=i([y.reset(),M.cls()],[D]),G;p.value.hasBarLine?G=r.metrics.defaultRuleThickness/d.style.sizeMultiplier:G=0;var Q,X,T;y.size===u.DISPLAY.size?(Q=r.metrics.num1,G>0?X=3*G:X=7*r.metrics.defaultRuleThickness,T=r.metrics.denom1):(G>0?(Q=r.metrics.num2,X=G):(Q=r.metrics.num3,X=3*r.metrics.defaultRuleThickness),T=r.metrics.denom2);var N;if(G===0){var H=Q-L.depth-(D.height-T);H<X&&(Q+=.5*(X-H),T+=.5*(X-H)),N=t.makeVList([{type:"elem",elem:Z,shift:T},{type:"elem",elem:q,shift:-Q}],"individualShift",null,d)}else{var ee=r.metrics.axisHeight;Q-L.depth-(ee+.5*G)<X&&(Q+=X-(Q-L.depth-(ee+.5*G))),ee-.5*G-(D.height-T)<X&&(T+=X-(ee-.5*G-(D.height-T)));var te=i([d.style.reset(),u.TEXT.cls(),"frac-line"]);te.height=G;var g=-(ee-.5*G);N=t.makeVList([{type:"elem",elem:Z,shift:T},{type:"elem",elem:te,shift:g},{type:"elem",elem:q,shift:-Q}],"individualShift",null,d)}N.height*=y.sizeMultiplier/d.style.sizeMultiplier,N.depth*=y.sizeMultiplier/d.style.sizeMultiplier;var l;y.size===u.DISPLAY.size?l=r.metrics.delim1:l=r.metrics.getDelim2(y);var x,E;return p.value.leftDelim==null?x=a(d):x=o.customSizedDelim(p.value.leftDelim,l,!0,d.withStyle(y),p.mode),p.value.rightDelim==null?E=a(d):E=o.customSizedDelim(p.value.rightDelim,l,!0,d.withStyle(y),p.mode),i(["mord",d.style.reset(),y.cls()],[x,i(["mfrac"],[N]),E],d.getColor())},b.array=function(p,d,w){var y,k,M=p.value.body.length,L=0,q=new Array(M),D=1/r.metrics.ptPerEm,Z=5*D,G=12*D,Q=c.deflt(p.value.arraystretch,1),X=Q*G,T=.7*X,N=.3*X,H=0;for(y=0;y<p.value.body.length;++y){var ee=p.value.body[y],te=T,g=N;L<ee.length&&(L=ee.length);var l=new Array(ee.length);for(k=0;k<ee.length;++k){var x=m(ee[k],d);g<x.depth&&(g=x.depth),te<x.height&&(te=x.height),l[k]=x}var E=0;if(p.value.rowGaps[y]){switch(E=p.value.rowGaps[y].value,E.unit){case"em":E=E.number;break;case"ex":E=E.number*r.metrics.emPerEx;break;default:console.error("Can't handle unit "+E.unit),E=0}E>0&&(E+=N,g<E&&(g=E),E=0)}l.height=te,l.depth=g,H+=te,l.pos=H,H+=g+E,q[y]=l}var R=H/2+r.metrics.axisHeight,z=p.value.cols||[],I=[],C,j;for(k=0,j=0;k<L||j<z.length;++k,++j){for(var $=z[j]||{},U=!0;$.type==="separator";){if(U||(C=i(["arraycolsep"],[]),C.style.width=r.metrics.doubleRuleSep+"em",I.push(C)),$.separator==="|"){var J=i(["vertical-separator"],[]);J.style.height=H+"em",J.style.verticalAlign=-(H-R)+"em",I.push(J)}else throw new e("Invalid separator type: "+$.separator);j++,$=z[j]||{},U=!1}if(!(k>=L)){var V;(k>0||p.value.hskipBeforeAndAfter)&&(V=c.deflt($.pregap,Z),V!==0&&(C=i(["arraycolsep"],[]),C.style.width=V+"em",I.push(C)));var W=[];for(y=0;y<M;++y){var O=q[y],Y=O[k];if(Y){var ie=O.pos-R;Y.depth=O.depth,Y.height=O.height,W.push({type:"elem",elem:Y,shift:ie})}}W=t.makeVList(W,"individualShift",null,d),W=i(["col-align-"+($.align||"c")],[W]),I.push(W),(k<L-1||p.value.hskipBeforeAndAfter)&&(V=c.deflt($.postgap,Z),V!==0&&(C=i(["arraycolsep"],[]),C.style.width=V+"em",I.push(C)))}}return q=i(["mtable"],I),i(["mord"],[q],d.getColor())},b.spacing=function(p,d,w){return p.value==="\\ "||p.value==="\\space"||p.value===" "||p.value==="~"?i(["mord","mspace"],[t.mathsym(p.value,p.mode)]):i(["mord","mspace",t.spacingFunctions[p.value].className])},b.llap=function(p,d,w){var y=i(["inner"],[m(p.value.body,d.reset())]),k=i(["fix"],[]);return i(["llap",d.style.cls()],[y,k])},b.rlap=function(p,d,w){var y=i(["inner"],[m(p.value.body,d.reset())]),k=i(["fix"],[]);return i(["rlap",d.style.cls()],[y,k])},b.op=function(p,d,w){var y,k,M=!1;p.type==="supsub"&&(y=p.value.sup,k=p.value.sub,p=p.value.base,M=!0);var L=["\\smallint"],q=!1;d.style.size===u.DISPLAY.size&&p.value.symbol&&!c.contains(L,p.value.body)&&(q=!0);var D,Z=0,G=0;if(p.value.symbol){var Q=q?"Size2-Regular":"Size1-Regular";D=t.makeSymbol(p.value.body,Q,"math",d.getColor(),["op-symbol",q?"large-op":"small-op","mop"]),Z=(D.height-D.depth)/2-r.metrics.axisHeight*d.style.sizeMultiplier,G=D.italic}else{for(var X=[],T=1;T<p.value.body.length;T++)X.push(t.mathsym(p.value.body[T],p.mode));D=i(["mop"],X,d.getColor())}if(M){D=i([],[D]);var N,H,ee,te;if(y){var g=m(y,d.withStyle(d.style.sup()));N=i([d.style.reset(),d.style.sup().cls()],[g]),H=Math.max(r.metrics.bigOpSpacing1,r.metrics.bigOpSpacing3-g.depth)}if(k){var l=m(k,d.withStyle(d.style.sub()));ee=i([d.style.reset(),d.style.sub().cls()],[l]),te=Math.max(r.metrics.bigOpSpacing2,r.metrics.bigOpSpacing4-l.height)}var x,E,R;if(!y)E=D.height-Z,x=t.makeVList([{type:"kern",size:r.metrics.bigOpSpacing5},{type:"elem",elem:ee},{type:"kern",size:te},{type:"elem",elem:D}],"top",E,d),x.children[0].style.marginLeft=-G+"em";else if(!k)R=D.depth+Z,x=t.makeVList([{type:"elem",elem:D},{type:"kern",size:H},{type:"elem",elem:N},{type:"kern",size:r.metrics.bigOpSpacing5}],"bottom",R,d),x.children[1].style.marginLeft=G+"em";else{if(!y&&!k)return D;R=r.metrics.bigOpSpacing5+ee.height+ee.depth+te+D.depth+Z,x=t.makeVList([{type:"kern",size:r.metrics.bigOpSpacing5},{type:"elem",elem:ee},{type:"kern",size:te},{type:"elem",elem:D},{type:"kern",size:H},{type:"elem",elem:N},{type:"kern",size:r.metrics.bigOpSpacing5}],"bottom",R,d),x.children[0].style.marginLeft=-G+"em",x.children[2].style.marginLeft=G+"em"}return i(["mop","op-limits"],[x])}else return p.value.symbol&&(D.style.top=Z+"em"),D},b.katex=function(p,d,w){var y=i(["k"],[t.mathsym("K",p.mode)]),k=i(["a"],[t.mathsym("A",p.mode)]);k.height=(k.height+.2)*.75,k.depth=(k.height-.2)*.75;var M=i(["t"],[t.mathsym("T",p.mode)]),L=i(["e"],[t.mathsym("E",p.mode)]);L.height=L.height-.2155,L.depth=L.depth+.2155;var q=i(["x"],[t.mathsym("X",p.mode)]);return i(["katex-logo","mord"],[y,k,M,L,q],d.getColor())},b.overline=function(p,d,w){var y=m(p.value.body,d.withStyle(d.style.cramp())),k=r.metrics.defaultRuleThickness/d.style.sizeMultiplier,M=i([d.style.reset(),u.TEXT.cls(),"overline-line"]);M.height=k,M.maxFontSize=1;var L=t.makeVList([{type:"elem",elem:y},{type:"kern",size:3*k},{type:"elem",elem:M},{type:"kern",size:k}],"firstBaseline",null,d);return i(["overline","mord"],[L],d.getColor())},b.underline=function(p,d,w){var y=m(p.value.body,d),k=r.metrics.defaultRuleThickness/d.style.sizeMultiplier,M=i([d.style.reset(),u.TEXT.cls(),"underline-line"]);M.height=k,M.maxFontSize=1;var L=t.makeVList([{type:"kern",size:k},{type:"elem",elem:M},{type:"kern",size:3*k},{type:"elem",elem:y}],"top",y.height,d);return i(["underline","mord"],[L],d.getColor())},b.sqrt=function(p,d,w){var y=m(p.value.body,d.withStyle(d.style.cramp())),k=r.metrics.defaultRuleThickness/d.style.sizeMultiplier,M=i([d.style.reset(),u.TEXT.cls(),"sqrt-line"],[],d.getColor());M.height=k,M.maxFontSize=1;var L=k;d.style.id<u.TEXT.id&&(L=r.metrics.xHeight);var q=k+L/4,D=(y.height+y.depth)*d.style.sizeMultiplier,Z=D+q+k,G=i(["sqrt-sign"],[o.customSizedDelim("\\surd",Z,!1,d,p.mode)],d.getColor()),Q=G.height+G.depth-k;Q>y.height+y.depth+q&&(q=(q+Q-y.height-y.depth)/2);var X=-(y.height+q+k)+G.height;G.style.top=X+"em",G.height-=X,G.depth+=X;var T;if(y.height===0&&y.depth===0?T=i():T=t.makeVList([{type:"elem",elem:y},{type:"kern",size:q},{type:"elem",elem:M},{type:"kern",size:k}],"firstBaseline",null,d),p.value.index){var N=m(p.value.index,d.withStyle(u.SCRIPTSCRIPT)),H=i([d.style.reset(),u.SCRIPTSCRIPT.cls()],[N]),ee=Math.max(G.height,T.height),te=Math.max(G.depth,T.depth),g=.6*(ee-te),l=t.makeVList([{type:"elem",elem:H}],"shift",-g,d),x=i(["root"],[l]);return i(["sqrt","mord"],[x,G,T])}else return i(["sqrt","mord"],[G,T])},b.sizing=function(p,d,w){var y=s(p.value.value,d.withSize(p.value.size),w),k=i(["mord"],[i(["sizing","reset-"+d.size,p.value.size,d.style.cls()],y)]),M=t.sizingMultiplier[p.value.size];return k.maxFontSize=M*d.style.sizeMultiplier,k},b.styling=function(p,d,w){var y={display:u.DISPLAY,text:u.TEXT,script:u.SCRIPT,scriptscript:u.SCRIPTSCRIPT},k=y[p.value.style],M=s(p.value.value,d.withStyle(k),w);return i([d.style.reset(),k.cls()],M)},b.font=function(p,d,w){var y=p.value.font;return m(p.value.body,d.withFont(y),w)},b.delimsizing=function(p,d,w){var y=p.value.value;return y==="."?i([f[p.value.delimType]]):i([f[p.value.delimType]],[o.sizedDelim(y,p.value.size,d,p.mode)])},b.leftright=function(p,d,w){for(var y=s(p.value.body,d.reset()),k=0,M=0,L=0;L<y.length;L++)k=Math.max(y[L].height,k),M=Math.max(y[L].depth,M);k*=d.style.sizeMultiplier,M*=d.style.sizeMultiplier;var q;p.value.left==="."?q=a(d):q=o.leftRightDelim(p.value.left,k,M,d,p.mode),y.unshift(q);var D;return p.value.right==="."?D=a(d):D=o.leftRightDelim(p.value.right,k,M,d,p.mode),y.push(D),i(["minner",d.style.cls()],y,d.getColor())},b.rule=function(p,d,w){var y=i(["mord","rule"],[],d.getColor()),k=0;p.value.shift&&(k=p.value.shift.number,p.value.shift.unit==="ex"&&(k*=r.metrics.xHeight));var M=p.value.width.number;p.value.width.unit==="ex"&&(M*=r.metrics.xHeight);var L=p.value.height.number;return p.value.height.unit==="ex"&&(L*=r.metrics.xHeight),k/=d.style.sizeMultiplier,M/=d.style.sizeMultiplier,L/=d.style.sizeMultiplier,y.style.borderRightWidth=M+"em",y.style.borderTopWidth=L+"em",y.style.bottom=k+"em",y.width=M,y.height=L+k,y.depth=-k,y},b.accent=function(p,d,w){var y=p.value.base,k;if(p.type==="supsub"){var M=p;p=M.value.base,y=p.value.base,M.value.base=y,k=m(M,d.reset(),w)}var L=m(y,d.withStyle(d.style.cramp())),q;if(A(y)){var D=_(y),Z=m(D,d.withStyle(d.style.cramp()));q=Z.skew}else q=0;var G=Math.min(L.height,r.metrics.xHeight),Q=t.makeSymbol(p.value.accent,"Main-Regular","math",d.getColor());Q.italic=0;var X=p.value.accent==="\\vec"?"accent-vec":null,T=i(["accent-body",X],[i([],[Q])]);T=t.makeVList([{type:"elem",elem:L},{type:"kern",size:-G},{type:"elem",elem:T}],"firstBaseline",null,d),T.children[1].style.marginLeft=2*q+"em";var N=i(["mord","accent"],[T]);return k?(k.children[0]=N,k.height=Math.max(N.height,k.height),k.classes[0]="mord",k):N},b.phantom=function(p,d,w){var y=s(p.value.value,d.withPhantom(),w);return new t.makeFragment(y)};var m=function(p,d,w){if(!p)return i();if(b[p.type]){var y=b[p.type](p,d,w),k;return d.style!==d.parentStyle&&(k=d.style.sizeMultiplier/d.parentStyle.sizeMultiplier,y.height*=k,y.depth*=k),d.size!==d.parentSize&&(k=t.sizingMultiplier[d.size]/t.sizingMultiplier[d.parentSize],y.height*=k,y.depth*=k),y}else throw new e("Got group of unknown type: '"+p.type+"'")},S=function(p,d){p=JSON.parse(JSON.stringify(p));var w=s(p,d),y=i(["base",d.style.cls()],w),k=i(["strut"]),M=i(["strut","bottom"]);k.style.height=y.height+"em",M.style.height=y.height+y.depth+"em",M.style.verticalAlign=-y.depth+"em";var L=i(["katex-html"],[k,M,y]);return L.setAttribute("aria-hidden","true"),L};return uu=S,uu}var tu,it;function fa(){if(it)return tu;it=1;var e=Ue();function u(o,n){this.type=o,this.attributes={},this.children=n||[]}u.prototype.setAttribute=function(o,n){this.attributes[o]=n},u.prototype.toNode=function(){var o=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(var n in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,n)&&o.setAttribute(n,this.attributes[n]);for(var r=0;r<this.children.length;r++)o.appendChild(this.children[r].toNode());return o},u.prototype.toMarkup=function(){var o="<"+this.type;for(var n in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,n)&&(o+=" "+n+'="',o+=e.escape(this.attributes[n]),o+='"');o+=">";for(var r=0;r<this.children.length;r++)o+=this.children[r].toMarkup();return o+="</"+this.type+">",o};function t(o){this.text=o}return t.prototype.toNode=function(){return document.createTextNode(this.text)},t.prototype.toMarkup=function(){return e.escape(this.text)},tu={MathNode:u,TextNode:t},tu}var ru,at;function da(){if(at)return ru;at=1;var e=T0(),u=h0(),t=fa(),o=Ze(),n=R0(),r=Ue(),c=e.makeSpan,i=e.fontMap,s=function(a,b){return n[b][a]&&n[b][a].replace&&(a=n[b][a].replace),new t.TextNode(a)},f=function(a,b){var m=b.font;if(!m)return null;var S=a.mode;if(m==="mathit")return"italic";var p=a.value;if(r.contains(["\\imath","\\jmath"],p))return null;n[S][p]&&n[S][p].replace&&(p=n[S][p].replace);var d=i[m].fontName;return u.getCharacterMetrics(p,d)?i[b.font].variant:null},h={};h.mathord=function(a,b){var m=new t.MathNode("mi",[s(a.value,a.mode)]),S=f(a,b);return S&&m.setAttribute("mathvariant",S),m},h.textord=function(a,b){var m=s(a.value,a.mode),S=f(a,b)||"normal",p;return/[0-9]/.test(a.value)?(p=new t.MathNode("mn",[m]),b.font&&p.setAttribute("mathvariant",S)):(p=new t.MathNode("mi",[m]),p.setAttribute("mathvariant",S)),p},h.bin=function(a){var b=new t.MathNode("mo",[s(a.value,a.mode)]);return b},h.rel=function(a){var b=new t.MathNode("mo",[s(a.value,a.mode)]);return b},h.open=function(a){var b=new t.MathNode("mo",[s(a.value,a.mode)]);return b},h.close=function(a){var b=new t.MathNode("mo",[s(a.value,a.mode)]);return b},h.inner=function(a){var b=new t.MathNode("mo",[s(a.value,a.mode)]);return b},h.punct=function(a){var b=new t.MathNode("mo",[s(a.value,a.mode)]);return b.setAttribute("separator","true"),b},h.ordgroup=function(a,b){var m=v(a.value,b),S=new t.MathNode("mrow",m);return S},h.text=function(a,b){var m=v(a.value.body,b),S=new t.MathNode("mtext",m);return S},h.color=function(a,b){var m=v(a.value.value,b),S=new t.MathNode("mstyle",m);return S.setAttribute("mathcolor",a.value.color),S},h.supsub=function(a,b){var m=[_(a.value.base,b)];a.value.sub&&m.push(_(a.value.sub,b)),a.value.sup&&m.push(_(a.value.sup,b));var S;a.value.sub?a.value.sup?S="msubsup":S="msub":S="msup";var p=new t.MathNode(S,m);return p},h.genfrac=function(a,b){var m=new t.MathNode("mfrac",[_(a.value.numer,b),_(a.value.denom,b)]);if(a.value.hasBarLine||m.setAttribute("linethickness","0px"),a.value.leftDelim!=null||a.value.rightDelim!=null){var S=[];if(a.value.leftDelim!=null){var p=new t.MathNode("mo",[new t.TextNode(a.value.leftDelim)]);p.setAttribute("fence","true"),S.push(p)}if(S.push(m),a.value.rightDelim!=null){var d=new t.MathNode("mo",[new t.TextNode(a.value.rightDelim)]);d.setAttribute("fence","true"),S.push(d)}var w=new t.MathNode("mrow",S);return w}return m},h.array=function(a,b){return new t.MathNode("mtable",a.value.body.map(function(m){return new t.MathNode("mtr",m.map(function(S){return new t.MathNode("mtd",[_(S,b)])}))}))},h.sqrt=function(a,b){var m;return a.value.index?m=new t.MathNode("mroot",[_(a.value.body,b),_(a.value.index,b)]):m=new t.MathNode("msqrt",[_(a.value.body,b)]),m},h.leftright=function(a,b){var m=v(a.value.body,b);if(a.value.left!=="."){var S=new t.MathNode("mo",[s(a.value.left,a.mode)]);S.setAttribute("fence","true"),m.unshift(S)}if(a.value.right!=="."){var p=new t.MathNode("mo",[s(a.value.right,a.mode)]);p.setAttribute("fence","true"),m.push(p)}var d=new t.MathNode("mrow",m);return d},h.accent=function(a,b){var m=new t.MathNode("mo",[s(a.value.accent,a.mode)]),S=new t.MathNode("mover",[_(a.value.base,b),m]);return S.setAttribute("accent","true"),S},h.spacing=function(a){var b;return a.value==="\\ "||a.value==="\\space"||a.value===" "||a.value==="~"?b=new t.MathNode("mtext",[new t.TextNode(" ")]):(b=new t.MathNode("mspace"),b.setAttribute("width",e.spacingFunctions[a.value].size)),b},h.op=function(a){var b;return a.value.symbol?b=new t.MathNode("mo",[s(a.value.body,a.mode)]):b=new t.MathNode("mi",[new t.TextNode(a.value.body.slice(1))]),b},h.katex=function(a){var b=new t.MathNode("mtext",[new t.TextNode("KaTeX")]);return b},h.font=function(a,b){var m=a.value.font;return _(a.value.body,b.withFont(m))},h.delimsizing=function(a){var b=[];a.value.value!=="."&&b.push(s(a.value.value,a.mode));var m=new t.MathNode("mo",b);return a.value.delimType==="open"||a.value.delimType==="close"?m.setAttribute("fence","true"):m.setAttribute("fence","false"),m},h.styling=function(a,b){var m=v(a.value.value,b),S=new t.MathNode("mstyle",m),p={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]},d=p[a.value.style];return S.setAttribute("scriptlevel",d[0]),S.setAttribute("displaystyle",d[1]),S},h.sizing=function(a,b){var m=v(a.value.value,b),S=new t.MathNode("mstyle",m);return S.setAttribute("mathsize",e.sizingMultiplier[a.value.size]+"em"),S},h.overline=function(a,b){var m=new t.MathNode("mo",[new t.TextNode("‾")]);m.setAttribute("stretchy","true");var S=new t.MathNode("mover",[_(a.value.body,b),m]);return S.setAttribute("accent","true"),S},h.underline=function(a,b){var m=new t.MathNode("mo",[new t.TextNode("‾")]);m.setAttribute("stretchy","true");var S=new t.MathNode("munder",[_(a.value.body,b),m]);return S.setAttribute("accentunder","true"),S},h.rule=function(a){var b=new t.MathNode("mrow");return b},h.llap=function(a,b){var m=new t.MathNode("mpadded",[_(a.value.body,b)]);return m.setAttribute("lspace","-1width"),m.setAttribute("width","0px"),m},h.rlap=function(a,b){var m=new t.MathNode("mpadded",[_(a.value.body,b)]);return m.setAttribute("width","0px"),m},h.phantom=function(a,b,m){var S=v(a.value.value,b);return new t.MathNode("mphantom",S)};var v=function(a,b){for(var m=[],S=0;S<a.length;S++){var p=a[S];m.push(_(p,b))}return m},_=function(a,b){if(!a)return new t.MathNode("mrow");if(h[a.type])return h[a.type](a,b);throw new o("Got group of unknown type: '"+a.type+"'")},A=function(a,b,m){var S=v(a,m),p=new t.MathNode("mrow",S),d=new t.MathNode("annotation",[new t.TextNode(b)]);d.setAttribute("encoding","application/x-tex");var w=new t.MathNode("semantics",[p,d]),y=new t.MathNode("math",[w]);return c(["katex-mathml"],[y])};return ru=A,ru}var nu,ot;function ha(){if(ot)return nu;ot=1;function e(t){this.style=t.style,this.color=t.color,this.size=t.size,this.phantom=t.phantom,this.font=t.font,t.parentStyle===void 0?this.parentStyle=t.style:this.parentStyle=t.parentStyle,t.parentSize===void 0?this.parentSize=t.size:this.parentSize=t.parentSize}e.prototype.extend=function(t){var o={style:this.style,size:this.size,color:this.color,parentStyle:this.style,parentSize:this.size,phantom:this.phantom,font:this.font};for(var n in t)t.hasOwnProperty(n)&&(o[n]=t[n]);return new e(o)},e.prototype.withStyle=function(t){return this.extend({style:t})},e.prototype.withSize=function(t){return this.extend({size:t})},e.prototype.withColor=function(t){return this.extend({color:t})},e.prototype.withPhantom=function(){return this.extend({phantom:!0})},e.prototype.withFont=function(t){return this.extend({font:t})},e.prototype.reset=function(){return this.extend({})};var u={"katex-blue":"#6495ed","katex-orange":"#ffa500","katex-pink":"#ff00af","katex-red":"#df0030","katex-green":"#28ae7b","katex-gray":"gray","katex-purple":"#9d38bd","katex-blueA":"#c7e9f1","katex-blueB":"#9cdceb","katex-blueC":"#58c4dd","katex-blueD":"#29abca","katex-blueE":"#1c758a","katex-tealA":"#acead7","katex-tealB":"#76ddc0","katex-tealC":"#5cd0b3","katex-tealD":"#55c1a7","katex-tealE":"#49a88f","katex-greenA":"#c9e2ae","katex-greenB":"#a6cf8c","katex-greenC":"#83c167","katex-greenD":"#77b05d","katex-greenE":"#699c52","katex-goldA":"#f7c797","katex-goldB":"#f9b775","katex-goldC":"#f0ac5f","katex-goldD":"#e1a158","katex-goldE":"#c78d46","katex-redA":"#f7a1a3","katex-redB":"#ff8080","katex-redC":"#fc6255","katex-redD":"#e65a4c","katex-redE":"#cf5044","katex-maroonA":"#ecabc1","katex-maroonB":"#ec92ab","katex-maroonC":"#c55f73","katex-maroonD":"#a24d61","katex-maroonE":"#94424f","katex-purpleA":"#caa3e8","katex-purpleB":"#b189c6","katex-purpleC":"#9a72ac","katex-purpleD":"#715582","katex-purpleE":"#644172","katex-mintA":"#f5f9e8","katex-mintB":"#edf2df","katex-mintC":"#e0e5cc","katex-grayA":"#fdfdfd","katex-grayB":"#f7f7f7","katex-grayC":"#eeeeee","katex-grayD":"#dddddd","katex-grayE":"#cccccc","katex-grayF":"#aaaaaa","katex-grayG":"#999999","katex-grayH":"#555555","katex-grayI":"#333333","katex-kaBlue":"#314453","katex-kaGreen":"#639b24"};return e.prototype.getColor=function(){return this.phantom?"transparent":u[this.color]||this.color},nu=e,nu}var iu,st;function pa(){if(st)return iu;st=1;var e=la(),u=da(),t=T0(),o=ha(),n=$t(),r=F0(),c=t.makeSpan,i=function(s,f,h){h=h||new n({});var v=r.TEXT;h.displayMode&&(v=r.DISPLAY);var _=new o({style:v,size:"size5"}),A=u(s,f,_),a=e(s,_),b=c(["katex"],[A,a]);return h.displayMode?c(["katex-display"],[b]):b};return iu=i,iu}var au={exports:{}},ct;function ma(){return ct||(ct=1,function(e){var u=Ue(),t=Ze();function o(i,s,f){typeof i=="string"&&(i=[i]),typeof s=="number"&&(s={numArgs:s});for(var h={numArgs:s.numArgs,argTypes:s.argTypes,greediness:s.greediness===void 0?1:s.greediness,allowedInText:!!s.allowedInText,numOptionalArgs:s.numOptionalArgs||0,handler:f},v=0;v<i.length;++v)e.exports[i[v]]=h}o("\\sqrt",{numArgs:1,numOptionalArgs:1},function(i,s){var f=s[0],h=s[1];return{type:"sqrt",body:h,index:f}}),o("\\text",{numArgs:1,argTypes:["text"],greediness:2},function(i,s){var f=s[0],h;return f.type==="ordgroup"?h=f.value:h=[f],{type:"text",body:h}}),o("\\color",{numArgs:2,allowedInText:!0,greediness:3,argTypes:["color","original"]},function(i,s){var f=s[0],h=s[1],v;return h.type==="ordgroup"?v=h.value:v=[h],{type:"color",color:f.value,value:v}}),o("\\overline",{numArgs:1},function(i,s){var f=s[0];return{type:"overline",body:f}}),o("\\underline",{numArgs:1},function(i,s){var f=s[0];return{type:"underline",body:f}}),o("\\rule",{numArgs:2,numOptionalArgs:1,argTypes:["size","size","size"]},function(i,s){var f=s[0],h=s[1],v=s[2];return{type:"rule",shift:f&&f.value,width:h.value,height:v.value}}),o("\\KaTeX",{numArgs:0},function(i){return{type:"katex"}}),o("\\phantom",{numArgs:1},function(i,s){var f=s[0],h;return f.type==="ordgroup"?h=f.value:h=[f],{type:"phantom",value:h}});var n={"\\bigl":{type:"open",size:1},"\\Bigl":{type:"open",size:2},"\\biggl":{type:"open",size:3},"\\Biggl":{type:"open",size:4},"\\bigr":{type:"close",size:1},"\\Bigr":{type:"close",size:2},"\\biggr":{type:"close",size:3},"\\Biggr":{type:"close",size:4},"\\bigm":{type:"rel",size:1},"\\Bigm":{type:"rel",size:2},"\\biggm":{type:"rel",size:3},"\\Biggm":{type:"rel",size:4},"\\big":{type:"textord",size:1},"\\Big":{type:"textord",size:2},"\\bigg":{type:"textord",size:3},"\\Bigg":{type:"textord",size:4}},r=["(",")","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","\\lceil","\\rceil","<",">","\\langle","\\rangle","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","\\lmoustache","\\rmoustache","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."],c={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak"};o(["\\blue","\\orange","\\pink","\\red","\\green","\\gray","\\purple","\\blueA","\\blueB","\\blueC","\\blueD","\\blueE","\\tealA","\\tealB","\\tealC","\\tealD","\\tealE","\\greenA","\\greenB","\\greenC","\\greenD","\\greenE","\\goldA","\\goldB","\\goldC","\\goldD","\\goldE","\\redA","\\redB","\\redC","\\redD","\\redE","\\maroonA","\\maroonB","\\maroonC","\\maroonD","\\maroonE","\\purpleA","\\purpleB","\\purpleC","\\purpleD","\\purpleE","\\mintA","\\mintB","\\mintC","\\grayA","\\grayB","\\grayC","\\grayD","\\grayE","\\grayF","\\grayG","\\grayH","\\grayI","\\kaBlue","\\kaGreen"],{numArgs:1,allowedInText:!0,greediness:3},function(i,s){var f=s[0],h;return f.type==="ordgroup"?h=f.value:h=[f],{type:"color",color:"katex-"+i.funcName.slice(1),value:h}}),o(["\\arcsin","\\arccos","\\arctan","\\arg","\\cos","\\cosh","\\cot","\\coth","\\csc","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\tan","\\tanh"],{numArgs:0},function(i){return{type:"op",limits:!1,symbol:!1,body:i.funcName}}),o(["\\det","\\gcd","\\inf","\\lim","\\liminf","\\limsup","\\max","\\min","\\Pr","\\sup"],{numArgs:0},function(i){return{type:"op",limits:!0,symbol:!1,body:i.funcName}}),o(["\\int","\\iint","\\iiint","\\oint"],{numArgs:0},function(i){return{type:"op",limits:!1,symbol:!0,body:i.funcName}}),o(["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint"],{numArgs:0},function(i){return{type:"op",limits:!0,symbol:!0,body:i.funcName}}),o(["\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom"],{numArgs:2,greediness:2},function(i,s){var f=s[0],h=s[1],v,_=null,A=null,a="auto";switch(i.funcName){case"\\dfrac":case"\\frac":case"\\tfrac":v=!0;break;case"\\dbinom":case"\\binom":case"\\tbinom":v=!1,_="(",A=")";break;default:throw new Error("Unrecognized genfrac command")}switch(i.funcName){case"\\dfrac":case"\\dbinom":a="display";break;case"\\tfrac":case"\\tbinom":a="text";break}return{type:"genfrac",numer:f,denom:h,hasBarLine:v,leftDelim:_,rightDelim:A,size:a}}),o(["\\llap","\\rlap"],{numArgs:1,allowedInText:!0},function(i,s){var f=s[0];return{type:i.funcName.slice(1),body:f}}),o(["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg","\\left","\\right"],{numArgs:1},function(i,s){var f=s[0];if(!u.contains(r,f.value))throw new t("Invalid delimiter: '"+f.value+"' after '"+i.funcName+"'",i.lexer,i.positions[1]);return i.funcName==="\\left"||i.funcName==="\\right"?{type:"leftright",value:f.value}:{type:"delimsizing",size:n[i.funcName].size,delimType:n[i.funcName].type,value:f.value}}),o(["\\tiny","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"],0,null),o(["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],0,null),o(["\\mathrm","\\mathit","\\mathbf","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],{numArgs:1,greediness:2},function(i,s){var f=s[0],h=i.funcName;return h in c&&(h=c[h]),{type:"font",font:h.slice(1),body:f}}),o(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot"],{numArgs:1},function(i,s){var f=s[0];return{type:"accent",accent:i.funcName,base:f}}),o(["\\over","\\choose"],{numArgs:0},function(i){var s;switch(i.funcName){case"\\over":s="\\frac";break;case"\\choose":s="\\binom";break;default:throw new Error("Unrecognized infix genfrac command")}return{type:"infix",replaceWith:s}}),o(["\\\\","\\cr"],{numArgs:0,numOptionalArgs:1,argTypes:["size"]},function(i,s){var f=s[0];return{type:"cr",size:f}}),o(["\\begin","\\end"],{numArgs:1,argTypes:["text"]},function(i,s){var f=s[0];if(f.type!=="ordgroup")throw new t("Invalid environment name",i.lexer,i.positions[1]);for(var h="",v=0;v<f.value.length;++v)h+=f.value[v].value;return{type:"environment",name:h,namepos:i.positions[1]}})}(au)),au.exports}var ou={exports:{}},su,lt;function Jt(){if(lt)return su;lt=1;function e(u,t,o){this.type=u,this.value=t,this.mode=o}return su={ParseNode:e},su}var ft;function ba(){return ft||(ft=1,function(e){var u=h0(),t=Jt(),o=Ze(),n=t.ParseNode;function r(i,s){for(var f=[],h=[f],v=[];;){var _=i.parseExpression(!1,null);f.push(new n("ordgroup",_,i.mode));var A=i.nextToken.text;if(A==="&")i.consume();else{if(A==="\\end")break;if(A==="\\\\"||A==="\\cr"){var a=i.parseFunction();v.push(a.value.size),f=[],h.push(f)}else{var b=Math.min(i.pos+1,i.lexer._input.length);throw new o("Expected & or \\\\ or \\end",i.lexer,b)}}}return s.body=h,s.rowGaps=v,new n(s.type,s,i.mode)}function c(i,s,f){typeof i=="string"&&(i=[i]),typeof s=="number"&&(s={numArgs:s});for(var h={numArgs:s.numArgs||0,argTypes:s.argTypes,greediness:1,allowedInText:!!s.allowedInText,numOptionalArgs:s.numOptionalArgs||0,handler:f},v=0;v<i.length;++v)e.exports[i[v]]=h}c("array",{numArgs:1},function(i,s){var f=s[0];f=f.value.map?f.value:[f];var h=f.map(function(_){var A=_.value;if("lcr".indexOf(A)!==-1)return{type:"align",align:A};if(A==="|")return{type:"separator",separator:"|"};throw new o("Unknown column alignment: "+_.value,i.lexer,i.positions[1])}),v={type:"array",cols:h,hskipBeforeAndAfter:!0};return v=r(i.parser,v),v}),c(["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix"],{},function(i){var s={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[i.envName],f={type:"array",hskipBeforeAndAfter:!1};return f=r(i.parser,f),s&&(f=new n("leftright",{body:[f],left:s[0],right:s[1]},i.mode)),f}),c("cases",{},function(i){var s={type:"array",arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:u.metrics.quad},{type:"align",align:"l",pregap:0,postgap:0}]};return s=r(i.parser,s),s=new n("leftright",{body:[s],left:"\\{",right:"."},i.mode),s}),c("aligned",{},function(i){var s={type:"array",cols:[]};s=r(i.parser,s);var f=new n("ordgroup",[],i.mode),h=0;s.value.body.forEach(function(a){var b;for(b=1;b<a.length;b+=2)a[b].value.unshift(f);h<a.length&&(h=a.length)});for(var v=0;v<h;++v){var _="r",A=0;v%2===1?_="l":v>0&&(A=2),s.value.cols[v]={type:"align",align:_,pregap:A,postgap:0}}return s})}(ou)),ou.exports}var cu,dt;function va(){if(dt)return cu;dt=1;function e(t){if(!t.__matchAtRelocatable){var o=t.source+"|()",n="g"+(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"");t.__matchAtRelocatable=new RegExp(o,n)}return t.__matchAtRelocatable}function u(t,o,n){if(t.global||t.sticky)throw new Error("matchAt(...): Only non-global regexes are supported");var r=e(t);r.lastIndex=n;var c=r.exec(o);return c[c.length-1]==null?(c.length=c.length-1,c):null}return cu=u,cu}var lu,ht;function ga(){if(ht)return lu;ht=1;var e=va(),u=Ze();function t(s){this._input=s}function o(s,f,h){this.text=s,this.data=f,this.position=h}var n=new RegExp(`([ \r
	]+)|(---?|[!-\\[\\]-‧‪-퟿豈-￿]|[\uD800-\uDBFF][\uDC00-\uDFFF]|\\\\(?:[a-zA-Z]+|[^\uD800-\uDFFF]))`),r=/\s*/;t.prototype._innerLex=function(s,f){var h=this._input;if(s===h.length)return new o("EOF",null,s);var v=e(n,h,s);if(v===null)throw new u("Unexpected character: '"+h[s]+"'",this,s);return v[2]?new o(v[2],null,s+v[2].length):f?this._innerLex(s+v[1].length,!0):new o(" ",null,s+v[1].length)};var c=/#[a-z0-9]+|[a-z]+/i;t.prototype._innerLexColor=function(s){var f=this._input,h=e(r,f,s)[0];s+=h.length;var v;if(v=e(c,f,s))return new o(v[0],null,s+v[0].length);throw new u("Invalid color",this,s)};var i=/(-?)\s*(\d+(?:\.\d*)?|\.\d+)\s*([a-z]{2})/;return t.prototype._innerLexSize=function(s){var f=this._input,h=e(r,f,s)[0];s+=h.length;var v;if(v=e(i,f,s)){var _=v[3];if(_!=="em"&&_!=="ex")throw new u("Invalid unit: '"+_+"'",this,s);return new o(v[0],{number:+(v[1]+v[2]),unit:_},s+v[0].length)}throw new u("Invalid size",this,s)},t.prototype._innerLexWhitespace=function(s){var f=this._input,h=e(r,f,s)[0];return s+=h.length,new o(h[0],null,s)},t.prototype.lex=function(s,f){if(f==="math")return this._innerLex(s,!0);if(f==="text")return this._innerLex(s,!1);if(f==="color")return this._innerLexColor(s);if(f==="size")return this._innerLexSize(s);if(f==="whitespace")return this._innerLexWhitespace(s)},lu=t,lu}var fu,pt;function xa(){if(pt)return fu;pt=1;var e=ma(),u=ba(),t=ga(),o=R0(),n=Ue(),r=Jt(),c=Ze();function i(a,b){this.lexer=new t(a),this.settings=b}var s=r.ParseNode;function f(a,b){this.result=a,this.isFunction=b}i.prototype.expect=function(a,b){if(this.nextToken.text!==a)throw new c("Expected '"+a+"', got '"+this.nextToken.text+"'",this.lexer,this.nextToken.position);b!==!1&&this.consume()},i.prototype.consume=function(){this.pos=this.nextToken.position,this.nextToken=this.lexer.lex(this.pos,this.mode)},i.prototype.parse=function(){this.mode="math",this.pos=0,this.nextToken=this.lexer.lex(this.pos,this.mode);var a=this.parseInput();return a},i.prototype.parseInput=function(){var a=this.parseExpression(!1);return this.expect("EOF",!1),a};var h=["}","\\end","\\right","&","\\\\","\\cr"];i.prototype.parseExpression=function(a,b){for(var m=[];;){var S=this.nextToken,p=this.pos;if(h.indexOf(S.text)!==-1||b&&S.text===b)break;var d=this.parseAtom();if(!d){if(!this.settings.throwOnError&&S.text[0]==="\\"){var w=this.handleUnsupportedCmd();m.push(w),p=S.position;continue}break}if(a&&d.type==="infix"){this.pos=p,this.nextToken=S;break}m.push(d)}return this.handleInfixNodes(m)},i.prototype.handleInfixNodes=function(a){for(var b=-1,m,S=0;S<a.length;S++){var p=a[S];if(p.type==="infix"){if(b!==-1)throw new c("only one infix operator per group",this.lexer,-1);b=S,m=p.value.replaceWith}}if(b!==-1){var d,w,y=a.slice(0,b),k=a.slice(b+1);y.length===1&&y[0].type==="ordgroup"?d=y[0]:d=new s("ordgroup",y,this.mode),k.length===1&&k[0].type==="ordgroup"?w=k[0]:w=new s("ordgroup",k,this.mode);var M=this.callFunction(m,[d,w],null);return[new s(M.type,M,this.mode)]}else return a};var v=1;i.prototype.handleSupSubscript=function(a){var b=this.nextToken.text,m=this.pos;this.consume();var S=this.parseGroup();if(S)if(S.isFunction){var p=e[S.result].greediness;if(p>v)return this.parseFunction(S);throw new c("Got function '"+S.result+"' with no arguments as "+a,this.lexer,m+1)}else return S.result;else{if(!this.settings.throwOnError&&this.nextToken.text[0]==="\\")return this.handleUnsupportedCmd();throw new c("Expected group after '"+b+"'",this.lexer,m+1)}},i.prototype.handleUnsupportedCmd=function(){for(var a=this.nextToken.text,b=[],m=0;m<a.length;m++)b.push(new s("textord",a[m],"text"));var S=new s("text",{body:b,type:"text"},this.mode),p=new s("color",{color:this.settings.errorColor,value:[S],type:"color"},this.mode);return this.consume(),p},i.prototype.parseAtom=function(){var a=this.parseImplicitGroup();if(this.mode==="text")return a;for(var b,m;;){var S=this.nextToken;if(S.text==="\\limits"||S.text==="\\nolimits"){if(!a||a.type!=="op")throw new c("Limit controls must follow a math operator",this.lexer,this.pos);var p=S.text==="\\limits";a.value.limits=p,a.value.alwaysHandleSupSub=!0,this.consume()}else if(S.text==="^"){if(b)throw new c("Double superscript",this.lexer,this.pos);b=this.handleSupSubscript("superscript")}else if(S.text==="_"){if(m)throw new c("Double subscript",this.lexer,this.pos);m=this.handleSupSubscript("subscript")}else if(S.text==="'"){var d=new s("textord","\\prime",this.mode),w=[d];for(this.consume();this.nextToken.text==="'";)w.push(d),this.consume();b=new s("ordgroup",w,this.mode)}else break}return b||m?new s("supsub",{base:a,sup:b,sub:m},this.mode):a};var _=["\\tiny","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"],A=["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"];return i.prototype.parseImplicitGroup=function(){var a=this.parseSymbol();if(a==null)return this.parseFunction();var b=a.result,m;if(b==="\\left"){var S=this.parseFunction(a);m=this.parseExpression(!1),this.expect("\\right",!1);var p=this.parseFunction();return new s("leftright",{body:m,left:S.value.value,right:p.value.value},this.mode)}else if(b==="\\begin"){var d=this.parseFunction(a),w=d.value.name;if(!u.hasOwnProperty(w))throw new c("No such environment: "+w,this.lexer,d.value.namepos);var y=u[w],k=this.parseArguments("\\begin{"+w+"}",y),M={mode:this.mode,envName:w,parser:this,lexer:this.lexer,positions:k.pop()},L=y.handler(M,k);this.expect("\\end",!1);var q=this.parseFunction();if(q.value.name!==w)throw new c("Mismatch: \\begin{"+w+"} matched by \\end{"+q.value.name+"}",this.lexer);return L.position=q.position,L}else return n.contains(_,b)?(m=this.parseExpression(!1),new s("sizing",{size:"size"+(n.indexOf(_,b)+1),value:m},this.mode)):n.contains(A,b)?(m=this.parseExpression(!0),new s("styling",{style:b.slice(1,b.length-5),value:m},this.mode)):this.parseFunction(a)},i.prototype.parseFunction=function(a){if(a||(a=this.parseGroup()),a)if(a.isFunction){var b=a.result,m=e[b];if(this.mode==="text"&&!m.allowedInText)throw new c("Can't use function '"+b+"' in text mode",this.lexer,a.position);var S=this.parseArguments(b,m),p=this.callFunction(b,S,S.pop());return new s(p.type,p,this.mode)}else return a.result;else return null},i.prototype.callFunction=function(a,b,m){var S={funcName:a,parser:this,lexer:this.lexer,positions:m};return e[a].handler(S,b)},i.prototype.parseArguments=function(a,b){var m=b.numArgs+b.numOptionalArgs;if(m===0)return[[this.pos]];for(var S=b.greediness,p=[this.pos],d=[],w=0;w<m;w++){var y=b.argTypes&&b.argTypes[w],k;if(w<b.numOptionalArgs){if(y?k=this.parseSpecialGroup(y,!0):k=this.parseOptionalGroup(),!k){d.push(null),p.push(this.pos);continue}}else if(y?k=this.parseSpecialGroup(y):k=this.parseGroup(),!k)if(!this.settings.throwOnError&&this.nextToken.text[0]==="\\")k=new f(this.handleUnsupportedCmd(this.nextToken.text),!1);else throw new c("Expected group after '"+a+"'",this.lexer,this.pos);var M;if(k.isFunction){var L=e[k.result].greediness;if(L>S)M=this.parseFunction(k);else throw new c("Got function '"+k.result+"' as argument to '"+a+"'",this.lexer,this.pos-1)}else M=k.result;d.push(M),p.push(this.pos)}return d.push(p),d},i.prototype.parseSpecialGroup=function(a,b){var m=this.mode;if(a==="original"&&(a=m),a==="color"||a==="size"){var S=this.nextToken;if(b&&S.text!=="[")return null;this.mode=a,this.expect(b?"[":"{");var p=this.nextToken;this.mode=m;var d;return a==="color"?d=p.text:d=p.data,this.consume(),this.expect(b?"]":"}"),new f(new s(a,d,m),!1)}else if(a==="text"){var w=this.lexer.lex(this.pos,"whitespace");this.pos=w.position}this.mode=a,this.nextToken=this.lexer.lex(this.pos,a);var y;return b?y=this.parseOptionalGroup():y=this.parseGroup(),this.mode=m,this.nextToken=this.lexer.lex(this.pos,m),y},i.prototype.parseGroup=function(){if(this.nextToken.text==="{"){this.consume();var a=this.parseExpression(!1);return this.expect("}"),new f(new s("ordgroup",a,this.mode),!1)}else return this.parseSymbol()},i.prototype.parseOptionalGroup=function(){if(this.nextToken.text==="["){this.consume();var a=this.parseExpression(!1,"]");return this.expect("]"),new f(new s("ordgroup",a,this.mode),!1)}else return null},i.prototype.parseSymbol=function(){var a=this.nextToken;return e[a.text]?(this.consume(),new f(a.text,!0)):o[this.mode][a.text]?(this.consume(),new f(new s(o[this.mode][a.text].group,a.text,this.mode),!1)):null},i.prototype.ParseNode=s,fu=i,fu}var du,mt;function ya(){if(mt)return du;mt=1;var e=xa(),u=function(t,o){var n=new e(t,o);return n.parse()};return du=u,du}var hu,bt;function ka(){if(bt)return hu;bt=1;var e=Ze(),u=$t(),t=pa(),o=ya(),n=Ue(),r=function(s,f,h){n.clearNode(f);var v=new u(h),_=o(s,v),A=t(_,s,v).toNode();f.appendChild(A)};typeof document<"u"&&document.compatMode!=="CSS1Compat"&&(typeof console<"u"&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype."),r=function(){throw new e("KaTeX doesn't work in quirks mode.")});var c=function(s,f){var h=new u(f),v=o(s,h);return t(v,s,h).toMarkup()},i=function(s,f){var h=new u(f);return o(s,h)};return hu={render:r,renderToString:c,__parse:i,ParseError:e},hu}var pu,vt;function wa(){if(vt)return pu;vt=1;var e=ka();function u(n,r){var c,i,s=n.posMax,f=!0,h=!0;return c=r>0?n.src.charCodeAt(r-1):-1,i=r+1<=s?n.src.charCodeAt(r+1):-1,(c===32||c===9||i>=48&&i<=57)&&(h=!1),(i===32||i===9)&&(f=!1),{can_open:f,can_close:h}}function t(n,r){var c,i,s,f,h;if(n.src[n.pos]!=="$")return!1;if(f=u(n,n.pos),!f.can_open)return r||(n.pending+="$"),n.pos+=1,!0;for(c=n.pos+1,i=c;(i=n.src.indexOf("$",i))!==-1;){for(h=i-1;n.src[h]==="\\";)h-=1;if((i-h)%2==1)break;i+=1}return i===-1?(r||(n.pending+="$"),n.pos=c,!0):i-c===0?(r||(n.pending+="$$"),n.pos=c+1,!0):(f=u(n,i),f.can_close?(r||(s=n.push("math_inline","math",0),s.markup="$",s.content=n.src.slice(c,i)),n.pos=i+1,!0):(r||(n.pending+="$"),n.pos=c,!0))}function o(n,r,c,i){var s,f,h,v,_=!1,A,a=n.bMarks[r]+n.tShift[r],b=n.eMarks[r];if(a+2>b||n.src.slice(a,a+2)!=="$$")return!1;if(a+=2,s=n.src.slice(a,b),i)return!0;for(s.trim().slice(-2)==="$$"&&(s=s.trim().slice(0,-2),_=!0),h=r;!_&&(h++,!(h>=c||(a=n.bMarks[h]+n.tShift[h],b=n.eMarks[h],a<b&&n.tShift[h]<n.blkIndent)));)n.src.slice(a,b).trim().slice(-2)==="$$"&&(v=n.src.slice(0,b).lastIndexOf("$$"),f=n.src.slice(a,v),_=!0);return n.line=h+1,A=n.push("math_block","math",0),A.block=!0,A.content=(s&&s.trim()?s+`
`:"")+n.getLines(r+1,h,n.tShift[r],!0)+(f&&f.trim()?f:""),A.map=[r,n.line],A.markup="$$",!0}return pu=function(r,c){c=c||{};var i=function(v){c.displayMode=!1;try{return e.renderToString(v,c)}catch(_){return c.throwOnError,v}},s=function(v,_){return i(v[_].content)},f=function(v){c.displayMode=!0;try{return"<p>"+e.renderToString(v,c)+"</p>"}catch(_){return c.throwOnError,v}},h=function(v,_){return f(v[_].content)+`
`};r.inline.ruler.after("escape","math_inline",t),r.block.ruler.after("blockquote","math_block",o,{alt:["paragraph","reference","blockquote","list"]}),r.renderer.rules.math_inline=s,r.renderer.rules.math_block=h},pu}var _a=wa();const Ca=kt(_a),Aa={class:"flex_between ai-header"},Da={__name:"header",setup(e){const u=nr(),t=()=>{u.back()};return(o,n)=>{const r=x0("Close"),c=x0("el-icon");return Oe(),je("div",Aa,[n[0]||(n[0]=le("div",{class:"flex_start left"},[le("img",{src:br,alt:""}),le("p",null,"AI助教")],-1)),y0(c,{style:{color:"#fff","font-size":"24px",cursor:"pointer"},onClick:t},{default:yt(()=>[y0(r)]),_:1})])}}},Ea=wt(Da,[["__scopeId","data-v-7c2e17e2"]]);function Sa(){return c0({url:"/ai/conversations/teaching/create",method:"post"})}function Fa(){return c0({url:"/ai/conversations/teaching/faq",method:"get"})}function Ra(e){return c0({url:"/ai/conversations/"+e.id+"/teaching/messages/",method:"get"})}function Ta(e){return c0({url:"/ai/conversations/"+e.id+"/teaching/clear-context/",method:"delete"})}function Ma(e){return c0({url:"/ai/asr",method:"post",data:e})}let Ke="当前浏览器不支持音频录制";function za(){return!!(navigator.mediaDevices&&navigator.mediaDevices.getUserMedia)&&typeof MediaRecorder<"u"}async function Ba(){try{return(await navigator.mediaDevices.getUserMedia({audio:!0})).getTracks().forEach(u=>u.stop()),!0}catch(e){return console.error("无法访问麦克风:",e),e.name==="NotAllowedError"?Ke="用户拒绝了麦克风权限":e.name==="NotFoundError"?Ke="未找到麦克风设备":e.name==="NotSupportedError"?Ke="当前URL不支持录音（需HTTPS）":e.name==="NotReadableError"?Ke="麦克风被占用":e.name==="SecurityError"&&(Ke="安全限制，无法访问麦克风"),!1}}async function La(){return za()?await Ba()?!0:(k0.error(Ke),!1):(k0.error("当前浏览器不支持音频录制"),!1)}var mu={exports:{}},gt;function qa(){return gt||(gt=1,function(e){function u(g,l){if(!g)throw"First parameter is required.";l=l||{type:"video"},l=new t(g,l);var x=this;function E(B){return l.disableLogs||x.version,B&&(l=new t(g,B)),l.disableLogs||""+l.type,O?(O.clearRecordedData(),O.record(),J("recording"),x.recordingDuration&&U(),x):(R(function(){x.recordingDuration&&U()}),x)}function R(B){B&&(l.initCallback=function(){B(),B=l.initCallback=null});var K=new o(g,l);O=new K(g,l),O.record(),J("recording"),l.disableLogs||(O.constructor.name,l.type)}function z(B){if(B=B||function(){},!O){W();return}if(x.state==="paused"){x.resumeRecording(),setTimeout(function(){z(B)},1);return}x.state!=="recording"&&!l.disableLogs&&console.warn('Recording state should be: "recording", however current state is: ',x.state),l.disableLogs||""+l.type,l.type!=="gif"?O.stop(K):(O.stop(),K()),J("stopped");function K(ne){if(!O){typeof B.call=="function"?B.call(x,""):B("");return}Object.keys(O).forEach(function(ue){typeof O[ue]!="function"&&(x[ue]=O[ue])});var F=O.blob;if(!F)if(ne)O.blob=F=ne;else throw"Recording failed.";if(F&&!l.disableLogs&&(F.type,S(F.size)),B){var P;try{P=h.createObjectURL(F)}catch{}typeof B.call=="function"?B.call(x,P):B(P)}l.autoWriteToDisk&&$(function(ue){var re={};re[l.type+"Blob"]=ue,X.Store(re)})}}function I(){if(!O){W();return}if(x.state!=="recording"){l.disableLogs||console.warn("Unable to pause the recording. Recording state: ",x.state);return}J("paused"),O.pause(),l.disableLogs}function C(){if(!O){W();return}if(x.state!=="paused"){l.disableLogs||console.warn("Unable to resume the recording. Recording state: ",x.state);return}J("recording"),O.resume(),l.disableLogs}function j(B){postMessage(new FileReaderSync().readAsDataURL(B))}function $(B,K){if(!B)throw"Pass a callback function over getDataURL.";var ne=K?K.blob:(O||{}).blob;if(!ne){l.disableLogs||console.warn("Blob encoder did not finish its job yet."),setTimeout(function(){$(B,K)},1e3);return}if(typeof Worker<"u"&&!navigator.mozGetUserMedia){var F=ue(j);F.onmessage=function(re){B(re.data)},F.postMessage(ne)}else{var P=new FileReader;P.readAsDataURL(ne),P.onload=function(re){B(re.target.result)}}function ue(re){try{var oe=h.createObjectURL(new Blob([re.toString(),"this.onmessage =  function (eee) {"+re.name+"(eee.data);}"],{type:"application/javascript"})),ae=new Worker(oe);return h.revokeObjectURL(oe),ae}catch{}}}function U(B){if(B=B||0,x.state==="paused"){setTimeout(function(){U(B)},1e3);return}if(x.state!=="stopped"){if(B>=x.recordingDuration){z(x.onRecordingStopped);return}B+=1e3,setTimeout(function(){U(B)},1e3)}}function J(B){x&&(x.state=B,typeof x.onStateChanged.call=="function"?x.onStateChanged.call(x,B):x.onStateChanged(B))}var V='It seems that recorder is destroyed or "startRecording" is not invoked for '+l.type+" recorder.";function W(){l.disableLogs!==!0&&console.warn(V)}var O,Y={startRecording:E,stopRecording:z,pauseRecording:I,resumeRecording:C,initRecorder:R,setRecordingDuration:function(B,K){if(typeof B>"u")throw"recordingDuration is required.";if(typeof B!="number")throw"recordingDuration must be a number.";return x.recordingDuration=B,x.onRecordingStopped=K||function(){},{onRecordingStopped:function(ne){x.onRecordingStopped=ne}}},clearRecordedData:function(){if(!O){W();return}O.clearRecordedData(),l.disableLogs},getBlob:function(){if(!O){W();return}return O.blob},getDataURL:$,toURL:function(){if(!O){W();return}return h.createObjectURL(O.blob)},getInternalRecorder:function(){return O},save:function(B){if(!O){W();return}p(O.blob,B)},getFromDisk:function(B){if(!O){W();return}u.getFromDisk(l.type,B)},setAdvertisementArray:function(B){l.advertisement=[];for(var K=B.length,ne=0;ne<K;ne++)l.advertisement.push({duration:ne,image:B[ne]})},blob:null,bufferSize:0,sampleRate:0,buffer:null,reset:function(){x.state==="recording"&&!l.disableLogs&&console.warn("Stop an active recorder."),O&&typeof O.clearRecordedData=="function"&&O.clearRecordedData(),O=null,J("inactive"),x.blob=null},onStateChanged:function(B){l.disableLogs},state:"inactive",getState:function(){return x.state},destroy:function(){var B=l.disableLogs;l={disableLogs:!0},x.reset(),J("destroyed"),Y=x=null,M.AudioContextConstructor&&(M.AudioContextConstructor.close(),M.AudioContextConstructor=null),l.disableLogs=B,l.disableLogs},version:"5.6.2"};if(!this)return x=Y,Y;for(var ie in Y)this[ie]=Y[ie];return x=this,Y}u.version="5.6.2",e.exports=u,u.getFromDisk=function(g,l){if(!l)throw"callback is mandatory.";""+(g==="all"?"blobs":g+" blob "),X.Fetch(function(x,E){g!=="all"&&E===g+"Blob"&&l&&l(x),g==="all"&&l&&l(x,E.replace("Blob",""))})},u.writeToDisk=function(g){g=g||{},g.audio&&g.video&&g.gif?g.audio.getDataURL(function(l){g.video.getDataURL(function(x){g.gif.getDataURL(function(E){X.Store({audioBlob:l,videoBlob:x,gifBlob:E})})})}):g.audio&&g.video?g.audio.getDataURL(function(l){g.video.getDataURL(function(x){X.Store({audioBlob:l,videoBlob:x})})}):g.audio&&g.gif?g.audio.getDataURL(function(l){g.gif.getDataURL(function(x){X.Store({audioBlob:l,gifBlob:x})})}):g.video&&g.gif?g.video.getDataURL(function(l){g.gif.getDataURL(function(x){X.Store({videoBlob:l,gifBlob:x})})}):g.audio?g.audio.getDataURL(function(l){X.Store({audioBlob:l})}):g.video?g.video.getDataURL(function(l){X.Store({videoBlob:l})}):g.gif&&g.gif.getDataURL(function(l){X.Store({gifBlob:l})})};function t(g,l){return!l.recorderType&&!l.type&&(l.audio&&l.video?l.type="video":l.audio&&!l.video&&(l.type="audio")),l.recorderType&&!l.type&&(l.recorderType===G||l.recorderType===Z||typeof te<"u"&&l.recorderType===te?l.type="video":l.recorderType===T?l.type="gif":l.recorderType===D?l.type="audio":l.recorderType===q&&(w(g,"audio").length&&w(g,"video").length||!w(g,"audio").length&&w(g,"video").length?l.type="video":w(g,"audio").length&&!w(g,"video").length&&(l.type="audio"))),typeof q<"u"&&typeof MediaRecorder<"u"&&"requestData"in MediaRecorder.prototype&&(l.mimeType||(l.mimeType="video/webm"),l.type||(l.type=l.mimeType.split("/")[0]),l.bitsPerSecond),l.type||(l.mimeType&&(l.type=l.mimeType.split("/")[0]),l.type||(l.type="audio")),l}function o(g,l){var x;return(a||v||_)&&(x=D),typeof MediaRecorder<"u"&&"requestData"in MediaRecorder.prototype&&!a&&(x=q),l.type==="video"&&(a||_)&&(x=G,typeof te<"u"&&typeof ReadableStream<"u"&&(x=te)),l.type==="gif"&&(x=T),l.type==="canvas"&&(x=Z),L()&&x!==Z&&x!==T&&typeof MediaRecorder<"u"&&"requestData"in MediaRecorder.prototype&&(w(g,"video").length||w(g,"audio").length)&&(l.type==="audio"?typeof MediaRecorder.isTypeSupported=="function"&&MediaRecorder.isTypeSupported("audio/webm")&&(x=q):typeof MediaRecorder.isTypeSupported=="function"&&MediaRecorder.isTypeSupported("video/webm")&&(x=q)),g instanceof Array&&g.length&&(x=H),l.recorderType&&(x=l.recorderType),!l.disableLogs&&x&&x.name&&(x.name||x.constructor.name),!x&&b&&(x=q),x}function n(g){this.addStream=function(l){l&&(g=l)},this.mediaType={audio:!0,video:!0},this.startRecording=function(){var l=this.mediaType,x,E=this.mimeType||{audio:null,video:null,gif:null};if(typeof l.audio!="function"&&L()&&!w(g,"audio").length&&(l.audio=!1),typeof l.video!="function"&&L()&&!w(g,"video").length&&(l.video=!1),typeof l.gif!="function"&&L()&&!w(g,"video").length&&(l.gif=!1),!l.audio&&!l.video&&!l.gif)throw"MediaStream must have either audio or video tracks.";if(l.audio&&(x=null,typeof l.audio=="function"&&(x=l.audio),this.audioRecorder=new u(g,{type:"audio",bufferSize:this.bufferSize,sampleRate:this.sampleRate,numberOfAudioChannels:this.numberOfAudioChannels||2,disableLogs:this.disableLogs,recorderType:x,mimeType:E.audio,timeSlice:this.timeSlice,onTimeStamp:this.onTimeStamp}),l.video||this.audioRecorder.startRecording()),l.video){x=null,typeof l.video=="function"&&(x=l.video);var R=g;if(L()&&l.audio&&typeof l.audio=="function"){var z=w(g,"video")[0];A?(R=new m,R.addTrack(z),x&&x===G&&(x=q)):(R=new m,R.addTrack(z))}this.videoRecorder=new u(R,{type:"video",video:this.video,canvas:this.canvas,frameInterval:this.frameInterval||10,disableLogs:this.disableLogs,recorderType:x,mimeType:E.video,timeSlice:this.timeSlice,onTimeStamp:this.onTimeStamp,workerPath:this.workerPath,webAssemblyPath:this.webAssemblyPath,frameRate:this.frameRate,bitrate:this.bitrate}),l.audio||this.videoRecorder.startRecording()}if(l.audio&&l.video){var I=this,C=L()===!0;(l.audio instanceof D&&l.video||l.audio!==!0&&l.video!==!0&&l.audio!==l.video)&&(C=!1),C===!0?(I.audioRecorder=null,I.videoRecorder.startRecording()):I.videoRecorder.initRecorder(function(){I.audioRecorder.initRecorder(function(){I.videoRecorder.startRecording(),I.audioRecorder.startRecording()})})}l.gif&&(x=null,typeof l.gif=="function"&&(x=l.gif),this.gifRecorder=new u(g,{type:"gif",frameRate:this.frameRate||200,quality:this.quality||10,disableLogs:this.disableLogs,recorderType:x,mimeType:E.gif}),this.gifRecorder.startRecording())},this.stopRecording=function(l){l=l||function(){},this.audioRecorder&&this.audioRecorder.stopRecording(function(x){l(x,"audio")}),this.videoRecorder&&this.videoRecorder.stopRecording(function(x){l(x,"video")}),this.gifRecorder&&this.gifRecorder.stopRecording(function(x){l(x,"gif")})},this.pauseRecording=function(){this.audioRecorder&&this.audioRecorder.pauseRecording(),this.videoRecorder&&this.videoRecorder.pauseRecording(),this.gifRecorder&&this.gifRecorder.pauseRecording()},this.resumeRecording=function(){this.audioRecorder&&this.audioRecorder.resumeRecording(),this.videoRecorder&&this.videoRecorder.resumeRecording(),this.gifRecorder&&this.gifRecorder.resumeRecording()},this.getBlob=function(l){var x={};return this.audioRecorder&&(x.audio=this.audioRecorder.getBlob()),this.videoRecorder&&(x.video=this.videoRecorder.getBlob()),this.gifRecorder&&(x.gif=this.gifRecorder.getBlob()),l&&l(x),x},this.destroy=function(){this.audioRecorder&&(this.audioRecorder.destroy(),this.audioRecorder=null),this.videoRecorder&&(this.videoRecorder.destroy(),this.videoRecorder=null),this.gifRecorder&&(this.gifRecorder.destroy(),this.gifRecorder=null)},this.getDataURL=function(l){this.getBlob(function(R){R.audio&&R.video?x(R.audio,function(z){x(R.video,function(I){l({audio:z,video:I})})}):R.audio?x(R.audio,function(z){l({audio:z})}):R.video&&x(R.video,function(z){l({video:z})})});function x(R,z){if(typeof Worker<"u"){var I=E(function($){postMessage(new FileReaderSync().readAsDataURL($))});I.onmessage=function(j){z(j.data)},I.postMessage(R)}else{var C=new FileReader;C.readAsDataURL(R),C.onload=function(j){z(j.target.result)}}}function E(R){var z=h.createObjectURL(new Blob([R.toString(),"this.onmessage =  function (eee) {"+R.name+"(eee.data);}"],{type:"application/javascript"})),I=new Worker(z),C;if(typeof h<"u")C=h;else if(typeof webkitURL<"u")C=webkitURL;else throw"Neither URL nor webkitURL detected.";return C.revokeObjectURL(z),I}},this.writeToDisk=function(){u.writeToDisk({audio:this.audioRecorder,video:this.videoRecorder,gif:this.gifRecorder})},this.save=function(l){l=l||{audio:!0,video:!0,gif:!0},l.audio&&this.audioRecorder&&this.audioRecorder.save(typeof l.audio=="string"?l.audio:""),l.video&&this.videoRecorder&&this.videoRecorder.save(typeof l.video=="string"?l.video:""),l.gif&&this.gifRecorder&&this.gifRecorder.save(typeof l.gif=="string"?l.gif:"")}}n.getFromDisk=u.getFromDisk,n.writeToDisk=u.writeToDisk,typeof u<"u"&&(u.MRecordRTC=n);var r="Fake/5.0 (FakeOS) AppleWebKit/123 (KHTML, like Gecko) Fake/12.3.4567.89 Fake/123.45";(function(g){g&&(typeof window<"u"||typeof ce>"u"||(ce.navigator={userAgent:r,getUserMedia:function(){}},ce.console||(ce.console={}),(typeof ce.console.log>"u"||typeof ce.console.error>"u")&&(ce.console.error=ce.console.log=ce.console.log||function(){}),typeof document>"u"&&(g.document={documentElement:{appendChild:function(){return""}}},document.createElement=document.captureStream=document.mozCaptureStream=function(){var l={getContext:function(){return l},play:function(){},pause:function(){},drawImage:function(){},toDataURL:function(){return""},style:{}};return l},g.HTMLVideoElement=function(){}),typeof location>"u"&&(g.location={protocol:"file:",href:"",hash:""}),typeof screen>"u"&&(g.screen={width:0,height:0}),typeof h>"u"&&(g.URL={createObjectURL:function(){return""},revokeObjectURL:function(){return""}}),g.window=ce))})(typeof ce<"u"?ce:null);var c=window.requestAnimationFrame;if(typeof c>"u"){if(typeof webkitRequestAnimationFrame<"u")c=webkitRequestAnimationFrame;else if(typeof mozRequestAnimationFrame<"u")c=mozRequestAnimationFrame;else if(typeof msRequestAnimationFrame<"u")c=msRequestAnimationFrame;else if(typeof c>"u"){var i=0;c=function(g,l){var x=new Date().getTime(),E=Math.max(0,16-(x-i)),R=setTimeout(function(){g(x+E)},E);return i=x+E,R}}}var s=window.cancelAnimationFrame;typeof s>"u"&&(typeof webkitCancelAnimationFrame<"u"?s=webkitCancelAnimationFrame:typeof mozCancelAnimationFrame<"u"?s=mozCancelAnimationFrame:typeof msCancelAnimationFrame<"u"?s=msCancelAnimationFrame:typeof s>"u"&&(s=function(g){clearTimeout(g)}));var f=window.AudioContext;typeof f>"u"&&(typeof webkitAudioContext<"u"&&(f=webkitAudioContext),typeof mozAudioContext<"u"&&(f=mozAudioContext));var h=window.URL;typeof h>"u"&&typeof webkitURL<"u"&&(h=webkitURL),typeof navigator<"u"&&typeof navigator.getUserMedia>"u"&&(typeof navigator.webkitGetUserMedia<"u"&&(navigator.getUserMedia=navigator.webkitGetUserMedia),typeof navigator.mozGetUserMedia<"u"&&(navigator.getUserMedia=navigator.mozGetUserMedia));var v=navigator.userAgent.indexOf("Edge")!==-1&&(!!navigator.msSaveBlob||!!navigator.msSaveOrOpenBlob),_=!!window.opera||navigator.userAgent.indexOf("OPR/")!==-1,A=navigator.userAgent.toLowerCase().indexOf("firefox")>-1&&"netscape"in window&&/ rv:/.test(navigator.userAgent),a=!_&&!v&&!!navigator.webkitGetUserMedia||d()||navigator.userAgent.toLowerCase().indexOf("chrome/")!==-1,b=/^((?!chrome|android).)*safari/i.test(navigator.userAgent);b&&!a&&navigator.userAgent.indexOf("CriOS")!==-1&&(b=!1,a=!0);var m=window.MediaStream;typeof m>"u"&&typeof webkitMediaStream<"u"&&(m=webkitMediaStream),typeof m<"u"&&typeof m.prototype.stop>"u"&&(m.prototype.stop=function(){this.getTracks().forEach(function(g){g.stop()})});function S(g){var l=1e3,x=["Bytes","KB","MB","GB","TB"];if(g===0)return"0 Bytes";var E=parseInt(Math.floor(Math.log(g)/Math.log(l)),10);return(g/Math.pow(l,E)).toPrecision(3)+" "+x[E]}function p(g,l){if(!g)throw"Blob object is required.";if(!g.type)try{g.type="video/webm"}catch{}var x=(g.type||"video/webm").split("/")[1];if(x.indexOf(";")!==-1&&(x=x.split(";")[0]),l&&l.indexOf(".")!==-1){var E=l.split(".");l=E[0],x=E[1]}var R=(l||Math.round(Math.random()*9999999999)+888888888)+"."+x;if(typeof navigator.msSaveOrOpenBlob<"u")return navigator.msSaveOrOpenBlob(g,R);if(typeof navigator.msSaveBlob<"u")return navigator.msSaveBlob(g,R);var z=document.createElement("a");z.href=h.createObjectURL(g),z.download=R,z.style="display:none;opacity:0;color:transparent;",(document.body||document.documentElement).appendChild(z),typeof z.click=="function"?z.click():(z.target="_blank",z.dispatchEvent(new MouseEvent("click",{view:window,bubbles:!0,cancelable:!0}))),h.revokeObjectURL(z.href)}function d(){return!!(typeof window<"u"&&typeof window.process=="object"&&window.process.type==="renderer"||typeof process<"u"&&typeof process.versions=="object"&&process.versions.electron||typeof navigator=="object"&&typeof navigator.userAgent=="string"&&navigator.userAgent.indexOf("Electron")>=0)}function w(g,l){return!g||!g.getTracks?[]:g.getTracks().filter(function(x){return x.kind===(l||"audio")})}function y(g,l){"srcObject"in l?l.srcObject=g:"mozSrcObject"in l?l.mozSrcObject=g:l.srcObject=g}function k(g,l){if(typeof EBML>"u")throw new Error("Please link: https://www.webrtc-experiment.com/EBML.js");var x=new EBML.Reader,E=new EBML.Decoder,R=EBML.tools,z=new FileReader;z.onload=function(I){var C=E.decode(this.result);C.forEach(function(J){x.read(J)}),x.stop();var j=R.makeMetadataSeekable(x.metadatas,x.duration,x.cues),$=this.result.slice(x.metadataSize),U=new Blob([j,$],{type:"video/webm"});l(U)},z.readAsArrayBuffer(g)}typeof u<"u"&&(u.invokeSaveAsDialog=p,u.getTracks=w,u.getSeekableBlob=k,u.bytesToSize=S,u.isElectron=d);var M={};typeof f<"u"?M.AudioContext=f:typeof webkitAudioContext<"u"&&(M.AudioContext=webkitAudioContext),typeof u<"u"&&(u.Storage=M);function L(){if(A||b||v)return!0;var g=navigator.userAgent,l=""+parseFloat(navigator.appVersion),x=parseInt(navigator.appVersion,10),E,R;return(a||_)&&(E=g.indexOf("Chrome"),l=g.substring(E+7)),(R=l.indexOf(";"))!==-1&&(l=l.substring(0,R)),(R=l.indexOf(" "))!==-1&&(l=l.substring(0,R)),x=parseInt(""+l,10),isNaN(x)&&(l=""+parseFloat(navigator.appVersion),x=parseInt(navigator.appVersion,10)),x>=49}function q(g,l){var U=this;if(typeof g>"u")throw'First argument "MediaStream" is required.';if(typeof MediaRecorder>"u")throw"Your browser does not support the Media Recorder API. Please try other modules e.g. WhammyRecorder or StereoAudioRecorder.";if(l=l||{mimeType:"video/webm"},l.type==="audio"){if(w(g,"video").length&&w(g,"audio").length){var x;navigator.mozGetUserMedia?(x=new m,x.addTrack(w(g,"audio")[0])):x=new m(w(g,"audio")),g=x}(!l.mimeType||l.mimeType.toString().toLowerCase().indexOf("audio")===-1)&&(l.mimeType=a?"audio/webm":"audio/ogg"),l.mimeType&&l.mimeType.toString().toLowerCase()!=="audio/ogg"&&navigator.mozGetUserMedia&&(l.mimeType="audio/ogg")}var E=[];this.getArrayOfBlobs=function(){return E},this.record=function(){U.blob=null,U.clearRecordedData(),U.timestamps=[],$=[],E=[];var J=l;l.disableLogs,C&&(C=null),a&&!L()&&(J="video/vp8"),typeof MediaRecorder.isTypeSupported=="function"&&J.mimeType&&(MediaRecorder.isTypeSupported(J.mimeType)||(l.disableLogs||console.warn("MediaRecorder API seems unable to record mimeType:",J.mimeType),J.mimeType=l.type==="audio"?"audio/webm":"video/webm"));try{C=new MediaRecorder(g,J),l.mimeType=J.mimeType}catch{C=new MediaRecorder(g)}J.mimeType&&!MediaRecorder.isTypeSupported&&"canRecordMimeType"in C&&C.canRecordMimeType(J.mimeType)===!1&&(l.disableLogs||console.warn("MediaRecorder API seems unable to record mimeType:",J.mimeType)),C.ondataavailable=function(V){if(V.data&&$.push("ondataavailable: "+S(V.data.size)),typeof l.timeSlice=="number"){if(V.data&&V.data.size&&(E.push(V.data),R(),typeof l.ondataavailable=="function")){var W=l.getNativeBlob?V.data:new Blob([V.data],{type:z(J)});l.ondataavailable(W)}return}if(!V.data||!V.data.size||V.data.size<100||U.blob){U.recordingCallback&&(U.recordingCallback(new Blob([],{type:z(J)})),U.recordingCallback=null);return}U.blob=l.getNativeBlob?V.data:new Blob([V.data],{type:z(J)}),U.recordingCallback&&(U.recordingCallback(U.blob),U.recordingCallback=null)},C.onstart=function(){$.push("started")},C.onpause=function(){$.push("paused")},C.onresume=function(){$.push("resumed")},C.onstop=function(){$.push("stopped")},C.onerror=function(V){V&&(V.name||(V.name="UnknownError"),$.push("error: "+V),l.disableLogs||(V.name.toString().toLowerCase().indexOf("invalidstate")!==-1?console.error("The MediaRecorder is not in a state in which the proposed operation is allowed to be executed.",V):V.name.toString().toLowerCase().indexOf("notsupported")!==-1?console.error("MIME type (",J.mimeType,") is not supported.",V):V.name.toString().toLowerCase().indexOf("security")!==-1?console.error("MediaRecorder security error",V):V.name==="OutOfMemory"?console.error("The UA has exhaused the available memory. User agents SHOULD provide as much additional information as possible in the message attribute.",V):V.name==="IllegalStreamModification"?console.error("A modification to the stream has occurred that makes it impossible to continue recording. An example would be the addition of a Track while recording is occurring. User agents SHOULD provide as much additional information as possible in the message attribute.",V):V.name==="OtherRecordingError"?console.error("Used for an fatal error other than those listed above. User agents SHOULD provide as much additional information as possible in the message attribute.",V):V.name==="GenericError"?console.error("The UA cannot provide the codec or recording option that has been requested.",V):console.error("MediaRecorder Error",V)),function(W){if(!U.manuallyStopped&&C&&C.state==="inactive"){delete l.timeslice,C.start(10*60*1e3);return}setTimeout(W,1e3)}(),C.state!=="inactive"&&C.state!=="stopped"&&C.stop())},typeof l.timeSlice=="number"?(R(),C.start(l.timeSlice)):C.start(36e5),l.initCallback&&l.initCallback()},this.timestamps=[];function R(){U.timestamps.push(new Date().getTime()),typeof l.onTimeStamp=="function"&&l.onTimeStamp(U.timestamps[U.timestamps.length-1],U.timestamps)}function z(J){return C&&C.mimeType?C.mimeType:J.mimeType||"video/webm"}this.stop=function(J){J=J||function(){},U.manuallyStopped=!0,C&&(this.recordingCallback=J,C.state==="recording"&&C.stop(),typeof l.timeSlice=="number"&&setTimeout(function(){U.blob=new Blob(E,{type:z(l)}),U.recordingCallback(U.blob)},100))},this.pause=function(){C&&C.state==="recording"&&C.pause()},this.resume=function(){C&&C.state==="paused"&&C.resume()},this.clearRecordedData=function(){C&&C.state==="recording"&&U.stop(I),I()};function I(){E=[],C=null,U.timestamps=[]}var C;this.getInternalRecorder=function(){return C};function j(){if("active"in g){if(!g.active)return!1}else if("ended"in g&&g.ended)return!1;return!0}this.blob=null,this.getState=function(){return C&&C.state||"inactive"};var $=[];this.getAllStates=function(){return $},typeof l.checkForInactiveTracks>"u"&&(l.checkForInactiveTracks=!1);var U=this;(function J(){if(!(!C||l.checkForInactiveTracks===!1)){if(j()===!1){l.disableLogs,U.stop();return}setTimeout(J,1e3)}})(),this.name="MediaStreamRecorder",this.toString=function(){return this.name}}typeof u<"u"&&(u.MediaStreamRecorder=q);function D(g,l){if(!w(g,"audio").length)throw"Your stream has no audio tracks.";l=l||{};var x=this,E=[],R=[],z=!1,I=0,C,j=2,$=l.desiredSampRate;l.leftChannel===!0&&(j=1),l.numberOfAudioChannels===1&&(j=1),(!j||j<1)&&(j=2),l.disableLogs||""+j,typeof l.checkForInactiveTracks>"u"&&(l.checkForInactiveTracks=!0);function U(){if(l.checkForInactiveTracks===!1)return!0;if("active"in g){if(!g.active)return!1}else if("ended"in g&&g.ended)return!1;return!0}this.record=function(){if(U()===!1)throw"Please make sure MediaStream is active.";ne(),P=K=!1,z=!0,typeof l.timeSlice<"u"&&oe()};function J(ae,me){function be(fe,t0){var Ee=fe.numberOfAudioChannels,ye=fe.leftBuffers.slice(0),Ie=fe.rightBuffers.slice(0),we=fe.sampleRate,Pe=fe.internalInterleavedLength,Se=fe.desiredSampRate;Ee===2&&(ye=M0(ye,Pe),Ie=M0(Ie,Pe),Se&&(ye=r0(ye,Se,we),Ie=r0(Ie,Se,we))),Ee===1&&(ye=M0(ye,Pe),Se&&(ye=r0(ye,Se,we))),Se&&(we=Se);function r0(de,Re,_e){var Ce=Math.round(de.length*(Re/_e)),ge=[],Ne=Number((de.length-1)/(Ce-1));ge[0]=de[0];for(var $e=1;$e<Ce-1;$e++){var L0=$e*Ne,Su=Number(Math.floor(L0)).toFixed(),tr=Number(Math.ceil(L0)).toFixed(),rr=L0-Su;ge[$e]=Kt(de[Su],de[tr],rr)}return ge[Ce-1]=de[de.length-1],ge}function Kt(de,Re,_e){return de+(Re-de)*_e}function M0(de,Re){for(var _e=new Float64Array(Re),Ce=0,ge=de.length,Ne=0;Ne<ge;Ne++){var $e=de[Ne];_e.set($e,Ce),Ce+=$e.length}return _e}function Qt(de,Re){for(var _e=de.length+Re.length,Ce=new Float64Array(_e),ge=0,Ne=0;Ne<_e;)Ce[Ne++]=de[ge],Ce[Ne++]=Re[ge],ge++;return Ce}function p0(de,Re,_e){for(var Ce=_e.length,ge=0;ge<Ce;ge++)de.setUint8(Re+ge,_e.charCodeAt(ge))}var m0;Ee===2&&(m0=Qt(ye,Ie)),Ee===1&&(m0=ye);var b0=m0.length,Yt=44+b0*2,z0=new ArrayBuffer(Yt),ve=new DataView(z0);p0(ve,0,"RIFF"),ve.setUint32(4,36+b0*2,!0),p0(ve,8,"WAVE"),p0(ve,12,"fmt "),ve.setUint32(16,16,!0),ve.setUint16(20,1,!0),ve.setUint16(22,Ee,!0),ve.setUint32(24,we,!0),ve.setUint32(28,we*Ee*2,!0),ve.setUint16(32,Ee*2,!0),ve.setUint16(34,16,!0),p0(ve,36,"data"),ve.setUint32(40,b0*2,!0);for(var er=b0,Eu=44,ur=1,B0=0;B0<er;B0++)ve.setInt16(Eu,m0[B0]*(32767*ur),!0),Eu+=2;if(t0)return t0({buffer:z0,view:ve});postMessage({buffer:z0,view:ve})}if(ae.noWorker){be(ae,function(fe){me(fe.buffer,fe.view)});return}var qe=V(be);qe.onmessage=function(fe){me(fe.data.buffer,fe.data.view),h.revokeObjectURL(qe.workerURL),qe.terminate()},qe.postMessage(ae)}function V(ae){var me=h.createObjectURL(new Blob([ae.toString(),";this.onmessage =  function (eee) {"+ae.name+"(eee.data);}"],{type:"application/javascript"})),be=new Worker(me);return be.workerURL=me,be}this.stop=function(ae){ae=ae||function(){},z=!1,J({desiredSampRate:$,sampleRate:B,numberOfAudioChannels:j,internalInterleavedLength:I,leftBuffers:E,rightBuffers:j===1?[]:R,noWorker:l.noWorker},function(me,be){x.blob=new Blob([be],{type:"audio/wav"}),x.buffer=new ArrayBuffer(be.buffer.byteLength),x.view=be,x.sampleRate=$||B,x.bufferSize=ie,x.length=I,P=!1,ae&&ae(x.blob)})},typeof u.Storage>"u"&&(u.Storage={AudioContextConstructor:null,AudioContext:window.AudioContext||window.webkitAudioContext}),(!u.Storage.AudioContextConstructor||u.Storage.AudioContextConstructor.state==="closed")&&(u.Storage.AudioContextConstructor=new u.Storage.AudioContext);var W=u.Storage.AudioContextConstructor,O=W.createMediaStreamSource(g),Y=[0,256,512,1024,2048,4096,8192,16384],ie=typeof l.bufferSize>"u"?4096:l.bufferSize;if(Y.indexOf(ie)===-1&&(l.disableLogs||""+JSON.stringify(Y,null,"	")),W.createJavaScriptNode)C=W.createJavaScriptNode(ie,j,j);else if(W.createScriptProcessor)C=W.createScriptProcessor(ie,j,j);else throw"WebAudio API has no support on this browser.";O.connect(C),l.bufferSize||(ie=C.bufferSize);var B=typeof l.sampleRate<"u"?l.sampleRate:W.sampleRate||44100;(B<22050||B>96e3)&&l.disableLogs,l.disableLogs||l.desiredSampRate&&""+l.desiredSampRate;var K=!1;this.pause=function(){K=!0},this.resume=function(){if(U()===!1)throw"Please make sure MediaStream is active.";if(!z){l.disableLogs,this.record();return}K=!1},this.clearRecordedData=function(){l.checkForInactiveTracks=!1,z&&this.stop(F),F()};function ne(){E=[],R=[],I=0,P=!1,z=!1,K=!1,W=null,x.leftchannel=E,x.rightchannel=R,x.numberOfAudioChannels=j,x.desiredSampRate=$,x.sampleRate=B,x.recordingLength=I,re={left:[],right:[],recordingLength:0}}function F(){C&&(C.onaudioprocess=null,C.disconnect(),C=null),O&&(O.disconnect(),O=null),ne()}this.name="StereoAudioRecorder",this.toString=function(){return this.name};var P=!1;function ue(ae){if(!K){if(U()===!1&&(l.disableLogs,C.disconnect(),z=!1),!z){O&&(O.disconnect(),O=null);return}P||(P=!0,l.onAudioProcessStarted&&l.onAudioProcessStarted(),l.initCallback&&l.initCallback());var me=ae.inputBuffer.getChannelData(0),be=new Float32Array(me);if(E.push(be),j===2){var qe=ae.inputBuffer.getChannelData(1),fe=new Float32Array(qe);R.push(fe)}I+=ie,x.recordingLength=I,typeof l.timeSlice<"u"&&(re.recordingLength+=ie,re.left.push(be),j===2&&re.right.push(fe))}}C.onaudioprocess=ue,W.createMediaStreamDestination?C.connect(W.createMediaStreamDestination()):C.connect(W.destination),this.leftchannel=E,this.rightchannel=R,this.numberOfAudioChannels=j,this.desiredSampRate=$,this.sampleRate=B,x.recordingLength=I;var re={left:[],right:[],recordingLength:0};function oe(){!z||typeof l.ondataavailable!="function"||typeof l.timeSlice>"u"||(re.left.length?(J({desiredSampRate:$,sampleRate:B,numberOfAudioChannels:j,internalInterleavedLength:re.recordingLength,leftBuffers:re.left,rightBuffers:j===1?[]:re.right},function(ae,me){var be=new Blob([me],{type:"audio/wav"});l.ondataavailable(be),setTimeout(oe,l.timeSlice)}),re={left:[],right:[],recordingLength:0}):setTimeout(oe,l.timeSlice))}}typeof u<"u"&&(u.StereoAudioRecorder=D);function Z(g,l){if(typeof html2canvas>"u")throw"Please link: https://www.webrtc-experiment.com/screenshot.js";l=l||{},l.frameInterval||(l.frameInterval=10);var x=!1;["captureStream","mozCaptureStream","webkitCaptureStream"].forEach(function(Y){Y in document.createElement("canvas")&&(x=!0)});var E=(!!window.webkitRTCPeerConnection||!!window.webkitGetUserMedia)&&!!window.chrome,R=50,z=navigator.userAgent.match(/Chrom(e|ium)\/([0-9]+)\./);E&&z&&z[2]&&(R=parseInt(z[2],10)),E&&R<52&&(x=!1),l.useWhammyRecorder&&(x=!1);var I,C;if(x)if(l.disableLogs,g instanceof HTMLCanvasElement)I=g;else if(g instanceof CanvasRenderingContext2D)I=g.canvas;else throw"Please pass either HTMLCanvasElement or CanvasRenderingContext2D.";else navigator.mozGetUserMedia&&(l.disableLogs||console.error("Canvas recording is NOT supported in Firefox."));var j;this.record=function(){if(j=!0,x&&!l.useWhammyRecorder){var Y;"captureStream"in I?Y=I.captureStream(25):"mozCaptureStream"in I?Y=I.mozCaptureStream(25):"webkitCaptureStream"in I&&(Y=I.webkitCaptureStream(25));try{var ie=new m;ie.addTrack(w(Y,"video")[0]),Y=ie}catch{}if(!Y)throw"captureStream API are NOT available.";C=new q(Y,{mimeType:l.mimeType||"video/webm"}),C.record()}else O.frames=[],W=new Date().getTime(),V();l.initCallback&&l.initCallback()},this.getWebPImages=function(Y){if(g.nodeName.toLowerCase()!=="canvas"){Y();return}var ie=O.frames.length;O.frames.forEach(function(B,K){var ne=ie-K;l.disableLogs||ne+""+ie,l.onEncodingCallback&&l.onEncodingCallback(ne,ie);var F=B.image.toDataURL("image/webp",1);O.frames[K].image=F}),l.disableLogs,Y()},this.stop=function(Y){j=!1;var ie=this;if(x&&C){C.stop(Y);return}this.getWebPImages(function(){O.compile(function(B){l.disableLogs,ie.blob=B,ie.blob.forEach&&(ie.blob=new Blob([],{type:"video/webm"})),Y&&Y(ie.blob),O.frames=[]})})};var $=!1;this.pause=function(){if($=!0,C instanceof q){C.pause();return}},this.resume=function(){if($=!1,C instanceof q){C.resume();return}j||this.record()},this.clearRecordedData=function(){j&&this.stop(U),U()};function U(){O.frames=[],j=!1,$=!1}this.name="CanvasRecorder",this.toString=function(){return this.name};function J(){var Y=document.createElement("canvas"),ie=Y.getContext("2d");return Y.width=g.width,Y.height=g.height,ie.drawImage(g,0,0),Y}function V(){if($)return W=new Date().getTime(),setTimeout(V,500);if(g.nodeName.toLowerCase()==="canvas"){var Y=new Date().getTime()-W;W=new Date().getTime(),O.frames.push({image:J(),duration:Y}),j&&setTimeout(V,l.frameInterval);return}html2canvas(g,{grabMouse:typeof l.showMousePointer>"u"||l.showMousePointer,onrendered:function(ie){var B=new Date().getTime()-W;if(!B)return setTimeout(V,l.frameInterval);W=new Date().getTime(),O.frames.push({image:ie.toDataURL("image/webp",1),duration:B}),j&&setTimeout(V,l.frameInterval)}})}var W=new Date().getTime(),O=new Q.Video(100)}typeof u<"u"&&(u.CanvasRecorder=Z);function G(g,l){l=l||{},l.frameInterval||(l.frameInterval=10),l.disableLogs||l.frameInterval,this.record=function(){l.width||(l.width=320),l.height||(l.height=240),l.video||(l.video={width:l.width,height:l.height}),l.canvas||(l.canvas={width:l.width,height:l.height}),j.width=l.canvas.width||320,j.height=l.canvas.height||240,$=j.getContext("2d"),l.video&&l.video instanceof HTMLVideoElement?(U=l.video.cloneNode(),l.initCallback&&l.initCallback()):(U=document.createElement("video"),y(g,U),U.onloadedmetadata=function(){l.initCallback&&l.initCallback()},U.width=l.video.width,U.height=l.video.height),U.muted=!0,U.play(),J=new Date().getTime(),V=new Q.Video,l.disableLogs||(j.width,j.height,U.width||j.width,U.height||j.height),x(l.frameInterval)};function x(W){W=typeof W<"u"?W:10;var O=new Date().getTime()-J;if(!O)return setTimeout(x,W,W);if(I)return J=new Date().getTime(),setTimeout(x,100);J=new Date().getTime(),U.paused&&U.play(),$.drawImage(U,0,0,j.width,j.height),V.frames.push({duration:O,image:j.toDataURL("image/webp")}),z||setTimeout(x,W,W)}function E(W){var O=-1,Y=W.length;(function ie(){if(O++,O===Y){W.callback();return}setTimeout(function(){W.functionToLoop(ie,O)},1)})()}function R(W,O,Y,ie,B){var K=document.createElement("canvas");K.width=j.width,K.height=j.height;var ne=K.getContext("2d"),F=[],P=W.length,ue={r:0,g:0,b:0},re=Math.sqrt(Math.pow(255,2)+Math.pow(255,2)+Math.pow(255,2)),oe=0,ae=0,me=!1;E({length:P,functionToLoop:function(be,qe){var fe,t0,Ee,ye=function(){!me&&Ee-fe<=Ee*ae||(me=!0,F.push(W[qe])),be()};if(me)ye();else{var Ie=new Image;Ie.onload=function(){ne.drawImage(Ie,0,0,j.width,j.height);var we=ne.getImageData(0,0,j.width,j.height);fe=0,t0=we.data.length,Ee=we.data.length/4;for(var Pe=0;Pe<t0;Pe+=4){var Se={r:we.data[Pe],g:we.data[Pe+1],b:we.data[Pe+2]},r0=Math.sqrt(Math.pow(Se.r-ue.r,2)+Math.pow(Se.g-ue.g,2)+Math.pow(Se.b-ue.b,2));r0<=re*oe&&fe++}ye()},Ie.src=W[qe].image}},callback:function(){F=F.concat(W.slice(P)),F.length<=0&&F.push(W[W.length-1]),B(F)}})}var z=!1;this.stop=function(W){W=W||function(){},z=!0;var O=this;setTimeout(function(){R(V.frames,-1,null,null,function(Y){V.frames=Y,l.advertisement&&l.advertisement.length&&(V.frames=l.advertisement.concat(V.frames)),V.compile(function(ie){O.blob=ie,O.blob.forEach&&(O.blob=new Blob([],{type:"video/webm"})),W&&W(O.blob)})})},10)};var I=!1;this.pause=function(){I=!0},this.resume=function(){I=!1,z&&this.record()},this.clearRecordedData=function(){z||this.stop(C),C()};function C(){V.frames=[],z=!0,I=!1}this.name="WhammyRecorder",this.toString=function(){return this.name};var j=document.createElement("canvas"),$=j.getContext("2d"),U,J,V}typeof u<"u"&&(u.WhammyRecorder=G);var Q=function(){function g(E){this.frames=[],this.duration=E||1,this.quality=.8}g.prototype.add=function(E,R){if("canvas"in E&&(E=E.canvas),"toDataURL"in E&&(E=E.toDataURL("image/webp",this.quality)),!/^data:image\/webp;base64,/ig.test(E))throw"Input must be formatted properly as a base64 encoded DataURI of type image/webp";this.frames.push({image:E,duration:R||this.duration})};function l(E){var R=h.createObjectURL(new Blob([E.toString(),"this.onmessage =  function (eee) {"+E.name+"(eee.data);}"],{type:"application/javascript"})),z=new Worker(R);return h.revokeObjectURL(R),z}function x(E){function R(B){var K=I(B);if(!K)return[];for(var ne=3e4,F=[{id:440786851,data:[{data:1,id:17030},{data:1,id:17143},{data:4,id:17138},{data:8,id:17139},{data:"webm",id:17026},{data:2,id:17031},{data:2,id:17029}]},{id:408125543,data:[{id:357149030,data:[{data:1e6,id:2807729},{data:"whammy",id:19840},{data:"whammy",id:22337},{data:Y(K.duration),id:17545}]},{id:374648427,data:[{id:174,data:[{data:1,id:215},{data:1,id:29637},{data:0,id:156},{data:"und",id:2274716},{data:"V_VP8",id:134},{data:"VP8",id:2459272},{data:1,id:131},{id:224,data:[{data:K.width,id:176},{data:K.height,id:186}]}]}]}]}],P=0,ue=0;P<B.length;){var re=[],oe=0;do re.push(B[P]),oe+=B[P].duration,P++;while(P<B.length&&oe<ne);var ae=0,me={id:524531317,data:z(ue,ae,re)};F[1].data.push(me),ue+=oe}return U(F)}function z(B,K,ne){return[{data:B,id:231}].concat(ne.map(function(F){var P=J({frame:F.data.slice(4),trackNum:1,timecode:Math.round(K)});return K+=F.duration,{data:P,id:163}}))}function I(B){if(!B[0]){postMessage({error:"Something went wrong. Maybe WebP format is not supported in the current browser."});return}for(var K=B[0].width,ne=B[0].height,F=B[0].duration,P=1;P<B.length;P++)F+=B[P].duration;return{duration:F,width:K,height:ne}}function C(B){for(var K=[];B>0;)K.push(B&255),B=B>>8;return new Uint8Array(K.reverse())}function j(B){return new Uint8Array(B.split("").map(function(K){return K.charCodeAt(0)}))}function $(B){var K=[],ne=B.length%8?new Array(9-B.length%8).join("0"):"";B=ne+B;for(var F=0;F<B.length;F+=8)K.push(parseInt(B.substr(F,8),2));return new Uint8Array(K)}function U(B){for(var K=[],ne=0;ne<B.length;ne++){var F=B[ne].data;typeof F=="object"&&(F=U(F)),typeof F=="number"&&(F=$(F.toString(2))),typeof F=="string"&&(F=j(F));var P=F.size||F.byteLength||F.length,ue=Math.ceil(Math.ceil(Math.log(P)/Math.log(2))/8),re=P.toString(2),oe=new Array(ue*7+7+1-re.length).join("0")+re,ae=new Array(ue).join("0")+"1"+oe;K.push(C(B[ne].id)),K.push($(ae)),K.push(F)}return new Blob(K,{type:"video/webm"})}function J(B){var K=0;K|=128;var ne=[B.trackNum|128,B.timecode>>8,B.timecode&255,K].map(function(F){return String.fromCharCode(F)}).join("")+B.frame;return ne}function V(B){for(var K=B.RIFF[0].WEBP[0],ne=K.indexOf("*"),F=0,P=[];F<4;F++)P[F]=K.charCodeAt(ne+3+F);var ue,re,oe;return oe=P[1]<<8|P[0],ue=oe&16383,oe=P[3]<<8|P[2],re=oe&16383,{width:ue,height:re,data:K,riff:B}}function W(B,K){return parseInt(B.substr(K+4,4).split("").map(function(ne){var F=ne.charCodeAt(0).toString(2);return new Array(8-F.length+1).join("0")+F}).join(""),2)}function O(B){for(var K=0,ne={};K<B.length;){var F=B.substr(K,4),P=W(B,K),ue=B.substr(K+4+4,P);K+=8+P,ne[F]=ne[F]||[],F==="RIFF"||F==="LIST"?ne[F].push(O(ue)):ne[F].push(ue)}return ne}function Y(B){return[].slice.call(new Uint8Array(new Float64Array([B]).buffer),0).map(function(K){return String.fromCharCode(K)}).reverse().join("")}var ie=new R(E.map(function(B){var K=V(O(atob(B.image.slice(23))));return K.duration=B.duration,K}));postMessage(ie)}return g.prototype.compile=function(E){var R=l(x);R.onmessage=function(z){if(z.data.error){console.error(z.data.error);return}E(z.data)},R.postMessage(this.frames)},{Video:g}}();typeof u<"u"&&(u.Whammy=Q);var X={init:function(){var g=this;if(typeof indexedDB>"u"||typeof indexedDB.open>"u"){console.error("IndexedDB API are not available in this browser.");return}var l=1,x=this.dbName||location.href.replace(/\/|:|#|%|\.|\[|\]/g,""),E,R=indexedDB.open(x,l);function z(C){C.createObjectStore(g.dataStoreName)}function I(){var C=E.transaction([g.dataStoreName],"readwrite");g.videoBlob&&C.objectStore(g.dataStoreName).put(g.videoBlob,"videoBlob"),g.gifBlob&&C.objectStore(g.dataStoreName).put(g.gifBlob,"gifBlob"),g.audioBlob&&C.objectStore(g.dataStoreName).put(g.audioBlob,"audioBlob");function j($){C.objectStore(g.dataStoreName).get($).onsuccess=function(U){g.callback&&g.callback(U.target.result,$)}}j("audioBlob"),j("videoBlob"),j("gifBlob")}R.onerror=g.onError,R.onsuccess=function(){if(E=R.result,E.onerror=g.onError,E.setVersion)if(E.version!==l){var C=E.setVersion(l);C.onsuccess=function(){z(E),I()}}else I();else I()},R.onupgradeneeded=function(C){z(C.target.result)}},Fetch:function(g){return this.callback=g,this.init(),this},Store:function(g){return this.audioBlob=g.audioBlob,this.videoBlob=g.videoBlob,this.gifBlob=g.gifBlob,this.init(),this},onError:function(g){console.error(JSON.stringify(g,null,"	"))},dataStoreName:"recordRTC",dbName:null};typeof u<"u"&&(u.DiskStorage=X);function T(g,l){if(typeof GIFEncoder>"u"){var x=document.createElement("script");x.src="https://www.webrtc-experiment.com/gif-recorder.js",(document.body||document.documentElement).appendChild(x)}l=l||{};var E=g instanceof CanvasRenderingContext2D||g instanceof HTMLCanvasElement;this.record=function(){if(typeof GIFEncoder>"u"){setTimeout(W.record,1e3);return}if(!j){setTimeout(W.record,1e3);return}E||(l.width||(l.width=$.offsetWidth||320),l.height||(l.height=$.offsetHeight||240),l.video||(l.video={width:l.width,height:l.height}),l.canvas||(l.canvas={width:l.width,height:l.height}),I.width=l.canvas.width||320,I.height=l.canvas.height||240,$.width=l.video.width||320,$.height=l.video.height||240),V=new GIFEncoder,V.setRepeat(0),V.setDelay(l.frameRate||200),V.setQuality(l.quality||10),V.start(),typeof l.onGifRecordingStarted=="function"&&l.onGifRecordingStarted();function O(Y){if(W.clearedRecordedData!==!0){if(R)return setTimeout(function(){O(Y)},100);U=c(O),typeof J===void 0&&(J=Y),!(Y-J<90)&&(!E&&$.paused&&$.play(),E||C.drawImage($,0,0,I.width,I.height),l.onGifPreview&&l.onGifPreview(I.toDataURL("image/png")),V.addFrame(C),J=Y)}}U=c(O),l.initCallback&&l.initCallback()},this.stop=function(O){O=O||function(){},U&&s(U),this.blob=new Blob([new Uint8Array(V.stream().bin)],{type:"image/gif"}),O(this.blob),V.stream().bin=[]};var R=!1;this.pause=function(){R=!0},this.resume=function(){R=!1},this.clearRecordedData=function(){W.clearedRecordedData=!0,z()};function z(){V&&(V.stream().bin=[])}this.name="GifRecorder",this.toString=function(){return this.name};var I=document.createElement("canvas"),C=I.getContext("2d");E&&(g instanceof CanvasRenderingContext2D?(C=g,I=C.canvas):g instanceof HTMLCanvasElement&&(C=g.getContext("2d"),I=g));var j=!0;if(!E){var $=document.createElement("video");$.muted=!0,$.autoplay=!0,$.playsInline=!0,j=!1,$.onloadedmetadata=function(){j=!0},y(g,$),$.play()}var U=null,J,V,W=this}typeof u<"u"&&(u.GifRecorder=T);function N(g,l){var x="Fake/5.0 (FakeOS) AppleWebKit/123 (KHTML, like Gecko) Fake/12.3.4567.89 Fake/123.45";(function(F){typeof u<"u"||F&&(typeof window<"u"||typeof ce>"u"||(ce.navigator={userAgent:x,getUserMedia:function(){}},ce.console||(ce.console={}),(typeof ce.console.log>"u"||typeof ce.console.error>"u")&&(ce.console.error=ce.console.log=ce.console.log||function(){}),typeof document>"u"&&(F.document={documentElement:{appendChild:function(){return""}}},document.createElement=document.captureStream=document.mozCaptureStream=function(){var P={getContext:function(){return P},play:function(){},pause:function(){},drawImage:function(){},toDataURL:function(){return""},style:{}};return P},F.HTMLVideoElement=function(){}),typeof location>"u"&&(F.location={protocol:"file:",href:"",hash:""}),typeof screen>"u"&&(F.screen={width:0,height:0}),typeof $>"u"&&(F.URL={createObjectURL:function(){return""},revokeObjectURL:function(){return""}}),F.window=ce))})(typeof ce<"u"?ce:null),l=l||"multi-streams-mixer";var E=[],R=!1,z=document.createElement("canvas"),I=z.getContext("2d");z.style.opacity=0,z.style.position="absolute",z.style.zIndex=-1,z.style.top="-1000em",z.style.left="-1000em",z.className=l,(document.body||document.documentElement).appendChild(z),this.disableLogs=!1,this.frameInterval=10,this.width=360,this.height=240,this.useGainNode=!0;var C=this,j=window.AudioContext;typeof j>"u"&&(typeof webkitAudioContext<"u"&&(j=webkitAudioContext),typeof mozAudioContext<"u"&&(j=mozAudioContext));var $=window.URL;typeof $>"u"&&typeof webkitURL<"u"&&($=webkitURL),typeof navigator<"u"&&typeof navigator.getUserMedia>"u"&&(typeof navigator.webkitGetUserMedia<"u"&&(navigator.getUserMedia=navigator.webkitGetUserMedia),typeof navigator.mozGetUserMedia<"u"&&(navigator.getUserMedia=navigator.mozGetUserMedia));var U=window.MediaStream;typeof U>"u"&&typeof webkitMediaStream<"u"&&(U=webkitMediaStream),typeof U<"u"&&typeof U.prototype.stop>"u"&&(U.prototype.stop=function(){this.getTracks().forEach(function(F){F.stop()})});var J={};typeof j<"u"?J.AudioContext=j:typeof webkitAudioContext<"u"&&(J.AudioContext=webkitAudioContext);function V(F,P){"srcObject"in P?P.srcObject=F:"mozSrcObject"in P?P.mozSrcObject=F:P.srcObject=F}this.startDrawingFrames=function(){W()};function W(){if(!R){var F=E.length,P=!1,ue=[];if(E.forEach(function(oe){oe.stream||(oe.stream={}),oe.stream.fullcanvas?P=oe:ue.push(oe)}),P)z.width=P.stream.width,z.height=P.stream.height;else if(ue.length){z.width=F>1?ue[0].width*2:ue[0].width;var re=1;(F===3||F===4)&&(re=2),(F===5||F===6)&&(re=3),(F===7||F===8)&&(re=4),(F===9||F===10)&&(re=5),z.height=ue[0].height*re}else z.width=C.width||360,z.height=C.height||240;P&&P instanceof HTMLVideoElement&&O(P),ue.forEach(function(oe,ae){O(oe,ae)}),setTimeout(W,C.frameInterval)}}function O(F,P){if(!R){var ue=0,re=0,oe=F.width,ae=F.height;P===1&&(ue=F.width),P===2&&(re=F.height),P===3&&(ue=F.width,re=F.height),P===4&&(re=F.height*2),P===5&&(ue=F.width,re=F.height*2),P===6&&(re=F.height*3),P===7&&(ue=F.width,re=F.height*3),typeof F.stream.left<"u"&&(ue=F.stream.left),typeof F.stream.top<"u"&&(re=F.stream.top),typeof F.stream.width<"u"&&(oe=F.stream.width),typeof F.stream.height<"u"&&(ae=F.stream.height),I.drawImage(F,ue,re,oe,ae),typeof F.stream.onRender=="function"&&F.stream.onRender(I,ue,re,oe,ae,P)}}function Y(){R=!1;var F=ie(),P=B();return P&&P.getTracks().filter(function(ue){return ue.kind==="audio"}).forEach(function(ue){F.addTrack(ue)}),g.forEach(function(ue){ue.fullcanvas}),F}function ie(){ne();var F;"captureStream"in z?F=z.captureStream():"mozCaptureStream"in z?F=z.mozCaptureStream():C.disableLogs||console.error("Upgrade to latest Chrome or otherwise enable this flag: chrome://flags/#enable-experimental-web-platform-features");var P=new U;return F.getTracks().filter(function(ue){return ue.kind==="video"}).forEach(function(ue){P.addTrack(ue)}),z.stream=P,P}function B(){J.AudioContextConstructor||(J.AudioContextConstructor=new J.AudioContext),C.audioContext=J.AudioContextConstructor,C.audioSources=[],C.useGainNode===!0&&(C.gainNode=C.audioContext.createGain(),C.gainNode.connect(C.audioContext.destination),C.gainNode.gain.value=0);var F=0;if(g.forEach(function(P){if(P.getTracks().filter(function(re){return re.kind==="audio"}).length){F++;var ue=C.audioContext.createMediaStreamSource(P);C.useGainNode===!0&&ue.connect(C.gainNode),C.audioSources.push(ue)}}),!!F)return C.audioDestination=C.audioContext.createMediaStreamDestination(),C.audioSources.forEach(function(P){P.connect(C.audioDestination)}),C.audioDestination.stream}function K(F){var P=document.createElement("video");return V(F,P),P.className=l,P.muted=!0,P.volume=0,P.width=F.width||C.width||360,P.height=F.height||C.height||240,P.play(),P}this.appendStreams=function(F){if(!F)throw"First parameter is required.";F instanceof Array||(F=[F]),F.forEach(function(P){var ue=new U;if(P.getTracks().filter(function(ae){return ae.kind==="video"}).length){var re=K(P);re.stream=P,E.push(re),ue.addTrack(P.getTracks().filter(function(ae){return ae.kind==="video"})[0])}if(P.getTracks().filter(function(ae){return ae.kind==="audio"}).length){var oe=C.audioContext.createMediaStreamSource(P);C.audioDestination=C.audioContext.createMediaStreamDestination(),oe.connect(C.audioDestination),ue.addTrack(C.audioDestination.stream.getTracks().filter(function(ae){return ae.kind==="audio"})[0])}g.push(ue)})},this.releaseStreams=function(){E=[],R=!0,C.gainNode&&(C.gainNode.disconnect(),C.gainNode=null),C.audioSources.length&&(C.audioSources.forEach(function(F){F.disconnect()}),C.audioSources=[]),C.audioDestination&&(C.audioDestination.disconnect(),C.audioDestination=null),C.audioContext&&C.audioContext.close(),C.audioContext=null,I.clearRect(0,0,z.width,z.height),z.stream&&(z.stream.stop(),z.stream=null)},this.resetVideoStreams=function(F){F&&!(F instanceof Array)&&(F=[F]),ne(F)};function ne(F){E=[],F=F||g,F.forEach(function(P){if(P.getTracks().filter(function(re){return re.kind==="video"}).length){var ue=K(P);ue.stream=P,E.push(ue)}})}this.name="MultiStreamsMixer",this.toString=function(){return this.name},this.getMixedStream=Y}typeof u>"u"&&(e.exports=N);function H(g,l){g=g||[];var x=this,E,R;l=l||{elementClass:"multi-streams-mixer",mimeType:"video/webm",video:{width:360,height:240}},l.frameInterval||(l.frameInterval=10),l.video||(l.video={}),l.video.width||(l.video.width=360),l.video.height||(l.video.height=240),this.record=function(){E=new N(g,l.elementClass||"multi-streams-mixer"),z().length&&(E.frameInterval=l.frameInterval||10,E.width=l.video.width||360,E.height=l.video.height||240,E.startDrawingFrames()),l.previewStream&&typeof l.previewStream=="function"&&l.previewStream(E.getMixedStream()),R=new q(E.getMixedStream(),l),R.record()};function z(){var I=[];return g.forEach(function(C){w(C,"video").forEach(function(j){I.push(j)})}),I}this.stop=function(I){R&&R.stop(function(C){x.blob=C,I(C),x.clearRecordedData()})},this.pause=function(){R&&R.pause()},this.resume=function(){R&&R.resume()},this.clearRecordedData=function(){R&&(R.clearRecordedData(),R=null),E&&(E.releaseStreams(),E=null)},this.addStreams=function(I){if(!I)throw"First parameter is required.";I instanceof Array||(I=[I]),g.concat(I),!(!R||!E)&&(E.appendStreams(I),l.previewStream&&typeof l.previewStream=="function"&&l.previewStream(E.getMixedStream()))},this.resetVideoStreams=function(I){E&&(I&&!(I instanceof Array)&&(I=[I]),E.resetVideoStreams(I))},this.getMixer=function(){return E},this.name="MultiStreamRecorder",this.toString=function(){return this.name}}typeof u<"u"&&(u.MultiStreamRecorder=H);function ee(g,l){if(!this)throw'Use "new RecordRTCPromisesHandler()"';if(typeof g>"u")throw'First argument "MediaStream" is required.';var x=this;x.recordRTC=new u(g,l),this.startRecording=function(){return new Promise(function(E,R){try{x.recordRTC.startRecording(),E()}catch(z){R(z)}})},this.stopRecording=function(){return new Promise(function(E,R){try{x.recordRTC.stopRecording(function(z){if(x.blob=x.recordRTC.getBlob(),!x.blob||!x.blob.size){R("Empty blob.",x.blob);return}E(z)})}catch(z){R(z)}})},this.pauseRecording=function(){return new Promise(function(E,R){try{x.recordRTC.pauseRecording(),E()}catch(z){R(z)}})},this.resumeRecording=function(){return new Promise(function(E,R){try{x.recordRTC.resumeRecording(),E()}catch(z){R(z)}})},this.getDataURL=function(E){return new Promise(function(R,z){try{x.recordRTC.getDataURL(function(I){R(I)})}catch(I){z(I)}})},this.getBlob=function(){return new Promise(function(E,R){try{E(x.recordRTC.getBlob())}catch(z){R(z)}})},this.getInternalRecorder=function(){return new Promise(function(E,R){try{E(x.recordRTC.getInternalRecorder())}catch(z){R(z)}})},this.reset=function(){return new Promise(function(E,R){try{E(x.recordRTC.reset())}catch(z){R(z)}})},this.destroy=function(){return new Promise(function(E,R){try{E(x.recordRTC.destroy())}catch(z){R(z)}})},this.getState=function(){return new Promise(function(E,R){try{E(x.recordRTC.getState())}catch(z){R(z)}})},this.blob=null,this.version="5.6.2"}typeof u<"u"&&(u.RecordRTCPromisesHandler=ee);function te(g,l){(typeof ReadableStream>"u"||typeof WritableStream>"u")&&console.error("Following polyfill is strongly recommended: https://unpkg.com/@mattiasbuelens/web-streams-polyfill/dist/polyfill.min.js"),l=l||{},l.width=l.width||640,l.height=l.height||480,l.frameRate=l.frameRate||30,l.bitrate=l.bitrate||1200,l.realtime=l.realtime||!0;var x;function E(){return new ReadableStream({start:function($){var U=document.createElement("canvas"),J=document.createElement("video"),V=!0;J.srcObject=g,J.muted=!0,J.height=l.height,J.width=l.width,J.volume=0,J.onplaying=function(){U.width=l.width,U.height=l.height;var W=U.getContext("2d"),O=1e3/l.frameRate,Y=setInterval(function(){if(x&&(clearInterval(Y),$.close()),V&&(V=!1,l.onVideoProcessStarted&&l.onVideoProcessStarted()),W.drawImage(J,0,0),$._controlledReadableStream.state!=="closed")try{$.enqueue(W.getImageData(0,0,l.width,l.height))}catch{}},O)},J.play()}})}var R;function z($,U){if(!l.workerPath&&!U){x=!1,fetch("https://unpkg.com/webm-wasm@latest/dist/webm-worker.js").then(function(V){V.arrayBuffer().then(function(W){z($,W)})});return}if(!l.workerPath&&U instanceof ArrayBuffer){var J=new Blob([U],{type:"text/javascript"});l.workerPath=h.createObjectURL(J)}l.workerPath||console.error("workerPath parameter is missing."),R=new Worker(l.workerPath),R.postMessage(l.webAssemblyPath||"https://unpkg.com/webm-wasm@latest/dist/webm-wasm.wasm"),R.addEventListener("message",function(V){V.data==="READY"?(R.postMessage({width:l.width,height:l.height,bitrate:l.bitrate||1200,timebaseDen:l.frameRate||30,realtime:l.realtime}),E().pipeTo(new WritableStream({write:function(W){if(x){console.error("Got image, but recorder is finished!");return}R.postMessage(W.data.buffer,[W.data.buffer])}}))):V.data&&(I||j.push(V.data))})}this.record=function(){j=[],I=!1,this.blob=null,z(g),typeof l.initCallback=="function"&&l.initCallback()};var I;this.pause=function(){I=!0},this.resume=function(){I=!1};function C($){if(!R){$&&$();return}R.addEventListener("message",function(U){U.data===null&&(R.terminate(),R=null,$&&$())}),R.postMessage(null)}var j=[];this.stop=function($){x=!0;var U=this;C(function(){U.blob=new Blob(j,{type:"video/webm"}),$(U.blob)})},this.name="WebAssemblyRecorder",this.toString=function(){return this.name},this.clearRecordedData=function(){j=[],I=!1,this.blob=null},this.blob=null}typeof u<"u"&&(u.WebAssemblyRecorder=te)}(mu)),mu.exports}var Ia=qa();const xt=kt(Ia),Pa={class:"mainer flex_center flex_column"},Na={class:"chat-box",id:"chat_list"},Oa={class:"example-box"},Ua={class:"question-list"},ja=["onClick"],Ha=["innerHTML"],Va=["innerHTML"],Ga={class:"ipt-box"},Wa={class:"action-list flex_between"},Za={class:"flex_start send-way"},$a={id:"recordingCanvas",class:"recording-icon"},Xa={__name:"index",setup(e){let u=vr(),{openFrom:t}=ir(u),o=Ae("");Ae(!0);let n=Ae(!0),r=Ae([]),c=Ae([]),i=Ae(""),s="",f=Ae(!0);const h=Ae(!1),v=Ae(null),_=Ae(null);let A=Ae(null),a=Ae(0),b=null;const m=new De({html:!0,linkify:!0,typographer:!0}).use(Ca,{throwOnError:!1,errorColor:"#cc0000",displayMode:!0}),S=async()=>{if(La())try{_.value=await navigator.mediaDevices.getUserMedia({audio:!0}),v.value=new xt(_.value,{type:"audio",mimeType:"audio/wav",recorderType:xt.StereoAudioRecorder,numberOfAudioChannels:1,desiredSampRate:44100,bitRate:128}),h.value=!0,v.value.startRecording(),b=setInterval(()=>{a.value++},1e3)}catch(T){console.error("初始化录音失败:",T)}},p=()=>{S()},d=()=>{v.value&&v.value.stopRecording(async()=>{const T=v.value.getBlob();if(A.value=new File([T],`recording-${Date.now()}.wav`,{type:"audio/wav",lastModified:new Date().getTime()}),h.value=!1,clearInterval(b),a.value>60){a.value=0,k0.warning("录音时间过长，请重新录音");return}a.value=0;let N=await Ma({file:A.value,voiceFormat:"wav",dataLen:A.value.size});N&&(o.value=N.data,D())})},w=()=>{_.value&&_.value.getTracks().forEach(T=>T.stop()),v.value&&v.value.destroy()};let y=T=>{n.value=!1,o.value=T.question,D()},k=async()=>{let T=await Sa();T&&(i.value=T.data,L(),localStorage.setItem("conversation",i.value))},M=async()=>{await Ta({id:i.value})&&(k0.success("清除成功"),localStorage.removeItem("conversation"),c.value=[],k())},L=async()=>{let T=await Ra({id:i.value});T&&(T.data.length>0?n.value=!1:n.value=!0,T.data.forEach(N=>{c.value.push({role:N.messageType=="user"?"user":"assistant",content:m.render(N.messageText),content_type:"text",reasoning_content:"",isComplete:!0})}),Z())},q=async()=>{f.value=!1;let T={teachingConversationsId:i.value,content:o.value};s=new AbortController;const N=s.signal;let H=gr();o.value="",fetch("http://ttxs.haianedu.net/api/dxs/ai/conversations/teaching/chat",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json","X-Haian-Platform-Member-Token":H?`Bearer ${H}`:void 0},mode:"cors",body:JSON.stringify(T),signal:N}).then(async ee=>{G(ee)}).catch(ee=>{})},D=()=>{f.value&&o.value&&(c.value.push({role:"user",content:o.value,content_type:"text",reasoning_content:"",isComplete:!0}),c.value.push({role:"assistant",content:"",content_type:"text",reasoning_content:"",isComplete:!1}),Z(),q())};function Z(){fr(()=>{let T=document.getElementById("chat_list");T.scrollTo({top:T.scrollHeight,behavior:"instant"})})}async function G(T){const N=T.body.getReader(),H=new TextDecoder("utf-8");let ee="",te="";for(;;){const{done:g,value:l}=await N.read();if(g){f.value=!0,c.value[c.value.length-1].isComplete=!0,setTimeout(()=>{Z()},200);break}const x=H.decode(l,{stream:!0});ee+=x;const E=ee.split(`
`);ee=E.pop(),E.forEach((R,z)=>{if(z+1<E.length&&R.length>0){const I=R.substring(5).trim();try{const C=JSON.parse(I);C.content&&(te+=C.content,c.value[c.value.length-1].content=m.render(te)),Z()}catch(C){console.error("Error parsing JSON:",C)}}})}}let Q=async()=>{let T=await Fa();T&&(r.value=T.data)},X=()=>{const T=document.getElementById("recordingCanvas"),N=T.getContext("2d");T.width=60,T.height=30;const H=5,ee=4,te=4,g=20;let l=new Array(H).fill(0),x=new Array(H).fill(0);function E(){N.clearRect(0,0,T.width,T.height),N.beginPath(),N.arc(10,15,5,0,Math.PI*2),N.fillStyle="red",N.fill();for(let R=0;R<H;R++){Math.random()<.1&&(x[R]=Math.random()*g),l[R]+=(x[R]-l[R])*.1;const z=20+R*(ee+te),I=(T.height-l[R])/2;N.fillStyle="#333",N.fillRect(z,I,ee,l[R])}requestAnimationFrame(E)}E()};return ar(()=>{w()}),or(()=>{X(),Q(),localStorage.getItem("conversation")?(i.value=localStorage.getItem("conversation"),L()):k()}),(T,N)=>{const H=x0("el-input"),ee=x0("el-tooltip"),te=sr("loading");return Oe(),je(I0,null,[Te(t)!="client"?(Oe(),cr(Ea,{key:0})):q0("",!0),le("div",Pa,[le("div",Na,[Xe(le("div",Oa,[N[3]||(N[3]=le("img",{src:xr,alt:""},null,-1)),N[4]||(N[4]=le("p",{class:"desc"},"嗨，您好呀！我是您的AI助教，很高兴能与您交流。我的使命是为您提供最优质的教学资源，配合您完成教学任务。",-1)),N[5]||(N[5]=le("p",{class:"ask"},"你可以尝试这样问：",-1)),le("ul",Ua,[(Oe(!0),je(I0,null,Fu(Te(r),(g,l)=>(Oe(),je("li",{class:"question-item flex_start",key:l,onClick:x=>Te(y)(g)},dr(g.question),9,ja))),128))])],512),[[n0,Te(c).length==0]]),le("div",null,[(Oe(!0),je(I0,null,Fu(Te(c),(g,l)=>(Oe(),je("div",{class:hr(["answer-item",g.role=="user"?"flex_end":""]),key:l},[g.role=="user"?(Oe(),je("p",{key:0,class:"me",innerHTML:g.content},null,8,Ha)):q0("",!0),g.role=="assistant"?Xe((Oe(),je("p",{key:1,class:"ai",style:pr(g.content?"":"background: none;padding:24px"),innerHTML:g.content},null,12,Va)),[[te,!g.isComplete,void 0,{lock:!0}]]):q0("",!0)],2))),128))])]),le("div",Ga,[y0(H,{placeholder:"请您输入问题",modelValue:Te(o),"onUpdate:modelValue":N[0]||(N[0]=g=>lr(o)?o.value=g:o=g),type:"textarea",rows:"2",resize:"none"},null,8,["modelValue"]),le("div",Wa,[le("button",{class:"clear flex_start",onClick:N[1]||(N[1]=(...g)=>Te(M)&&Te(M)(...g))},N[6]||(N[6]=[le("img",{src:mr,alt:""},null,-1),le("p",null,"清空上下文",-1)])),le("div",Za,[Xe(le("p",{onClick:d,class:"stop-recording"},"停止录音",512),[[n0,h.value]]),y0(ee,{class:"box-item",effect:"dark",content:"语音输入",placement:"top"},{default:yt(()=>[le("div",null,[Xe(le("canvas",$a,null,512),[[n0,h.value]]),Xe(le("img",{src:yr,alt:"",class:"audio",onClick:p},null,512),[[n0,!h.value]])])]),_:1}),Xe(le("img",{src:kr,alt:"",class:"send",onClick:N[2]||(N[2]=(...g)=>Te(D)&&Te(D)(...g))},null,512),[[n0,!h.value]])])])])])],64)}}},ro=wt(Xa,[["__scopeId","data-v-c9de0f9b"]]);export{ro as default};

<template>
    <div class="joiner">
      
           <div class="member subject-member " >
                 <div class="title flex_between">
                    <h3 class="flex_start">小组日记统计</h3>
                    <button @click="goExport('group')">导出</button>
                 </div>
                 <div id="member-group" class="echarts">

                 </div>
                 <div class="table">
                    <div class="flex_start sort" @click="sortGroup">
                            <img src="@/assets/pc/sortby.png" alt=""  v-show="!sortGroupAes">
                        <img src="@/assets/pc/sortby-up.png" alt="" v-show="sortGroupAes">
                        <p>{{!sortGroupAes?'降序':'升序'}}</p>
                    </div>
                    <el-table :data="groupData" :header-cell-style="{ background: '#F3F7FF','color':'2B2C33','fontSize':'14px' }" class="mt10">
                         <el-table-column prop="groupName" label="小组" align="center"  />
                         <el-table-column prop="numberOfRecord" label="日记数" align="center" />
                    </el-table>
                 </div>
            </div>
            <div class="member subject-member mt16" >
                 <div class="title flex_between">
                    <h3 class="flex_start">个人日记统计</h3>
                    <button @click="goExport('person')">导出</button>
                 </div>
                 <div id="member-person" class="echarts" style="height:240px">

                 </div>
                 <div class="table">
                     <div class="flex_start sort" @click="sortPerson">
                            <img src="@/assets/pc/sortby.png" alt=""  v-show="!sortPersonAes">
                        <img src="@/assets/pc/sortby-up.png" alt="" v-show="sortPersonAes">
                        <p>{{!sortPersonAes?'降序':'升序'}}</p>
                    </div>
                    <el-table :data="personData" :header-cell-style="{ background: '#F3F7FF','color':'2B2C33','fontSize':'14px' }" class="mt10">
                         <el-table-column prop="userName" label="教师姓名" align="center"  />
                         <el-table-column prop="numberOfRecord" label="日记数量" align="center" />
                          <el-table-column prop="numberOfFullScoreRecord" label="六星数量" align="center" />
                           <el-table-column prop="numberOfRecommendedRecord" label="导师推荐数量" align="center" />
                    </el-table>
                 </div>
             </div>
    </div>
</template>
<script setup>
import {onMounted, reactive, ref,onActivated, } from 'vue'
import {numberByMember,numberByMemberUser} from '@/api/pc/index.js'
import * as echarts from 'echarts';
import StaticLeft from '@/views/pc/component/act-research-association/statisticLeft.vue'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'
import Down from '@/utils/download.js'
import {useRouter} from 'vue-router'
let userInfo=useUserInfoStoreHook()
let {themeScrollTop}=storeToRefs(userInfo)
let router=useRouter()
let sortGroupAes=ref(false)
let echartGroup=''
let groupData=ref([])
let echartPerson=''
let personData=ref([])
let sortPersonAes=ref(false)

let goExport=async(type)=>{
    if(type=='group'){
     Down.excel('/xzs/dashboard/statistics/number-of-record-group-by-group/file','小组日记统计')
     }
     if(type=='person'){
     Down.excel('/xzs/dashboard/statistics/number-of-record-group-by-user/file','个人日记统计')
     }
}
let initechartGroup=(row)=>{
    echartGroup = echarts.init(document.getElementById('member-group'));

  let x=row.map(item=>item.groupName)
    let y=row.map(item=>item.numberOfRecord)
var salvProMax =[];//背景按最大值
for (let i = 0; i < y.length; i++) {
    salvProMax.push(y[0])
}
let option = {
    backgroundColor:"#fff",
    grid: {
        left: '2%',
        right: '2%',
        bottom: '2%',
        top: '2%',
        containLabel: true
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'none'
        },
        formatter: function(params) {
            return params[0].name  + ' : ' + params[0].value
        }
    },
    xAxis: {
        show: true,
        type: 'value',
     
         axisLine: {
            show: true,
            
        },
        axisLabel: {   // X轴线 标签修改 
            textStyle: {
                color: '#8DA2B5', //坐标值得具体的颜色
            }
        },
        splitLine:{  
            show: false, // X轴线 颜色类型的修改
            lineStyle: {
                type: 'dashed',
                color: ''
                }  
        },
        axisLine: { 
            show: true, // X轴 网格线 颜色类型的修改
            lineStyle: {
                type: 'solid',
                color: '#E8E9F0'
            }  
        },  
    },
      
    yAxis: [{
        type: 'category',
        inverse: true,
           itemStyle: {
            normal: {
                color: function(params) {
                    return 'red';
                }
            }
        },
        axisLabel: {
            show: true,
            textStyle: {
                color: '#8DA2B5'
            },
        },
        splitLine: {
            show: false
        },
        axisTick: {
            show: false
        },
         axisLine: { 
            show: true, // X轴 网格线 颜色类型的修改
            lineStyle: {
                 type: 'solid',
                 color: '#E8E9F0'
                 }  
            },
        data: x
      }],
      dataZoom: [
            {
                type: "slider",
                realtime: true, // 拖动时，是否实时更新系列的视图
                startValue: 0,
                endValue: 5,
                width: 5,
                height: "90%",
                top: "5%",
                right: 0,
                brushSelect: false,
                yAxisIndex: [0, 1], // 控制y轴滚动
                fillerColor: "#0093ff", // 滚动条颜色
                borderColor: "rgba(17, 100, 210, 0.12)",
                backgroundColor: "#cfcfcf", //两边未选中的滑动条区域的颜色
                handleSize: 0, // 两边手柄尺寸
                showDataShadow: false, //是否显示数据阴影 默认auto
                showDetail: false, // 拖拽时是否展示滚动条两侧的文字
                zoomLock: true,
                moveHandleStyle: {
                    opacity: 0,
                },
            },
            {
                type: "inside",
                // width: 0,
                startValue: 0,
                endValue: 10,
                minValueSpan: 10,
                //   maxValueSpan: 20,
                yAxisIndex: [0],
                zoomOnMouseWheel: false, // 关闭滚轮缩放
                moveOnMouseWheel: true, // 开启滚轮平移
                moveOnMouseMove: true, // 鼠标移动能触发数据窗口平移
            },
        ],

         series: [{
            name: '值',
            type: 'bar',
           zlevel: 2,
           label: {
                    show: true,
                    position: 'right',
                   color: '#94959C'
                },
            itemStyle: {
                normal: {
                    barBorderRadius: [0,30,30,0],
                    color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                        offset: 0,
                        color: 'rgb(57,89,255,1)'
                    }, {
                        offset: 1,
                        color: 'rgb(46,200,207,1)'
                    }]),
                },
            },
            barWidth: 16,
            data: y
        }
    ]
};
  echartGroup.setOption(option)
}
let initechartPerson=(row)=>{
      let x=row.map(item=>item.userName)
    let x1=row.map(item=>item.numberOfRecord)
     let x2=row.map(item=>item.numberOfFullScoreRecord)
      let x3=row.map(item=>item.numberOfRecommendedRecord)
  echartPerson = echarts.init(document.getElementById('member-person'));
  let option = {
    backgroundColor: '#fff',
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow'
        },
        formatter:(params)=>{
            return '<div>'+'<p>'+'日记数量：'+params[0].value+'</p><p>'+'六星数量：'+params[1].value+'</p><p>'+'导师推荐数量：'+params[2].value+'</p>'+'</div>'
        }
    },
    grid: {
        top: '6%',
        right: '0%',
        left: '5%',
        bottom: '18%'
    },
     dataZoom: [
            {
                type: 'slider',
                xAxisIndex: 0,
                start: 0,
                end:row.length<10?100:(row.length>10&&row.length<20)?60:(row.length>20&&row.length<50)?30:10,
                showDetail: false,
                height: "8px",
                width:"100%",
                bottom: "2%",
                right: 0,
            }
        ],
    xAxis: [{
        type: 'category',
        data:x,
        axisLine: {
            lineStyle: {
                color: 'rgba(255,255,255,0.12)',
                 
            }
        },
        axisLabel: {
            margin: 10,
            color: '#8DA2B5',
           
            textStyle: {
                fontSize: 12,
             
            },
             formatter: function (value, index) {
                        const maxLength = 6; // 每行最大字符数
                        const maxLines = 3; // 最大行数
                        let result = '';
                        for (let i = 0; i < value.length; i += maxLength) {
                            if (Math.ceil(i / maxLength) >= maxLines) {
                                result += '';
                                break;
                            }
                            result += value.slice(i, i + maxLength);
                            if (i + maxLength < value.length) {
                                result += '\n';
                            }
                        }
                        return result
                    }
         },
       }],
        yAxis: [{
            axisLabel: {
                formatter: '{value}',
                color: '#8DA2B5',
                textStyle: {
                    fontSize: 12
                },
            },
            axisLine: {
                show: false
            },
            splitLine: {
                lineStyle: {
                    color: ' #F1F2F5',
                    type:'dashed'
                }
            }
        }],
      
    series: [{
        type: 'bar',
        data:x1,
        barWidth: '12px',
        itemStyle: {
            normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: '#88C0FF' // 0% 处的颜色
                }, {
                    offset: 1,
                    color: '#508CFF' // 100% 处的颜色
                }], false),
                barBorderRadius: [30, 30, 0,0],
               
             
            }
        },
      
    },{
        type: 'bar',
        data: x2,
        barWidth: '12px',
        itemStyle: {
            normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: '#A4F3F5' // 0% 处的颜色
                }, {
                    offset: 1,
                    color: '#55C2F5' // 100% 处的颜色
                }], false),
                barBorderRadius: [30, 30, 0,0],
               
             
            }
        },
      
    },{
        type: 'bar',
        data: x3,
        barWidth: '12px',
        itemStyle: {
            normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: '#FFD390' // 0% 处的颜色
                }, {
                    offset: 1,
                    color: '#FF8988' // 100% 处的颜色
                }], false),
                barBorderRadius: [30, 30, 0,0],
               
             
            }
        },
      
    }]
  };
  echartPerson.setOption(option)
}
let sortGroup=()=>{
   sortGroupAes.value=!sortGroupAes.value
    if(!sortGroupAes.value){
       groupData.value.sort((a, b) => b.numberOfRecord - a.numberOfRecord);
    }else{
       groupData.value.sort((a, b) => a.numberOfRecord - b.numberOfRecord);
    }
}
let sortPerson=()=>{
   sortPersonAes.value=!sortPersonAes.value
    if(!sortPersonAes.value){
       personData.value.sort((a, b) => b.numberOfRecord - a.numberOfRecord);
    }else{
       personData.value.sort((a, b) => a.numberOfRecord - b.numberOfRecord);
    }
}
let setMember=async()=>{
    let res=await numberByMember()
    if(res){
          res.data.sort((a, b) => b.numberOfRecord - a.numberOfRecord);
        groupData.value=res.data
        initechartGroup(res.data)
    }
}
let setMemberUser=async()=>{
    let res=await numberByMemberUser()
    if(res){
        res.data.forEach(item=>{
            item.total=item.numberOfRecord+item.numberOfFullScoreRecord+item.numberOfRecommendedRecord
        })
         res.data.sort((a, b) => b.total - a.total);
        personData.value=res.data
        initechartPerson([...res.data])
    }
}
onMounted(()=>{
//  themes.value=[]
    setMember()
    setMemberUser()
    // initechartGroup()
    // 
})


</script>
<style lang="scss" scoped>
.joiner{
    margin-bottom: 10px;
   
}
.mt10{
    margin-top:10px
}
 .member{
            width: 32%;
            border-radius: 4px;
            padding: 16px;
            background: #fff;
            .title{
                h3{
                    font-size: 16px;
                    color: #2B2C33;
                    &::before{
                        content:'';
                        display:block;
                        width: 2px;
                        height: 14px;
                        background: #508CFF;
                        margin-right:6px ;
                    }
                }
                button{
                   padding: 0 10px;
                    height: 20px;
                    font-size: 14px;
                    color: #fff;
                    line-height: 14px;
                    background: #508CFF;
                    border-radius: 4px;
                    border:none
                }
            }
            .echarts{
                height: 160px;
                margin: 10px 0;
            }
             .sort{
                    font-size: 12px;
                    color: #508CFF;
                    cursor: pointer;
                    img{
                        width: 14px;
                        margin-right: 6px;
                        height: auto;
                    }
                    p{
                        line-height: 14px;
                    }
                }
        }
        .subject-member{
            width: 100%;
          
        }
</style>
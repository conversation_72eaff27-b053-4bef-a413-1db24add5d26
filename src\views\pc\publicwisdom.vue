<template>
  
</template>

<script setup>
import { onMounted } from 'vue'
import {useRouter,useRoute} from 'vue-router'
let router=useRouter()
let route=useRoute()
const MAX_MOBILE_WIDTH = 750
const _isMobile = () => {
  const rect = document.body.getBoundingClientRect()
  return rect.width - 1 < MAX_MOBILE_WIDTH
}
onMounted(()=>{
 
  if(_isMobile()){
 router.push('/wisdom?id='+route.query.id)
}else{
    router.push('/resource/wisdom/detail?id='+route.query.id)
}
})
</script>

<style>

</style>
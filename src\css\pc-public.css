*{padding: 0;margin: 0;box-sizing: border-box;}
.flex_between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .flex_between_stretch {
    display: flex;
    justify-content: space-between;
    align-items: stretch;
  }
  
  .flex_start_1 {
    display: flex;
    justify-content: flex-start;
  }
  .flex_wrap{
    flex-wrap: wrap;
  }
  .flex_between_0 {
    display: flex;
    justify-content: space-between;
  }
  
  .flex_between_end {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
  }
  
  .flex_between_start {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }
  
  .flex_start {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  
  .flex_start_1 {
    display: flex;
    justify-content: flex-start;
    align-content: flex-start;
  }
  
  .flex_start_start {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
  }
  
  .flex_end {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  
  .flex_center_end {
    display: flex;
    justify-content: center;
    align-items: flex-end;
  }
  
  .flex_center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .flex_start_0 {
    display: flex;
    justify-content: flex-start;
  }
  
  .flex_around {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
  
  .flex_column {
    display: flex;
    flex-direction: column;
  }
  
  .flex_column_start {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .flex_column_center {
    display: flex;
    flex-direction: column;
    align-items: center;
  
  }
  
  .flex_column_center_center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  .flex_column_between {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .flex_column_around {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }
  
  .flex_column_end {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
  }
  
  .flex_column_end1 {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
  
  .flex_between_start {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }
  
  .flex_between_end {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
  }
  
  .flex_column_end_end {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    flex-direction: column;
  }
  
  .flex_1 {
    flex: 1;
  }
  .flex_reverse{display: flex; flex-direction: row-reverse;}
  .content-box{
    width: 90%;
    max-width: 1140px;
    margin: 0 auto;
  }
  .mb10{
    margin-bottom: 10px;
  }
  .mt10{
    margin-top: 10px;
  }
  .mt20{
    margin-top: 20px;
  }
  /* dialog */
  .el-dialog__header .el-dialog__title{
    font-weight: bold;font-size: 20px;color: #2B2C33;
  }
  .el-dialog .el-select{width: 100%;}
  .el-dialog__headerbtn .el-dialog__close{
    font-size: 24px;font-weight: bold;
  }
  .el-dialog__footer{
    padding: 0 20px 24px 20px;
  }
  .el-dialog .el-form--inline .el-form-item{
    margin-right: 0;
  }
  .el-dialog .el-dialog__body{
    padding: 8px 0 2px 0;
  }
  .el-dialog .el-form-item--medium .el-form-item__label{
    font-size: 16px;
  color: #2B2C33;font-weight: bold;line-height: 16px;
  }
  .el-dialog .el-form--label-top .el-form-item__label{
    padding:0;
    font-weight: bold;
    font-size: 16px;
  }
.el-dialog .el-input__inner{
  height: 48px;font-size: 16px;
}
  .el-dialog .el-form-item{
    margin-bottom: 24px;
  }
  .el-dialog .el-dialog__footer{
    padding: 0;
  }
  .el-dialog .el-input--medium .el-input__inner{
    height: 40px;font-size: 16px;border-radius: 8px;
  }
  .el-dialog .dialog-footer .cancel{
    width: 112px;
  height: 48px;
  background: #FFFFFF;
  border-radius: 8px;
  border: 1px solid #0055FF;font-size: 18px;
  color: #6D6F75;
  line-height: 26px;cursor: pointer;margin-right: 20px;
  }
  .el-dialog .dialog-footer .confirm{
    width: 112px;
    height: 48px;
    background: #0055FF;
    border-radius: 8px;border:none;
  line-height: 26px;cursor: pointer;font-size: 18px;
  color: #FFFFFF;
  line-height: 26px;
  }
  .el-dialog .el-radio__input.is-checked .el-radio__inner{
    background: rgb(36,198,152);border:1px solid rgb(36,198,152);
    width: 24px;height: 24px;
  }
  .el-dialog .el-radio__input.is-checked .el-radio__inner::after{
    transform: translate(-50%, -50%) scale(2.2)
  }
  .el-dialog .el-radio__inner{width: 24px;height: 24px;}
  /* 弹出框 */
  .el-message-box__btns .el-button{
    width: 112px;
    height: 48px;
    background: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #0055FF;
    font-size: 18px;cursor: pointer;
    color: #6D6F75;
  }
  .el-message-box__btns .el-button--primary{
    width: 112px;
    height: 48px;
    background: #0055FF;cursor: pointer;
    color: #fff;
  }
  .el-message-box{
    padding: 0px;border-radius: 8px;
  }
  .el-message-box__header{
    padding: 24px 24px 0 24px;
  }
  .el-message-box__content{
    padding: 24px 24px 32px 24px;
  }
  .el-message-box__btns{
    padding: 0 24px 24px 24px;
  }
  .el-message-box__btns button:nth-child(2){
    margin-left: 20px;cursor: pointer;
  }
  .el-message-box__title{
    font-size: 20px;
    color: #2B2C33;
    font-weight: bold;
  }
  .el-message-box__content .el-message-box__container .el-message-box__message{
    font-size: 16px;
    color: #2B2C33;}
    .el-empty{
      background: #fff;
      margin-top: 10px;
      height: calc(100vh - 260px);
    }

    

    ::-webkit-scrollbar {
      width:10px; /* 设置滚动条宽度 */
     
  }
  
  ::-webkit-scrollbar-track {
      background: #f1f1f1; /* 设置滚动条轨道背景颜色 */
      border-radius: 30px;
  }
  
  ::-webkit-scrollbar-thumb {
      background: #888; /* 设置滚动条滑块背景颜色 */
      border-radius: 30px; /* 设置滚动条滑块圆角 */
  }
  
  ::-webkit-scrollbar-thumb:hover {
      background: #555; /* 设置滚动条滑块悬停时的背景颜色 */
  }
  .dialog-footer .el-button{
    width: 96px;
  height: 48px;
  background: #FFFFFF;
  border-radius: 8px;
  border: 1px solid #E6E6E6;
  font-size: 18px;
  color: #94959C;
  }
  /* Firefox 浏览器 */
.dialog-footer .el-button--primary{
  width: 96px;
  height: 48px;
  background: #3071FF;
  border-radius: 12px;
  color: #fff;
  border:none;
  cursor: pointer;

}
.el-dialog__body .el-form-item .el-form-item__label{
  font-size: 16px;
color: #2B2C33;
}
.el-dialog__body .el-form-item .el-select__wrapper{
  min-height: 48px;
}
.el-table .cell{
  height: 48px;
  line-height: 48px;
  font-size: 16px;
}
.el-carousel .el-carousel__button{
  width: 10px  !important;
  height: 10px !important;
  border-radius: 20px;
}
.el-carousel__indicators--horizontal{
  left:5%
}
.changeArticle button{
  width: 80px;
height: 28px;
border-radius: 19px;
border: 1px solid #E2E3E6;
font-size: 14px;
color: #C2C9CE;
cursor: pointer;
background: #fff;
}
.changeArticle  .exist{
  border: 1px solid #508CFF;
  color: #508CFF;
}
.changeArticle button img{
  width: 16px;
  height: auto;
 
}
.quizPutTag{
  display: inline-block;
  height: 24px;
  width:40px;
  border-bottom: 1px solid 
}
/* .composition tbody tr td{
  margin-right: 24px;
} */
.notice-dialog{
  background: #F6F9FA;
}
.notice-dialog .el-range-editor.el-input__wrapper{
  height: 44px;
}
.noticedetail-dialog{
  background: url(@/assets/pc/notice-bg.png) no-repeat;
  background-size: 100% 100%;
}
<template>
    <div>
         <div class="flex_start title" @click="$router.go(-1)" v-show="$route.name=='ActResearchAssociation-square-userRecord'">
            <el-icon style="margin-right: 4px;"><ArrowLeft /></el-icon>
              返回列表
          </div>
        <div class="flex_between">
            <div class="flex_1">
                <Sort @emitForm='emitForm' :isshowOwn='isshowOwn'/>
            </div>
           
            <el-dropdown 
                @command="exportData"
                v-if="((roles.indexOf('DIRECTOR')>-1)||
                (roles.indexOf('LEADER')>-1&&userInfo.xingZhiShe?.clubGroupId==clubGroupId)||
                (roles.indexOf('TUTOR')>-1&&(userInfo.xingZhiShe?.clubGroupId==clubGroupId)))&&
                !isshowOwn&&
                $route.name!='ActResearchAssociation-square-userRecord'
                " >
              <div 
             
                 :class="['flex_center','show-own-leader',isshowOwn?'show-own-leader-active':'']"  style="margin:0 12px 10px 2px;width:106px;cursor:pointer;padding:14px 6px">
                <img src="@/assets/pc/export.png" alt="" />
                <p>导出</p>
              </div>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item command="">全部</el-dropdown-item>
                        <el-dropdown-item command="6">六星日记</el-dropdown-item>
                    </el-dropdown-menu>
                    </template>
            </el-dropdown>
           
            <!-- <div :class="['flex_start','show-own-leader',isshowOwn?'show-own-leader-active':'']" v-if="!$route.query.clubId&&$route.name!='ActResearchAssociation-square-userRecord'" >
                <img src="@/assets/pc/circle.png" alt="" v-show="!isshowOwn" @click="changeIsShowOwn"/>
                <img src="@/assets/pc/circled.png" alt="" v-show="isshowOwn" @click="changeIsShowOwn"/>
                <p>推荐日记</p>
            </div> -->
            
              <div :class="['flex_start','show-own-leader',isshowNoComment?'show-own-leader-active':'']" v-if="!$route.query.clubId&&clubGroupId!=-1&&clubGroupId!=-2&&clubGroupId!=-3&&clubGroupId!=-4&&roles.indexOf('TUTOR')>-1&&(userInfo.xingZhiShe?.clubGroupId==clubGroupId)&&$route.name!='ActResearchAssociation-square-userRecord'" style="margin-left:10px">
                <img src="@/assets/pc/circle.png" alt="" v-show="!isshowNoComment" @click="changeNoComment"/>
                <img src="@/assets/pc/circled.png" alt="" v-show="isshowNoComment" @click="changeNoComment"/>
                <p>未评星</p>
            </div>
            
        </div>
        <div  v-show="total!=0">
          <div  class="infinite-list" style="overflow: auto" ref="squareRef">
             <DiaryItem v-for="item in tableData" @emitUserCenter='emitUserCenter' :clubGroupId='clubGroupId' @emitGoDetail='emitGoDetail' :key="item.id" @emitDel='emitDel' @emitRecommend='emitRecommend' @emitHide='emitHide' :row='item' @emitIsLike='emitIsLike' :url='url' />
          </div>
          <el-pagination background style="margin-bottom:10px" layout="prev, pager, next" :page-size='pageSize' :total="total" @current-change='handlelCurrentChange' />
        </div>
     
    </div>
</template>
<script setup>
import Sort from '@/views/pc/component/act-research-association/sort.vue'
import DiaryItem from '@/views/pc/component/act-research-association/diaryItem.vue'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {useRouter,onBeforeRouteLeave} from 'vue-router'
let router=useRouter()
let userInfoStore=useUserInfoStoreHook()
import {storeToRefs} from 'pinia'
import {ref,defineEmits, onActivated} from 'vue'
let props=defineProps(['tableData','url','clubGroupId','total','pageSize'])
let emits=defineEmits(['emitRecommend','emitsParentForm','emitParentDel','emitParentHide','emitParentRecommend','emitIsShowNoComment','emitParentDetail','emitCurrentChange'])
let isshowOwn=ref(false)
let isshowNoComment=ref(false)

let {squareScrollTop,squareUserCenterScrollTop,squareCommonScrollTop,userInfo,roles}=storeToRefs(userInfoStore)
let squareRef=ref()
let emitForm=(val)=>{
  emits('emitsParentForm',val)
}
let exportData=(score)=>{
    emits('exportData',roles.value,score)
}
let handlelCurrentChange=(val)=>{
    squareRef.value.scrollTop=0
    squareScrollTop.value=0
    emits('emitCurrentChange',val)
}

let emitRecommend=(val,recommendType)=>{
    emits('emitParentRecommend',val,recommendType)
}
let emitDel=(val)=>{
    emits('emitParentDel',val)

}
let emitUserCenter=(val)=>{
    squareUserCenterScrollTop.value= squareRef.value.scrollTop
}
let emitHide=(val)=>{
    emits('emitParentHide',val)

}
let emitGoDetail=(val)=>{
     squareScrollTop.value=squareRef.value.scrollTop
     emits('emitParentDetail',val)
}
let emitIsLike=(islike,id,type)=>{
    emits('emitParentIsLike',islike,id,type)
}
// let changeIsShowOwn=()=>{
//     isshowOwn.value=!isshowOwn.value
//     emits('emitIsShowOwn',isshowOwn.value)
// }
let changeNoComment=()=>{
    isshowNoComment.value=!isshowNoComment.value
     emits('emitIsShowNoComment',isshowNoComment.value)
}
onActivated(()=>{
squareRef.value.scrollTop=squareCommonScrollTop.value?squareCommonScrollTop.value:0
      
})
</script>
<style scoped>
.infinite-list{
    height: calc(100vh - 250px);
}
.title{
        font-weight: bold;
        font-size: 20px;
         color: #2B2C33;cursor: pointer;
         margin-bottom: 12px;
    }
.upload{
    width: 120px;
    height: 44px;
    background: #508CFF;
    border-radius: 4px;
    font-size: 16px;
    color: #FFFFFF;
    font-weight: bold;
    cursor: pointer;
    border:none
}
.upload img{
    width: 12px;
    height: auto;
    margin-right: 8px;
}
.show-own-leader{
    font-size: 16px;
    color: #6D6F75;
    background: #fff;
    border-radius: 4px;
    padding: 14px 12px;
    margin-bottom: 10px;
    white-space: nowrap;
   
}
.show-own-leader img{
    width: 16px;
    height: auto;
    margin-right: 8px;
    cursor: pointer;
}
.show-own-leader-active{
    color: #508CFF;
}
</style>


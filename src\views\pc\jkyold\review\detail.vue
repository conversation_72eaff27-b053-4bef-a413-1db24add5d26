<template>
    <div class="jky-review-detail">
        <ul class="detail-tab flex_start text_16 mt_10">
            <li v-for="item in reviewSections" :class="{active:current_tab === item.reviewSectionId}">{{item.reviewSectionName}}</li>
        </ul>
        <div class="flex_between_stretch mt_10">
            <div class="flex_1 preview-box mr_10">
                <p class="text_20_bold flex_start mr_40 pointer" @click="router.back()"><el-icon><ArrowLeftBold /></el-icon>查看专家评审</p> 
                <div class="flex_between_0" style="height:calc(100% - 50px)">
                    <ul class="material-ul">
                        <li v-for="(item,index) in current_task.schoolReviewMaterials" :key="item.fileId">
                            <p class="text_15">材料{{hz[index]}}</p>
                            <div class="flex_start_1 file-box" :class="current_material.fileId == item.fileId ? 'active-file' : ''">
                                <img v-if="item.type == 'pdf'" class="file-icon" src="@/assets/pc/jky/pdf.png" alt="">
                                <img v-else class="file-icon" src="@/assets/pc/jky/word.png" alt="">
                                <p class="ellipsis1 text_15">{{item.fileName}}</p>
                            </div>
                        </li>
                    </ul>
                    <Preview :row='current_material' style="height:100%" class="preview-item" />
                </div>
            </div>
            <div class="suggestion">
                <div class="suggestion-item">
                    <p class="suggest-label">审核意见</p>
                    <el-input v-model="current_material.reviewComment" :disabled="!current_material.isReviewCapable" :rows="route.query.type == '1' ? 6 : 18" class="my-textarea" type="textarea" placeholder="请输入审核意见" />
                    <div class="flex_between" v-if="current_material.isReviewCapable">
                        <el-button class="plain-btn btn_132_40 mt_16" style="width:100%;" type="primary" @click="submitClick(2)">不提交审阅</el-button>
                        <el-button class="primary-btn btn_132_40 mt_16" style="width:100%;" type="primary" @click="submitClick(1)">提交审阅</el-button>
                    </div>
                    
                </div>
                <ul class="suggest-ul" v-if="route.query.type == '1'">
                    <li v-for="item in 2" :key="item" class="text_14">
                        <div class="flex_between mb_16">
                            <p class="blue1"><img src="@/assets/pc/jky/expert-icon.png" class="expert-icon" alt="">专家一</p>
                            <p class="grey">2024-01-02 18:00</p>
                        </div>
                        <p>审读意见审读意见审读意见审读意见审读意见</p>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import Preview from '@/views/pc/component/resource/preview.vue'
    import { ElMessage } from "element-plus"
    import { reviewSchDetail, reviewAreaDetail, reviewSchAdd } from '@/api/pc/jky'
    import {useRoute,useRouter} from 'vue-router'
    let route = useRoute()
    let router = useRouter()
    const current_tab = ref(1)
    const reviewSections = ref([])
    const current_task = ref({})
    const current_material = ref({})
    const row = ref({})
    const hz = ['一','二','三','四','五','六','七','八','九','十','十一','十二','十三','十四','十五','十六','十七','十八','十九','二十','二十一','二十二','二十三','二十四','二十五','二十六','二十七','二十八','二十九','三十']
    const getFileExtension = (fileName) => {
        const lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex !== -1 && lastDotIndex !== 0) {
            return fileName.substring(lastDotIndex + 1);
        }
        return '';
    }
    const getDetail = async () => {
        let params = {
            taskMasterId:route.params.taskMasterId,
            schoolReviewUserId:route.params.schoolReviewUserId,
            reviewUserId:route.params.reviewUserId,
            schoolId:route.params.schoolId
        }

        let res = await (route.query.type == '1' ? reviewAreaDetail(params) : reviewSchDetail(params))
        if(res){
            reviewSections.value = res.data.reviewSections
            reviewSections.value.forEach(item => {
                item.schoolReviewMaterials.forEach(i => {
                    let type = getFileExtension(i.fileName)
                    i.type = type
                    i.fileType = 1
                    i.fileUrl = i.filePreviewUrl
                })
            })
            current_task.value = reviewSections.value && reviewSections.value.length > 0 ? reviewSections.value[0] : {}
            current_material.value = current_task.value.schoolReviewMaterials && current_task.value.schoolReviewMaterials.length > 0 ? current_task.value.schoolReviewMaterials[0] : {}
            current_tab.value = res.data.reviewSections[0].reviewSectionId
        }
    }
    const submitClick = async (schoolReviewResult) => {
        // if(!schoolReviewComment.value){
        //     ElMessage.error('请输入审核意见')
        //     return
        // }
        let params = {
            taskMasterId:route.params.taskMasterId,
            schoolId:route.params.schoolId,
            reviewSectionId:current_task.value.reviewSectionId,
            schoolReviewUserId:route.params.schoolReviewUserId,
            fileId:current_material.value.fileId,
        }
        let data = {
            schoolReviewResult:schoolReviewResult,
            schoolReviewComment:current_material.value.reviewComment
        }
        let res = await reviewSchAdd(params,data)
        if(res){
            ElMessage.success('提交成功')
        }
    }
    onMounted(() => {
        getDetail()
    })
</script>
<style lang="scss" scoped>
.jky-review-detail{max-width: 1128px;margin:0 auto;font-family: PingFangSC, PingFang SC;padding:10px 0;
    .detail-tab{color: #6D6F75;background: #FFFFFF;border-radius: 8px;display: inline-flex;overflow:hidden;
        li{padding: 8px 38px;cursor: pointer;}
        .active{background: #508CFF;color: #fff;}
    }
    .suggestion{width: 270px;
        .suggestion-item{border-radius: 8px;background: #FFFFFF;padding: 66px 24px 24px;position:relative;height: 100%;
            .suggest-label{position: absolute;top:16px;left: 0;background: #FF9A32;border-radius: 0px 100px 100px 0px;padding: 7px 20px 6px 16px;font-family: PingFangSC, PingFang SC; font-weight: 500; font-size: 15px; color: #FFFFFF; line-height: 21px; text-align: left;}
        }
        .suggest-ul{
            li{background: #FFFFFF;margin: 10px 0;padding: 16px;border-radius: 8px;
                .expert-icon{width: 16px;margin-right: 4px;}
            }
        }
    } 
    // min-height:600px;
    .preview-box{border-radius: 8px;background: #FFFFFF;padding: 24px;
        .material-ul{width: 200px;margin:16px 0;
            li{cursor: pointer;
                .file-box{border: 1px solid #E2E3E6;border-radius: 8px;padding: 16px 13px;margin-top: 8px;}
                .file-icon{width: 24px;margin-right: 8px;}
                .active-file{border: 2px solid #508CFF;}
            }
        }
    }
    // .preview-container{min-height: 600px;}
    .preview-item{width: calc(100% - 216px);}
}
</style> 
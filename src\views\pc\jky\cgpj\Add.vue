<template>
    <div class="publish-add-main">
        <el-form :model="add_form" ref="addRef" :rules="rules" class="search_form">
            <el-form-item label="立项任务" prop="taskTitle" style="margin-right: 16px;">
                <el-select class="select_48" v-model="add_form.sch" placeholder="请选择" @change="changeExtend">
                    <el-option v-for="item in stage_list" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="报送截止日期" prop="taskTitle"  style="margin-right: 16px;">
                <el-date-picker v-model="add_form.recordDate" class="my-date-picker" type="date" value-format="YYYY-MM-DD" placeholder="选择日期"> </el-date-picker>
            </el-form-item>
        </el-form>
        <div class="flex_end">
            <el-button class="plain-btn btn_96_48" type="plain" @click="emit('close')">取消</el-button>
            <el-button class="primary-btn btn_96_48" type="primary" @click="submitClick">创建</el-button>
        </div>
    </div>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import {ElMessage,ElLoading,ElMessageBox} from 'element-plus'
    import { taskAdd, taskEdit, taskDetail } from '@/api/pc/jky'
    const formObj = function(obj = {}){
        this.taskTitle = obj.taskTitle || ''
        this.taskContent = obj.taskContent || ''
        this.reviewSections = obj.reviewSections || []
    }
    const tab_list = ref([{id:1,title:'基本信息'},{id:2,title:'指标设置'}])
    const props = defineProps({
        id: 0, // 默认值
    });
    const addRef = ref(null)
    const rules = ref({
        taskTitle:[{required: true,message: '请输入课题名称',trigger: 'blur'}],
        reviewSections:[{required: true,message: '请上传材料清单',trigger: 'blur'}]
    })
    const emit = defineEmits(['close'])
    const add_form = ref(new formObj({}))

    function getQueryParams(src,type) {
        const url = new URL(src);
        const params = url.searchParams.get(type);
        return params
    }
    const removeFile = (index) => {
        add_form.value.reviewSections.splice(index, 1)
    }
    const getDetail = async () => {
        let res = await taskDetail(props.id)
        if(res){
            add_form.value = new formObj(res.data)
            add_form.value.reviewSections.forEach(item => {
                item.name = item.fileName
            })
            // add_form.value.reviewSections = add_form.value.reviewSections.map(item => item.name = item.fileName)
        }
    }
    const submitClick = async () => {
        if (!addRef.value) return
        await addRef.value.validate(async(valid, fields) => {
            if (valid) {
                let res = await (props.id && props.id != 0 ? taskEdit(add_form.value, props.id) : taskAdd(add_form.value))
                if(res){
                    ElMessage.success('发布成功')
                    emit('close')
                }
            } else {
                console.log('error submit!', fields)
            }
        })
        
    }
    const closeClick = () => {
        emit('close')
    }
    onMounted(() => {
        if(props.id && props.id != 0){
            getDetail()
        }
    })
</script>
<style lang="scss" scoped>
.publish-add-main{
    .upload-file-box{border: 1px solid #E2E3E6;border-radius: 8px;padding: 14px 16px 0;flex:1;min-height: 48px;margin-right: 8px;width: calc(100% - 104px );}
    .file-icon{width: 17px;height: auto;margin-right: 8px;}
    .file-close-icon{width: 24px;height: auto;}
    .file-name{width: calc(100% - 40px);}
    .upload-tip{position: absolute;top: -29px;left: 74px;}
    .tab-ul{border-bottom: 1px solid #E2E3E6;margin-bottom:20px;display:inline-flex;
        li{margin-right: 32px;color: #94959C;padding: 6px 0;cursor: pointer;}
        .active-tab{font-weight: bold;color: #508CFF;border-bottom: 2px solid #508CFF;}
    }
    .download-btn{font-weight: 400; font-size: 16px; color: #508CFF; line-height: 24px; text-align: left; font-style: normal; text-decoration-line: underline;margin-left: 16px;cursor: pointer;}
    
}
</style>
<style lang="scss">
    .el-upload-list{display: none;}
    .el-dialog__headerbtn{top: 14px;right: 14px;}
</style>
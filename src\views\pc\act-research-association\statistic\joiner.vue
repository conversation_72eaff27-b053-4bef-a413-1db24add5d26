<template>
    <div class="joiner">
        <div class="top flex_between">
             <div class="member">
                 <div class="title flex_between">
                    <h3 class="flex_start">年龄情况</h3>
                    <button @click="goExport('age')">导出</button>
                 </div>
                 <div id="member-age" class="echarts">

                 </div>
                 <div class="table">
                    <div class="flex_start sort" @click="sortAge">
                        <img src="@/assets/pc/sortby.png" alt=""  v-show="!sortAgeAes">
                        <img src="@/assets/pc/sortby-up.png" alt="" v-show="sortAgeAes">
                        <p>{{!sortAgeAes?'降序':'升序'}}</p>
                    </div>
                    <el-table :data="ageData" :header-cell-style="{ background: '#F3F7FF','color':'2B2C33','fontSize':'14px' }" class="mt10" height="180px">
                         <el-table-column prop="ageRange" label="年龄段" align="center"  />
                         <el-table-column prop="numberOfUser" label="人员数量" align="center" />
                    </el-table>
                 </div>
             </div>
            <div class="member">
                 <div class="title flex_between">
                    <h3 class="flex_start">小组情况</h3>
                    <button @click="goExport('group')">导出</button>
                 </div>
                 <div id="member-member" class="echarts">

                 </div>
                 <div class="table">
                    <div class="flex_start sort" @click="sortMember">
                           <img src="@/assets/pc/sortby.png" alt=""  v-show="!sortMemberAes">
                        <img src="@/assets/pc/sortby-up.png" alt="" v-show="sortMemberAes">
                        <p>{{!sortMemberAes?'降序':'升序'}}</p>
                    </div>
                    <el-table :data="memberData" :header-cell-style="{ background: '#F3F7FF','color':'2B2C33','fontSize':'14px' }" class="mt10" height="180px">
                         <el-table-column prop="groupName" label="小组名称" align="center"  />
                         <el-table-column prop="numberOfUser" label="人员数量" align="center" />
                    </el-table>
                 </div>
             </div>
              <div class="member">
                 <div class="title flex_between">
                    <h3 class="flex_start">学段情况</h3>
                    <button @click="goExport('stage')">导出</button>
                 </div>
                 <div id="member-stage" class="echarts">

                 </div>
                 <div class="table">
                    <div class="flex_start sort" @click="sortStage">
                        <img src="@/assets/pc/sortby.png" alt=""  v-show="!sortStageAes">
                        <img src="@/assets/pc/sortby-up.png" alt="" v-show="sortStageAes">
                        <p>{{!sortStageAes?'降序':'升序'}}</p>
                    </div>
                    <el-table :data="stageData" :header-cell-style="{ background: '#F3F7FF','color':'2B2C33','fontSize':'14px' }" class="mt10" height="180px">
                         <el-table-column prop="stageName" label="学段" align="center"  />
                         <el-table-column prop="numberOfUser" label="人员数量" align="center" />
                    </el-table>
                 </div>
             </div>
           </div>
           <div class="member subject-member mt16" >
                 <div class="title flex_between">
                    <h3 class="flex_start">学科情况</h3>
                    <button @click="goExport('subject')">导出</button>
                 </div>
                 <div id="member-subject" class="echarts">

                 </div>
                 <div class="table">
                    <div class="flex_start sort" @click="sortSubject">
                       <img src="@/assets/pc/sortby.png" alt=""  v-show="!sortSubjectAes">
                        <img src="@/assets/pc/sortby-up.png" alt="" v-show="sortSubjectAes">
                          <p>{{!sortSubjectAes?'降序':'升序'}}</p>
                    </div>
                    <el-table :data="subjectData" :header-cell-style="{ background: '#F3F7FF','color':'2B2C33','fontSize':'14px' }" class="mt10">
                         <el-table-column prop="subjectName" label="学科" align="center"  />
                         <el-table-column prop="numberOfUser" label="人员数量" align="center" />
                    </el-table>
                 </div>
            </div>
            <div class="member subject-member mt16" >
                 <div class="title flex_between">
                    <h3 class="flex_start">单位人员情况</h3>
                    <button @click="goExport('tenant')">导出</button>
                 </div>
                 <div id="member-worker" class="echarts" style="height:240px">

                 </div>
                 <div class="table">
                    <div class="flex_start sort" @click="sortTenant">>
                         <img src="@/assets/pc/sortby.png" alt=""  v-show="!sortTenantAes">
                        <img src="@/assets/pc/sortby-up.png" alt="" v-show="sortTenantAes">
                         <p>{{!sortTenantAes?'降序':'升序'}}</p>
                    </div>
                    <el-table :data="tenantData" :header-cell-style="{ background: '#F3F7FF','color':'2B2C33','fontSize':'14px' }" class="mt10">
                         <el-table-column prop="tenantName" label="单位" align="center"  />
                         <el-table-column prop="numberOfUser" label="人员数量" align="center" />
                    </el-table>
                 </div>
             </div>
    </div>
</template>
<script setup>
import {onMounted, reactive, ref,onActivated, } from 'vue'
import {themeList,numberByAge,numberByGroup,numberByStage,numberByTenant,numberBySubject,exportByAge} from '@/api/pc/index.js'
import * as echarts from 'echarts';
import StaticLeft from '@/views/pc/component/act-research-association/statisticLeft.vue'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'
import Down from '@/utils/download.js'
import {useRouter} from 'vue-router'
let userInfo=useUserInfoStoreHook()
let {themeScrollTop}=storeToRefs(userInfo)
let router=useRouter()
let echartAge=ref()
let ageData=ref([])
let sortAgeAes=ref(false)
let echartMember=ref()
let sortMemberAes=ref(false)
let memberData=ref([])
let echartStage=ref()
let sortStageAes=ref(false)
let stageData=ref([])
let echartSubject=''
let subjectData=ref([])
let sortSubjectAes=ref(false)
let echartWorker=''
let tenantData=ref([])
let data=ref([])
let initechartAge=(row)=>{
       let total=0
   row.forEach(item=>{
          total=total+item.numberOfUser
    })
    var scaleData =row.map(item=>{
        return {
            name:item.ageRange,
            value:item.numberOfUser,

        }
    })

var data = [];
var color=['#3B78FF','#7DAEFF','#C7E4FF','#A2EFFF']
for (var i = 0; i < scaleData.length; i++) {
    data.push({
        value: scaleData[i].value,
        name: scaleData[i].name,
        itemStyle: {
            normal: {
                borderWidth: 0,
                shadowBlur: 1200,
                borderColor:color[i%color.length],
                shadowColor: "#fff",
                color:color[i],
            }
        }
    }, {
        value:scaleData.length==1?0:(total/70)*scaleData.length,
        name: '',
        itemStyle: {
            normal: {
                label: {
                    show: false
                },
                labelLine: {
                    show: false
                },
                color: 'rgba(0, 0, 0, 0)',
                borderColor: 'rgba(0, 0, 0, 0)',
                borderWidth: 0
            }
        }
    });
}

    echartAge.value = echarts.init(document.getElementById('member-age'));
    let option={
           backgroundColor: '#ffffff',
            tooltip: {
                show: false
            },
            
            legend: {
                show: false
            },
            toolbox: {
                show: false
            },
            series: [{
                    name: '',
                    type: 'pie',
                    clockWise: false,
                    radius: [40, 70],
                    hoverAnimation: false,
                    itemStyle: {
                        normal: {
                            label: {
                                show: true,
                                position: 'outside',
                                color: '#8DA2B5',
                                formatter: function(params) {
                                    var percent = 0;
                                    var total = 0;
                                    for (var i = 0; i < scaleData.length; i++) {
                                        total += scaleData[i].value;
                                    }
                                    percent = ((params.value / total) * 100).toFixed(0);
                                    if(params.name !== '') {
                                        return params.name ;
                                    }else {
                                        return '';
                                    }
                                },
                                rich: {
                                    white: {
                                        color: '#ddd',
                                        align: 'center',
                                        padding: [3, 0]
                                    }
                                }
                            },
                            labelLine: {
                                length:10,
                                length2:10,
                                show: true,
                                color:'#00ffff'
                            }
                        }
                    },
                    data: data
                }]
            }
    echartAge.value.setOption(option)
}
let initechartMember=(row)=>{
     let total=0
   row.forEach(item=>{
          total=total+item.numberOfUser
    })
   
   var scaleData =row.map(item=>{
        return {
            name:item.groupName,
            value:item.numberOfUser,

        }
    })

var data = [];
var color=['#FF883F','#FFAB39','#FFD837','#C2E227']
for (var i = 0; i < scaleData.length; i++) {
    data.push({
        value: scaleData[i].value,
        name: scaleData[i].name,
        itemStyle: {
            normal: {
                borderWidth: 0,
                shadowBlur: 1200,
                borderColor:color[i%color.length],
                shadowColor: "#fff",
                color:color[i],
            }
        }
    }, {
          value:scaleData.length==1?0:(total/70)*scaleData.length,
        name: '',
        itemStyle: {
            normal: {
                label: {
                    show: false
                },
                labelLine: {
                    show: false
                },
                color: 'rgba(0, 0, 0, 0)',
                borderColor: 'rgba(0, 0, 0, 0)',
                borderWidth: 0
            }
        }
    });
}

    echartAge.value = echarts.init(document.getElementById('member-member'));
    let option={
           backgroundColor: '#ffffff',
            tooltip: {
                show: false
            },
         
            legend: {
                show: false
            },
            toolbox: {
                show: false
            },
            series: [{
                    name: '',
                    type: 'pie',
                    clockWise: false,
                    radius: [40, 70],
                    hoverAnimation: false,
                    itemStyle: {
                        normal: {
                            label: {
                                show: true,
                                position: 'outside',
                                color: '#8DA2B5',
                                formatter: function(params) {
                                    var percent = 0;
                                    var total = 0;
                                    for (var i = 0; i < scaleData.length; i++) {
                                        total += scaleData[i].value;
                                    }
                                    percent = ((params.value / total) * 100).toFixed(0);
                                    if(params.name !== '') {
                                        return params.name ;
                                    }else {
                                        return '';
                                    }
                                },
                                rich: {
                                    white: {
                                        color: '#ddd',
                                        align: 'center',
                                        padding: [3, 0]
                                    }
                                }
                            },
                            labelLine: {
                                length:10,
                                length2:10,
                                show: true,
                                color:'#00ffff'
                            }
                        }
                    },
                    data: data
                }]
            }
    echartAge.value.setOption(option)
}
let initechartStage=(row)=>{
      let total=0
   row.forEach(item=>{
          total=total+item.numberOfUser
    })
   
   var scaleData =row.map(item=>{
        return {
            name:item.stageName,
            value:item.numberOfUser,

        }
    })

var data = [];
var color=['#FFC7E2','#FF72B6','#FF72B6','#FFB6B6']
for (var i = 0; i < scaleData.length; i++) {
    data.push({
        value: scaleData[i].value,
        name: scaleData[i].name,
        itemStyle: {
            normal: {
                borderWidth: 0,
                shadowBlur: 1200,
                borderColor:color[i%color.length],
                shadowColor: "#fff",
                color:color[i],
            }
        }
    }, {
        value: 1,
        name: '',
        itemStyle: {
            normal: {
                label: {
                    show: false
                },
                labelLine: {
                    show: false
                },
                color: 'rgba(0, 0, 0, 0)',
                borderColor: 'rgba(0, 0, 0, 0)',
                borderWidth: 0
            }
        }
    });
}

    echartAge.value = echarts.init(document.getElementById('member-stage'));
    let option={
           backgroundColor: '#ffffff',
            tooltip: {
                show: false
            },
            legend: {
                show: false
            },
            toolbox: {
                show: false
            },
            series: [{
                    name: '',
                    type: 'pie',
                    clockWise: false,
                    radius: [40, 70],
                    hoverAnimation: false,
                    itemStyle: {
                        normal: {
                            label: {
                                show: true,
                                position: 'outside',
                                color: '#8DA2B5',
                                formatter: function(params) {
                                    var percent = 0;
                                    var total = 0;
                                    for (var i = 0; i < scaleData.length; i++) {
                                        total += scaleData[i].value;
                                    }
                                    percent = ((params.value / total) * 100).toFixed(0);
                                    if(params.name !== '') {
                                        return params.name ;
                                    }else {
                                        return '';
                                    }
                                },
                                rich: {
                                    white: {
                                        color: '#ddd',
                                        align: 'center',
                                        padding: [3, 0]
                                    }
                                }
                            },
                            labelLine: {
                                length:10,
                                length2:10,
                                show: true,
                                color:'#00ffff'
                            }
                        }
                    },
                    data: data
                }]
            }
    echartAge.value.setOption(option)
}
let initechartSubject=(row)=>{
    let x=row.map(item=>item.subjectName)
    let y=row.map(item=>item.numberOfUser)
    echartSubject= echarts.init(document.getElementById('member-subject'));
  let option = {
    backgroundColor: '#fff',
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow'
        },
        // formatter: '{a} <br/>{b} : {c}' // {a}、{b}、{c} 是占位符
    },
    
    grid: {
        top: '6%',
        right: '0%',
        left: '5%',
        bottom: '15%'
    },
    xAxis: [{
        type: 'category',
        data: x,
        axisLine: {
            lineStyle: {
                color: 'rgba(255,255,255,0.12)'
            }
        },
        axisLabel: {
            margin: 10,
            color: '#8DA2B5',
            textStyle: {
                fontSize: 12
            },
        },
    }],
    yAxis: [{
        axisLabel: {
            formatter: '{value}',
            color: '#8DA2B5',
             textStyle: {
                fontSize: 12
            },
        },
        axisLine: {
            show: false
        },
        splitLine: {
            lineStyle: {
                color: ' #F1F2F5',
                type:'dashed'
            }
        }
    }],
    series: [{
        type: 'bar',
        data: y,
        barWidth: '20px',
        itemStyle: {
            normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: 'rgb(123,210,243)' // 0% 处的颜色
                }, {
                    offset: 1,
                    color: 'rgb(80,184,246)' // 100% 处的颜色
                }], false),
                barBorderRadius: [30, 30, 0,0],
               
             
            }
        },
      
    }]
  };
  echartSubject.setOption(option)
}
let initechartWorker=(row)=>{
 echartWorker = echarts.init(document.getElementById('member-worker'));
     let x=row.map(item=>item.tenantName)
    let y=row.map(item=>item.numberOfUser)
  let option = {
    backgroundColor: '#fff',
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow'
        }
    },
    grid: {
        top: '6%',
        right: '0%',
        left: '5%',
        bottom: '24%'
    },
   dataZoom: [
                {
                    type: 'slider',
                    xAxisIndex: 0,
                     start: 0,
                     end: 10,
                      showDetail: false,
                height: "8px",
                width:"100%",
                bottom: "6%",
                right: 0,
                }
            ],

    xAxis: [{
        type: 'category',
        data: x,
        axisLine: {
            lineStyle: {
                color: 'rgba(255,255,255,0.12)',
                 
            }
        },
        axisLabel: {
            margin: 10,
            color: '#8DA2B5',
           
            textStyle: {
                fontSize: 12,
             
            },
             formatter: function (value, index) {
                        const maxLength = 6; // 每行最大字符数
                        const maxLines = 3; // 最大行数
                        let result = '';
                        for (let i = 0; i < value.length; i += maxLength) {
                            if (Math.ceil(i / maxLength) >= maxLines) {
                                result += '';
                                break;
                            }
                            result += value.slice(i, i + maxLength);
                            if (i + maxLength < value.length) {
                                result += '\n';
                            }
                        }
                        return result
                    }
        },
    }],
    yAxis: [{
        axisLabel: {
            formatter: '{value}',
            color: '#8DA2B5',
             textStyle: {
                fontSize: 12
            },
        },
        axisLine: {
            show: false
        },
        splitLine: {
            lineStyle: {
                color: ' #F1F2F5',
                type:'dashed'
            }
        }
    }],
    series: [{
        type: 'bar',
        data: y,
        barWidth: '20px',
        itemStyle: {
            normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: '#88C0FF' // 0% 处的颜色
                }, {
                    offset: 1,
                    color: '#508CFF' // 100% 处的颜色
                }], false),
                barBorderRadius: [30, 30, 0,0],
               
             
            }
        },
      
    }]
  };
  echartWorker.setOption(option)
}
let sortAge=async()=>{
    sortAgeAes.value=!sortAgeAes.value
    if(!sortAgeAes.value){
       ageData.value.sort((a, b) => b.numberOfUser - a.numberOfUser);
    }else{
      ageData.value.sort((a, b) => a.numberOfUser - b.numberOfUser);
    }
}
let setAge=async()=>{
    let res=await numberByAge()
    if(res){
        // console.log(res)
        res.data.sort((a, b) => b.numberOfUser - a.numberOfUser);
        ageData.value=res.data
        initechartAge(res.data)
    }
}
let sortMember=()=>{
  sortMemberAes.value=!sortMemberAes.value
    if(!sortMemberAes.value){
       memberData.value.sort((a, b) => b.numberOfUser - a.numberOfUser);
    }else{
      memberData.value.sort((a, b) => a.numberOfUser - b.numberOfUser);
    }
}
let setMember=async()=>{
    let res=await numberByGroup()
    if(res){
        res.data.sort((a, b) => b.numberOfUser - a.numberOfUser);
         memberData.value=res.data
         initechartMember(res.data)
    }
}
let sortStage=()=>{
      sortStageAes.value=!sortStageAes.value
    if(!sortStageAes.value){
       stageData.value.sort((a, b) => b.numberOfUser - a.numberOfUser);
    }else{
      stageData.value.sort((a, b) => a.numberOfUser - b.numberOfUser);
    }
}
let sortSubject=()=>{
      sortSubjectAes.value=!sortSubjectAes.value
    if(!sortSubjectAes.value){
       subjectData.value.sort((a, b) => b.numberOfUser - a.numberOfUser);
    }else{
     subjectData.value.sort((a, b) => a.numberOfUser - b.numberOfUser);
    }
}
let sortTenant=()=>{
      sortTenantAes.value=!sortTenantAes.value
    if(!sortTenantAes.value){
       tenantData.value.sort((a, b) => b.numberOfUser - a.numberOfUser);
    }else{
     tenantData.value.sort((a, b) => a.numberOfUser - b.numberOfUser);
    }
}
let setStage=async()=>{
      let res=await numberByStage()
    if(res){
       res.data.sort((a, b) => b.numberOfUser - a.numberOfUser);
         stageData.value=res.data
         initechartStage(res.data)
    }
}
let setSubject=async()=>{
      let res=await numberBySubject()
    if(res){
     res.data.sort((a, b) => b.numberOfUser - a.numberOfUser);
         subjectData.value=res.data
         initechartSubject(res.data)
    }
}
let setTenant=async()=>{
      let res=await numberByTenant()
    if(res){
      res.data.sort((a, b) => b.numberOfUser - a.numberOfUser);
         tenantData.value=res.data
         initechartWorker(res.data)
    }
}
let goExport=async(type)=>{
    if(type=='age'){
       Down.excel('/xzs/dashboard/statistics/number-of-user-group-by-age-range/file','年龄情况')
    }
    if(type=='group'){
       Down.excel('/xzs/dashboard/statistics/number-of-user-group-by-group/file','小组人员情况')
    }
    if(type=='stage'){
       Down.excel('/xzs/dashboard/statistics/number-of-user-group-by-stage/file','学段情况')
    }
    if(type=='subject'){
       Down.excel('/xzs/dashboard/statistics/number-of-user-group-by-subject/file','学科情况')
    }
   if(type=='tenant'){
       Down.excel('/xzs/dashboard/statistics/number-of-user-group-by-tenant/file','单位人员情况')
    }
}
onMounted(()=>{
    setAge()
    setMember()
    setStage()
    setSubject()
    setTenant()
//  themes.value=[]
    // initechartAge()
    // initechartMember()
    // initechartStage()
    // initechartSubject()
    // initechartWorker()
})


</script>
<style lang="scss" scoped>
.joiner{
    margin-bottom: 10px;
   
}
.mt10{
    margin-top:10px
}
 .member{
            width: 32%;
            border-radius: 4px;
            padding: 16px;
            background: #fff;
            .title{
                h3{
                    font-size: 16px;
                    color: #2B2C33;
                    &::before{
                        content:'';
                        display:block;
                        width: 2px;
                        height: 14px;
                        background: #508CFF;
                        margin-right:6px ;
                    }
                }
                button{
                    cursor: pointer;
                   padding: 0 10px;
                    height: 20px;
                    font-size: 14px;
                    color: #fff;
                    line-height: 14px;
                    background: #508CFF;
                    border-radius: 4px;
                    border:none
                }
            }
            .echarts{
                height: 180px;
                margin: 10px 0;
            }
             .sort{
                    font-size: 12px;
                    color: #508CFF;
                    cursor: pointer;
                    img{
                        width: 14px;
                        margin-right: 6px;
                        height: auto;
                    }
                    p{
                        line-height: 14px;
                    }
                }
        }
        .subject-member{
            width: 100%;
          
        }
</style>
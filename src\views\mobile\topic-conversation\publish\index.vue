<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from "vue"
import WangEditor from "@/components/WangEditor/index.vue"
import { createTopicConversationApi, getGroupListApi, getTopicConversationDetailApi, editTopicConversationApi } from "@/api/mobile/topic-conversation"
import { getTopicListApi } from "@/api/mobile/topic"
import { showToast } from "vant";
import { useRouter, useRoute } from "vue-router";

const router = useRouter()
const route = useRoute()
const addForm = ref({
  topic: "",
  clubPostType: "2",
  content: "",
  clubGroupId: '',
  clubGroupName: ''
})

const activeTopic = ref({})
const isEdit = ref(false) // 是否是编辑模式
const postId = ref('') // 帖子ID

const topicList = ref([])

// 获取主题列表
const getTopicList = async () => {
  try {
    const res = await getTopicListApi()
    if (res) {
      topicList.value = res.data
      addForm.value.topic = res.data[0].id
    }
  } catch (error) {
    console.log(error)
  }
}

const groupList = ref([])
const getGroupList = async () => {
  try {
    const res = await getGroupListApi()
    if (res) {
      groupList.value = res.data.map(item => ({
        text: item.name,
        value: item.id
      }))
    }
  } catch (error) {
    console.log(error)
  }
}

// 获取详情数据
const getDetail = async (id) => {
  try {
    const res = await getTopicConversationDetailApi(id)
    if (res) {
      const detail = res.data
      // 填充表单数据
      addForm.value.topic = detail.clubTopicName || ''
      addForm.value.clubPostType = detail.postType && detail.postType.toString() || '2'
      addForm.value.content = detail.content || ''
      nextTick(() => {
        if (addForm.value.clubPostType === '1' && detail.clubGroupId) {
          addForm.value.clubGroupId = detail.clubGroupId
          addForm.value.clubGroupName = groupList.value.find(item => item.value === detail.clubGroupId)?.text || ''
        }
      })
    }
  } catch (error) {
    console.log(error)
    showToast('获取详情失败')
  }
}

const showPicker = ref(false)
const selectedGroup = ref([])
const openSelectGroup = () => {
  selectedGroup.value = addForm.value.clubGroupId ? [addForm.value.clubGroupId] : []
  showPicker.value = true
}

const onConfirm = ({ selectedOptions }) => {
  addForm.value.clubGroupId = selectedOptions?.[0].value
  addForm.value.clubGroupName = selectedOptions?.[0].text

  showPicker.value = false
}

const onCancel = () => {
  showPicker.value = false
}

// 提交表单
const submitDiary = async () => {
  try {
    if (!addForm.value.topic) {
      showToast('请输入主题')
      return
    }

    if (addForm.value.content === '<p><br></p>') {
      showToast('请输入内容')
      return
    }

    if (addForm.value.clubPostType === '1' && !addForm.value.clubGroupId) {
      showToast("请选择小组")
      return
    }

    const formData = {
      content: addForm.value.content,
      clubTopicName: addForm.value.topic,
      clubPostType: addForm.value.clubPostType,
      clubGroupId: addForm.value.clubPostType === '1' ? addForm.value.clubGroupId : ''
    }

    let res
    if (isEdit.value && postId.value) {
      // 编辑模式
      res = await editTopicConversationApi(postId.value, formData)
    } else {
      // 新建模式
      res = await createTopicConversationApi(formData)
    }

    if (res) {
      showToast(isEdit.value ? '编辑成功' : '提交成功')
      router.back()
    }
  } catch (error) {
    console.log(error)
  }
}

onMounted(() => {
  // 获取小组列表
  getGroupList()
  // 检查是否有ID参数，如果有则是编辑模式
  if (route.query.id) {
    isEdit.value = true
    postId.value = route.query.id
    getDetail(postId.value)
  }
})

</script>

<template>
  <div class="add-diary-page">
    <div class="form-item flex-column-start-start">
      <div class="label">主题标签</div>
      <van-field v-model="addForm.topic" placeholder="请输入主题标签" />
    </div>
    <div class="form-item flex-column-start-start">
      <div class="label">帖子类型</div>
      <van-radio-group v-model="addForm.clubPostType" direction="horizontal">
        <van-radio name="2">全社讨论</van-radio>
        <van-radio name="1">小组讨论</van-radio>
      </van-radio-group>
    </div>
    <div class="form-item flex-column-start-start" v-if="addForm.clubPostType === '1'">
      <div class="label">小组</div>
      <van-field v-model="addForm.clubGroupName" @click="openSelectGroup" readonly placeholder="请选择小组" />
      <van-popup v-model:show="showPicker" position="bottom">
        <van-picker
          title="选择小组"
          v-model="selectedGroup"
          :columns="groupList"
          @confirm="onConfirm"
          @cancel="onCancel"
        ></van-picker>
      </van-popup>
    </div>
    <div class="form-item editor-container flex-column-start-start m-pt20">
      <div class="label">发布内容</div>
      <div class="editor-content">
        <WangEditor v-model="addForm.content" :height="'300px'" />
      </div>
    </div>
    <div class="m-btn-container flex-start-center m-pt20">
      <van-button class="m-submit-btn" @click="submitDiary">{{ isEdit ? '保存' : '提交' }}</van-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "@/views/mobile/styles/custom-global-mobile-style.scss";

.add-diary-page {
  width: 100%;
  height: 100%;
  background-color: #fff;
  padding: 20px 15px;
  overflow-y: auto;

  .form-item {
    .label {
      font-weight: 400;
      font-size: 14px;
      color: #2b2c33;
      line-height: 24px;
      margin-right: 27px;
    }

    :deep(.van-field) {
      padding-left: 0;
      padding-right: 0;
    }

    :deep(.van-cell::after) {
      display: none;
    }

    :deep(.van-radio-group) {
      height: 44px;
      line-height: 44px;

      .van-radio {

        &__label {
          font-size: 14px;
        }
      }
    }
  }

  .choose-topic {
    .topic-list {
      max-height: 150px;
      overflow-y: auto;
    }

    .topic-item {
      padding: 3px 10px;
      height: 24px;
      border-radius: 19px;
      margin-right: 10px;
      border: 1px solid #e2e3e6;

      &.active {
        background-color: #508cff;
        border-color: #508cff;
        color: #fff;
      }
    }
  }

  .clocking-time {
    :deep(.van-field) {
      padding: 0;
    }
  }
}

::-webkit-scrollbar{
  display: none;
}
</style>

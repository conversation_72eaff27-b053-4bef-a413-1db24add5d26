import { request } from "@/utils/service"

// 点赞列表
export function getLikeListA<PERSON>(recordId, params) {
  return request({
    url: `xzs/club-record/${recordId}/like`,
    method: "get",
    params
  })
}

// 评论列表
export function getCommentListApi(recordId, params) {
  return request({
    url: `xzs/club-record/${recordId}/comment`,
    method: "get",
    params
  })
}

// 点赞/取消点赞评论
export function likeCommentApi(commentId, data) {
  return request({
    url: `xzs/club-record-comment/${commentId}/like`,
    method: "post",
    data
  })
}

// 打星
export function starApi(recordId, data) {
  return request({
    url: `xzs/club-record/${recordId}/rate`,
    method: "post",
    data
  })
}

// 删除评论
export function deleteCommentApi(commentId) {
  return request({
    url: `xzs/club-record-comment/${commentId}`,
    method: "delete"
  })
}

// 编辑评论
export function editCommentApi(commentId, data) {
  return request({
    url: `xzs/club-record-comment/${commentId}`,
    method: "put",
    data
  })
}

// 临时票据
export function getTempTicketApi() {
  return request({
    url: `ticket`,
    method: "get"
  })
}

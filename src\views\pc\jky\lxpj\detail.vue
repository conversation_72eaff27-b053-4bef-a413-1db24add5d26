<template>
    <div class="lxpj-detail content-box">
        <p class="text_20_bold flex_start mr_40 pointer" style="display:inline-flex;" @click="router.back()"><el-icon><ArrowLeftBold /></el-icon>返回</p>  
        <div class="mt_16 mb_24" :class="detail.state != 4 ? 'flex_between' : ''">
            <div class="flex_start">
                <p class="text_18_bold mr_24">XXX课题研究XXX课题研究</p>
                <div class="flex_start text_14">
                    <img v-if="detail.state == 1" src="@/assets/pc/jky/state-blue.png" class="state-icon" alt="">
                    <img v-else-if="detail.state == 2" src="@/assets/pc/jky/state-orange.png" class="state-icon" alt="">
                    <img v-else-if="detail.state == 3" src="@/assets/pc/jky/state-green.png" class="state-icon" alt="">
                    <img v-else src="@/assets/pc/jky/state-grey.png" class="state-icon"  alt="">
                    <span :class="detail.state == 1 ? 'blue' : detail.state == 2 ? 'orange' : detail.state == 3 ? 'green' : 'grey'">{{detail.state == 1 ? '申报中' : detail.state == 2 ? '待分配' : detail.state == 3 ? '评鉴中' : '已结束'}}</span>
                </div>
            </div>
            <!-- 当状态是已结束时： 其余状态flex_end -->
            <div :class="detail.state != 4 ? 'flex_end' : 'mt_26'">
                <el-form inline :model="search_form" class="flex_start detail-form">
                    <el-form-item label="单位名称" style="margin-right: 16px;margin-bottom: 0;">
                        <el-input v-model="search_form.name" class="input_40" style="width:300px;" clearable placeholder="请输入"></el-input>
                    </el-form-item>
                    <el-form-item label="" class="hidden-label flex_1" style="margin-right: 0;margin-bottom: 0;" >
                        <el-button class="primary-btn btn_132_40" type="primary" @click="searchClick">查询</el-button>
                        <div class="ml_24">
                            <!-- 只有待分配的状态会出现评委分配按钮  -->
                            <el-button v-if="detail.state == 2 " class="plain-btn2 btn_132_40" type="primary" @click="allocation_visible = true">评委分配</el-button>
                            <el-button v-if="(detail.state == 3 && has_pwmd) || detail.state == 4" class="plain-btn2 btn_132_40" type="primary" @click="goJudges">评委名单</el-button>
                            <!-- 只有已结束的时候才会出现这个按钮  -->
                            <el-button v-if="detail.state == 4" class="plain-btn2 btn_132_40" type="primary" @click="result_visible = true">总成绩单</el-button>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <el-table :data="task_list" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F9F9F9"}'>
            <el-table-column type="index" label="序号" width="80"  />
            <el-table-column prop="taskTitle" label="单位名称" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="createTime" label="指标总数" />
            <el-table-column prop="createTime" label="上报人数" />
            <!-- 只有评鉴中时才会展示评审状态 -->
            <el-table-column prop="createTime" label="评审状态" />
            <el-table-column label="操作" width="120">
                <template #default='scope'>
                    <div class="flex_start" style="height:100%">
                        <el-button type="primary" link style="font-size:16px;font-weight:400;" @click="goDetail(scope.row)">查看</el-button>
                    </div>
                </template> 
            </el-table-column>
        </el-table>
        <el-pagination background layout="total,prev, pager, next" :total="total" class="mt-4" :current-page='search_form.pageNum' @current-change='currentChange' />
        <el-dialog v-model="allocation_visible" v-if="allocation_visible" title="评委分配" width="920px" center class="my-dialog">
            <Allocation :id="taskMasterId" @close="allocation_visible = false, init()"></Allocation>
        </el-dialog> 
        <el-dialog v-model="result_visible" v-if="result_visible" title="成绩单" width="920px" center class="my-dialog">
            <Result :id="taskMasterId" @close="result_visible = false, init()"></Result>
        </el-dialog> 
    </div>
</template>
<script setup> 
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import Allocation from './Allocation.vue'
    import Result from './Result.vue'
    import { useRouter, useRoute } from 'vue-router'
    const router = useRouter()
    const route = useRoute()
    const formObj = function(obj = {}){
        this.name = obj.name || ''
        this.pageNum = obj.pageNum || 1
        this.pageSize = obj.pageSize || 10
    }
    const search_form = ref(new formObj({}))
    const allocation_visible = ref(false)
    const detail = ref({state:4}) // 1:申报中 2:待分配 3: 评鉴中 4:已结束
    const task_list = ref([{}]) 
    const total = ref(0) 
    const has_pwmd = ref(true)
    const result_visible = ref(false) // 总成绩单

    const goDetail = (row) => {
        router.push(`/jky/lxpj/schtask/${row.taskMasterId}`)
    }
    const goJudges = () => {
        router.push(`/jky/lxpj/judges/0`)
    }
    onMounted(() => {
    })
</script>
<style lang="scss" scoped>
.lxpj-detail{margin: 16px auto;background: #FFFFFF;padding: 24px;border-radius: 12px;
    .state-icon{width: 14px;margin-right: 5px;}
    .detail-form{
        :deep(.el-form-item__label){height:40px!important;line-height:40px!important;font-weight: 600; font-size: 16px; color: #2B2C33; line-height: 24px; text-align: left;}
    }
    .hidden-label{
        :deep(.el-form-item__content){justify-content:space-between;}
    }
}
</style>
<script setup>
import { ref, watch, onMounted } from "vue"
import { EchartsUI, useEcharts } from "@/components/Echart"

const chartRef = ref()
const { renderEcharts } = useEcharts(chartRef)

const props = defineProps({
  chartData: {
    type: Object,
    default: () => ({
      data: [],
      color: []
    })
  }
})

watch(
  () => props.chartData,
  () => {
    paintChart()
  },
  { deep: true }
)

onMounted(() => {
  paintChart()
})

const paintChart = () => {
  const data = props.chartData.data
  
  // 提取数据
  const xAxisData = data.map(item => item.clubGroupName)
  const goodCreatorData = data.map(item => item.awesomeAuthorNumber)
  const activeStarData = data.map(item => item.activeStarNumber)

  renderEcharts({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    legend: {
      data: ['优质创作者', '活跃之星'],
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 12,
        color: '#8DA2B5'
      },
      bottom: 0,
      left: 'center',
      orient: 'horizontal'
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#E2E3E6'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#8DA2B5',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#E2E3E6',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#8DA2B5',
        fontSize: 12
      }
    },
    series: [
      {
        name: '优质创作者',
        type: 'bar',
        barWidth: 12,
        data: goodCreatorData,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#FFDD75' },
              { offset: 1, color: '#FF9C48' }
            ]
          },
          borderRadius: [3, 3, 0, 0]
        }
      },
      {
        name: '活跃之星',
        type: 'bar',
        barWidth: 12,
        data: activeStarData,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#88C0FF' },
              { offset: 1, color: '#508CFF' }
            ]
          },
          borderRadius: [3, 3, 0, 0]
        }
      }
    ]
  })
}
</script>

<template>
  <EchartsUI ref="chartRef" :height="'235px'" />
</template>

<style scoped></style>

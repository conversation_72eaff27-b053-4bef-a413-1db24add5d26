<template>
    <div class="content-box  preview mt10 flex_start_0" style="width:100%;height:100%">
        <div class="left flex_column flex_start_0">
                <div class="flex_between">
                    <div class="flex_start title" @click="goCancel">
                        <el-icon style="margin-right: 4px;"><ArrowLeft /></el-icon>
                       {{row.title}}
                    </div>
                    <div class="flex_start" v-if="currentInfo.type=='teacher'">
                        <div class="flex_start liked" @click="changeFavorite">
                             <img src="@/assets/pc/r-comment.png" alt="" v-show="!row.isFavorite" />
                              <img src="@/assets/pc/r-liked.png" alt="" v-show="row.isFavorite" />
                            <p>{{row.favoriteCount?row.favoriteCount:0}}</p>
                        </div>
                        <div class="flex_start liked star" @click="changeLike">
                             <img src="@/assets/pc/star.png" alt="" v-show="!row.isLike" />
                            <img src="@/assets/pc/r-star.png" alt="" v-show="row.isLike" />
                            <p>{{row.likeCount}}</p>
                        </div>
                    </div>
                </div>
                <div class="flex_between" style="margin-top:8px">
                    <div class="flex_start detail">
                        <p>上传时间：{{row.addTime}}</p>
                        <p>播放量：{{row.viewCount}}</p>
                        <p>分享次数：{{row.shareCount}}</p>
                    </div>
                  
                      <div class="flex_start action">
                              <div class="flex_start copy" @click="goCopy">
                                    <img src="@/assets/pc/action-copy.png" alt=""  />
                                    <p>复制链接</p>
                              </div>
                              
                    </div>
                </div>
                
                <div class="file flex_1">
                    <Preview :row='row' />
                </div>
                <div class="flex_between bottom-detail">
                   <div class="flex_start autor">
                      <div class="flex_start autor1">
                          <div class="flex_start">
                            <img src="@/assets/pc/autor1.png" alt="">
                            <p class="label">资源作者</p>
                          </div>
                           <p class="value">{{row.teacherTitle}}</p>
                      </div>
                       <div class="flex_start autor1">
                          <div class="flex_start">
                            <img src="@/assets/pc/autor2.png" alt="">
                            <p class="label">所属单位</p>
                          </div>
                          <p class="value">{{row.schoolTitle}}</p>
                      </div>
                  </div>
                </div>
               
        </div>
        <div class="right flex_1">
            <div class="title">相关推荐</div>
            <el-empty description="暂无相关推荐" v-show="recommendList.length==0"></el-empty>
            <ul  v-show="recommendList.length!=0">
                <li class="item" v-for="item in recommendList" :key="item.id" @click="goRecommend(item)">
                    <p>{{item.title}}</p>
                    <div class="recommend-cover">
                         <img src="@/assets/pc/emptyvideo.png" alt="">
                    </div>
                </li>
            </ul>
        </div>
       
     
    </div >
</template>
<script setup>
import {onMounted, reactive, ref} from 'vue'
import { resourceHdktDetail,hdktResourceShare,hdktResourceRescommend,hdktResourceLike,hdktResourceFavourite,} from '@/api/pc/index.js'
import Preview from '@/views/pc/component/resource/preview.vue'

import {useRoute,useRouter} from 'vue-router'
import {ElMessageBox,ElMessage} from 'element-plus'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'
let userInfo=useUserInfoStoreHook()
let {currentInfo}=storeToRefs(userInfo)
let route=useRoute()
let router=useRouter()
let resourceId=''
let row=ref({})
let loading=ref(false)
let recommendList=ref([])
let goCancel=()=>{
    router.go(-1)
}

let goCopy=async()=>{
    let url=import.meta.env.VITE_REDIRECT_URL+'/dxs/#/resource/teach/preview?id='+resourceId
       
            try {
                // 创建一个 ClipboardItem 对象，包含 HTML 和纯文本内容
                await navigator.clipboard.writeText(url).then(
                    async() => {
                         let res=await hdktResourceShare({
                            resourceId
                         })
                        
                    
                         if(res){
                            row.value.shareCount=row.value.shareCount+1
                         }
                        ElMessage.success("链接已复制到剪贴板（请在电脑端打开）");
                    },
                    (err) => {
                        console.log("~ copyText ~ err:", err);
                    })
            } catch (err) {
                console.error('复制失败:', err);
            }
}


let goRecommend=(item)=>{
     router.push('/resource/teach/hdktpreview?id='+item.resourceId).then(()=>{
         resourceId=route.query.id
    
         getDetail()
     })
}

let getDetail=async()=>{
    let res=await resourceHdktDetail({
          resourceId:resourceId
        })
   
    if(res){   
        res.data.from='hdkt'
        res.data.fileUrl=res.data.videoUrl
        res.data.fileType=2
        row.value=res.data
    }
}

let changeFavorite=async()=>{
    let res=await hdktResourceLike({resourceId:resourceId})
   
     if(res){
        row.value.isFavorite=!row.value.isFavorite
        if(row.value.isFavorite){
              row.value.favoriteCount= row.value.favoriteCount+1
        }else{
            row.value.favoriteCount= row.value.favoriteCount-1
        }
     }
}
let changeLike=async()=>{
 let  res=await hdktResourceLike({resourceId:resourceId})
    
   if(res){
        row.value.isLike=!row.value.isLike
        if(row.value.isLike){
              row.value.likeCount= row.value.likeCount+1
        }else{
            row.value.likeCount= row.value.likeCount-1
        }
     }
}
let getRecommend=async()=>{
    
    let  res=await hdktResourceRescommend({
            resourceId
        }) 
   
   
    if(res){
      
       recommendList.value=res.data
    }
}
onMounted(()=>{
    resourceId=route.query.id
    
    getDetail()
    getRecommend()
})
</script>
    <style scoped lang='scss'>
    .preview{
       background: #fff;
       padding: 24px;
       .left{
        width: 70%;
        padding-right: 24px;
        border-right: 1px solid #E2E3E6;
        .title{
            font-size: 20px;
            color: #2B2C33;
            font-weight: bold;
            cursor: pointer;
        }
        .liked{
            img{
            width: 24px;
            height: auto;
            margin-right: 4px;
              cursor: pointer;
            }
            p{
                font-size: 14px;
                color: #94959C;
            }
        }
        .star{
            margin-left: 32px;
            cursor: pointer;
        }
        .detail{
          
            p{
                font-size: 14px;
                color: #6D6F75;
                margin-right: 32px;
            }
        }
        .action{
            font-size: 14px;
            img{
                width: 14px;
                height: auto;
                margin-right: 4px;
            }
            .edit{
                color: #508CFF;
                margin-right: 24px;
                cursor: pointer;
            }
            .del{
             color: #E72C4A;
             margin-right: 24px; cursor: pointer;
            }
            .copy{
              color: #508CFF;   cursor: pointer;
            }
        }
        .file{
            // height: 452px;
            margin-top: 16px;
            // border:1px solid
        }
        .bottom-detail{
            margin-top: 24px;
            .autor{
           
            .autor1{
                margin-right: 80px;
                &:last-child{
                    margin-right: 0;
                }
                img{
                    height: 16px;
                    width: auto;
                    margin-right: 4px;
                }
                .label{
                    font-size: 14px;
                   color: #6D6F75;
                   margin-right: 24px;
                }
                .value{
                    font-size: 14px;
                   color: #2B2C33;
                }
            }
        }
        .uplevel{
             width: 128px;
                height: 40px;
                background: #508CFF;
                border-radius: 12px;
                color: #fff;
                border:none;
                cursor: pointer;
           }
        }
      
       }
       .right{
         padding-left: 24px;
        .title{
            font-size: 18px;
           color: #2B2C33;
           font-weight: bold;
           margin-bottom: 16px;
        }
        ul{
            height: calc(100vh - 300px);
            overflow: auto;
        }
        .item{
            margin-bottom: 16px;
            &:last-child{
                margin-bottom: 0;
            }
             p{
                font-size: 16px;
               color: #2B2C33;
               
               margin-bottom: 10px;
             }
             .recommend-cover{
                width: 100%;
                height: 125px;
                img{
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
             }
        }
       }
    }
    
    </style>
<script setup>
import { ref, reactive, onMounted, nextTick, onBeforeUnmount, watch, computed, onActivated, onDeactivated } from "vue"
import { useRouter, useRoute } from "vue-router"
import dayjs from "dayjs"
import { showConfirmDialog, showSuccessToast } from "vant"
import {
  getTopStatisticsApi,
  getMyDiaryListApi,
  getDirectorDiaryListApi,
  getDirectorRecommendDiaryListApi,
  getTutorRecommendDiaryListApi,
  getMyFavoriteListApi
} from "@/api/mobile/personalSpace"
import { likeDiaryApi, favoriteDiaryApi } from "@/api/mobile/memberOperation"
import { useUserStore } from "@/store/modules/user"
import { deleteDiaryApi } from "@/api/mobile/diarySquare"
import DefaultAvatar from "@/assets/layouts/default-avatar.png"
import ActMileage from "./act-mileage/index.vue"

const userStore = useUserStore()

const router = useRouter()
const route = useRoute()

const weekMap = {
  "Monday": "周一",
  "Tuesday": "周二",
  "Wednesday": "周三",
  "Thursday": "周四",
  "Friday": "周五",
  "Saturday": "周六",
  "Sunday": "周日"
}

const activeTab = ref()
const tabs = ref([
  {
    name: 1,
    title: "行知里程"
  },
  {
    name: 0,
    title: "个人日记"
  },
  {
    name: 4,
    title: "我的收藏"
  }
])

// 添加计算属性来判断用户角色
const isDirector = computed(() => userStore.roles?.includes("DIRECTOR"))
const isMember = computed(() => userStore.roles?.includes("MEMBER"))
const isTutor = computed(() => userStore.roles?.includes("TUTOR"))
const isViceDirector = computed(() => userStore.roles?.includes("VICE_DIRECTOR"))
const isLeader = computed(() => userStore.roles?.includes("LEADER"))

const getTabList = () => {
  if (isDirector.value || isViceDirector.value || isTutor.value) {
    tabs.value = tabs.value.filter(item => item.name !== 1)
  }

  activeTab.value = tabs.value[0].name
}

const handleClickTab = (tab) => {

  activeTab.value = tab.name
  page.pageNum = 1
  finished.value = false
  getList()
  // 切换标签时重置滚动位置为0
  const listArea = listAreaRef.value
  if (listArea) {
    listArea.scrollTop = 0
  }
}

// 获取顶部统计
const topStatistics = ref({})
const getTopStatistics = async () => {
  try {
    const res = await getTopStatisticsApi()
    if (res) {
      topStatistics.value = res.data
    }
  } catch (err) {
    console.log("获取顶部统计失败", err)
  }
}

// 打开排序弹窗
const sortSheetVisible = ref(false)
const activeSortAction = ref("TIME")
const activeSortName = ref("时间排序")
const openSortSheet = () => {
  sortSheetVisible.value = true
}
const sortActions = ref([
  {
    id: "TIME",
    name: "时间排序"
  },
  {
    id: "VIEWS",
    name: "阅读数排序"
  },
  {
    id: "SCORE",
    name: "得星数排序"
  },
  {
    id: "EVALUATIONS",
    name: "评价数排序"
  },
  {
    id: "COMMENTS",
    name: "评论数排序"
  },
  {
    id: "LIKES",
    name: "点赞数排序"
  }
])
const selectSortAction = (item) => {
  activeSortAction.value = item.id
  activeSortName.value = item.name
  sortSheetVisible.value = false
  page.pageNum = 1
  finished.value = false
  getList()
}

// 打开筛选日期弹窗
const filterDateSheetVisible = ref(false)
const activeFilterDate = ref([])
const activeFilterDateForHtml = ref("")
const minDate = ref()
const maxDate = ref()
const openFilterDateSheet = () => {
  // 获取年月日
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1
  const day = now.getDate()
  maxDate.value = new Date(year, month, day)
  minDate.value = new Date(year, month - 6, day)
  filterDateSheetVisible.value = true
}

const handleFilterDateConfirm = (value) => {
  activeFilterDate.value = value
  activeFilterDateForHtml.value = `${dayjs(value?.[0]).format("YYYY-MM-DD")}至${dayjs(value?.[1]).format("YYYY-MM-DD")}`
  filterDateSheetVisible.value = false
  page.pageNum = 1
  finished.value = false
  getList()
}

const filterDateSheetRef = ref()
const resetFilterDate = () => {
  filterDateSheetRef.value?.reset()
  activeFilterDate.value = []
  activeFilterDateForHtml.value = ""
  filterDateSheetVisible.value = false
  page.pageNum = 1
  finished.value = false
  getList()
}

// 获取列表
const list = ref([])
const loading = ref(false)
const finished = ref(false)
const page = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})
const getList = async () => {
  loading.value = true

  try {
    let res

    const orderBy = activeSortAction.value.startsWith("TIME")
      ? activeSortAction.value.split("_")[0]
      : (activeSortAction.value === "TUTOR_READ" || activeSortAction.value === "TUTOR_UNREAD") ? "" : activeSortAction.value
    const orderRule = activeSortAction.value.startsWith("TIME") ? activeSortAction.value.split("_")[1] : ""

    const query = {
      pageNum: page.pageNum,
      pageSize: page.pageSize,
      orderBy,
      orderRule,
      beginRecordDate: activeFilterDate.value?.[0] ? dayjs(activeFilterDate.value?.[0]).format("YYYY-MM-DD") : "",
      endRecordDate: activeFilterDate.value?.[1] ? dayjs(activeFilterDate.value?.[1]).format("YYYY-MM-DD") : ""
    }

    loading.value = true

    if (activeTab.value === 0) {
      res = await getMyDiaryListApi(query)
    } else if (activeTab.value === 1) {
      res = await getDirectorDiaryListApi(query)
    } else if (activeTab.value === 2) {
      res = await getDirectorRecommendDiaryListApi(query)
    } else if (activeTab.value === 3) {
      res = await getTutorRecommendDiaryListApi(query)
    } else if (activeTab.value === 4) {
      res = await getMyFavoriteListApi(query)
    }
    if (res) {
      res.data.list.forEach(item => {
        item.score = item.score === -1 || item.score === null ? 0 : item.score
      })
      if (page.pageNum === 1) {
        list.value = res.data.list || []
      } else {
        list.value = [...list.value, ...res.data.list]
      }
      page.total = res.data.total
      loading.value = false

      if (list.value.length >= page.total) {
        finished.value = true
      } else {
        page.pageNum++
      }
    }
  } catch (err) {
    console.log("获取列表失败", err)
  }
}

const clickLike = async (row, isLike) => {
  try {
    if (!row.isLikeCapable) {
      return
    }
    const res = await likeDiaryApi(row.id)
    if (res) {
      const item = list.value.find((item) => item.id === row.id)
      item.isLike = !item.isLike
      item.likes = Number(item.likes) + (isLike ? 1 : -1)
      showSuccessToast(isLike ? "点赞成功" : "取消点赞成功")
    }
  } catch (err) {
    console.log(err)
  }
}

const clickFavorite = async (id, isFavorite) => {
  try {
    const res = await favoriteDiaryApi(id, {
      favorite: isFavorite
    })
    if (res) {
      list.value.find((item) => item.id === id).isFavorite = !list.value.find((item) => item.id === id).isFavorite
      list.value.find((item) => item.id === id).favorites =
        Number(list.value.find((item) => item.id === id).favorites) + (isFavorite ? 1 : -1)
      showSuccessToast(isFavorite ? "收藏成功" : "取消收藏成功")
    }
  } catch (err) {
    console.log(err)
  }
}

// 存储溢出状态的对象
const textOverflowMap = ref({})

// 在 data 部分添加新的 ref
const expandedItems = ref({}) // 用于跟踪每个项目的展开状态

// 添加切换展开/收起的方法
/* 注释掉展开/收起功能
const toggleExpand = (id, event) => {
  event.stopPropagation() // 阻止冒泡，避免触发跳转详情
  expandedItems.value[id] = !expandedItems.value[id]
}
*/

// 修改 isTextOverflow 方法
const isTextOverflow = (id) => {
  // 只保留基本检测逻辑，不需要展开/收起功能
  const contentElement = document.querySelector(`[data-id="${id}"] .content-text`)
  if (contentElement) {
    return contentElement.scrollHeight > contentElement.clientHeight
  }
  return false
  
  /* 注释掉原来复杂的展开/收起相关逻辑
  // 只有当内容未展开时才进行检查
  if (!expandedItems.value[id]) {
    const contentElement = document.querySelector(`[data-id="${id}"] .content-text`)
    if (contentElement) {
      const lineHeight = parseInt(window.getComputedStyle(contentElement).lineHeight)
      const height = contentElement.clientHeight
      const lines = height / lineHeight
      
      // 如果不足3行，不显示省略号
      if (lines < 3) {
        return false
      }
      
      // 如果超过3行，一定显示省略号
      if (lines > 3) {
        return true
      }
      
      // 如果刚好3行，检查最后一行是否占满除按钮外的宽度
      const clone = contentElement.cloneNode(true)
      clone.style.cssText = `
        position: absolute;
        visibility: hidden;
        height: auto;
        width: ${contentElement.clientWidth}px;
        -webkit-line-clamp: 2;
      `
      document.body.appendChild(clone)
      
      // 计算前两行的高度
      const twoLinesHeight = clone.clientHeight
      document.body.removeChild(clone)
      
      // 再创建一个克隆来获取完整内容的高度
      const fullClone = contentElement.cloneNode(true)
      fullClone.style.cssText = `
        position: absolute;
        visibility: hidden;
        height: auto;
        width: ${contentElement.clientWidth}px;
        -webkit-line-clamp: unset;
      `
      document.body.appendChild(fullClone)
      
      // 如果完整内容高度大于3行高度，说明第三行是满的
      const isThirdLineFull = fullClone.clientHeight > 3 * lineHeight
      document.body.removeChild(fullClone)
      
      return isThirdLineFull
    }
  }
  return false
  */
}

// 添加一个变量来存储滚动位置
const scrollPosition = ref(0)
const listAreaRef = ref(null)

// 修改 goToDetail 方法
const goToDetail = (item) => {
  // 检查 listAreaRef 是否为 null
  const listArea = listAreaRef.value
  if (listArea) {
    scrollPosition.value = listArea.scrollTop
  } else {
    console.error("listAreaRef is null.")
  }
  const query = {
    orderBy: activeSortAction.value,
    beginRecordDate: activeFilterDate.value?.[0] ? dayjs(activeFilterDate.value?.[0]).format("YYYY-MM-DD") : "",
    endRecordDate: activeFilterDate.value?.[1] ? dayjs(activeFilterDate.value?.[1]).format("YYYY-MM-DD") : ""
  }
  sessionStorage.setItem("m-diaryLocation", JSON.stringify(query))
  sessionStorage.setItem("m-diaryId", JSON.stringify(item.id))

  router.push({
    path: "/personalSpace/detail",
    query: {
      id: item.id,
      from: "home",
      activeTab: activeTab.value
    }
  })
}

// 检测文本是否溢出并更新状态
const checkTextOverflow = () => {
  nextTick(() => {
    const items = document.querySelectorAll(".list-item")
    items.forEach((item) => {
      const id = item.getAttribute("data-id")
      const contentText = item.querySelector(".content-text")
      if (contentText) {
        // 检查元素是否溢出
        const isOverflow = contentText.scrollHeight > contentText.clientHeight
        textOverflowMap.value[id] = isOverflow
      }
    })
  })
}

watch(
  () => list.value,
  () => {
    nextTick(() => {
      checkTextOverflow()
    })
  },
  { deep: true }
)

// 删除
const handleDelete = (id) => {
  showConfirmDialog({
    title: "删除",
    message: "确定删除该日记吗？",
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    confirmButtonColor: "#508CFF",
    cancelButtonColor: "#C7CFDF",
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      try {
        const res = await deleteDiaryApi(id)
        if (res) {
          showSuccessToast("删除成功")
          list.value = list.value.filter((item) => item.id !== id)
        }
      } catch (err) {
        console.log(err)
      }
    })
    .catch(() => {})
}

// 隐藏
const handleHidden = (id, isNormal) => {
  showConfirmDialog({
    title: "隐藏",
    message: "确定隐藏该日记吗？",
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    confirmButtonColor: "#508CFF",
    cancelButtonColor: "#C7CFDF",
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      try {
        const res = await hideDiaryApi(id, {
          isSetNormal: isNormal
        })
        if (res) {
          showSuccessToast("隐藏成功")
          list.value = list.value.filter((item) => item.id !== id)
        }
      } catch (err) {
        console.log(err)
      }
    })
    .catch(() => {})
}

const showOverlay = ref(false)
const showPopover = ref(false)
const actions = ref([
  {
    text: "发布通知",
    color: "#2b2c33",
    type: "publish"
  },
  {
    text: "上传日记",
    color: "#2b2c33",
    type: "upload"
  }
])
const getActions = () => {
  if (isMember.value) {
    actions.value = actions.value.filter((item) => item.text !== "发布通知")
  }
}
const selectPopAction = (action) => {
  if (action.type === "publish") {
    router.push("/personalSpace/publish")
  } else if (action.type === "upload") {
    router.push("/personalSpace/add")
  }
}
const openPopover = () => {
  showOverlay.value = true
  showPopover.value = true
}
const closeOverlay = () => {
  showOverlay.value = false
  showPopover.value = false
}

onMounted(() => {
  getTopStatistics()
  getActions()
  getTabList()
  getList()
  // 在列表加载后检查文本溢出
  nextTick(() => {
    checkTextOverflow()
    window.addEventListener("resize", checkTextOverflow)
    // 确保 listAreaRef 正确引用
    if (!listAreaRef.value) {
      console.error("listAreaRef is not set correctly.")
    }
  })
})

onActivated(() => {
  console.log('onActivated', scrollPosition.value)
  closeOverlay()
  getTopStatistics()
  // 从详情页或上传页返回时，重新获取列表
  const fromPath = route.query.from
  if (fromPath === 'add') {
    // 如果是从上传页返回，重置页码并重新获取列表
    page.pageNum = 1
    finished.value = false
    list.value = []
    getList()
  } else if (list.value.length > 0) {
    // 否则只更新现有列表的状态
    refreshListStatus()
    
    // 恢复滚动位置
    nextTick(() => {
      const listArea = listAreaRef.value
      if (listArea) {
        // 优先使用 ref 中保存的位置，如果没有则尝试从 sessionStorage 获取
        const savedPosition = scrollPosition.value || Number(sessionStorage.getItem('m-personalSpaceScrollPosition'))
        if (savedPosition && savedPosition > 0) {
          console.log('恢复滚动位置', savedPosition)
          listArea.scrollTop = savedPosition
        }
      }
    })
  }
})

// 添加 deactivated 钩子函数
onDeactivated(() => {
  // 页面离开前保存滚动位置
})

// 修改 refreshListStatus 方法
const refreshListStatus = async () => {
  try {
    // 保存当前页码和完成状态
    const currentPageNum = page.pageNum
    const currentFinished = finished.value
    
    // 重置页码并获取第一页数据
    page.pageNum = 1
    
    let res
    const orderBy = activeSortAction.value.startsWith("TIME")
      ? activeSortAction.value.split("_")[0]
      : (activeSortAction.value === "TUTOR_READ" || activeSortAction.value === "TUTOR_UNREAD") ? "" : activeSortAction.value
    const orderRule = activeSortAction.value.startsWith("TIME") ? activeSortAction.value.split("_")[1] : ""
    
    const query = {
      pageNum: 1,
      pageSize: currentPageNum * page.pageSize,
      orderBy,
      orderRule,
      beginRecordDate: activeFilterDate.value?.[0] ? dayjs(activeFilterDate.value?.[0]).format("YYYY-MM-DD") : "",
      endRecordDate: activeFilterDate.value?.[1] ? dayjs(activeFilterDate.value?.[1]).format("YYYY-MM-DD") : ""
    }

    if (activeTab.value === 0) {
      res = await getMyDiaryListApi(query)
    } else if (activeTab.value === 1) {
      res = await getDirectorDiaryListApi(query)
    } else if (activeTab.value === 2) {
      res = await getDirectorRecommendDiaryListApi(query)
    } else if (activeTab.value === 3) {
      res = await getTutorRecommendDiaryListApi(query)
    } else if (activeTab.value === 4) {
      res = await getMyFavoriteListApi(query)
    }

    if (res && res.data && res.data.list) {
      // 创建一个新的数组来存储更新后的列表
      const updatedList = list.value.map(item => {
        const newItem = res.data.list.find(newData => newData.id === item.id)
        if (newItem) {
          // 使用解构赋值创建一个新对象
          return {
            ...item,
            ...newItem,
            score: newItem.score === -1 || newItem.score === null ? 0 : newItem.score
          }
        }
        return item
      })

      // 使用新数组替换原数组，触发响应式更新
      list.value = updatedList

      // 恢复页码和完成状态
      page.pageNum = currentPageNum
      finished.value = currentFinished

      // 强制检查文本溢出状态并恢复滚动位置
      nextTick(() => {
        checkTextOverflow()
        if (scrollPosition.value > 0) {
          window.scrollTo(0, scrollPosition.value)
        }
      })
    }
  } catch (err) {
    console.log("刷新列表状态失败", err)
  }
}

const goEdit = (id) => {
  router.push({
    path: "/personalSpace/add",
    query: {
      id
    }
  })
}

const goToUserRecord = (item) => {
  // 保存当前滚动位置
  const listArea = listAreaRef.value
  if (listArea) {
    scrollPosition.value = listArea.scrollTop
    console.log('保存滚动位置', scrollPosition.value)
    // 同时保存到 sessionStorage，以防页面刷新
    sessionStorage.setItem('m-personalSpaceScrollPosition', scrollPosition.value.toString())
  }
  
  router.push({
    path: "/userRecord",
    query: {
      userId: item.userId,
      userName: item.userName
    }
  })
}
onBeforeUnmount(() => {
  window.removeEventListener("resize", checkTextOverflow)
})

// 添加一个解码HTML实体的函数
const decodeHtmlEntities = (text) => {
  if (!text) return ''
  const textArea = document.createElement('textarea')
  textArea.innerHTML = text
  return textArea.value
}
</script>

<template>
  <div class="personal-space">
    <header class="header-area">
      <van-tabs v-model:active="activeTab" @click-tab="handleClickTab" swipe-threshold="3">
        <van-tab v-for="tab in tabs" :key="tab.name" :name="tab.name" :title="tab.title"></van-tab>
      </van-tabs>
    </header>

    <act-mileage v-if="activeTab == 1"></act-mileage>

    <section v-if="activeTab == 0 || activeTab == 4" class="statistic-area">
      <div class="statistic-item flex-between-center">
        <span class="m-text-12-18-400 grey3">日记数</span>
        <span class="m-text-20-28-600 blue1">{{ topStatistics?.totalRecords || 0 }}</span>
      </div>
      <div class="statistic-item flex-between-center star" v-if="!isDirector">
        <span class="m-text-12-18-400 grey3">得星数</span>
        <span class="m-text-20-28-600 orange1">{{ topStatistics?.totalScore || 0 }}</span>
      </div>
      <div class="statistic-item flex-between-center" v-if="!isDirector">
        <span class="m-text-12-18-400 grey3">评价数</span>
        <span class="m-text-20-28-600 blue1">{{ topStatistics?.totalEvaluations || 0 }}</span>
      </div>
      <div class="statistic-item flex-between-center" v-if="!isDirector">
        <span class="m-text-12-18-400 grey3">评论数</span>
        <span class="m-text-20-28-600 blue1">{{ topStatistics?.totalComments || 0 }}</span>
      </div>
      <div class="statistic-item flex-between-center">
        <span class="m-text-12-18-400 grey3">点赞数</span>
        <span class="m-text-20-28-600 red1">{{ topStatistics?.totalLikes || 0 }}</span>
      </div>
      <div class="statistic-item flex-between-center read">
        <span class="m-text-12-18-400 grey3">阅读数</span>
        <span class="m-text-20-28-600 blue1">{{ topStatistics?.totalViews || 0 }}</span>
      </div>
    </section>
    <div v-if="activeTab == 0 || activeTab == 4" class="flex-between-center sort-area">
      <div class="sort-item flex-start-center" @click="openSortSheet">
        <span class="m-text-14-22-400 grey1 pr5">{{ activeSortName || "选择排序方式" }}</span>
        <img src="@/assets/mobile/personal-space/arrow-down.png" alt="" width="8" height="6" />
      </div>
      <div class="flex-start-center">
        <div class="sort-item flex-start-center" @click="openFilterDateSheet">
          <span class="m-text-14-22-400 grey1 pr5">{{ activeFilterDateForHtml || "筛选日期" }}</span>
          <img src="@/assets/mobile/personal-space/arrow-down.png" alt="" width="8" height="6" />
        </div>
        <img class="m-ml12" src="@/assets/mobile/personal-space/reset.png" alt="" width="18" height="16"
          @click="resetFilterDate" />
      </div>
    </div>
    <section v-if="activeTab == 0 || activeTab == 4" class="list-area" ref="listAreaRef">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="getList"
        :immediate-check="false">
        <div class="list-item flex-start-start m-mb12 pointer" v-for="item in list" :key="item.id" :data-id="item.id"
          @click="goToDetail(item)">
          <div class="left-box" @click.stop="goToUserRecord(item)">
            <img :src="item.userAvatarUrl || DefaultAvatar" alt="" width="32" height="32" style="border-radius: 50%;" />
          </div>
          <div class="right-box flex-1 m-pl5">
            <div class="right-top flex-between-center">
              <span class="flex-start-center">
                <span @click.stop="goToUserRecord(item)" class="m-text-14-20-600 grey1 m-pr8">{{ item.userName }}</span>
                <span class="m-text-12-18-400 grey3">{{ item.recordDate }}</span>
                <span v-if="isDirector && item.recordDate" class="m-text-12-18-400 grey3">（{{
                  weekMap[dayjs(item.recordDate).format('dddd')] }}）</span>
              </span>
              <van-rate v-if="item.userClubRole === 'LEADER' || item.userClubRole === 'MEMBER'" v-model="item.score"
                :size="14" color="#FFB160" void-icon="star" void-color="#C7CFDF" :count="6" readonly />
            </div>
            <div class="m-text-14-20-600 m-pt2 m-pb2"><span class="m-text-14-20-600" v-if="item.title">标题：</span>{{
              item.title}}</div>
            <div class="right-middle">
              <div class="content-wrapper">
                <p class="m-text-14-20-400 grey1 content-text" v-html="decodeHtmlEntities(item.content)"
                  :class="{ 'expanded': expandedItems[item.id] }">
                </p>
              </div>
            </div>
            <div class="right-bottom flex-between-start">
              <div class="flex-start-center flex-wrap">
                <div v-for="label in item.topics" :key="label.id"
                  class="label m-text-12-18-400 grey1 m-mr8 m-mb4 flex-start-center">
                  <img src="@/assets/mobile/personal-space/label.png" alt="" width="12" height="12" />
                  <span class="m-pl4">{{ label.name }}</span>
                </div>
              </div>
              <div class="flex-end-center">
                <van-button v-if="userStore?.userInfo?.user?.id === item.userId" class="inner-btn delete-btn"
                  @click.stop="handleDelete(item.id)">
                  <template #icon>
                    <img src="@/assets/mobile/personal-space/delete.png" alt="" width="11" height="11" />
                  </template>
                  <span class="m-text-12-18-400 red1">删除</span>
                </van-button>
                <van-button v-if="userStore?.userInfo?.user?.id === item.userId" class="inner-btn delete-btn m-ml6"
                  @click.stop="goEdit(item.id)">
                  <template #icon>
                    <img src="@/assets/mobile/personal-space/edit.png" alt="" width="11" height="11" />
                  </template>
                  <span class="m-text-12-18-400 blue1">编辑</span>
                </van-button>
              </div>
            </div>
            <span class="m-text-12-18-400 grey3 flex-end-center" v-if="item.lastUpdateTime">最后修改时间:
              <span class="m-text-12-18-400 grey3">{{ item.lastUpdateTime }}</span>
              <span class="m-text-12-18-400 grey3">（{{ weekMap[dayjs(item.lastUpdateTime).format('dddd')] }}）</span>
            </span>
            <div class="bottom flex-start-center m-pt8">
              <div class="count-item view-count flex-start-center m-mr24">
                <img v-if="item.isViewed" class="m-mr6" src="@/assets/mobile/personal-space/view-active.png" alt=""
                  width="14" height="10" />
                <img v-else class="m-mr6" src="@/assets/mobile/personal-space/view.png" alt="" width="14" height="10" />
                <span class="m-text-12-18-400 grey3">{{ item.views }}</span>
              </div>
              <div class="count-item like-count flex-start-center m-mr24"
                @click.stop="clickFavorite(item.id, !item?.isFavorite)">
                <img v-if="!item.isFavorite" class="m-mr6" src="@/assets/mobile/personal-space/like.png" alt=""
                  width="14" height="13" />
                <img v-else class="m-mr6" src="@/assets/mobile/personal-space/like-active.png" alt="" width="14"
                  height="13" />
                <span class="m-text-12-18-400 grey3">{{ item.favorites }}</span>
              </div>
              <div class="count-item comment-count flex-start-center m-mr24" v-if="item.userClubRole !== 'DIRECTOR'">
                <img v-if="item.isCommented" class="m-mr6" src="@/assets/mobile/personal-space/comment-active.png"
                  alt="" width="14" height="14" />
                <img v-else class="m-mr6" src="@/assets/mobile/personal-space/comment.png" alt="" width="14"
                  height="14" />
                <span class="m-text-12-18-400 grey3">{{ item.comments }}</span>
              </div>
              <div class="count-item good-count flex-start-center" @click.stop="clickLike(item, !item?.isLike)">
                <img v-if="!item.isLike" class="m-mr6" src="@/assets/mobile/personal-space/good.png" alt="" width="14"
                  height="14" />
                <img v-else class="m-mr6" src="@/assets/mobile/personal-space/good-active.png" alt="" width="14"
                  height="14" />
                <span class="m-text-12-18-400 grey3">{{ item.likes }}</span>
              </div>
            </div>
          </div>
        </div>
      </van-list>
    </section>

    <van-popover v-if="activeTab == 0 || activeTab == 4" v-model:show="showPopover" :actions="actions" @select="selectPopAction" placement="top-end"
      teleport="body" @open="openPopover">
      <template #reference>
        <img v-if="!showPopover" class="add-btn" src="@/assets/mobile/personal-space/add.png" alt="" width="48"
          height="48" />
        <img v-if="showPopover" class="add-btn" src="@/assets/mobile/personal-space/close.png" alt="" width="48"
          height="48" />
      </template>
      <template #action="{ action }">
        <span class="flex-center-center">
          <img v-if="action.type === 'publish'" src="@/assets/mobile/personal-space/publish.png" alt="" width="14"
            height="14" />
          <img v-if="action.type === 'upload'" src="@/assets/mobile/personal-space/upload.png" alt="" width="14"
            height="14" />
          <span class="m-text-14-20-400 grey1 m-pl8">{{ action.text }}</span>
        </span>
      </template>
    </van-popover>

    <van-overlay v-model:show="showOverlay" z-index="990" @click="closeOverlay" />

    <van-action-sheet v-model:show="sortSheetVisible" cancel-text="取消" close-on-click-action :actions="sortActions"
      @select="selectSortAction" safe-area-inset-bottom />

    <van-calendar ref="filterDateSheetRef" v-model:show="filterDateSheetVisible" type="range"
      @confirm="handleFilterDateConfirm" safe-area-inset-bottom :min-date="minDate" :max-date="maxDate"
      allow-same-day />
  </div>
</template>

<style lang="scss" scoped>
@use "@/views/mobile/styles/custom-global-mobile-style.scss";
.personal-space {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .header-area {
    width: 100%;
    height: 57px;
    background: linear-gradient(270deg, #508cff 0%, #535dff 100%), #508cff;
    padding: 12px 0 14px;

    :deep(.van-tabs) {
      .van-tabs__nav {
        background: transparent;
      }

      .van-tab {
        color: #e2e3e6;
        font-size: 14px;

        &--active {
          color: #ffffff !important;
          font-weight: 600 !important;
        }
      }

      .van-tabs__wrap {
        height: 30px;
      }

      .van-tabs__line {
        background: #fff;
        border-radius: 3px;
      }
    }
  }

  .statistic-area {
    display: flex;
    flex-wrap: wrap;
    gap: 12px 10px;
    width: 100%;
    padding: 10px 15px 15px;

    .statistic-item {
      padding: 0 6px;
      width: calc(33.33% - 6.66px);
      height: 36px;
      line-height: 36px;
      background-color: #fff;
      border-radius: 4px;
    }
  }

  .sort-area {
    padding: 0 15px 12px;
  }

  .list-area {
    position: relative;
    flex: 1;
    overflow-y: auto;

    .list-item {
      width: 100%;
      background-color: #fff;
      padding: 15px;

      .right-middle {
        position: relative;

        .content-wrapper {
          display: inline;
        }
        .content-text {
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-all;
          margin: 0;
          line-height: 20px !important;
          width: 100%;
          box-sizing: border-box;

          /* 注释掉展开功能，始终保持3行显示 */
          /*
          &.expanded {
            display: block;
            -webkit-line-clamp: unset;
          }
          */

          :deep(img) {
            max-width: 100% !important;
            height: auto !important;
            object-fit: contain;
          }
        }
      }

      .right-bottom {
        padding-top: 8px;

        .label {
          padding: 0 7px;
          border-radius: 19px;
          border: 1px solid #e2e3e6;
          height: 24px;
          line-height: 24px;
        }

        .clocking-time {
          padding: 0 11px;
          border-radius: 19px;
          background-color: #fff5e7;
          height: 24px;
          line-height: 24px;
        }
      }
    }
  }

  .inner-btn {
    width: 56px;
    height: 24px;
    line-height: 24px;
    border-radius: 4px;
    border: 1px solid #e2e3e6;
    padding: 0 4px;

    &.delete-btn {
      /* margin-right: 6px; */
    }

    &.has-hidden-btn {
      width: 68px;
    }
  }

  :deep(.van-popover__wrapper) {
    position: fixed;
    right: 20px;
    bottom: calc(60px + env(safe-area-inset-bottom));
    width: 50px;
    z-index: 999;
  }

  ::-webkit-scrollbar {
    display: none;
  }
}
</style>

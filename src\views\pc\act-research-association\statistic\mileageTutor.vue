<template>
    <div class="flex_between_0 flex_wrap">
        <div class="echart-box flex_start_0 flex_column">
            <p class="title flex_start">社员里程排名</p>
            <ul class="flex_between split">
                <li :class="mileageActive==1?'active':''" @click="choiceMileage(1)">本周</li>
                <li :class="mileageActive==2?'active':''" @click="choiceMileage(2)">每月</li>
                <li :class="mileageActive==3?'active':''" @click="choiceMileage(3)">历史</li>
            </ul>
            <div class="rank-list">
                <div class="flex_start rank" v-for="(item,index) in mileageList" :key="item.userId">
                    <img src="@/assets/pc/score/first.png" alt="" v-if="index==0">
                    <img src="@/assets/pc/score/first.png" alt="" v-if="index==1">
                    <img src="@/assets/pc/score/first.png" alt="" v-if="index==2">
                    <div class="rank-number" v-if="index>2">
                        <h3>{{index+1}}</h3>
                    </div>
                    <div class="flex_1">
                        <div class="flex_between">
                            <div class="flex_start">
                                <p class="name">{{item.userName}}</p>
                                <img src="@/assets/pc/score/good.png" alt="" v-show="item.isAwesomeAuthor" />
                                 <img src="@/assets/pc/score/star.png" alt="" v-show="item.isActiveStar" />
                            </div>
                            <p class="steps">{{item.points}}</p>
                        </div>
                        <el-progress :percentage="item.percent" class="progress"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="echart-box flex_start_0 flex_column">
            <p class="title flex_start">小组统计</p>
            <ul class="flex_between split">
                <li :class="groupActive==1?'active':''" @click="choiceGroup(1)">本周</li>
                <li :class="groupActive==2?'active':''" @click="choiceGroup(2)">每月</li>
                <li :class="groupActive==3?'active':''" @click="choiceGroup(3)">历史</li>
            </ul>
            <div class="flex_1" id="member-group" style="width:100%">
                
            </div>
        </div>
        
        <div class="echart-box flex_start_0 flex_column" style="width:100%">
            <p class="title flex_start">近三十天总里程趋势</p>
            <div class="flex_1" id="thirdty-day" style="width:100%">
                
            </div>
        </div>
    </div>
</template>
<script setup>
import * as echarts from 'echarts';
import {
TutorMileageWeekRank,TutorMileageMonthRank,TutorMileageHistoryRank,
TutorMileageDistributionWeek,TutorMileageDistributionMonth,TutorMileageDistributionHistory,
TutorMileageThirdtyPoints
} from '@/api/pc/mileage.js'
import {onMounted, ref} from 'vue'
let mileageList=ref([])
let mileageActive=ref('1')
let choiceMileage=async(arg)=>{
    let res=''
    mileageActive.value=arg
    if(mileageActive.value==1){
      res=await TutorMileageWeekRank()
    }
    if(mileageActive.value==2){
      res=await TutorMileageMonthRank()
    }
    if(mileageActive.value==3){
      res=await TutorMileageHistoryRank()
    }
    if(res){
        let arrNum=res.data.map(item=>item.points)
        let max=Math.max(...arrNum)
        res.data.forEach(item=>{
            if(max==0){
                item.percent=0
            }else{
                item.percent=(item.points/max)*100
            }
        })
        mileageList.value=res.data
    }
}

let echartGroup=''
let groupActive=ref('1')
let choiceGroup=async(arg)=>{
      let res=''
      groupActive.value=arg
      if(arg==1){
        res=await TutorMileageDistributionWeek()
      }
      if(arg==2){
        res=await TutorMileageDistributionMonth()
      }
      if(arg==3){
        res=await TutorMileageDistributionHistory()
      }
      if(res){
         initEchartGroup(res.data)
      }
}
let initEchartGroup=(row)=>{
    echartGroup = echarts.init(document.getElementById('member-group'));

   let x=row.map(item=>item.label)
    let y=row.map(item=>item.points)
 
let option = {
     
    backgroundColor: '#fff',
    grid: {
        top: "10%",
        bottom: "10%"//也可设置left和right设置距离来控制图表的大小
    },
    tooltip: {
        trigger: "axis",
        axisPointer: {
            type: "shadow",
            label: {
                show: true
            }
        }
    },
   
    xAxis: {
        data: x,
        axisLine: {
            show: true, //隐藏X轴轴线
            
            lineStyle: {
                        color: '#8DA2B5'
                        }
        },
        axisTick: {
            show: true,//隐藏X轴刻度
            type:'dashed',
          
        },
        axisLabel: {
            show: true,
            textStyle: {
                color: "#8DA2B5" //X轴文字颜色
            }
        },
           axisLabel: {
                show: true, // 是否显示刻度标签，默认显示
            interval: 0, // 坐标轴刻度标签的显示间隔，在类目轴中有效；默认会采用标签不重叠的策略间隔显示标签；可以设置成0强制显示所有标签；如果设置为1，表示『隔一个标签显示一个标签』，如果值为2，表示隔两个标签显示一个标签，以此类推。
            //   rotate: -60, // 刻度标签旋转的角度，在类目轴的类目标签显示不下的时候可以通过旋转防止标签之间重叠；旋转的角度从-90度到90度
            inside: false, // 刻度标签是否朝内，默认朝外
            margin: 6, // 刻度标签与轴线之间的距离
            formatter: (params)=>{
                console.log()
                return params.slice(0,4)+'\n'+params.slice(4)
            }
           
       },
    },
    yAxis: [{
            type: "value",
            name: "",
            nameTextStyle: {
                color: "red"
            },
            splitLine: {
                show: true,
                
                 lineStyle: {
                   color: '#E8E9F0',
                    type:'dashed',
                 }
            },
            axisTick: {
                show: false
            },
            axisLine: {
                show: true,
                lineStyle: {
                   color: '#FFFFFF'
                }
            },
            axisLabel: {
                show: true,

                textStyle: {
                    color: "#8DA2B5"
                }
            },
             
        },
        
 
    ],
    series: [
        {
            name: "里程统计",
            type: "bar",
            barWidth: 15,
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: "#88C0FF"
                        },
                        {
                            offset: 1,
                            color: "#508CFF"
                        }
                    ])
                }
            },
            data: y
        }
    ]
};
  echartGroup.setOption(option)
}
 let echartThirty=''
 let choiceThirty=async(row)=>{
        let res=await TutorMileageThirdtyPoints()
        if(res){
           initEchartThirty(res.data) 
        }
 }
 let initEchartThirty=(row)=>{
    let x=row.map(item=>item.date)
    let y=row.map(item=>item.points)
    echartThirty = echarts.init(document.getElementById('thirdty-day'));
    let option = {
    title: {
        text: '',
        textStyle: {
            fontWeight: 'normal',
            fontSize: 16,
            color: '#F1F1F3'
        },
        left: '6%'
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            lineStyle: {
                color: '#57617B'
            }
        }
    },
  
    grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
    },
    xAxis: [{
        type: 'category',
        boundaryGap: false,
        axisLine: {
            lineStyle: {
                color: '#8DA2B5'
            }
        },
    
        data: x
    }],
    yAxis: [{
        type: 'value',
        name: '',
        axisTick: {
            show: false
        },
        axisLine: {
            lineStyle: {
                color: '#8DA2B5'
            }
        },
        axisLabel: {
            margin: 10,
            textStyle: {
                fontSize: 14
            }
        },
        splitLine: {
            lineStyle: {
                color: '#E8E9F0',
                type:'dashed'

            }
        }
    }],
    series: [{
        name: '里程',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 5,
        showSymbol: false,
        lineStyle: {
            normal: {
                width: 1
            }
        },
        areaStyle: {
            normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: 'rgb(178,238,255)'
                }, {
                    offset: 0.8,
                    color: 'rgb(201,221,255)'
                }], false),
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 10
            }
        },
        itemStyle: {
            normal: {
                color: 'rgb(178,238,255)',
                borderColor: 'rgb(178,238,255)',
                borderWidth: 12

            }
        },
        data: y
    }]
};
    echartThirty.setOption(option)
 }
onMounted(()=>{
  choiceMileage(mileageActive.value)
  choiceGroup(groupActive.value)
  choiceThirty()

})
</script>
<style lang='scss' scoped>
.echart-box{
  width: 49.5%;
  min-height: 398px;
  background: #fff;
  margin-bottom: 10px;
  padding: 16px;
  border-radius: 4px;
  .title{
    font-weight: bold;
    font-size: 16px;
    color: #2B2C33;
    line-height: 24px;
    &::before{
       content: '';
       display: block;
       width: 2px;
        height: 14px;
        background: #508CFF;
        border-radius: 1px;
        margin-right: 6px;
    }
  }
  .split{
    height: 32px;
    width: 204px;
    line-height: 32px;
    background: #F0F5FF;
    border-radius: 4px;
    border: 1px solid #508CFF;
    margin-top: 12px;
    li{
        flex:1;
        text-align: center;
        font-size: 14px;
        color: #508CFF;
        cursor: pointer;
    }
    .active{
       background: #508CFF;
        color: #fff;
    }
  }
  .rank-list{
     height:300px;
     overflow:auto;
     margin-top: 20px;
     padding-right: 10px;
      .rank{
        margin-bottom: 20px;
        .rank-number{
            width: 28px;
            font-weight: bold;
             margin-right: 10px;
            font-size: 16px;
            color: #2B2C33;
            line-height: 19px;
            font-weight: bold;
            h3{
                text-align: center;
            }
        }
        &:last-child{
            margin-bottom: 0;
        }
      img{
        width: 28px;
        height: auto;
        margin-right: 10px;
      }
      :deep(.el-progress__text){
        display: none;
      }
      :deep(.el-progress-bar__inner){
       background: linear-gradient( 270deg, #508CFF 0%, #55C2F5 100%);
      }
      .name{
        font-size: 16px;
        color: #2B2C33;
        line-height: 22px;
        font-weight: bold;
        margin-right: 8px;
      }
      .steps{
        font-weight: bold;
        font-size: 16px;
        color: #2B2C33;
      }
      .progress{
        margin-top: 6px;
      }
  }
  }
 
}
</style>
<template>
    <div class="content-box  preview mt10 flex_start_0" style="width:100%;height:100%">
        <div class="left flex_column flex_start_0">
                <div class="flex_between">
                    <div class="flex_start title" @click="goCancel">
                        <el-icon style="margin-right: 4px;"><ArrowLeft /></el-icon>
                       {{row.name}}
                    </div>
                    <div class="flex_start" v-if="currentInfo.type=='teacher'">
                        <div class="flex_start liked" @click="changeFavorite">
                             <img src="@/assets/pc/r-comment.png" alt="" v-show="!row.isFavourite" />
                              <img src="@/assets/pc/r-liked.png" alt="" v-show="row.isFavourite" />
                            <p>{{row.favourites?row.favourites:0}}</p>
                        </div>
                        <div class="flex_start liked star" @click="changeLike">
                             <img src="@/assets/pc/star.png" alt="" v-show="!row.isLike" />
                            <img src="@/assets/pc/r-star.png" alt="" v-show="row.isLike" />
                            <p>{{row.likes}}</p>
                        </div>
                    </div>
                </div>
                <div class="flex_between" style="margin-top:8px">
                    <div class="flex_start detail">
                        <p>上传时间：{{row.createTime}}</p>
                        <p>播放量：{{row.views}}</p>
                        <p>分享次数：{{row.shares}}</p>
                    </div>
                    <div class="flex_start action" v-show="$route.query.type!='preview'&&currentInfo.type=='teacher'">
                              <div class="flex_start edit" @click="goEdit" v-show="row.isUpdateCapable">
                                    <img src="@/assets/pc/action-edit.png" alt="">
                                    <p>编辑</p>
                              </div>
                              <div class="flex_start del" @click="goDel" v-show="row.isDeleteCapable">
                                    <img src="@/assets/pc/action-del.png" alt="">
                                    <p>删除</p>
                              </div>

                              <div class="flex_start copy" @click="goCopy">
                                    <img src="@/assets/pc/action-copy.png" alt=""  />
                                    <p>复制链接</p>
                              </div>
                              
                    </div>
                      <div class="flex_start action" v-show="$route.query.type=='preview'&&currentInfo.type=='teacher'">
                              <div class="flex_start copy" @click="goCopy">
                                    <img src="@/assets/pc/action-copy.png" alt=""  />
                                    <p>复制链接</p>
                              </div>
                              
                    </div>
                </div>
                
                <div class="file flex_1">
                    <Preview :row='row' />
                </div>
                <div class="flex_between bottom-detail">
                   <div class="flex_start autor">
                      <div class="flex_start autor1">
                          <div class="flex_start">
                            <img src="@/assets/pc/autor1.png" alt="">
                            <p class="label">资源作者</p>
                          </div>
                           <p class="value">{{row.createUserName}}</p>
                      </div>
                       <div class="flex_start autor1">
                          <div class="flex_start">
                            <img src="@/assets/pc/autor2.png" alt="">
                            <p class="label">所属单位</p>
                          </div>
                          <p class="value">{{row.tenantName}}</p>
                      </div>
                  </div>
                  <button class="uplevel" v-show="(row.upgrade==2||row.upgrade==3)&&currentInfo.type=='teacher'" @click="row.upgrade==3&&goUpLevel()">{{row.upgrade==2?'审核中':'我要升级'}}</button>
               
                </div>
               
        </div>
        <div class="right flex_1">
            <div class="title">相关推荐</div>
            <el-empty description="暂无相关推荐" v-show="recommendList.length==0"></el-empty>
            <ul  v-show="recommendList.length!=0">
                <li class="item" v-for="item in recommendList" :key="item.id" @click="goRecommend(item)">
                    <p>{{item.name}}</p>
                    <div class="recommend-cover">
                         <img :src="item.coverFileUrl" alt="">
                    </div>
                </li>
            </ul>
        </div>
       
        <el-dialog
            v-model="dialogVisible"
            title="升级资源"
            width="456"
            :before-close="cancelUpLevel"
        >
            <el-form label-position="top"  ref="levelRef"
            style="max-width: 600px"
            :model="levelForm"
            :rules="levelRules">
                <el-form-item label='升级范围'  prop='level'>
                        <el-select v-model="levelForm.level" placeholder='请选择升级范围' @change="changeLevel">
                            <el-option v-for="item in tenantTypes" :key="item.id" :value='item.id' :label='item.title'></el-option>
                        </el-select>
                </el-form-item>
                    </el-form>
                <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelUpLevel">取消</el-button>
                    <el-button type="primary" @click="confirmUpLevel" :loading='loading'>
                    确认
                    </el-button>
                </div>
                </template>
            </el-dialog>  
    </div >
</template>
<script setup>
import {onMounted, reactive, ref} from 'vue'
import {resourcePortalDetail,resourcePortalDetailViewCount,resourceRescommend,resourceFavourite,resourceLike,resourceDel,resourceShare,userTenant,userTenantToParent,resourceUpLevel} from '@/api/pc/index.js'
import Preview from '@/views/pc/component/resource/preview.vue'
import {bkResourceShare,bkResourceDetail,bkRecommendResource,bkResourceFavourite,bkResourceLike}  from '@/api/pc/bk.js'
import {useRoute,useRouter} from 'vue-router'
import {ElMessageBox,ElMessage} from 'element-plus'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'
let userInfo=useUserInfoStoreHook()
let {currentInfo}=storeToRefs(userInfo)
let route=useRoute()
let router=useRouter()
let resourceId=''
let dialogVisible=ref(false)
let row=ref({})
let loading=ref(false)
let recommendList=ref([])
let goCancel=()=>{
    router.go(-1)
}
let levelForm=reactive({
    toTenantId:'',
    level:''
})
let levelRules={
    level:[{required: true, message: '请选择升级范围',trigger: 'change',},],
    toTenantId:[{required: true, message: '请选择所属单位',trigger: 'change',},]
}
let levelRef=ref()
let tenantOptions=ref([])
let tenantTypes=ref([])
let tenantMap={
      30:'区(县、县级市)',
      40:'街道/镇',  
      50:'学校',  
      60:'集团校'

}
let changeLevel=(val)=>{
    if(val==50){
       Object.assign(levelForm,{
           toTenantId:row.value.tenantId
        })
    }else{
        getUserTenant()
    }
}
// let goShare=async()=>{
//      goCopy()
    
// }
let goJYFX=()=>{
  
    window.open(import.meta.env.VITE_REDIRECT_URL+'/jyfx/#/login?resourceId='+resourceId)
}
let goCopy=async()=>{
    let url=import.meta.env.VITE_REDIRECT_URL+'/dxs/#/resource/teach/preview?id='+resourceId
       
            try {
                // 创建一个 ClipboardItem 对象，包含 HTML 和纯文本内容
                await navigator.clipboard.writeText(url).then(
                    async() => {
                         let res=''
                        if(route.path=='/bk/res/preview'){
                          res=await bkResourceShare({
                            resourceId
                         })
                        }else{
                            res=await resourceShare({
                            resourceId
                         })
                        }
                    
                         if(res){
                            row.value.shares=row.value.shares+1
                         }
                        ElMessage.success("链接已复制到剪贴板（请在电脑端打开）");
                    },
                    (err) => {
                        console.log("~ copyText ~ err:", err);
                    })
            } catch (err) {
                console.error('复制失败:', err);
            }
}
let goEdit=()=>{
    router.push('/resource/teach/upload?resourceId='+resourceId)
}
let goUpLevel=(row)=>{
    dialogVisible.value=true
   
}
let cancelUpLevel=()=>{
  dialogVisible.value=false
  Object.assign(levelForm,{
     toTenantId:'',
      level:''
  })
  levelRef.value.resetFields()
}
let confirmUpLevel=async()=>{
   if (!levelRef.value) return
  await levelRef.value.validate(async(valid, fields) => {
    if (valid) {
        loading.value=true
        let res=await resourceUpLevel({
            toTenantId:levelForm.toTenantId,
            level:levelForm.level,
            resourceId:resourceId
        })
        if(res){
            loading.value=false
            ElMessage.success('申请已提交审核')
            row.value.upgrade=2
            cancelUpLevel()
        }else{
             loading.value=false
        }
    //   console.log('submit!')
    } else {
      console.log('error submit!', fields)
    }
  })
}
let goRecommend=(item)=>{
     router.push('/resource/teach/preview?id='+item.id).then(()=>{
         resourceId=route.query.id
    
         getDetail()
     })
}
let goDel=()=>{
     ElMessageBox.confirm(
    '确认删除该资源吗?',
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async() => {
        let res=await resourceDel({
            resourceId
        })
        if(res){
           router.go(-1)
            ElMessage({
                type: 'success',
                message: '删除成功',
            })

        }
    
    })
    .catch(() => {
      
    })
}
let getDetail=async()=>{
    let res=''
    if(route.path=='/bk/res/preview'){
        res=await bkResourceDetail({
            resourceId:resourceId
        })
    }else{
        res=await resourcePortalDetail({
          resourceId:resourceId
        })
        await resourcePortalDetailViewCount({resourceId,role:currentInfo.value.type=='teacher'?'T':'S'})
    }
     
    if(res){
        if(res.data.tenantTypes){
            res.data.tenantTypes.forEach(item=>{
                tenantTypes.value.push({
                    id:item,
                    title:tenantMap[item]
                })
            })
        }
       
        
        row.value=res.data
    }
}
let getUserTenant=async()=>{
    let res=await userTenantToParent({
        tenantId:row.value.tenantId
    })
    if(res){
        Object.assign(levelForm,{
           toTenantId:res.data
        })
       
        // tenantOptions.value=res.data
    }
}
let changeFavorite=async()=>{
    let res=''
     if(route.path=='/bk/res/preview'){
            res=await bkResourceFavourite({resourceId:resourceId})
     }else{
          res=await resourceFavourite({resourceId:resourceId})
     }
    
     if(res){
        row.value.isFavourite=!row.value.isFavourite
        if(row.value.isFavourite){
              row.value.favourites= row.value.favourites+1
        }else{
            row.value.favourites= row.value.favourites-1
        }
     }
}
let changeLike=async()=>{
 let res=''
     if(route.path=='/bk/res/preview'){
            res=await bkResourceLike({resourceId:resourceId})
     }else{
          res=await resourceLike({resourceId:resourceId})
     }
   if(res){
        row.value.isLike=!row.value.isLike
        if(row.value.isLike){
              row.value.likes= row.value.likes+1
        }else{
            row.value.likes= row.value.likes-1
        }
     }
}
let getRecommend=async()=>{
    let res=''
    if(route.path=='/bk/res/preview'){
        res=await bkRecommendResource({
            resourceId
        }) 
        await resourcePortalDetailViewCount({resourceId,role:currentInfo.value.type=='teacher'?'T':'S'})
    }else{
        res=await resourceRescommend({
            resourceId
        })
    }
   
    if(res){
        res.data.forEach(item=>{
            item.coverFileUrl=import.meta.env.VITE_BASE_URL+'/fs/file?fileId='+item.coverFileId
        })
       recommendList.value=res.data
    }
}
onMounted(()=>{
    resourceId=route.query.id
    
    getDetail()
    getRecommend()
})
</script>
    <style scoped lang='scss'>
    .preview{
       background: #fff;
       padding: 24px;
       .left{
        width: 70%;
        padding-right: 24px;
        border-right: 1px solid #E2E3E6;
        .title{
            font-size: 20px;
            color: #2B2C33;
            font-weight: bold;
            cursor: pointer;
        }
        .liked{
            img{
            width: 24px;
            height: auto;
            margin-right: 4px;
              cursor: pointer;
            }
            p{
                font-size: 14px;
                color: #94959C;
            }
        }
        .star{
            margin-left: 32px;
            cursor: pointer;
        }
        .detail{
          
            p{
                font-size: 14px;
                color: #6D6F75;
                margin-right: 32px;
            }
        }
        .action{
            font-size: 14px;
            img{
                width: 14px;
                height: auto;
                margin-right: 4px;
            }
            .edit{
                color: #508CFF;
                margin-right: 24px;
                cursor: pointer;
            }
            .del{
             color: #E72C4A;
             margin-right: 24px; cursor: pointer;
            }
            .copy{
              color: #508CFF;   cursor: pointer;
            }
        }
        .file{
            // height: 452px;
            margin-top: 16px;
            // border:1px solid
        }
        .bottom-detail{
            margin-top: 24px;
            .autor{
           
            .autor1{
                margin-right: 80px;
                &:last-child{
                    margin-right: 0;
                }
                img{
                    height: 16px;
                    width: auto;
                    margin-right: 4px;
                }
                .label{
                    font-size: 14px;
                   color: #6D6F75;
                   margin-right: 24px;
                }
                .value{
                    font-size: 14px;
                   color: #2B2C33;
                }
            }
        }
        .uplevel{
             width: 128px;
                height: 40px;
                background: #508CFF;
                border-radius: 12px;
                color: #fff;
                border:none;
                cursor: pointer;
           }
        }
      
       }
       .right{
         padding-left: 24px;
        .title{
            font-size: 18px;
           color: #2B2C33;
           font-weight: bold;
           margin-bottom: 16px;
        }
        ul{
            height: calc(100vh - 300px);
            overflow: auto;
        }
        .item{
            margin-bottom: 16px;
            &:last-child{
                margin-bottom: 0;
            }
             p{
                font-size: 16px;
               color: #2B2C33;
               
               margin-bottom: 10px;
             }
             .recommend-cover{
                width: 100%;
                height: 125px;
                img{
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
             }
        }
       }
    }
    
    </style>
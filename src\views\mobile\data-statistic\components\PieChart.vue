<script setup>
import { ref, watch, onMounted } from "vue"
import { EchartsUI, useEcharts } from "@/components/Echart"

const chartRef = ref()
const { renderEcharts } = useEcharts(chartRef)

const props = defineProps({
  chartData: {
    type: Object,
    default: () => ({
      data: []
    })
  }
})

watch(
  () => props.chartData,
  () => {
    paintChart()
  },
  { deep: true }
)

onMounted(() => {
  paintChart()
})

const paintChart = () => {
  renderEcharts({
    backgroundColor: '#fff',
    color: props.chartData.color || [],
    series: [
      {
        name: '饼图',
        type: 'pie',
        radius: ['40%', '70%'],
        padAngle: 3,
        itemStyle: {
          borderRadius: 0,
          borderColor: '#fff',
          borderWidth: 1
        },
        label: {
          show: true,
          fontSize: 12,
          color: '#8DA2B5'
        },
        data: props.chartData.data || []
      }
    ]
  })
}
</script>

<template>
  <EchartsUI ref="chartRef" :height="'164px'" />
</template>

<style scoped></style>

<template>
	<div class="chat_main">
		<div class="flex_between header_view">
			<div class="flex_start left">
				<img src="@/assets/layouts/logo.png" alt="">
				<p>备课助手</p>
			</div>
			<el-icon @click="router.back()">
				<Close />
			</el-icon>
		</div>
		<div class="mobile flex_column_start ">
			<div class="chat_list flex_1" id="chat_list" >
				<div class="flex_start" v-if="messageList.length==0">
					<div class="tsy_view">
						<img src="https://cdn.tqxxkj.cn/static/aizntpt/ai.png">
						<p class="botName">备课助手</p>
						<p class="botSlogan">
							你好！我是备课小能手，能快速分析学科知识，为您生成完整教学设计。从教学目标到作业、反思，都精心规划。我还会依据学情调整内容，提高教学适配度。提供多种教学形式提示，助力您高效备课，让教学更轻松！
						</p>
						<div class="botQuestions">
							<p class="text_14 grey2">你可以尝试这样问：</p>
							<ul>
								<li v-for="(item,index) in qaList" :key="index"
									@click="sendSuggest(item.question)">{{item.question}}</li>
							</ul>
						</div>
					</div>
				</div>
				<div v-for="(item,index) in messageList" :key="index">
					<div class="flex_column_end" style="align-items: flex-end;" v-if="item.messageType == 'user'">
						<!-- <ul class="flex_end flex_wrap chat_img" v-if="item.imgList.length>0">
							<li v-for="(item1,index1) in item.imgList" :key="index1" class="flex_center">
								<el-image :src="item1.file_url" :zoom-rate="1.2" :max-scale="7"
									style="width: 100%;height: 100%;" :min-scale="0.2" show-progress
									:initial-index="index1" :preview-src-list="previewImg(item.imgList)"
									:preview-teleported="true" fit="cover" />
							</li>
						</ul> -->
						<p class="chat_my" v-html="item.messageText"></p>
					</div>
					<div class="chat_bot" v-if="item.messageType != 'user'">
						<div class="chat_answer" v-html="renderMarkdownWithMath(item.messageText)"
						v-if="item.messageText"></div>
						<div v-if="item.messageUrl&&item.messageUrl.length>0">
							<div v-for="(item1,index1) in item.messageUrl" :key="index1">
								<el-image  :key="index1" :src="item1.content" :zoom-rate="1.2" :max-scale="7"
									:min-scale="0.2" show-progress :initial-index="index1"
									:preview-src-list="previewImg(item.messageUrl)" v-if="item1.type=='image'" />
								<div class="flex_start_0" v-show="item1.type=='resource'||item1.type=='docx'"
									style="margin-top:10px">
									<p style="white-space: nowrap;">文件：</p>
									<a target="_blank" style="color: #508cff;" :href="item1.content">{{item1.name}}</a>
								</div>
							</div>
						</div>

					</div>
					<van-loading style="width: 20px;color: #1A61F7;" v-if="!is_send&&index==messageList.length-1" />
					<!-- <p @click="pauseChat(item)" class="pause_text flex_center"
							v-if="!is_send&&index==messageList.length-1&&(item.content||item.reasoning_content)&&item.messageType != 'user'">
							<img src="../assets/pause.png">停止回答
						</p> -->
				</div>
			</div>
			<Bkzs @sendMsg='sendSuggest' v-if="showBk" @backCommon="showBk=false,type=''" />
			<div style="width: 100%;">
				<ul class="flex_start tool_ul" v-if="!type">
					<li v-for="(item,index) in typeList" :key="index" class="flex_center" @click="sendTool(item.id)">
						<!-- <img v-if="item.id==type" :src="item.url"> -->
						{{item.title}}
						<!-- <el-icon @click.stop="type=''" v-if="item.id==type" ><CircleCloseFilled /></el-icon> -->
					</li>
				</ul>
				<div class="send_view send_view_pc" v-if="!showBk">
					<div class="flex_end" style="padding-top: 10px;">
						<img v-if="type" src="@/assets/pc/bkzs/back.png" alt="" class="back"
							@click="backNormal" />
					</div>
					<div class=" grey_bottom margin_bottom_16" style="padding-top:4px">
						<el-input class=""  :autosize="{ minRows: 1, maxRows: 8 }" :rows="1"
							type="textarea" resize="none" id="text_input" @keydown="keyEnter" v-model="content"
							:placeholder="type=='4'?'请输入资源名称':type=='2'?'请告诉我您想生成的图片内容':'请您输入问题，shift+回车换行，回车发送'" />

					</div>
					<div class="flex_between">
						<p class="clear_text" @click="clearList"><img src="@/assets/pc/cleanall.png">清空上下文</p>
						<div class="flex_end">
							<img class="send_img" v-if="content&&is_send&&content.trim()!= ''" @click="sendChat"
								src="@/assets/pc/bkzs/send.png">
							<img class="send_img" v-else src="@/assets/pc/bkzs/send-grey.png">
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
	import { reactive, ref, onMounted, nextTick, onBeforeUnmount, watch } from "vue";
	import Bkzs from './bkzs.vue'
	import {createConversations,faqList,messagesList,clearContext} from '@/api/pc/bkzs.js'
	import { useRouter, useRoute } from "vue-router";
	import { getToken } from "@/utils/cache/cookies"
	import 'katex/dist/katex.min.css'; // 引入 KaTeX 的样式
	import { marked } from 'marked'
	import katex from 'katex';
	import scjaImg from '@/assets/pc/bkzs/scja.png';
	import sctpImg from '@/assets/pc/bkzs/sctp.png';
	import sckjImg from '@/assets/pc/bkzs/sckj.png';
	import zyssImg from '@/assets/pc/bkzs/zyss.png';
	const router = useRouter();
	const route = useRoute();
	const content = ref("");
	const messageList = ref([]);
	const id = ref('')
	const loading = ref(false)
	const createLoading = ref(false)
	const is_send = ref(true)
	let scrollHeight1 = 0
	let scrollHeight2 = 0
	let cozeAppId = ref('')
	const chatId = ref(null)
	const conversationId = ref("");
	let abortController = null
	const showBk = ref(false)
	const type = ref('')
	const typeList = ref([
		{ id: '1', title: '生成教案', url: scjaImg },
		{ id: '2', title: '生成图片', url: sctpImg },
		{ id: '4', title: '资源搜索', url: zyssImg }
	])
	const qaList=ref([])
	const renderMarkdownWithMath = (content) => {
		// 渲染 Markdown
		let html = marked(content, { sanitize: true, breaks: true });
  
	   // 渲染独立公式 ($$...$$)
	   html = html.replace(/\$\$(.*?)\$\$/g, (match, p1) => {
		  return katex.renderToString(p1, { displayMode: true, throwOnError: false, });
		});
  
		// 渲染行内公式 ($...$ 和 \(...\) 和 \\(...\\))
		html = html.replace(/(^|[^\\])\$(.*?)\$|\\(\((.*?)\\)|\\\((.*?)\\\)/g, (match, p1, p2, p3, p4, p5) => {
		  if (p2) {
			// 匹配 $...$
			return p1 + katex.renderToString(p2, { displayMode: false, throwOnError: false,   });
		  } else if (p4) {
			// 匹配 \(...\)
			return p1 + katex.renderToString(p4, { displayMode: false, throwOnError: false,    });
		  } else if (p5) {
			// 匹配 \\(...\\)
			return p1 + katex.renderToString(p5, { displayMode: false, throwOnError: false, });
		  }
		  return match; // 如果没有匹配到公式，返回原内容
		});
  
		return html.replace(/\s+/g, ' ').trim();
	  };
	    //预览图片
	function previewImg(data){
        let images = []
        data.forEach((item) => {
            images.push(item.content)
        })
            return images
    }
	function backNormal(){
		type.value=''
		content.value=''
		getFaqList()
	}
	function sendTool(id) {
		if (!is_send.value) return
		type.value = id
		getFaqList()
		if (id == '1') {
			showBk.value = true;
			nextTick(() => {
				scrollBottom()
			})
		}
	}
	async function getChat(type) {
		let res = await createConversations()
		if (res) {
			conversationId.value = res.data;
			getMessageList();
			localStorage.setItem('bkConversationId',conversationId.value)
		}
	}
	function sendSuggest(item) {
		content.value = item
		if (!is_send.value) return
		sendChat()
	}
	function keyEnter() {
			if (event.key === 'Enter' && !event.shiftKey) {
				event.preventDefault();
				sendChat()
			}
	}
	function sendChat() {
		if (!is_send.value) return
		if (!content.value) return;
		if (content.value.trim() == '') return
		is_send.value = false
		messageList.value.push({
			messageType: "user",
			messageText: content.value,
		});
		messageList.value.push({
			messageType: "ai",
			messageText: "",
			messageUrl:[],
			type: type.value,
		});
		scrollBottom();
		let data = {
			bkConversationsId: conversationId.value,
			type: type.value?type.value:1,
			content: content.value,
		};
		content.value = "";
		let token=getToken()
		fetch(
			import.meta.env.VITE_BASE_URL + `/ai/conversations/bk/chat`,
			{
				method: "POST",
				headers: {
                    'Accept': 'text/event-stream',
					"Content-Type": "application/json",
					"X-Haian-Platform-Member-Token": token ? `Bearer ${token}` : undefined,
				},
				mode: "cors",
				body: JSON.stringify(data),
			}
		)
			.then(async (response) => {
				processStream(response);
			})
			.catch((error) => { });
	}
	async function processStream(response) {
		const reader = response.body.getReader();
		const decoder = new TextDecoder("utf-8");
		let buffer = "";
		// let num = 0
		let content = ''
		let fileList = ''
		while (true) {
			const { done, value } = await reader.read();
			if (done) {
				// 流结束
				is_send.value = true
				setTimeout(() => {
					scrollBottom();
				}, 200);
				break;
			}
			const chunk = decoder.decode(value, { stream: true });
			buffer += chunk;
			const lines = buffer.split("\n");
			buffer = lines.pop(); // 保留最后一行，因为它可能是不完整的
			lines.forEach((line, index) => {
				// if (line == 'event:conversation.message.delta' && index + 1 < lines.length) {
					const data = line.substring(5).trim(); // 去掉'data:'前缀和任何前导空格
					// 尝试解析JSON数据
					// try {
					if(type.value!=1&&type.value){
						if (data.startsWith('[{')) {
							messageList.value[messageList.value.length-1].messageUrl=JSON.parse(data)
						}else{
							messageList.value[messageList.value.length - 1].messageText += data
						}
					}else{
						try{
							let message=JSON.parse(data)
							console.log(message.content)
							if (message.content.startsWith('[{')) {
							messageList.value[messageList.value.length-1].messageUrl=JSON.parse(message.content)
						}else{
							messageList.value[messageList.value.length - 1].messageText += message.content
						}
						}catch{

						}
					}
						// const message = JSON.parse(data);
						// console.log(message)

						// if (message.length>0) {
						// 	// fileList = message
						// 	messageList.value[messageList.value.length-1].messageUrl=message
						// 	} else {
						// 		content += message
						// 		messageList.value[messageList.value.length - 1].messageText += message
						// 	}
						scrollBottom();
					// } catch (error) {
					// 	console.error("Error parsing JSON:", error);
					// }
				// }
			});
		}
	}

	//流式语音输出
	async function getMessageList(type) {
		let data = {
			id: conversationId.value,
		}
		let res = await messagesList(data)
		if (res) {
			messageList.value=res.data
			res.data.forEach((item) => {
				if (item.messageUrl ) {
					item.messageUrl = JSON.parse(item.messageUrl)
				}
			})
				nextTick(async () => {
					if (messageList.value.length > 0) {
						await waitForImagesToLoad();
						scrollBottom();
					}
				})
		}
	}
	const  waitForImagesToLoad=()=> {
		const images = Array.from(document.querySelectorAll('.chat_bot img'));
		const promises = images.map(img => {
			if (img.complete) {
				return Promise.resolve();
			}
			return new Promise((resolve) => {
				img.addEventListener('load', resolve);
				img.addEventListener('error', resolve);
			});
		});
		return Promise.all(promises);
	}
	function scrollBottom() {
		nextTick(() => {
			let scrollWrap = document.getElementById("chat_list");
			scrollWrap.scrollTo({
				top: scrollWrap.scrollHeight,
				behavior: "instant", //auto-自动滚动 instant-瞬间滚动 smooth-平滑滚动
			});
			scrollHeight2 = scrollWrap.scrollHeight
		});
	}
	async function clearList() {
		let data={
			id:conversationId.value
		}
		let res=await clearContext(data)
		if(res){
			messageList.value = []
			getChat();
		}
	}
	async function getFaqList() {
		let data={
			type:type.value?type.value:1
		}
		let res=await faqList(data)
		if(res){
			qaList.value=res.data
		}
	}
	onMounted(async () => {
		getFaqList()
		if(localStorage.getItem('bkConversationId')){
			conversationId.value=localStorage.getItem('bkConversationId')
			getMessageList();
		}else{
			getChat()
		}

	})
	onBeforeUnmount(() => {

	});
</script>
<style>
	.katex-display>.katex>.katex-html {
		white-space: normal;
		font-size: 16px
	}
</style>
<style lang="scss" scoped>
	::-webkit-scrollbar-thumb {
		border-radius: 1em;
		background-color: #e3e3e3;
	}

	/* 滑块颜色 */
	::-webkit-scrollbar-track {
		/*border-radius: 1em;*/
		background-color: transparent;
	}

	/* 滚动条的滑轨背景颜色 */

	::-webkit-scrollbar-button {
		background-color: transparent;
		height: 0px;
		width: 0px
	}

	/* 滑轨两头的监听按钮颜色 */

	::-webkit-scrollbar-corner {
		background-color: transparent;
		height: 0px;
		width: 0px;
	}

	.chat_main {
		height: 100vh;
		/* height: calc(100vh - 64px); */
		background: url(@/assets/pc/ai-bg.png) no-repeat;
		background-size: cover;
		padding-bottom: 24px;

		.header_view {
			background: linear-gradient(90deg, #508CFF 0%, #95B8FF 100%);
			border-radius: 0px 0px 0px 0px;
			padding: 18px 24px;
			font-weight: 600;
			height: 64px;
			font-size: 20px;
			color: #FFFFFF;

			img {
				width: 24px;
				margin-right: 8px;
			}

			i {
				width: 16px;
				cursor: pointer;
			}
		}
	}


	.upload_input {
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		overflow: hidden;
		border-radius: 4px 4px 4px 4px;
		height: 32px;
		width: 32px;
	}

	.upload_input:hover {
		background: rgba(0, 0, 0, 0.08);
	}



	.mobile {
		max-width: 900px;
		margin: 0 auto;
		height: calc(100vh - 90px);

		/* height: 100%; */
		// max-width: 90vw;
		.audio-wave {
			display: flex;
			align-items: center;
			margin-right: 4px;
		}

		.bar {
			width: 2px;
			height: 10px;
			background-color: #007BFF;
			margin: 0 1px;
			animation-duration: .5s;
			animation-iteration-count: infinite;
			animation-direction: alternate;
		}

		.bar:nth-child(1) {
			animation-name: wave1;
			opacity: .2;
		}

		.bar:nth-child(2) {
			animation-name: wave2;
			animation-delay: 0.2s;
			opacity: .6;
		}

		.bar:nth-child(3) {
			animation-name: wave3;
			animation-delay: 0.4s;
		}

		@keyframes wave1 {
			from {
				height: 5px;
			}

			to {
				height: 10px;
			}
		}

		@keyframes wave2 {
			from {
				height: 5px;
			}

			to {
				height: 10px;
			}
		}

		@keyframes wave3 {
			from {
				height: 5px;
			}

			to {
				height: 10px;
			}
		}

		.bf_hover:hover {
			background: rgba(0, 0, 0, 0.08);
			border-radius: 4px 4px 4px 4px;
		}

		.bf_hover {
			padding: 2px 5px;
		}

		.regenerate_copy {
			font-size: 14px;
			color: #94959C;

			p {
				cursor: pointer;
				margin-right: 32px;
				white-space: nowrap
			}

			img {
				width: 13px;
				height: auto;
				margin-right: 4px;
			}
		}

		.pause_text {
			background: #FFFFFF;
			border-radius: 20px;
			border: 1px solid #E2E3E4;
			font-size: 14px;
			color: #B1B3B6;
			padding: 10px 24px;
			width: max-content;
			margin: 0 auto;
			cursor: pointer;

			img {
				width: 16px;
				height: 16px;
				margin-right: 4px;
			}
		}

		.tool_ul {
			// margin-bottom: 10px;
			// padding-top: 16px;
			padding: 10px 0px;

			li {
				background: #FFFFFF;
				border-radius: 8px 8px 8px 8px;
				border: 1px solid #6897FF;
				font-size: 14px;
				color: #1A61F7;
				padding: 8px 12px;
				margin-right: 12px;
				cursor: pointer;
				min-width: 112px;
				position: relative;

				img {
					width: 16px;
					height: auto;
					margin-right: 8px
				}
			}

			i {
				position: absolute;
				right: -5px;
				top: -5px;
				color: #0b121dba;
			}

			li:hover {
				background: rgba(80, 122, 252, 0.04);
			}

			li.active {
				background: linear-gradient(270deg, #496EEE 0%, #0AC1FA 100%), #FFFFFF;
				color: #FFFFFF;
				border: 0;
			}
		}

		.kj_text {
			background: rgba(218, 218, 218, 0.25);
			border-radius: 8px 8px 8px 8px;
			padding: 7px 12px;
			font-size: 12px;
			color: #2B2C33;
			cursor: pointer;
			// margin-top: 16px;
			width: fit-content;
			width: max-content;

			i {
				margin-left: 4px;
			}
		}

		.voice_input {
			font-size: 14px;
			background: #F9F9F9;
			height: 45px;
			// line-height: 45px;
			cursor: pointer;
			-webkit-touch-callout: none;
			-webkit-user-select: none;
			-khtml-user-select: none;
			-moz-user-select: none;
			-ms-user-select: none;
			user-select: none;
			padding: 12px;
			color: #2B2C33;
			border-radius: 8px;
			// margin-top: 16px;
		}

		.tsy_view {
			background: #FFFFFF;
			border-radius: 12px 12px 12px 12px;
			padding: 32px;
			margin-bottom: 16px;
			width: 70%;

			img {
				width: 54px;
				height: 54px;
				border-radius: 8px;
			}

			.botName {
				font-weight: 600;
				font-size: 24px;
				color: #2B2C33;
				margin: 12px 0 8px;
			}

			.botSlogan {
				font-size: 14px;
				color: #4B5563;
				margin-bottom: 24px;
			}

			.botQuestions {
				background: #F7FAFF;
				border-radius: 12px 12px 12px 12px;
				padding: 24px;
			}

			li {
				font-size: 14px;
				color: #2668FF;
				margin-top: 16px;
				cursor: pointer;
				position: relative;
			}


			li:last-child {
				margin-bottom: 0;
			}
		}

		.tsy_view_mobile {
			padding: 24px;
			width: 100%;
			background: #FFFFFF;
			border-radius: 12px 12px 12px 12px;
			margin-bottom: 16px;

			img {
				width: 60px;
				height: 60px;
				border-radius: 8px;
			}

			.botName {
				font-weight: 600;
				font-size: 20px;
				color: #2B2C33;
				margin: 12px 0 8px;
			}

			.botSlogan {
				font-size: 14px;
				color: #4B5563;
				margin-bottom: 8px;
			}

			.botQuestions {
				background: #F7FAFF;
				border-radius: 12px 12px 12px 12px;
				padding: 16px;
			}

			li {
				font-size: 12px;
				color: #2668FF;
				margin-top: 16px;
				cursor: pointer;
				position: relative;
			}


			li:last-child {
				margin-bottom: 0;
			}
		}

		.clear_text {
			border-radius: 14px;
			border: 1px solid #E2E3E4;
			font-size: 14px;
			color: #B4B6BE;
			cursor: pointer;
			padding: 4px 10px;

			img {
				width: 14px;
				height: 14px;
				margin-right: 6px;
			}
		}

		.bottom_tips {
			font-size: 12px;
			color: #B4B6BE;
			text-align: center;
			padding: 16px 024px;
		}

		.send_view {
			background: #ffffff;
			min-height: 70px;
			padding: 0px 16px 22px;
			box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
			border-radius: 12px 12px 0 0;
			// position: fixed;
			// bottom: 0;
			// left: 0;
			// right: 0;
			// max-width: 90vw;
			// margin: 0 auto;
			width: 100%;

			.send_img {
				width: 24px;
				height: 24px;
				cursor: pointer;
				margin-left: 8px;
			}

			.picture_ul {
				// border-bottom: 1px solid #E2E3E4;
				// margin-bottom: 8px;
				padding-top: 16px;

				li {
					width: 60px;
					height: 60px;
					margin: 0 16px 16px 0;
					position: relative;

					img:nth-child(1) {
						width: 100%;
						height: 100%;
						border-radius: 8px;
						object-fit: cover;
					}

					img:nth-child(2) {
						width: 16px;
						height: 16px;
						cursor: pointer;
						position: absolute;
						top: -5px;
						right: -8px;
					}
				}
			}

			.upload_img {
				position: absolute;
				right: 0;
				top: 0;
				left: 0;
				bottom: 0;
				height: 100%;
				width: 100%;
				opacity: 0;
			}

			.grey_bottom {
				// background: #F9F9F9;
				border-radius: 8px;
				// padding: 0 14px 0 0;
				position: relative;
				/* margin-right: 14px; */
			}

			.picture_img {
				height: 20px;
				cursor: pointer;
				object-fit: cover;
			}

			::v-deep .el-textarea__inner {
				// background: #F9F9F9;
				border-radius: 8px;
				border: 0;
				box-shadow: none;
				font-size: 14px;
				color: #2B2C33;
				padding: 12px 0;
				height: 48px;
			}

		}

		.mobile_input {
			::v-deep .el-textarea__inner {
				background: #F9F9F9;
				padding: 12px;
			}
		}

		.send_view_pc {
			box-shadow: none;
			border: 1px solid #6897FF;
			border-radius: 12px;

			.back {
				width: 50px;
				height: auto;
				cursor: pointer;
			}
		}

		.chat_img {
			margin-bottom: 8px;

			li {
				width: 100px;
				height: 100px;
				border-radius: 8px;
				border: 1px solid #e2e3e6;
				cursor: pointer;
				overflow: hidden;
				margin: 0 6px 6px 0;

				img {
					width: 100px;
					height: 100px;
					object-fit: cover;
				}
			}
		}

		.chat_list {
			padding: 15px;
			width: 100%;
			/* max-height: calc(100vh - 74px); */
			overflow-y: auto;
			position: relative;

			.chat_my {
				background: #347aff;
				border-radius: 8px 0px 8px 8px;
				font-size: 16px;
				color: #ffffff;
				padding: 10px 12px;
				margin-bottom: 16px;
				max-width: 90%;
				word-break: break-all;
				line-height: 1.7;
				width: fit-content;
			}

			.chat_bot {
				background: #ffffff;
				border-radius: 0px 8px 8px 8px;
				padding: 10px 12px;
				color: #2b2c33;
				width: 100%;
				/* width: fit-content; */
				max-width: 100%;
				margin-bottom: 16px;
				word-break: break-all;
				font-size: 16px;
				line-height: 1.7;
				word-break: break-all;
				// width: fit-content;

				::v-deep pre {
					white-space: pre-wrap;
				}

				.chat_answer {
					::v-deep img {
						max-width: 100%;
						height: 200px;
					}
				}

			}

		}

		::v-deep table {
			width: 100%;
			border-collapse: collapse;

			th,
			td {
				border: 1px solid #ddd;
				padding: 8px;
				text-align: left;
			}

			th {
				background-color: #f2f2f2;
			}
		}

	}
</style>
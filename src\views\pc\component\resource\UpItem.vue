<template>
   <div class="upitem flex_between">

       <el-form :inline="false" :model="itemForm" class="demo-form-inline " label-position="top" style="width:45%">
            <el-form-item label="资源" style="width:100%">
                 <template #label>
                    <div class="flex_start label">
                        <h3>资源</h3>
                        <p>（建议格式为word、excel、ppt等）</p>
                    </div>
                </template>
                <div class="flex_start" style="width:100%">
                    <div class="up-resource flex_start flex_1">
                        <div class="flex_start flex_1">
                            <img src="@/assets/pc/filetype.png" alt="" class="filetype" v-show="itemForm.url[0]">
                             <p class="filename ">{{itemForm.url[0]?.name}}</p>
                        </div>
                       
                        <img src="@/assets/pc/del-file.png" alt="" @click="removeResource" v-show="!resourceId&&itemForm.url[0]">
                    </div>
                   
                     <el-upload
                        class="upload-demo"
                        action="#"
                        :disabled='resourceId!==""'
                        :on-change="handleChange"
                       :http-request="handleUpResource"
                       :before-upload='resourceBeforeUpload'
                    >
                        <el-button type="primary" class="local-upload"  :disabled='resourceId!==""'>本地上传</el-button>
                    </el-upload>
                </div>
            </el-form-item>
            
             <el-form-item label="资源名称" style="width:100%%">
                 <el-input v-model="itemForm.name"   :disabled='resourceId!==""' placeholder="请输入资源名称" clearable style="width:100%" @change="changeName" />
            </el-form-item>
       </el-form>
        <el-form :inline="false" :model="itemForm" v-if="itemForm.url[0]&&(itemForm.url[0].type=='2'||itemForm.url[0].type=='3')" class="demo-form-inline flex_between_0 flex_wrap" label-position="top"  style="width:45%">
            
             <el-form-item label="上传封面" style="width:100%">
                <template #label>
                    <div class="flex_start label">
                        <h3>上传封面</h3>
                        <p class="flex_1" style="font-size:10px">（图片建议为jpg,png,jpeg格式，建议尺寸为224x126px，大小为2M以内）</p>
                    </div>
                </template>
                 <el-upload
                    v-model:file-list="itemForm.cover"
                    action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
                    list-type="picture-card"
                    :on-preview="handlePictureCardPreview"
                    :on-remove="handleRemove"
                    :http-request="handleUpCover"
                    :before-upload='CoverBeforeUpload'
                      :on-change="handleCoverChange"
                    :disabled='resourceId!==""'
                    :class="itemForm.cover.length>0?'hideorgin':''"
                >
                        <div class="flex_center flex_column">
                            <img src="@/assets/pc/add-upload.png" alt="" class="cover-img">
                            <p class="cover-p">点击上传图片</p>
                        </div>
                </el-upload>
            </el-form-item>
           <el-dialog v-model="dialogVisible">
                <img w-full :src="dialogImageUrl" alt="Preview Image" />
            </el-dialog>
       </el-form>
       <img src="@/assets/pc/del-item.png" alt="" class="del-item" @click="delItem" />
   </div>
</template>
<script setup>
import {ref,defineProps,defineEmits, onMounted, watch} from 'vue'
import {resourceDetail,} from '@/api/pc/index.js'
import { Plus } from '@element-plus/icons-vue'
import {useRoute} from 'vue-router'
import {ElMessage,ElLoading} from 'element-plus'
 import {uploadFileFunc} from '@/utils/pc-upload.js'
 let allowType=['doc','docx','xls','xlsx','ppt','pptx','pdf','mp4','flv','dash','hls','mp3','wav','aac','webm']
 let docType=['doc','docx','xls','xlsx','ppt','pptx','pdf',]
 let  streamVideoType=['mp4','flv','dash','hls']
 let  streamAudioType=['mp3','wav','aac','webm']
let itemForm=ref({
    url:[],
    cover:[],
    name:'',
   
})
let route=useRoute()
let resourceId=ref('')
let props=defineProps(['id'])
let emits=defineEmits(['emitResourceItem'])
watch(()=>props.id,(newval)=>{
    itemForm.value={
         url:[],
         cover:[],
         name:'',
    }
})
let loadingInstance =''
let dialogVisible=ref(false)
let dialogImageUrl=ref('')
let fileResource=''
let coverResource=''
onMounted(async()=>{
    // console.log(import.meta.env)
     resourceId.value=route.query.resourceId?route.query.resourceId:''
   if(resourceId.value){
      let res=await resourceDetail({
        resourceId:resourceId.value
      })  
      if(res){
           itemForm.value={
            name:res.data.name,
            url:[
                {
                    url:'',
                    name:res.data.fileName,
                   fileId:res.data.fileId
                }
            ],
            cover:[
                {
                    url:'',
                    name:res.data.coverFileName,
                   fileId:res.data.coverFileId
                }
            ],
           }
      }
    }
})
const getExtensionString = (str) => {
    var index = str.lastIndexOf('.')
    // 如果找到了第一个"."并且它不在字符串的起始处或结尾处
    if (index != -1 && index != 0 && index + 1 != str.length) {
        return str.substr(index + 1)
    } else {
        return ''
    }
}
function getQueryParams(src,type) {
    const url = new URL(src);
    const params = url.searchParams.get(type);
    return params
}

let resourceBeforeUpload=(file)=>{
  let type=getExtensionString(file.name)
  if(allowType.indexOf(type)>-1){
     if(type=='ppt'||type=='pptx'){
         itemForm.value.cover=[
            {
                 url:'',
                name:'',
                fileId:import.meta.env.VITE_PPT
            }
         ]
     }
      if(type=='doc'||type=='docx'){
         itemForm.value.cover=[
            {
                 url:'',
                name:'',
                fileId:import.meta.env.VITE_DOC
            }
         ]
     }
      if(type=='xlsx'||type=='xls'){
         itemForm.value.cover=[
            {
                 url:'',
                name:'',
                fileId:import.meta.env.VITE_EXCEL
            }
         ]
     }
     if(type=='pdf'){
        itemForm.value.cover=[
            {
                url:'',
                name:'',
                fileId:import.meta.env.VITE_PDF
            }
         ]
     }
    //  console.log( itemForm.value.cover)
    return true
  }else{
      ElMessage.warning('请上传以下格式的文件'+allowType.join('，'))
      return false
  }
}
let changeName=()=>{
     emits('emitResourceItem',props.id,itemForm.value)
}
let delItem=()=>{
     emits('emitResourceItemDel',props.id)
}
let CoverBeforeUpload=(file)=>{
  let type=getExtensionString(file.name)
  let allowType=['jpg','png','jpeg','webp']
  if(allowType.indexOf(type)>-1){
    return true
  }else{
      ElMessage.warning('请上传以下格式的文件'+allowType.join('，'))
      return false
  }
}
let removeResource=()=>{
  itemForm.value.url=[]
  fileResource=''
}
let handlePictureCardPreview=()=>{
  dialogVisible.value=true
  dialogImageUrl.value= itemForm.value.cover[0]?.url
}
let handleUpCover=()=>{
      uploadFileFunc(coverResource,(val)=>{
        itemForm.value.cover=[
            {
                url:val,
                name:coverResource.name,
                fileId:getQueryParams(val,'fileId')
            }
        ]
    
      emits('emitResourceItem',props.id,itemForm.value)
    })
}
let handleRemove=()=>{
  itemForm.value.cover=[]
  fileResource=''
}
let handleUpResource=()=>{
  uploadFileFunc(fileResource,(val)=>{
    itemForm.value.url=[
        {
             url:val,
             type:docType.indexOf(getExtensionString(fileResource.name))>-1?'1':streamVideoType.indexOf(getExtensionString(fileResource.name))>-1?'2':'3',
             name:fileResource.name,
             fileId:getQueryParams(val,'fileId')
        }
    ]
    
   emits('emitResourceItem',props.id,itemForm.value)
  })
}
let handleChange=(val)=>{
    // console.log(val)
    fileResource=val.raw
}
let handleCoverChange=(val)=>{
    coverResource=val.raw
}
</script>

<style lang='scss' scoped>
.upitem{
    padding: 24px 10px 0 10px;
    background: #F9F9F9;
    border-radius: 12px;
    position: relative;
}
.del-item{
    position: absolute;
    top:20px;
    right: 20px;
    width: 24px;
    height: auto;
    cursor: pointer;
}
.label{
  h3{
    font-size: 16px;
    color: #2B2C33;
    margin-right: 10px;
  }
  p{
    font-size: 12px;
    color: #B4B6BE;
  }
}
.up-resource{
    height: 48px;
    background: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #E2E3E6;
    padding: 0 16px;
    margin-right: 8px;
    img{
        width: 24px;
        height: 24px;
        cursor: pointer;
    }
    .filetype{
        width: 17px;
            height: auto;
            margin-right: 6px;
    }
    .filename{
        width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
.local-upload{
    width: 96px;
    height: 48px;
    background: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #508CFF;
    font-size: 16px;
    color: #508CFF;
    line-height: 16px;
}
:deep(.upload-demo .el-upload-list){
        display: none;
    }
    :deep(.el-upload){
        background: #fff;
    }
    .cover-img{
        width: 24px;
        height: auto;
    }
    .cover-p{
        font-size: 14px;
color: #4260FD;
margin-top: 8px;
    }
    :deep(.hideorgin .el-upload){
        display: none;
    }
</style>
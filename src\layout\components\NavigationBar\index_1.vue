<script setup>
import Logo from "../Logo/index.vue"
// import { useUserStore } from "@/store/modules/user"
import Sidebar from "../Sidebar/index.vue"
import { useDevice } from "@/hooks/useDevice"
import DefaultAvatar from "@/assets/layouts/default-avatar.png"

const { isMobile } = useDevice()
const userInfo = sessionStorage.getItem("userInfo") ? JSON.parse(sessionStorage.getItem("userInfo")) : {}

</script>

<template>
  <div class="navigation-bar">
    <Logo />
    <!-- <Sidebar v-if="!isMobile" class="sidebar" /> -->
    <div class="right-menu pl64">
      <div class="right-menu-avatar">
        <el-avatar :src="userInfo.avatar || DefaultAvatar" class="avatar" :size="32" />
        <span class="user-name">用户名</span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.navigation-bar {
  height: var(--v3-navigationbar-height);
  overflow: hidden;
  color: var(--v3-navigationbar-text-color);
  background-color: #508CFF;
  display: flex;
  justify-content: space-between;
  padding: 0 12%;

  .hamburger {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 15px;
    cursor: pointer;
  }
  .breadcrumb {
    flex: 1;
    // 参考 Bootstrap 的响应式设计将宽度设置为 576
    @media screen and (max-width: 576px) {
      display: none;
    }
  }
  .sidebar {
    flex: 1;
    // 设置 min-width 是为了让 Sidebar 里的 el-menu 宽度自适应
    min-width: 0px;
    :deep(.el-menu) {
      background-color: transparent;
    }
    :deep(.el-sub-menu) {
      &.is-active {
        .el-sub-menu__title {
          color: var(--el-color-primary) !important;
        }
      }
    }
  }
  .right-menu {
    margin-right: 10px;
    height: 100%;
    display: flex;
    align-items: center;
    .right-menu-item {
      padding: 0 10px;
      cursor: pointer;
    }
    .right-menu-avatar {
      display: flex;
      align-items: center;
      margin-right: 14px;

      .el-avatar {
        margin-right: 10px;
      }
      .user-name {
        color: #fff;
        font-size: 16px;
      }
    }
  }
}
</style>

<template>
    <div>
        <ul class="detail-ul text_16">
            <li class="flex_start">
                <p class="detail-label grey2">课题任务名称</p>
                <p>{{detail.title}}</p>
            </li>
            <li class="flex_start">
                <p class="detail-label grey2">申请截止时间</p> 
                <p>{{detail.deadline}}</p>
            </li>
            <li class="flex_start">
                <p class="detail-label grey2">备注</p>
                <p class="flex_1">{{detail.remark}}</p>
            </li>
            <li class="flex_start">
                <p class="detail-label grey2">附件</p> 
                <div class="flex_1">
                    <div v-for="(item,index) in detail.file" :key="index" class="flex_between file-box">
                        <p class="pointer">{{item.name}}</p>
                        <p class="flex_start pointer"><el-icon class="mr_2"><Download /></el-icon>下载</p>
                    </div>
                </div>
            </li>
        </ul> 
        <div class="flex_end mt_24" v-if="props.hasBtn">
            <el-button type="primary" class="btn_120_48 primary-btn" style="font-size:16px;font-weight:400;" @click="goDeclare">去申报</el-button>
        </div>
    </div>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import { Download } from '@element-plus/icons-vue'
    import { useRouter, useRoute } from 'vue-router'
    const router = useRouter()
    const route = useRoute()

    const props = defineProps({
        hasBtn:true
    });
    
    const detail = ref({
        title:'XXX课题研究',
        deadline:'2022-12-31',
        remark:'这是一段备注说明这是一段备注说明这是一段备注说明这是一段备注说明这是一段备注说明这是一段备注说明',
        file:[{name:'附件1.pdf',url:'http://www.baidu.com'}],
    })

    const goDeclare = () => {
        router.push(`/jky/lxsq/application/0`)
    }

    onMounted(() => {
    })
</script>
<style lang="scss" scoped>
.detail-ul{
    li{margin-bottom: 16px;color: #2B2C33;
        .detail-label{margin-right: 32px;width: 100px;text-align: right;}
        .file-box{width: 100%;color: #508CFF;}
    }
}
</style>
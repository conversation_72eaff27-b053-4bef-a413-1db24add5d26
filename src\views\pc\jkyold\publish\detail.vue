<template>
    <div class="jky-detail-main">
        <div class="radius-box">
            <div class="flex_start_start"> 
                <p class="text_20_bold flex_start mr_40 pointer" @click="router.back()"><el-icon><ArrowLeftBold /></el-icon>查看详情</p> 
                <el-form inline :model="search_form" class="search_form flex_start flex_1">
                    <el-form-item label="目标学校" style="margin-right: 16px;width: calc(33% - 16px);">
                        <el-input v-model="search_form.schoolName" clearable @input="changeUserName" class="input_40" placeholder="请输入"></el-input>
                    </el-form-item>
                    <!-- <el-form-item label="考核专家" style="margin-right: 16px;width: calc(33% - 16px);">
                        <el-input v-model="search_form.name" @input="changeUserName" class="input_40" placeholder="请输入"></el-input>
                    </el-form-item> -->
                    <el-form-item label="操作" class="hidden-label" style="width: 132px;margin-right: 0;flex:1;" >
                        <div class="flex_end" style="width: 100%"> 
                            <el-button class="primary-btn btn_132_40" type="primary" @click="searchClick">搜索</el-button>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
            <el-table :data="sch_list" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F9F9F9"}'>
                <el-table-column type="index" label="序号" width="80"  />
                <el-table-column prop="taskTitle" label="任务名称" show-overflow-tooltip></el-table-column> 
                <el-table-column prop="createTime" label="创建时间" />
                <el-table-column prop="schoolName" label="目标学校" />
                <el-table-column prop="reviewUserNames" label="考核专家" />
                <el-table-column prop="address" label="操作" width="120"> 
                    <template #default='scope'>
                        <div class="flex_start" style="height:100%">
                            <el-button type="primary" link style="font-size:16px;font-weight:400;" @click="examine_visible = true, schoolId = scope.row.schoolId">查看结果</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination background layout="total,prev, pager, next" :total="total" class="mt-4" :current-page='search_form.pageNum' @current-change='currentChange' />
        </div>
            
        <el-dialog v-model="add_visible" v-if="add_visible" title="创建任务" width="456px" center class="my-dialog">
            <Add></Add>
        </el-dialog>
        <el-dialog v-model="examine_visible" v-if="examine_visible" title="查看结果" width="456px" center class="my-dialog">
            <Result :id="schoolId"></Result>
        </el-dialog>
        <el-dialog v-model="allocation_visible" v-if="allocation_visible" title="分配专家" width="456px" center class="my-dialog">
            <Allocation></Allocation>
        </el-dialog>
    </div>
</template> 
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import { CirclePlusFilled, ArrowLeftBold } from '@element-plus/icons-vue'
    import { schList } from '@/api/pc/jky'
    import Add from './add.vue'
    import Result from './result.vue'
    import Allocation from './allocation.vue'
    import { useRouter, useRoute } from 'vue-router'
    const router = useRouter()
    const route = useRoute()
    const formObj = function(obj = {}){
        this.schoolName = obj.schoolName || ''
        this.pageNum = obj.pageNum || 1
        this.pageSize = obj.pageSize || 10
    }
    const search_form = ref(new formObj({}))
    const total = ref(0)
    const sch_list = ref([])
    const add_visible = ref(false)
    const examine_visible = ref(false)
    const allocation_visible = ref(false)
    const schoolId = ref(0)

    const getSchList = async () => {
        let params = search_form.value
        params.taskMasterId = route.params.id
        let res = await schList(params)
        if(res){
            sch_list.value = res.data.list
            total.value = res.data.total
        }
    }

    const searchClick = () => {
        search_form.value.pageNum = 1
        getSchList()
    }
    const currentChange = (page) => {
        search_form.value.pageNum = page
        getSchList()
    }
    onMounted(() => {
        getSchList()
    })
</script>
<style lang="scss" scoped>
.jky-detail-main {max-width: 1128px;margin:0 auto;font-family: PingFangSC, PingFang SC;
    .radius-box{border-radius: 12px;background: #fff;padding: 24px 24px 6px;margin: 10px 0;}
}
</style> 
<style lang="scss">
    .el-table th.el-table__cell.is-leaf{border: none!important;}
</style>
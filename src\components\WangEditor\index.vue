<!-- 富文本编辑 -->
<template>
  <div style="border: 1px solid #e2e3e6; width: 100%; border-radius: 8px; overflow: hidden" v-loading="loading">
    <Toolbar ref="toolbarRef" style="border-bottom: 1px solid #e2e3e6" :defaultConfig="toolbarConfig"
      :editor="editorRef" :mode="mode" />
    <Editor :style="{ height: props.height || '300px', overflowY: 'hidden' }" v-model="valueHtml"
      :defaultConfig="editorConfig" :defaultContent="jsonContent" :mode="mode" @onCreated="handleCreated"
      @onChange="handleChange" @customPaste="customPaste" />
  </div>
</template>

<script setup>
import "@wangeditor/editor/dist/css/style.css" // 引入 css
import { onBeforeUnmount, ref, shallowRef, onMounted, watch, computed } from "vue"
import { Editor, Toolbar } from "@wangeditor/editor-for-vue"
import { uploadFileApi } from "@/api/common"
// import { uploadFileFunc } from '@/utils/pc-upload.js'
  import {uploadHtmlFileFunc} from '@/utils/pc-upload.js'
// import { DomEditor } from "@wangeditor/editor"
// import { uploadFileWang } from "@/utils/upload";
// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()
const mode = "default"
const props = defineProps({
  modelValue: "",
  height: { type: String, default: "300px" }
})
const emit = defineEmits()
const loading = ref(false)

const valueHtml = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit("update:modelValue", value)
  }
})

const jsonContent = ref([
  {
    type: "paragraph",
    children: [{ text: "" }],
    fontSize: "17px",
    fontFamily: "Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, 微软雅黑, Arial, sans-serif",
    lineHeight: 1.5,
  },
],)

// 模拟 ajax 异步获取内容
onMounted(() => {
  // valueHtml.value = props.content
  // if (editorRef.value) {
  //         editorRef.value.setHtml(props.modelValue);
  //     }
})

const toolbarRef = ref(null)

watch(
  () => props.modelValue,
  (newVal) => {
    if (editorRef.value) {
      // console.log(editorRef.value.getAllMenuKeys())
      editorRef.value.setHtml(newVal)
    }
  }
)

const toolbarConfig = {
  excludeKeys: [
    "codeBlock",
    "insertVideo",
    "uploadVideo",
    "editVideoSize",
    "codeSelectLang",
    "fullScreen",
    "group-video"
  ]
}

const editorConfig = { placeholder: "请输入内容...", MENU_CONF: {}, defaultFontSize: "17px" }
editorConfig.MENU_CONF['uploadImage'] = {
  // 自定义上传
  async customUpload(file, insertFn) {

    uploadHtmlFileFunc(file, (url) => {

      insertFn(url)
    })
    // insertFn(url, alt, href)
  },
}
editorConfig.MENU_CONF["uploadVideo"] = {
  async customUpload(file, insertFn) {
    loading.value = true
    // uploadFileWang(file, (files) => {
    //   // add_form.value.files.push(files)
    //   insertFn(files.url)
    //   loading.value = false
    // })
  }
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})

const handleCreated = (editor) => {
  editorRef.value = editor // 记录 editor 实例，重要！
  if (editorRef.value) {
    editorRef.value.setHtml(props.modelValue)
  }
}

const handleChange = (editor) => {
  // editor changed
}

const customPaste = (editor, event, callback) => {
  // 确保 editor 和 editor.cmd 存在且不是 undefined
  if (!editor || !editor.cmd) {
    console.error("Editor or editor.cmd is not defined")
    return
  }
  const html = event.clipboardData.getData("text/html") // 获取粘贴的 html
  const text = event.clipboardData.getData("text/plain") // 获取粘贴的纯文本
  const rtf = event.clipboardData.getData("text/rtf") // 获取 rtf 数据（如从 word wsp 复制粘贴）
  // 检查是否包含表格标签
  if (html.includes("<table")) {
    // 直接插入HTML，以保留表格格式
    editor.cmd.do("insertHTML", html)

    // 阻止默认粘贴行为
    event.preventDefault()
    callback(false)
  } else {
    // 对于非表格内容，继续使用原逻辑
    editor.insertText(text)

    // 返回 false ，阻止默认粘贴行为
    event.preventDefault()
    callback(false)
  }
  // // 自定义插入内容
  // editor.insertText(text)

  // 返回 false ，阻止默认粘贴行为
  event.preventDefault()
  callback(false) // 返回值（注意，vue 事件的返回值，不能用 return）

  // 返回 true ，继续默认的粘贴行为
  // callback(true)
}
</script>
<style>
.w-e-full-screen-container {
  z-index: 11111;
}
</style>

<script setup>
import { ref, watch, onMounted } from "vue"
import { EchartsUI, useEcharts } from "@/components/Echart"

const chartRef = ref()
const { renderEcharts } = useEcharts(chartRef)

const props = defineProps({
  chartData: {
    type: Object,
    default: () => ({
      data: [],
      color: []
    })
  }
})

watch(
  () => props.chartData,
  () => {
    paintChart()
  },
  { deep: true }
)

onMounted(() => {
  paintChart()
})

const paintChart = () => {
  const data = props.chartData.data
  // 处理x轴标签文字，在"进步"前添加换行符
  const xAxisData = data.map(item => {
    if (item.name.includes('进步')) {
      return item.name.replace('进步', '\n进步')
    }
    return item.name
  })
  const seriesData = data.map(item => item.value)

  // 计算需要显示的x轴标签
  const interval = Math.floor(xAxisData.length / 6)

  renderEcharts({
    grid: {
      top: '10%',
      left: '3%',
      right: '4%',
      bottom: '15%',  // 增加底部留白以容纳换行的标签
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#E2E3E6'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#8DA2B5',
        fontSize: 12,
        interval: interval > 0 ? interval : 0,
        lineHeight: 16
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#E2E3E6'
        }
      },
      axisLabel: {
        color: '#8DA2B5',
        fontSize: 12
      }
    },
    series: [
      {
        data: seriesData,
        type: 'bar',
        barWidth: 20,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#88C0FF' },
              { offset: 1, color: '#508CFF' }
            ]
          },
          borderRadius: [4, 4, 0, 0]
        },
        label: {
          show: false
        }
      }
    ]
  })
}
</script>

<template>
  <EchartsUI ref="chartRef" :height="'287px'" />
</template>

<style scoped></style>

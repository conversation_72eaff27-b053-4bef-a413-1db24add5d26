<template>
      <div>
         <el-dialog title="" :model-value="props.changePasswordVisible" width="456px" :close-on-click-modal='false'>
           
                <div class="header">
                  <img src="@/assets/pc/logo.png" alt="" class="header-bg">
                  <img src="@/assets/pc/dialog-del.png" alt="" class="dialog-del" @click="goCancel"/>
                </div>
                
            <div class="login forget">
                <div class="tab">
                    <div>修改密码</div>
                </div>
                <div class="form">
                  <el-form
                    label-position="top"
                    label-width="auto"
                    ref="resetRef"
                    :model="resetForm"
                    :rules="resetRules"
                >
                
                    <el-form-item label="手机号" prop='mobile'>
                       <el-input v-model="resetForm.mobile" placeholder="请输入手机号" disabled />
                    </el-form-item>
                    <el-form-item label="密码" prop='newPassword'>
                        <el-input v-model="resetForm.newPassword" placeholder="请输入密码"   />
                       
                    </el-form-item>
                     <el-form-item label="确认密码" prop='confirmnewPassword'>
                        <el-input v-model="resetForm.confirmnewPassword" placeholder="请再次输入密码"  />
                     </el-form-item>
                      <el-form-item label="验证码" prop='code'>
                             <el-input v-model="resetForm.code" placeholder="请输入验证码">
                                 <template #append>
                                    <p @click="sendCode">{{seconds==60?'发送验证码':(seconds+'秒')}}</p>
                                 </template>
                             </el-input>
                       </el-form-item>
                   </el-form>
                </div>
                <el-button class="confirm-btn" @click="confirmReset">确认</el-button>
            </div>
            <!-- 1 -->
         </el-dialog>
      </div>
</template>
<script setup>
import {reactive, ref,defineProps,defineEmits, onMounted, watch} from 'vue'
import {storeToRefs} from 'pinia'
// import { useUserStore } from "@/store/modules/user"
import { getToken, } from "@/utils/cache/cookies"
import {sendCaptcha,resetPassword,} from '@/api/pc/index.js'
import {useUserInfoStore} from "@/store/modules/pc"
import { ElMessage } from 'element-plus'
let props=defineProps(['changePasswordVisible'])
let emits=defineEmits(['emitChangePasswordVisible'])
let useUserInfo=useUserInfoStore()
let {userInfo}=storeToRefs(useUserInfo)
let inter=''
let seconds=ref(60)
let resetRef=ref()
let msg=ref('发送验证码')
let validateconfirmnewPassword=(rule,value,callback)=>{
  if(resetForm.newPassword!=resetForm.confirmnewPassword){
    callback(new Error('两次密码请输入一致'))
  }else{
     callback()
  }
}
let validatePassword=(rule,value,callback)=>{
  if(resetForm.newPassword.length<6){
    callback(new Error('密码不低于6位。'))
  }else{
     callback()
  }
}
let resetRules={
      code:[{
      required: true,
      message: '请输入验证码',
      trigger: 'change',
    }],
    newPassword:[ 
        {required: true, message: '请输入密码', trigger: 'change'},
         { validator:validatePassword , trigger: 'blur' }
    ],
     mobile:[ {
      required: true,
      message: '请输入手机号',
      trigger: 'change',
    }],
    confirmnewPassword:[{required: true, message: '请输入确认密码',trigger: 'change',},
     { validator: validateconfirmnewPassword, trigger: 'blur' }
    ]
}

let resetForm=reactive({
    mobile:'',
    newPassword:'',
    confirmnewPassword:'',
    code:''
})
let goCancel=()=>{
   emits('emitChangePasswordVisible')
}
let sendCode=async()=>{
    resetRef.value.validateField(['mobile'],async(vaild)=>{
          if(vaild){
              if(seconds.value!=60){
                    return
                }else{
                    let res=await sendCaptcha({
                        mobile:resetForm.mobile
                    })
                    if(res){
                          ElMessage.success('发送成功')
                    }
                }
                inter=setInterval(()=>{
                    if(seconds.value>0){
                        seconds.value=seconds.value-1
                    }else{
                        seconds.value=60

                        clearInterval(inter)
                    }
                    
                },1000)
          }
    })
    
}
let confirmReset=()=>{
    
        resetRef.value.validate(async(valid)=>{
        if(valid){
             let res=await resetPassword(resetForm)
             if(res){
                ElMessage.success('重置成功')
                Object.assign(resetForm,{
                    mobile:'',
                    newPassword:'',
                    confirmnewPassword:'',
                    code:''
                })
                 resetRef.value.resetFields()
                 goCancel()
             }
        }
    })
}
watch(()=>props.changePasswordVisible,(newval)=>{
  
    Object.assign(resetForm,{
        mobile:userInfo.value.user.mobile
    })
})
</script>
<style scoped lang='scss'>
.header{
    position: relative;
   .header-bg{
    width: 100%;
  }
  .dialog-del{
    position: absolute;
    top:24px;
    right: 24px;
    height: 16px;
    width: 16px;
    cursor: pointer;
  }
}
:deep(.el-dialog__header){
    display: none;
}
:deep(.el-overlay-dialog .el-dialog){
    padding: 0;
    border-radius: 7px;
}
:deep(.el-dialog__body){
    padding: 0;
    border-radius:8px ;
}

.form{
    padding: 8px 24px 0 24px;
    :deep(.el-form-item__content .el-form-item__error){
        position: absolute;
        right: 0;
        left:auto;
        top:-22px
    }
    .forget-password{
        font-size: 12px;
        color: #3071FF;
        text-align: right;
        width: 100%;
        cursor: pointer;
    }
  
}
  .confirm-btn{
        width: 184px;
        height: 48px;
        background: #3071FF;
        font-size: 18px;
        color: #FFFFFF;
        cursor: pointer;
        border-radius: 12px;
        margin-left: 50%;
        margin-bottom: 32px;
        transform: translateX(-50%);
    }
    .forget{
        margin-top: 24px;
        .tab{
            margin-left: 24px;
            font-size: 24px;
            color: #2B2C33;
            font-weight: bold;
            padding-bottom: 8px;
            padding-left: 4px;
            background: url(@/assets/pc/bottom-tab.png) no-repeat;
            background-size: 120px 12px;
            background-position: left 84%;
        }
   
        :deep(.el-input-group__append){
           background: #fff;
          color: #3071FF;
          cursor: pointer;
        }
    }
  
    :deep(.el-input-group__append){
        background: #fff;
        color: #3071FF;
    }

 
</style>
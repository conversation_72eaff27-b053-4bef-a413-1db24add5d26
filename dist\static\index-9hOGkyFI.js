import{_ as D,a as L,b as O,c as R}from"./state-grey-dChy74ls.js";import{b as A}from"./jky-em_s15p3.js";import{T as H}from"./TaskDetail-EgrJsau_.js";import{aE as K,az as P,r as u,h as Q,ag as o,z as m,A as i,B as r,H as x,K as y,Q as t,I as s,P as q,a6 as G,M as v,E as J,O as W}from"./vue-CelzWiMA.js";import{_ as X}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DCRFd5B4.js";import"./element-BwwoSxMX.js";import"./vant-C4eZMtet.js";const Y={class:"jky-main"},Z={class:"radius-box"},j={class:"flex_end",style:{width:"100%"}},ee={class:"radius-box"},te={class:"flex_start"},ae={key:0,src:D,class:"state-icon",alt:""},le={key:1,src:L,class:"state-icon",alt:""},se={key:2,src:O,class:"state-icon",alt:""},oe={key:3,src:R,class:"state-icon",alt:""},ne={class:"flex_start",style:{height:"100%"}},ie={__name:"index",setup(re){const h=K();P();const w=u(0),T=function(l={}){this.taskTitle=l.taskTitle||"",this.reviewUserName=l.reviewUserName||"",this.name=l.name||"",this.pageNum=l.pageNum||1,this.pageSize=l.pageSize||10},d=u(new T({})),C=u(0),N=u([{state:1,taskTitle:"xxx课题研究",createTime:"2025-02-23",deadline:"2025-04-09",num:4},{state:2,taskTitle:"xxx课题研究",createTime:"2025-02-23",deadline:"2025-04-09",num:4},{state:3,taskTitle:"xxx课题研究",createTime:"2025-02-23",deadline:"2025-04-09",num:4},{state:4,taskTitle:"xxx课题研究",createTime:"2025-02-23",deadline:"2025-04-09",num:4},{state:1,taskTitle:"xxx课题研究",createTime:"2025-02-23",deadline:"2025-04-09",num:4}]),c=u(!1),p=u(!1),_=async()=>{let l=d.value;await A(l)},V=()=>{k()},k=()=>{d.value.pageNum=1,_()},z=l=>{h.push(`/jky/lxcp/detailExpert/${l.taskMasterId}`)},U=l=>{d.value.pageNum=l,_()};return Q(()=>{_()}),(l,a)=>{const B=o("el-input"),g=o("el-form-item"),I=o("el-option"),E=o("el-select"),f=o("el-button"),S=o("el-form"),n=o("el-table-column"),F=o("el-table"),M=o("el-pagination"),b=o("el-dialog");return i(),m("div",Y,[r("div",Z,[t(S,{inline:"",model:d.value,class:"search_form flex_start"},{default:s(()=>[t(g,{label:"任务名称",style:{"margin-right":"16px",width:"calc(33% - 16px)"}},{default:s(()=>[t(B,{modelValue:d.value.taskTitle,"onUpdate:modelValue":a[0]||(a[0]=e=>d.value.taskTitle=e),onInput:l.changeUserName,class:"input_40",clearable:"",placeholder:"请输入"},null,8,["modelValue","onInput"])]),_:1}),t(g,{label:"任务状态",style:{"margin-right":"16px",width:"calc(33% - 16px)"}},{default:s(()=>[t(E,{class:"select_40",placeholder:"请选择",onChange:l.changeExtend},{default:s(()=>[(i(!0),m(q,null,G(l.expert_select,e=>(i(),x(I,{key:e.value,value:e.value,label:e.label},null,8,["value","label"]))),128))]),_:1},8,["onChange"])]),_:1}),t(g,{label:"操作",class:"hidden-label",style:{width:"132px","margin-right":"0"}},{default:s(()=>[r("div",j,[t(f,{class:"primary-btn btn_132_40",type:"primary",onClick:V},{default:s(()=>a[5]||(a[5]=[v("查询")])),_:1})])]),_:1})]),_:1},8,["model"])]),r("div",ee,[a[9]||(a[9]=r("div",{class:"flex_between"},[r("p",{class:"text_20_bold"},"任务列表")],-1)),t(F,{data:N.value,style:{width:"100%","margin-top":"16px"},"header-cell-style":{color:"#94959C",fontSize:"16px",background:"#F9F9F9"}},{default:s(()=>[t(n,{type:"index",label:"序号",width:"80"}),t(n,{prop:"taskTitle",label:"课题任务","show-overflow-tooltip":""}),t(n,{prop:"createTime",label:"创建时间"}),t(n,{prop:"deadline",label:"申请截止时间"}),t(n,{prop:"num",label:"指标总数"}),t(n,{prop:"num",label:"申报人数"}),t(n,{prop:"reviewUserNames",label:"任务状态"},{default:s(e=>[r("div",te,[e.row.state==1?(i(),m("img",ae)):e.row.state==2?(i(),m("img",le)):e.row.state==3?(i(),m("img",se)):(i(),m("img",oe)),r("span",{class:J(e.row.state==1?"blue":e.row.state==2?"orange":e.row.state==3?"green":"grey")},W(e.row.state==1?"申报中":e.row.state==2?"待分配":e.row.state==3?"评鉴中":"已结束"),3)])]),_:1}),t(n,{prop:"num",label:"待我初评"}),t(n,{label:"操作",width:"200"},{default:s(e=>[r("div",ne,[t(f,{type:"primary",link:"",style:{"font-size":"16px","font-weight":"400"},onClick:a[1]||(a[1]=$=>p.value=!0)},{default:s(()=>a[6]||(a[6]=[v("查看")])),_:1}),a[8]||(a[8]=r("p",{class:"table-line"},null,-1)),t(f,{type:"primary",link:"",style:{"font-size":"16px","font-weight":"400"},onClick:$=>z(e.row)},{default:s(()=>a[7]||(a[7]=[v("申报列表")])),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"]),t(M,{background:"",layout:"total,prev, pager, next",total:C.value,class:"mt-4","current-page":d.value.pageNum,"page-size":d.value.pageSize,onCurrentChange:U},null,8,["total","current-page","page-size"])]),c.value?(i(),x(b,{key:0,modelValue:c.value,"onUpdate:modelValue":a[2]||(a[2]=e=>c.value=e),title:"新建任务",width:"456px",center:"",class:"my-dialog"},null,8,["modelValue"])):y("",!0),p.value?(i(),x(b,{key:1,modelValue:p.value,"onUpdate:modelValue":a[4]||(a[4]=e=>p.value=e),title:"任务详情",width:"456px",center:"",class:"my-dialog"},{default:s(()=>[t(H,{hasBtn:!1,id:w.value,onClose:a[3]||(a[3]=e=>(p.value=!1,k()))},null,8,["id"])]),_:1},8,["modelValue"])):y("",!0)])}}},xe=X(ie,[["__scopeId","data-v-78ce995b"]]);export{xe as default};

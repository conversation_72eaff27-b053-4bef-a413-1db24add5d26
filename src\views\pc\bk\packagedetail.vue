<template>
   <div class="content-box mt10">
     <div class="header flex_between">
             <div class="left flex_start" @click="$router.go(-1)">
                <el-icon style="font-size:20px"><ArrowLeftBold /></el-icon>
                   <img :src="currentForm.url" alt="">
                    <p>{{currentForm.fileName}}</p>
             </div>
             <!-- <div class="right">
                      <el-button style='height:40px;width:96px;color: #508CFF;'>
                          <img src="@/assets/pc/kl-download.png" alt="">
                          下载
                      </el-button>
                      <el-button style='height:40px;width:116px;color: #508CFF;'>
                          <img src="@/assets/pc/kl-edit.png" alt="">
                          在线编辑
                      </el-button>
             </div> -->
          </div>
        <div class="title">
            授课流程
        </div>
       <div class="process flex_start_0">
             <div class="left">
                 <div class="item flex_start_0" v-for="item in form.process" :key='item.id'>
                   <div class="flex_center flex_column" style="margin-right: 16px;">
                        <img src="@/assets/pc/kl-logo.png" alt="" class="kl-logo">
                        <p class="line"></p>
                   </div>
                    <div class="flex_1">
                        <h3>{{item.processName}}</h3>
                        <div :class="['box','flex_start',item.id==currentForm.id?'active':'']" @click="changeCurrent(item)"> 
                           <img :src="item.url" alt="">
                           <p class="filename">{{item.fileName}}</p>
                           <img src="@/assets/pc/box-checked.png" alt="" class="box-checked" v-show="item.id==currentForm.id" />
                        </div>
                    </div>
                 </div>
             </div>
             <div class="right flex_1">
                <Preview :row='currentForm' />
             </div>
      </div>
   </div>
</template>
<script setup>
import {onMounted, ref} from 'vue'
import {useRoute} from 'vue-router'
import Preview from '@/views/pc/component/resource/preview.vue'
import {packageDetail} from '@/api/pc/bk.js'
let route=useRoute()
let packageId=ref('')
let form=ref({})
let currentForm=ref({})
let changeCurrent=(row)=>{
    currentForm.value=row
    //  res=await resourcePortalDetail({
    //       resourceId:resourceId
    //     })
}
let getDetail=async()=>{
    let res=await packageDetail({
        packageId:packageId.value
      })  
      if(res){
           res.data.process=JSON.parse(res.data.process)
           res.data.process.forEach(item=>{
                let obj=JSON.parse(import.meta.env.VITE_FILE)
                item.url=obj[item.coverFileId]?(import.meta.env.VITE_BASE_URL+'/fs/file?fileId='+obj[item.coverFileId]):(import.meta.env.VITE_BASE_URL+'/fs/file?fileId='+item.coverFileId)
                item.fileType=item.fileType
                item.fileUrl=import.meta.env.VITE_BASE_URL+'/office/editor?fileId='+item.fileId+'&type=EMBEDDED'
                // "https://test.tq-edu.com/api/hasgktltsxt/dxs/1915684026725769218"
           })
           form.value=res.data
           currentForm.value=res.data.process[0]?res.data.process[0]:''
      }
}
onMounted(()=>{
    packageId.value=route.query.packageId
    getDetail()
})
</script>
<style scoped lang='scss'>
.header{
    background: #FFFFFF;
    border-radius: 8px;
    padding: 24px;
    .left{
        cursor: pointer;
      img{
        width: 24px;
        height: auto;
        margin: 0 8px;
      }
      p{
        font-size: 16px;
        color: #2B2C33;
        font-weight: bold;
      }
      
    }
    .right{
        button{
           border-radius: 8px;
           border: 1px solid #508CFF;
           img{
                width: 16px;
                height: auto;
                margin-right: 6px;
           }
        }
    }
}
.title{
    padding: 24px;
    background: #fff;
    font-size: 20px;
    color: #2B2C33;
    margin-top: 10px;
    font-weight: bold;
}
.process{
    
    .left{
         background: #fff;
         width:30%;
         padding: 24px;
         height: calc(100vh - 320px);
         overflow: auto;
         padding: 24px 0;
         .item{
            margin-bottom: 8px;
            padding: 0 24px;
             .kl-logo{
                width: 32px;
                height: 32px;
             
             }
             .line{
                height: 120px;
                width: 1px;
                border-left:2px dashed  #B4B6BE;
                margin-top: 4px;
             }
             h3{
                font-size: 16px;
                color: #2B2C33;
                line-height: 24px;
                margin-bottom: 8px;
                margin-top: 10px;
             }
             .box{
                cursor: pointer;
                border-radius: 12px;
                width: 100%;
                border: 1px solid #E2E3E6;
                padding: 24px;
                font-size: 16px;
               color: #2B2C33;
               position: relative;
                img{
                    width: 32px;
                    height: auto;
                    margin-right: 8px;
                }
                .box-checked{
                    position: absolute;
                    top:10px;
                    right: 0px;
                    width: 20px;
                    height: auto;
                }
                .filename{
                    width: 120px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
             }
               .active{
                    border: 2px solid #23D486;
                }
         }
    }
    .right{
        margin: 24px 0 0 24px;
        background: #fff;
    }
}
</style>
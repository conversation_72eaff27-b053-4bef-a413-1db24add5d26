import { request } from "@/utils/service"
import { method } from "lodash-es"

// 保存我的草稿
export function saveMyDraftApi(data) {
  return request({
    url: "xzs/club-record-draft/me",
    method: "post",
    data
  })
}

// 获得我的草稿
export function getMyDraftApi(params) {
  return request({
    url: "xzs/club-record-draft/me",
    method: "get",
    params
  })
}

// 删除我的草稿
export function deleteMyDraftApi() {
  return request({
    url: `xzs/club-record-draft/me`,
    method: "delete"
  })
}

// 提交日记
export function submitDiaryApi(data) {
  return request({
    url: "xzs/club-record/me",
    method: "post",
    data
  })
}

// 浏览日记
export function viewDiaryApi(recordId) {
  return request({
    url: `xzs/club-record/${recordId}/view`,
    method: "get"
  })
}

// 点赞/取消点赞
export function likeDiaryApi(recordId) {
  return request({
    url: `xzs/club-record/${recordId}/like`,
    method: "post"
  })
}

// 收藏/取消收藏
export function favoriteDiaryApi(recordId, params) {
  return request({
    url: `xzs/club-record/${recordId}/favorite`,
    method: "post",
    params
  })
}

// 推荐
export function recommendDiaryApi(recordId, data) {
  return request({
    url: `xzs/club-record/${recordId}/recommend`,
    method: "post",
    data
  })
}

// 取消推荐
export function cancelRecommendDiaryApi(recordId, params) {
  return request({
    url: `xzs/club-record/${recordId}/recommend`,
    method: "delete",
    params
  })
}

// 获取组（我的）
export function getMyGroupApi() {
  return request({
    url: "xzs/club-group/me",
    method: "get"
  })
}

// 浏览计分（积分模块）
export function viewPointsApi(recordId) {
  return request({
    url: `xzs/club-record/${recordId}/view/points`,
    method: "patch"
  })
}
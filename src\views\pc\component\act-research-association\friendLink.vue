<template>
    <!--  友情链接-->
    <div class="link">
         <p class="title">推荐日记</p>
         <ul v-infinite-scroll="load" class="infinite-list" style="overflow: auto">
            <li class="flex_start" v-for="item in recommendList" :key="item.id">
                <p class="circle"></p>
                <p>{{item.title}}</p>
            </li>
         </ul>
    </div>
</template>
<script setup>
import {ref,onMounted, reactive,defineProps} from 'vue'
let props=defineProps(['recommendList'])
let load=()=>{
    
}
onMounted(async()=>{
  
})
</script>
<style lang="scss" scoped>
.link{
    background: #fff;
    border-radius: 8px;
    
    padding-bottom: 4px;
    .title{
        padding: 18px 0 24px 18px;
        font-size: 18px;
        color: #2B2C33;
        font-weight: bold;
    }
    ul{
        padding-left: 20px;
        li{
            font-size: 15px;
            cursor: pointer;
            color: #2B2C33;
            margin-bottom: 20px;
           .circle{
            width: 4px;
            height: 4px;
            border-radius: 100px;
            background: #D8D8D8;
            margin-right: 12px;
           }
           &:hover{
            color: #508CFF;
           }
        }
    }
}
</style>

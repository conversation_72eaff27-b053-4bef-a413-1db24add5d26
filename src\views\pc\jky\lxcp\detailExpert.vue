<template>
    <div class="lxpj-detail content-box">
        <p class="text_20_bold flex_start mr_40 pointer" style="display:inline-flex;" @click="router.back()"><el-icon><ArrowLeftBold /></el-icon>返回</p> 
        <div class="flex_start mt_20">
            <p class="text_18_bold mr_24">XXX课题研究XXX课题研究</p>
            <div class="flex_start text_14">
                <img v-if="detail.state == 1" src="@/assets/pc/jky/state-blue.png" class="state-icon" alt="">
                <img v-else-if="detail.state == 2" src="@/assets/pc/jky/state-orange.png" class="state-icon" alt="">
                <img v-else-if="detail.state == 3" src="@/assets/pc/jky/state-green.png" class="state-icon" alt="">
                <img v-else src="@/assets/pc/jky/state-grey.png" class="state-icon" alt="">
                <span :class="detail.state == 1 ? 'blue' : detail.state == 2 ? 'orange' : detail.state == 3 ? 'green' : 'grey'">{{detail.state == 1 ? '申报中' : detail.state == 2 ? '待分配' : detail.state == 3 ? '评鉴中' : '已结束'}}</span>
            </div>
        </div>
        <el-table :data="task_list" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F9F9F9"}'>
            <el-table-column type="index" label="序号" width="80"  />
            <el-table-column prop="taskTitle" label="课题名称" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="createTime" label="是否青年专项" />
            <el-table-column prop="reviewUserNames" label="状态">
                <template #default='scope'> 
                    <div class="flex_start">
                        <img v-if="scope.row.state == 1" src="@/assets/pc/jky/state-blue.png" class="state-icon" alt="">
                        <img v-else-if="scope.row.state == 2" src="@/assets/pc/jky/state-orange.png" class="state-icon" alt="">
                        <img v-else-if="scope.row.state == 3" src="@/assets/pc/jky/state-green.png" class="state-icon" alt="">
                        <img v-else src="@/assets/pc/jky/state-grey.png" class="state-icon" alt="">
                        <span :class="scope.row.state == 1 ? 'blue' : scope.row.state == 2 ? 'orange' : scope.row.state == 3 ? 'green' : 'grey'">{{scope.row.state == 1 ? '待初评' : '已初评'}}</span>
                    </div>
                </template>
            </el-table-column> 
            <el-table-column prop="createTime" label="评分" show-overflow-tooltip />
            <el-table-column label="申请表"> 
                <template #default='scope'>
                    <!-- 连个按钮二选一 -->
                    <el-button type="primary" link style="font-size:16px;font-weight:400;" @click="mark_visible = true">评分</el-button>
                    <el-button type="primary" link style="font-size:16px;font-weight:400;" @click="goDetail(scope.row)">查看</el-button>
                </template> 
            </el-table-column>
        </el-table>  
        <el-pagination 
                background
                layout="total,prev, pager, next"
                :total="total"
                class="mt-4"
                :current-page='search_form.pageNum'
                @current-change='currentChange'
            />
        <el-dialog v-model="mark_visible" v-if="mark_visible" title="评分 " width="456px" center class="my-dialog">
            <Mark></Mark>
        </el-dialog>
    </div>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import Mark from './Mark.vue'
    import { useRouter, useRoute } from 'vue-router'
    const router = useRouter()
    const route = useRoute()
    const formObj = function(obj = {}){
        this.name = obj.name || ''
        this.pageNum = obj.pageNum || 1
        this.pageSize = obj.pageSize || 10
    }
    const search_form = ref(new formObj({}))
    const detail = ref({state:2}) // 1:申报中 2:待分配 3: 评鉴中 4:已结束
    const task_list = ref([{}])
    const total = ref(0) 
    const mark_visible = ref(false)

    const goDetail = () => {
        router.push(`/jky/lxcp/application/0`)
    }
    
    onMounted(() => {
    })
</script>
<style lang="scss" scoped>
.lxpj-detail{margin: 16px auto;background: #FFFFFF;padding: 24px;border-radius: 12px;
    .state-icon{width: 14px;margin-right: 5px;}
    .back-icon{width: 16px;margin-right: 8px; }
    .detail-form{
        :deep(.el-form-item__label){height:40px!important;line-height:40px!important;font-weight: 600; font-size: 16px; color: #2B2C33; line-height: 24px; text-align: left;}
    }
}
</style> 
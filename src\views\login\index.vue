<script setup>
import { reactive, ref, onMounted } from 'vue'
import { useUserStore } from "@/store/modules/user"
import { getCaptchaApi } from "@/api/login"
import { router } from '../../router'

const userStore = useUserStore()
const loginRef = ref()
const activeIndex = ref(1)
const changeTab = (arg) => {
  activeIndex.value = arg
}
const loginForm = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  code: ''
})
const loginRules = ref({
  username: [{
    required: true,
    message: '请输入用户名',
    trigger: 'change',
  }],
  password: [{
    required: true,
    message: '请输入密码',
    trigger: 'change',
  }],
  code: [{
    required: true,
    message: '请输入验证码',
    trigger: 'change',
  }],
  confirmPassword: [{
    required: true,
    message: '请输入确认密码',
    trigger: 'change',
  }, {
    validator: (rule, value, callback) => {
      if (value !== loginForm.password) {
        callback(new Error('两次密码不一致'))
      } else {
        callback()
      }
    },
  }]
})
const resetForm = reactive({
  mobile: '',
  password: '',
  confirmPassword: ''
})
const confirm = async () => {
  if (!loginRef.value) return
  await loginRef.value.validate((valid, fields) => {
    if (valid) {
      //移除备课助手AI存储的会话id，因为不同身份的人用同一个会话id会报错！！！
      localStorage.removeItem('bkConversationId')
      userStore.login({
        ...loginForm,
        codeKey: captcha.value.uuid
      }).then((res) => {
        if (!res.successful) { 
          // 登录失败时刷新验证码
          getCaptcha()
          loginForm.code = ''
          loginRules.value.code[0].message = res.data.msg || '请输入验证码'
        }
      })
    } else {
      console.log('验证错误!', fields)
    }
  })
}

const confirmReset = async () => {
  // if (!loginRef.value) return
  //   await loginRef.value.validate((valid, fields) => {
  //     if (valid) {
  //     userStore.login(loginForm)
  //   }
  // })
}

const captcha = ref({})
const getCaptcha = async () => {
  const res = await getCaptchaApi()
  if (res) {
    captcha.value = res.data
  }
}

onMounted(() => {
  getCaptcha()
})

</script>

<template>
  <div class="login-container">

    <div class="header">
      <img src="@/assets/layouts/mobile-login-bg.png" alt="" class="header-bg">
    </div>
    <div class="tabs flex-center-center">
      <div class="tab">
        <span class="active blue1">账号登录</span>
      </div>
    </div>
    <div class="login" v-show="activeIndex == 1">
      <div class="form">
        <el-form label-position="top" label-width="auto" :model="loginForm" :rules="loginRules" ref="loginRef">

          <el-form-item label="用户名" prop='username'>
            <el-input v-model="loginForm.username" placeholder="请输入用户名" />
          </el-form-item>
          <el-form-item label="密码" prop='password'>
            <el-input v-model="loginForm.password" placeholder="请输入密码" show-password />
            <div v-if="false" class="forget-password" @click="changeTab(3)">忘记密码</div>
          </el-form-item>
          <el-form-item label="验证码" prop='code'>
            <el-input v-model="loginForm.code" placeholder="请输入验证码">
              <template #append>
                <img @click="getCaptcha" :src="'data:image/jpeg;base64,' + captcha.img" alt="" class="captcha-img" width="100" height="50">
              </template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <el-button class="confirm-btn" @click="confirm">登录</el-button>
    </div>
    <div class="login forget" v-show="activeIndex == 3">
      <div class="form">
        <el-form label-position="top" label-width="auto" :model="resetForm" :rules="loginRules">

          <el-form-item label="用户名" prop='username'>
            <el-input v-model="resetForm.username" placeholder="请输入用户名" />
          </el-form-item>
          <el-form-item label="密码" prop='password'>
            <el-input v-model="resetForm.password" placeholder="请输入密码" />

          </el-form-item>
          <el-form-item label="确认密码" prop='confirmPassword'>
            <el-input v-model="resetForm.confirmPassword" placeholder="请再次输入密码" />
          </el-form-item>
          <!-- <el-form-item label="验证码" prop='password'>
            <el-input v-model="resetForm.code" placeholder="请输入验证码">
              <template #append>
                <p>发送验证码</p>
              </template>
</el-input>
</el-form-item> -->
        </el-form>
        <div class="back-login flex-start-center" @click="changeTab(1)">
          <el-icon>
            <ArrowLeft />
          </el-icon>
          <span class="back-login-text">返回登录</span>
        </div>
      </div>
      <el-button class="confirm-btn" @click="confirmReset">确认</el-button>
    </div>
  </div>
</template>

<style scoped lang='scss'>
.login-container {
  background-color: #fff;
  height: 100vh;
}

.header {
  position: relative;

  .header-bg {
    width: 100%;
  }

  .dialog-del {
    position: absolute;
    top: 24px;
    right: 24px;
    height: 16px;
    width: 16px;
    cursor: pointer;
  }
}


.tab {
  font-size: 18px;
  color: #6D6F75;
  padding: 24px 0 40px 0;
  z-index: 12;

  div {
    cursor: pointer;
    padding-bottom: 6px;
  }

  .active {
    font-size: 18px;
    color: #3071FF;
    font-weight: bold;
    background: url(@/assets/layouts/title-bg.png) no-repeat;
    background-size: 74px 12px;
    background-position: center 100%;
  }
}

.form {
  padding: 8px 24px 0 24px;

  /* :deep(.el-form-item__content .el-form-item__error) {
    position: absolute;
    right: 0;
    left: auto;
    top: -22px
  } */

  .forget-password {
    font-size: 12px;
    color: #3071FF;
    text-align: right;
    width: 100%;
    cursor: pointer;
  }

  :deep(.el-input-group__append) {
    padding: 0;
  }
}

:deep(.el-form) {
  .el-form-item {
    &__label {
      font-size: 16px;
      color: #2B2C33;
      font-weight: bold;
    }
  }
}

:deep(.el-input) {
  .el-input__inner {
    height: 48px;
    border-radius: 8px;
  }
}

.confirm-btn {
  width: 184px;
  height: 48px;
  background: linear-gradient(90deg, #6F9EFF 0%, #3071FF 100%);
  box-shadow: 0px 4px 6px 0px rgba(66, 96, 253, 0.24);
  font-size: 18px;
  color: #FFFFFF;
  cursor: pointer;
  border-radius: 12px;
  margin-left: 50%;
  margin-bottom: 32px;
  transform: translateX(-50%);
  margin-top: 12px;
}

.forget {
  .tab {
    margin-left: 24px;
    font-size: 24px;
    color: #2B2C33;
    font-weight: bold;
    background: url(@/assets/pc/bottom-tab.png) no-repeat;
    background-size: 74px 12px;
    background-position: left 84%;
  }

  .sendcode {}

  :deep(.el-input-group__append) {
    background: #fff;
    color: #3071FF;
    cursor: pointer;
  }
}

.back-login {
  font-size: 12px;
  color: #3071FF;
  margin-bottom: 16px;
  cursor: pointer;

  .back-login-text {
    margin-left: 4px;
  }

}
</style>
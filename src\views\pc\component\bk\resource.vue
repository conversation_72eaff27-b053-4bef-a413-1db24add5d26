<template>
  <div class="resource-item">
       <div class="desc flex_between">
           <div class="flex_start logo">
               <img :src="row.url" alt="">
               <div>
            
                  <h4>{{row.name}}</h4>
                  <p>{{row.createTime}}</p>
               </div>
           </div>
            <el-dropdown placement="bottom" @command="commandEdit">
                <img src="@/assets/pc/three-circle.png" alt="" style="width:24px;height:auto;outline:none">
                <template #dropdown>
                    <el-dropdown-menu>
                    <el-dropdown-item command='edit'>编辑</el-dropdown-item>
                    <el-dropdown-item command='del'>删除</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
       </div>
       <ul class="belong" @click="goPreview">
          <li class="flex_between">
            <p class="label">学科</p>
            <p class="value">{{row.subjectName?row.subjectName:'-'}}</p>
          </li>
          <li class="flex_between">
            <p class="label">教材</p>
            <p class="value">{{row.editionName?row.editionName:'-'}}</p>
          </li>
          <li class="flex_between">
            <p class="label">章（课）节</p>
            <p class="value">{{row.volumeName?row.volumeName:'-'}}</p>
          </li>
       </ul>
       <div class="item-action flex_between">
           <ul class="flex_start">
               <!-- <li class="has-border flex_start">在线编辑</li>
               <li class="has-border flex_start">共享</li>
               <li>升级</li> -->
           </ul>
           <button :class="['join',row.inSet?'hasjoin':'']" @click="!row.inSet&&joinBasket(row)" :disabled='canJoin'>加入备课篮</button>
       </div>
  </div>
</template>
<script setup>
import {ref,defineProps,defineEmits} from 'vue'
import {useRouter} from 'vue-router'
import {bkResourceDel} from '@/api/pc/bk.js'
import { ElMessage,ElMessageBox } from 'element-plus'
let router=useRouter()
let canJoin=ref(false)
let props=defineProps(['row'])
let emits=defineEmits(['delAction','joinBasket'])
let commandEdit=(val)=>{
    console.log(val)
    if(val=='del'){
        handelDel(props.row.id)
    }else{
        router.push('/bk/res/upload?resourceId='+props.row.id)
    }
}
let joinBasket=(row)=>{
    canJoin.value=true
    setTimeout(()=>{
         canJoin.value=false
    },1500)
   emits('joinBasket',row)
}
let goPreview=()=>{
    router.push('/bk/res/preview?id='+props.row.id)
}
let handelDel=(arg)=>{
 ElMessageBox.confirm(
           '确认删除吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
            
          let res=await bkResourceDel({
            resourceId:arg
          })
          if(res){
            ElMessage.success('删除成功')
            emits('delAction')
          }
        })
        .catch(() => {
      
        })
}
</script>
<style lang="scss" scoped>
.resource-item{
    padding: 24px;
    background: #FFFFFF;
    box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.12);
    border-radius: 12px;
    .logo{
        img{
            width: 48px;
            height: auto;
            margin-right: 16px;
        }
        h4{
            font-size: 18px;
            color: #2B2C33;
            max-width: 180px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        p{
          font-size: 14px;
         color: #94959C;  
         margin-top: 4px;
        }
    }
    .belong{
       padding: 16px 0;
       border-bottom: 1px solid #E2E3E6;
        li{
            margin-bottom: 8px;
            &:last-child{
                margin-bottom: 0;
            }
            .label{
                font-size: 16px;
                color: #6D6F75;
            }
            .value{
                font-size: 16px;
                color: #2B2C33;
            }
        }
        
    }
    .item-action{
        margin-top: 16px;
        ul{
          li{
            font-size: 16px;
            color: #508CFF;
          }  
          .has-border{
            &::after{
                content:'';
                display:block;
                width: 1px;
                height: 18px;
                background: #B4B6BE;
                margin:0 8px
            }
          }
        }
        .join{
            width: 112px;
            height: 40px;
            background: #508CFF;
            box-shadow: 0px 4px 6px 0px rgba(66,96,253,0.24);
            border-radius: 12px;
            font-size: 16px;
            color: #FFFFFF;
            font-weight: bold;
            border:none;
            cursor: pointer;
        }
        .hasjoin{
           opacity: 0.3;
        }
    }
}
</style>
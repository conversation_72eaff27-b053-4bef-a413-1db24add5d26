<script setup>
import { ref, watch, onMounted } from "vue"
import { EchartsUI, useEcharts } from "@/components/Echart"

const chartRef = ref()
const { renderEcharts } = useEcharts(chartRef)

const props = defineProps({
  chartData: {
    type: Object,
    default: () => ({
      data: [],
      color: []
    })
  }
})

watch(
  () => props.chartData,
  () => {
    paintChart()
  },
  { deep: true }
)

onMounted(() => {
  paintChart()
})

const paintChart = () => {
  const data = props.chartData.data
  const yAxisData = data.map(item => item.clubGroupName) 
  const xAxisData = data.map(item => item.points)

  // 计算刻度值
  const maxValue = xAxisData.length > 0 ? Math.max(...xAxisData) : 0
  const minValue = 0 // 假设最小值为0
  
  // 计算最大值向上取整到100的倍数，确保即使无数据时也至少有500的刻度
  const roundedMax = Math.max(Math.ceil(maxValue / 100) * 100, 500)
  
  // 计算需要显示的刻度点（最多5个点，且都是100的倍数）
  const axisLabels = []
  let step = 100
  
  // 如果最大值较大，调整步长确保最多显示5个刻度
  if (roundedMax > 400) {
    // 计算合适的步长，使得最多有5个刻度点
    step = Math.ceil(roundedMax / 5 / 100) * 100
  }
  
  // 生成刻度点
  for (let i = 0; i <= roundedMax; i += step) {
    axisLabels.push(i)
    if (axisLabels.length >= 5) break
  }
  
  renderEcharts({
    backgroundColor: "#fff",
    color: props.chartData.color,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        // 确保params是数组且有元素
        if (!Array.isArray(params) || params.length === 0) {
          return '';
        }
        
        // 找到有效的数据项（第二个系列是实际显示的柱状图）
        const validParam = params.find(param => param.seriesName !== 'assist') || params[0];
        const dataIndex = validParam.dataIndex;
        
        // 确保索引有效
        if (dataIndex === undefined || dataIndex < 0 || dataIndex >= yAxisData.length) {
          return '';
        }
        
        const clubName = yAxisData[dataIndex];
        const value = xAxisData[dataIndex];
        return `${clubName}: ${value}`;
      }
    },
    xAxis: {
      type: "value",
      axisTick: {
        show: false,
      },
      splitLine: {     //网格线
        show: false
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#8DA2B5",
        },
      },
      axisLabel: {
        color: "#8DA2B5",
        fontSize: 12,
        showMaxLabel: true,
        showMinLabel: true,
      },
      max: roundedMax,
      min: minValue,
      interval: step, // 根据计算的步长设置间隔
      data: xAxisData,
    },
    yAxis: {
      type: "category",
      realtimeSort: true,
      data: yAxisData,
      axisLine: {
        show: true,
        lineStyle: {
          color: "#8DA2B5",
        },
      },
      splitLine: {     //网格线
        show: false
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: "#8DA2B5",
      },
    },
    grid: {
      bottom: "1%",
      top: "1%",
      left: "1%",
      right: "8%",
      containLabel: true,
      borderColor: "#8DA2B5",
    },
    series: [
      {
        name: "assist",
        type: "bar",
        stack: "1",
        itemStyle: {
          normal: {
            barBorderColor: "rgba(0,0,0,0)",
            color: "rgba(0,0,0,0)",
          },
          emphasis: {
            barBorderColor: "rgba(0,0,0,0)",
            color: "rgba(0,0,0,0)",
          },
        },
        tooltip: {
          trigger: "none",
        },
        data: [],
      }, //设置两个柱状图进行重叠，第一层柱子设置透明度,由此来实现柱子与坐标轴之间的距离  stack:''设置重叠效果
      {
        type: "bar",
        stack: "1",
        barWidth: 12,
        barBorderRadius: 30,
        itemStyle: {
          barBorderRadius: [0, 8, 8, 0],
          color: function (params) {
            return props.chartData.color[params.dataIndex % 4]
          }
        },
        data: xAxisData
      },
    ],
  })
}
</script>

<template>
  <EchartsUI ref="chartRef" :height="'258px'" />
</template>

<style scoped></style>

<template>
    <div class="publish-result-main">
        <p class="text_17_bold">专家组：专家组1</p>
        <p class="text_16_bold mt_24">专家名单</p>
        <el-table :data="expert_list" class="my-table" style="width: 100%;margin-top:8px" :header-cell-style='{"color":"#94959C",fontSize:"14px",background: "#F8F9FF"}'>
                <el-table-column type="index" label="序号" width="60"  />
                <el-table-column prop="reviewUserName" label="人员名称" show-overflow-tooltip></el-table-column> 
                <el-table-column prop="reviewUserMobile" label="手机号" show-overflow-tooltip />
                <el-table-column prop="reviewSectionName" label="评审部分" show-overflow-tooltip />
                <el-table-column prop="address" label="评审结果" show-overflow-tooltip>
                    <template #default='scope'>
                        <div class="flex_start" style="height:100%">
                            <el-button type="primary" link style="font-size:14px;font-weight:400;" @click="examine_visible = true">查看</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
    </div>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import { result } from '@/api/pc/jky'
    import { useRouter, useRoute } from 'vue-router'
    const router = useRouter()
    const route = useRoute()
    const expert_list = ref([{}])
    const props = defineProps({
        id: 0, // 默认值
    });
    const getResult = async () => {
        let res = await result(route.params.id,{schoolId: props.id})
        if(res){
            expert_list.value = res.data.taskReviews
        }
    }
    onMounted(() => {
        getResult()
    })
</script>
<style lang="scss" scoped>
.publish-result-main{}
</style> 
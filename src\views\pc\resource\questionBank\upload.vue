<template>
     <div class="content-box  upload mt10" >
        <div class="select-form">
             <div class="flex_start title" @click="goCancel">
                <el-icon style="margin-right: 4px;"><ArrowLeft /></el-icon>
                上传题目
            </div>
            <el-form :inline="true" ref='selectRef' :model="formInline" :rules="rules" class="demo-form-inline flex_between flex_wrap" label-width='50px' label-position='top' style="margin-top:24px">
                <el-form-item label="学段" style="width:45%" prop='stage'>
                   <el-select
                        v-model="formInline.stage"
                        placeholder="请选择学段"
                        clearable
                        @change="changeStage"
                    >
                        <el-option :label="item.title" :value="item.id" v-for="item in stages" :key="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="学科" style="width:45%" prop='subjectId'>
                    <el-select
                        v-model="formInline.subjectId"
                        placeholder="请选择学科"
                        clearable
                        @change="changeSubject"
                    >
                         <el-option :label="item.label" :value="item.value" v-for="item in subjects" :key="item.value" />
                    </el-select>
                </el-form-item>
                 <el-form-item label="教材" style="width:45%">
                    <el-select
                        v-model="formInline.editionId"
                        placeholder="请选择教材"
                        clearable
                        @change="changeEdition"
                    >
                      <el-option :label="item.label" :value="item.value" v-for="item in editions" :key="item.value" />
                    </el-select>
                </el-form-item>
                 <el-form-item label="册" style="width:45%">
                    <el-select
                        v-model="formInline.volumeId"
                        placeholder="请选择册"
                        clearable
                        @change='changeVolume'
                    >
                         <el-option :label="item.label" :value="item.value" v-for="item in volumes" :key="item.value" />
                    </el-select>
                </el-form-item>
                 <el-form-item label="章/节" style="width:45%">
                       <el-cascader :options="chapters" v-model="formInline.chapterIds" :props="{
                        checkStrictly: true,
                       
                        }" clearable />
                 
                </el-form-item>
                
                 <el-form-item label="题目类型"  style="width:45%" prop='questionCategory'>
                    <el-select
                        v-model="formInline.questionCategory"
                        placeholder="请选择题目类型"
                        clearable
                    >
                          <el-option :label="item.title" :value="item.id" v-for="item in questionCategory" :key="item.id" />
                    </el-select>
                </el-form-item>
                  <el-form-item label="难度"  style="width:45%" prop='questionDegree'>
                      <el-radio-group v-model="formInline.questionDegree">
                        <el-radio :value="item.id" size="large" v-for="item in questionDegree" :key="item.id" >{{item.title}}</el-radio>
                      </el-radio-group>
                 </el-form-item>
                 <el-form-item label="所属单位"  style="width:45%" prop='belongToTenantId'>
                      <el-select
                        v-model="formInline.belongToTenantId"
                        placeholder="请选择"
                        clearable
                    >
                        <el-option
                            v-for="item in userInfo.userInfo?.userTenants"
                            :key="item.tenantId"
                            :label="item.tenantName"
                            :value="item.tenantId"
                        />
                    </el-select>
                </el-form-item>
            </el-form>
        </div>
        <div class="select-form mt10 question-form">
             <el-form :inline="true" :model="formInline" ref='contentRef' :rules="rules" class="demo-form-inline flex_between flex_wrap" label-width='50px' label-position='top' >
                <el-form-item label="题目" style="margin-bottom:0" prop='questionContent'>
                    <WangEditor :html='formInline.questionContent' @emitHtml='emitContentHtml'  />
                </el-form-item>
             </el-form>
           
        </div>
        <div class="select-form mt10 question-form">
             <el-form :inline="true" :model="formInline" ref='answerRef' :rules="rules" class="demo-form-inline flex_between flex_wrap" label-width='50px' label-position='top' >
                <el-form-item label="答案" style="margin-bottom:0" prop='questionAnswer'>
                    <WangEditor :html='formInline.questionAnswer'  @emitHtml='emitAnswerHtml'/>
                </el-form-item>
             </el-form>
           
        </div>
        <div class="select-form mt10 question-form">
             <el-form :inline="true" :model="formInline" ref="analysisRef" :rules="rules" class="demo-form-inline flex_between flex_wrap" label-width='50px' label-position='top' >
                <el-form-item label="解析" style="margin-bottom:0" prop='questionAnalysis'>
                    <WangEditor :html='formInline.questionAnalysis'  @emitHtml='emitAnalysisHtml'/>
                </el-form-item>
             </el-form>
           
        </div>
        <div class="btn-list flex_end">
             <el-buton class="cancel" @click="goCancel">取消</el-buton>
             <el-buton class="submit" @click="goSubmit">提交</el-buton>
        </div>
    </div>
</template>
<script setup>
import WangEditor from '@/views/pc/component/wangeditor.vue'
import {ref,reactive,onMounted} from 'vue'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {subjectList,editionList,volumeList,chapterTree,questionAdd,questionDetail,questionEdit,} from '@/api/pc/index.js'
import {ElMessage,ElMessageBox} from 'element-plus'
import {storeToRefs} from 'pinia'
import {useRouter,useRoute} from 'vue-router'
let router=useRouter()
let route=useRoute()
let tenantoptions=ref([])
let userInfo=useUserInfoStoreHook()
let {questionCategory,questionDegree,stages}=storeToRefs(userInfo)
let subjects=ref([])
let editions=ref([])
let volumes=ref([])
let chapters=ref([])
let selectRef=ref()
let contentRef=ref()
let answerRef=ref()
let analysisRef=ref()
let questionId=ref('')
let formInline=reactive({
    stage:'',
    belongToTenantId:'',
    subjectId:'',
    editionId:'',
    volumeId:'',
    chapterIds:[],
    chapter1Id:'',
    chapter2Id:'',
    questionCategory:'',
    questionDegree:'',
    questionContent:'',
     questionAnswer:"",
    questionAnalysis:'',
})
let changeStage=async()=>{
     editions.value=[]
     volumes.value=[]
     chapters.value=[]
    Object.assign(formInline,{
        subjectId:'',
        editionId:'',
        volumeId:'',
        chapterIds:[]
    })
    let res=await subjectList({
        stage:formInline.stage
    })
    if(res){
        subjects.value=res.data
    }
}
onMounted(async()=>{
    questionId.value=route.query.questionId
    // getUserTenant()
   if(questionId.value){
      let res=await questionDetail({
        questionId:questionId.value
      })  
      if(res){
        let chapterIds=[]
        Object.assign(formInline,{
            stage:res.data.stage.toString(),
            subjectId:res.data.subjectId,
            editionId:res.data.editionId?res.data.editionId:"",
            volumeId:res.data.volumeId?res.data.volumeId:"",
            chapterIds:[],
            chapter1Id:'',
            chapter2Id:'',
            belongToTenantId:res.data.belongToTenantId,
            questionCategory:res.data.questionCategory.toString(),
            questionDegree:res.data.questionDegree.toString(),
            questionContent:res.data.questionContent,
            questionAnswer:res.data.questionAnswer,
            questionAnalysis:res.data.questionAnalysis,
        })
        if(res.data.chapter1Id){
            chapterIds[0]=res.data.chapter1Id
        }
        if(res.data.chapter2Id){
            chapterIds[1]=res.data.chapter2Id
        }
        Object.assign(formInline,{
            chapterIds:chapterIds
        })
          let res0=await subjectList({
                stage:formInline.stage
            })
            if(res0){
                subjects.value=res0.data
            }
           let res1=await editionList({
                stage:formInline.stage,
                subjectId:formInline.subjectId,
            })
            if(res1){
                editions.value=res1.data
            }
            if(formInline.editionId){
                let res2=await volumeList({
                    editionId:formInline.editionId
                })
                if(res2){
                    volumes.value=res2.data
                }
            }
             if(formInline.volumeId){
                let res3=await chapterTree({
                    volumeId:formInline.volumeId
                })
                if(res3){
                    chapters.value=res3.data
                }
             }
          
      } 
   } 
})
let changeSubject=async()=>{
      volumes.value=[],
      chapters.value=[]
       Object.assign(formInline,{
        editionId:'',
        volumeId:'',
        chapterIds:[]
    })
    let res=await editionList({
        stage:formInline.stage,
        subjectId:formInline.subjectId,
    })
    if(res){
        editions.value=res.data
    }
}
// let getUserTenant=async()=>{
//     let res=await userTenant()
//     if(res){
      
//         tenantoptions.value=res.data
//     }
// }
let changeEdition=async()=>{
    chapters.value=[]
    
     Object.assign(formInline,{
        volumes:'',
        chapterIds:[]
    })
     let res=await volumeList({
        editionId:formInline.editionId
     })
     if(res){
        volumes.value=res.data
     }
}
let changeVolume=async()=>{
     Object.assign(formInline,{
        chapterIds:[]
    })
    let res=await chapterTree({
        volumeId:formInline.volumeId
    })
    if(res){
        chapters.value=res.data
    }
}

let rules={
    stage:[{ required: true, message: '请选择学段', trigger: 'blur' }],
    subjectId:[{ required: true, message: '请选择学科', trigger: 'blur' }],
    questionCategory:[{ required: true, message: '请选择题目类型', trigger: 'blur' }],
    questionDegree:[{ required: true, message: '请选择难度', trigger: 'blur' }],
    questionContent:[{ required: true, message: '请输入题目', trigger: 'blur' }],
    questionAnswer:[{ required: true, message: '请输入答案', trigger: 'blur' }],
    questionAnalysis:[{ required: true, message: '请输入解析', trigger: 'blur' }],
    belongToTenantId:[{ required: true, message: '请选择所属单位', trigger: 'blur' }],
}
let emitContentHtml=(val)=>{
  Object.assign(formInline,{
    questionContent:val
  })
}
let emitAnswerHtml=(val)=>{
 Object.assign(formInline,{
    questionAnswer:val
  })
}
let emitAnalysisHtml=(val)=>{
  Object.assign(formInline,{
    questionAnalysis:val
  })
}
let goCancel=()=>{
    router.go(-1)
}
let goSubmit=()=>{
    console.log()
    let p1=new Promise((resolve,reject)=>{
         selectRef.value.validate((valid, fields) => {
            if (valid) {
               resolve()
            } else {
              reject()
            }
        })
    })
      let p2=new Promise((resolve,reject)=>{
        contentRef.value.validate((valid, fields) => {
            if (valid) {
               resolve()
            } else {
              reject()
            }
        })
    })
      let p3=new Promise((resolve,reject)=>{
         answerRef.value.validate((valid, fields) => {
            if (valid) {
               resolve()
            } else {
              reject()
            }
        })
    })
     let p4=new Promise((resolve,reject)=>{
         analysisRef.value.validate((valid, fields) => {
            if (valid) {
               resolve()
            } else {
              reject()
            }
        })
    })
    Promise.all([p1,p2,p3,p4]).then(data=>{
        console.log(11)
        let params={
                stage:formInline.stage,
                subjectId:formInline.subjectId,
                editionId:formInline.editionId,
                volumeId:formInline.volumeId,
                chapter1Id:formInline.chapterIds[0]?formInline.chapterIds[0]:'',
                chapter2Id:formInline.chapterIds[1]?formInline.chapterIds[1]:'',
                questionCategory:formInline.questionCategory,
                questionDegree:formInline.questionDegree,
                questionContent:formInline.questionContent,
                questionAnswer:formInline.questionAnswer,
                questionAnalysis:formInline.questionAnalysis,
                belongToTenantId:formInline.belongToTenantId
        }
    
        ElMessageBox.confirm(
            questionId.value?'确认编辑该题目吗?':'确认上传该题目吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
              if(questionId.value){
                params.questionId=questionId.value
              let res=await questionEdit(params)
                if(res){
                    ElMessage.success('编辑成功')
                    goCancel()
                }
              }else{
                let res=await questionAdd(params)
                if(res){
                    ElMessage.success('上传成功')
                    goCancel()
                }
              }
          
              
        })
        .catch(() => {
      
        })
    }).catch(err=>{
        console.log(err)
    })
}
</script>
<style lang="scss" scoped>
 .select-form{
    border-radius: 8px;
    padding: 24px 24px 0 24px;
    background: #fff;
    .title{
        font-size: 20px;
        color: #2B2C33;
        font-weight: bold;
        cursor: pointer;
    }
    :deep(.el-form--inline .el-form-item){
        margin-right: 0;
        margin-bottom: 24px;
    }
    :deep(.el-form-item--label-top .el-form-item__label){
        font-size: 16px;
        color: #2B2C33;
        font-weight: bold;
    }
    :deep(.el-select__wrapper){
        height: 48px;
    }
    :deep(.el-radio.el-radio--large .el-radio__label){
        font-size: 16px;color: #2B2C33;
    }
    :deep(.el-radio.el-radio--large .el-radio__inner){
        height: 24px;
        width: 24px;
        &::after{
            transform: translate(-50%,-50%) scale(2.4);
        }
    }
    :deep(.el-radio__input.is-checked .el-radio__inner){
        background: rgb(36,198,152);
        border-color: rgb(36,198,152);
    }
    :deep(.el-cascader){
        width: 100%;
    }
    :deep(.el-input__wrapper){
        height: 48px;
    }
 }
 .question-form{
    padding: 24px;
 }
 .btn-list{
    padding: 16px 0 24px 0;
    .cancel{
        font-weight: bold;
        font-size: 18px;
        color: #6D6F75;
        line-height: 26px;
        padding: 11px 42px;
        border-radius: 12px;
        border: 1px solid #E6E6E6;
        background: #fff; cursor: pointer;
    }
    .submit{
        padding: 11px 46px;
         line-height: 26px;
        background: #508CFF;
        border-radius: 12px;
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        margin-left: 16px;
        cursor: pointer;
    }
 }
</style>
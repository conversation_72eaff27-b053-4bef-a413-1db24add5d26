import { request } from "@/utils/service"

// 顶部统计
export function getTopStatisticsApi() {
  return request({
    url: "xzs/club-record/me/profile",
    method: "get"
  })
}

// 详情
export function getDetailApi(recordId) {
  return request({
    url: `xzs/club-record/me/${recordId}`,
    method: "get"
  }) 
}

// 个人日记列表
export function getMyDiaryListApi(params) {
  return request({
    url: "xzs/club-record/me",
    method: "get",
    params
  })
}

// 社长日记列表
export function getDirectorDiaryListApi(params) {
  return request({
    url: "xzs/club-record/me/director-record",
    method: "get",
    params
  })
}

// 社长推荐的日记列表
export function getDirectorRecommendDiaryListApi(params) {
  return request({
    url: "xzs/club-record/me/director-recommended-record",
    method: "get",
    params
  })
}

// 导师推荐的日记列表
export function getTutorRecommendDiaryListApi(params) {
  return request({
    url: "xzs/club-record/me/tutor-recommended-record",
    method: "get",
    params
  })
}

// 我的收藏
export function getMyFavoriteListApi(params) {
  return request({
    url: "xzs/club-record/me/favorite",
    method: "get",
    params
  })
}

// 发表评价（评论）
export function publishCommentApi(recordId, data) {
  return request({
    url: `xzs/club-record/${recordId}/comment`,
    method: "post",
    data
  })
}

// 编辑日记
export function editDiaryApi(recordId, data) {
  return request({
    url: `xzs/club-record/me/${recordId}`,
    method: "put",
    data
  })
}

// 我的日记 - 位置
export function getMyDiaryLocationApi(params) {
  return request({
    url: "xzs/club-record/me/location",
    method: "get",
    params
  })
}

// 我的收藏 - 位置
export function getMyFavoriteLocationApi(params) {
  return request({
    url: "xzs/club-record/me/favorite/location",
    method: "get",
    params
  })
}

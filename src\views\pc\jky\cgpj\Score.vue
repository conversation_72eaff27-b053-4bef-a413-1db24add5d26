<!-- 成绩单 -->
<template>
    <div class="cgpj-score-main">
        <ul class="flex_start tab-ul">
            <li v-for="item in tab_list" :key="item.id" :class="current_tab === item.id? 'active-tab' : ''" @click="current_tab = item.id">{{item.title}}</li>
        </ul>
        <div class="flex_start flex_wrap cgpj-score-search" v-if="current_tab === 1">
            <el-select class="select_40 mr_24" placeholder="全部组别" @change="changeExtend">
                <el-option v-for="item in expert_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
            <el-select class="select_40 mr_24" placeholder="所在单位" @change="changeExtend">
                <el-option v-for="item in expert_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
            <el-select class="select_40 mr_24" placeholder="申报类型" @change="changeExtend">
                <el-option v-for="item in expert_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
            <el-select class="select_40 mr_24" placeholder="全部学科" @change="changeExtend">
                <el-option v-for="item in expert_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
            <el-select class="select_40 mr_24" placeholder="全部学段" @change="changeExtend">
                <el-option v-for="item in expert_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
            <el-select class="select_40 mr_24" placeholder="是否青年专项" @change="changeExtend">
                <el-option v-for="item in expert_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
            <div class="flex_start mr_24 mb_16">
                <el-input class="input_40" placeholder="请输入得分" style="width:146px;"></el-input>
                <span class="mlr_5 grey">—</span>
                <el-input class="input_40" placeholder="请输入得分" style="width:146px;"></el-input>
            </div>
            <el-button type="primary" class="green_btn btn_132_40 mb_16 mr_12">导出成绩单</el-button>
            <el-button type="primary" class="primary-btn btn_132_40 mb_16">导出上报材料</el-button>
        </div>
        <div v-else class="flex_between">
            <div class="flex_start">
                <el-input v-model="search_form.input" class="input_40" style="width:192px;" placeholder="请输入优秀率">
                    <template #append>%</template>
                </el-input>
                <el-input v-model="search_form.input" class="input_40" style="width:192px;" placeholder="请输入合格率">
                    <template #append>%</template>
                </el-input>
                <el-button type="primary" class="primary-btn btn_132_40 mr_12">生成通过名单</el-button>
            </div>
            <el-button type="primary" class="green_btn btn_132_40 mr_12">导出</el-button>
        </div>
        <el-table v-if="current_tab === 1" :data="score_list" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F9F9F9"}'>
            <el-table-column prop="taskTitle" label="组内序号" min-width="90" show-overflow-tooltip></el-table-column>
            <el-table-column prop="taskTitle" label="申报编码" min-width="90" show-overflow-tooltip></el-table-column>
            <el-table-column prop="taskTitle" label="学段" show-overflow-tooltip></el-table-column>
            <el-table-column prop="createTime" label="学科" show-overflow-tooltip />
            <el-table-column prop="deadline" label="课题名称" min-width="90" show-overflow-tooltip />
            <el-table-column prop="deadline" label="所在单位" min-width="90" show-overflow-tooltip />
            <el-table-column prop="deadline" label="主持人 " show-overflow-tooltip />
            <el-table-column prop="deadline" label="是否青年专项" show-overflow-tooltip min-width="120" />
            <el-table-column prop="deadline" fixed="right" label="总得分" show-overflow-tooltip min-width="120" />
        </el-table>
        <el-table v-else :data="score_list" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F9F9F9"}'>
            <el-table-column prop="taskTitle" label="申报编码" min-width="90" show-overflow-tooltip></el-table-column>
            <el-table-column prop="taskTitle" label="学段" show-overflow-tooltip></el-table-column>
            <el-table-column prop="createTime" label="学科" show-overflow-tooltip />
            <el-table-column prop="deadline" label="课题名称" min-width="90" show-overflow-tooltip />
            <el-table-column prop="deadline" label="所在单位" min-width="90" show-overflow-tooltip /> 
            <el-table-column prop="deadline" label="主持人" show-overflow-tooltip />
            <el-table-column prop="deadline" label="是否青年专项" show-overflow-tooltip min-width="120" />
            <el-table-column prop="deadline" fixed="right" label="等级" show-overflow-tooltip min-width="120" />
        </el-table>
        <el-pagination background layout="total,prev, pager, next" :total="total" class="mt-4" :current-page='search_form.pageNum' @current-change='currentChange' />
        <div class="flex_center" v-if="current_tab === 2">
            <el-button class="plain-btn btn_96_48" type="plain" @click="emit('close')">取消</el-button>
            <el-button class="primary-btn btn_96_48" type="primary" @click="submitClick">确认</el-button>
        </div>
    </div>
</template>

<script setup>
import { reactive, ref, computed, onBeforeMount, onMounted, nextTick, watch, toRefs } from 'vue'
import { toRaw } from "@vue/reactivity"
import Group from '@/views/pc/component/jky/Group.vue'
import Judges from './Judges.vue'

const formObj = function(obj = {}){
    this.groupId = obj.groupId || ''
    this.pageNum = obj.pageNum || 1
    this.pageSize = obj.pageSize || 10
}
const search_form = reactive(new formObj())
const total = ref(0)
const tab_list = ref([{id:1,title:'总成绩单'},{id:2,title:'评鉴通过名单'}])
const current_tab = ref(1)
const score_list = ref([])

// 生命周期钩子
onMounted(() => {  
}) 

</script>


<style lang="scss" scoped>
.cgpj-score-main{
    .tab-ul{ border-bottom: 1px solid #E2E3E6; margin-bottom:20px; display:inline-flex;
        li{ margin-right: 32px; color: #94959C; padding: 6px 0; cursor: pointer; }
        .active-tab{ font-weight: bold; color: #508CFF; border-bottom: 2px solid #508CFF; }
    }
    .cgpj-score-search{
        :deep(.el-select){width: calc(25% - 24px);margin-bottom: 16px;}
    }
}
:deep(.el-input-group__append){background: transparent; border: none; box-shadow: none; padding: 0; right: 24px;}
</style>    
<template>
    <div class="question-item">
       
        <div class="top flex_between_0">
            <div class="question-type">{{questionCategoryMap[row.questionCategory]}}</div>
            <div class="flex_start action">
                <a href="javascript:;" @click="clipBoard">复制题目</a>
                <div class="degree flex_start">
                    <p>难度:</p>
                    <div v-for="item in 3" :key="item" class="star-box">
                        <img src="@/assets/pc/star.png" alt="" v-if="item>row.degree">
                        <img src="@/assets/pc/stared.png" alt="" v-else>
                    </div>
                </div>
                <p class="used-num">被引量 {{row.references}}</p>
            </div>
        </div>
        <div class="question-content">
            <div class="flex_between">
                <p class="label">题目</p>
                <el-dropdown @command="commandAction" v-show="row.isUpdateCapable&&row.isUpdateCapable&&currentInfo.type=='teacher'">
                   <img src="@/assets/pc/three-circle.png" alt="" style="width:24px;height:auto;outline:none" />
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item command='edit' v-show="row.isUpdateCapable">编辑</el-dropdown-item>
                            <el-dropdown-item command='del' v-show="row.isUpdateCapable">删除</el-dropdown-item>
                            
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
            
            <div class="html" ref="htmlRef" v-html="row.questionContent" :id="'html'+row.id">
           
            </div>
        </div>
        <div class="flex_between see-analysis">
            <div class="flex_start answer" @click="isopen=!isopen">
                <p>答案与解析</p>
                <el-icon  v-show="!isopen"><ArrowDown /></el-icon>
                <el-icon  v-show="isopen"><ArrowUp /></el-icon>
            </div>
            <el-button class="join-tj" v-show="!row.isInQuestionBasket&&currentInfo.type=='teacher'" @click="joinBasket(row)">加入题集篮</el-button>
            <el-button class="hasjoin-tj" v-show="row.isInQuestionBasket&&currentInfo.type=='teacher'">加入题集篮</el-button>
        </div>
        <div v-show="isopen">
          <div class="question-content">
            <p class="label">答案</p>
            <div class="html" v-html="row.questionAnswer">
               
            </div>
         </div>
          <div class="question-content">
            <p class="label">解析</p>
            <div class="html" v-html="row.questionAnalysis">
           
            </div>
         </div>
        </div>
        <el-image-viewer
          v-if="showPreview"
          :url-list="srcList"
          @close="showPreview = false"
      />
    </div>
</template>
<script setup>
import {ref,defineProps,defineEmits, onMounted, nextTick} from 'vue'
import {ElMessage} from 'element-plus'
import {useRouter} from 'vue-router'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'
let userInfo=useUserInfoStoreHook()
let {currentInfo,questionCategoryMap}=storeToRefs(userInfo)
let emits=defineEmits(['emitDel','emitJoinBasket'])
let router=useRouter()
let isopen=ref(false)
let htmlRef=ref()
let showPreview=ref(false)
let srcList=ref([])
let props=defineProps(['row'])
let commandAction=(val)=>{
     if(val=='edit'){
         router.push('/resource/question/upload?questionId='+props.row.id)
     }else{
        emits('emitDel',props.row.id)
     }
}
const joinBasket=(row)=>{
  emits('emitJoinBasket',row)
}
onMounted(()=>{

      document.getElementById('html'+props.row.id).addEventListener('click',(e)=>{
        if(e.target.tagName=='IMG'){
             showPreview.value=true
             srcList.value=[e.target.src]
        }
    })

})
const clipBoard=async()=>{
   
     const richTextContent =htmlRef.value.innerHTML;
            try {
                // 创建一个 ClipboardItem 对象，包含 HTML 和纯文本内容
                await navigator.clipboard.write([
                    new ClipboardItem({
                        'text/html': new Blob([richTextContent], { type: 'text/html' }),
                        'text/plain': new Blob([htmlRef.value.textContent], { type: 'text/plain' })
                    })
                ]);
                console.log('富文本已复制到剪贴板');
                ElMessage.success('已复制到剪贴板')
            } catch (err) {
                console.error('复制失败:', err);
            }
}
</script>
<style lang="scss" scoped>
.question-item{
  background: #fff;
  border-radius: 8px;
  padding: 24px 0;
  .top{
    .question-type{
        font-weight: 600;
        font-size: 16px;
        color: #FFFFFF;
        padding: 6px 20px;
        background: #FF9E32;
        border-radius: 0px 12px 12px 0px;
    }
    .action{
        a{
            font-size: 14px;
            color: #508CFF;
            text-decoration:underline;
            margin-right: 32px;
        }
        .degree{
            margin-right: 32px;
            font-size: 14px;
            color: #94959C;
            .star-box{
                margin-left: 8px;
                img{
                    width: 12px;
                    height: auto;
                }
            }
        }
        .used-num{
            font-size: 14px;
            color: #94959C;
            margin-right: 24px;
        }
    }
  }
  .see-analysis{
    padding: 0 24px;
    margin-top: 24px;
    .answer{
        font-size: 16px;
       color: #508CFF;
       cursor: pointer;
    }
    .join-tj{
        width: 128px;
        height: 40px;
        background: #508CFF;
        border-radius: 12px;
        color: #fff;
        font-size: 16px;
        color: #FFFFFF;
    }
    .hasjoin-tj{
        width: 128px;
        height: 40px;
        background:  #508CFF;
        border-radius: 12px;
        color: #fff;
        font-size: 16px;
        opacity: 0.3;
        color: #FFFFFF;
    }
  }
  .question-content{
    margin-top: 24px;
    padding: 0 24px;
    .label{
        font-size: 16px;
        color: #94959C;
        margin-bottom: 8px;
    }
  }
}
:deep(.html){
    img{
        max-width: 100% !important;
     
      }
    .question{
        margin-bottom:16px;
      img{
        max-width: 100% !important;
        max-height: 100% !important;
      }
    
    }
     ol{
      list-style-type:upper-alpha;
      li{
        margin-bottom: 8px;
      }
    }
}
</style>
<template>
    <div class="publish-add-main">
        <ul class="flex_start tab-ul text_16">
            <li v-for="item in tab_list" :key="item.id" :class="current_tab == item.id? 'active-tab' : ''" @click="changeTab(item)">{{item.title}}</li>
        </ul>
        <!-- 基本信息 -->
        <el-form v-show="current_tab == 1" :model="add_form" ref="addRef" :rules="rules" class="search_form">
            <el-form-item label="课题任务名称" prop="taskName" style="margin-right: 16px;">
                <el-input v-model="add_form.taskName" class="input_48" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="申请截止时间" prop="applicationDeadline" style="margin-right: 16px;">
                <el-date-picker v-model="add_form.applicationDeadline" class="my-date-picker" :disabled-date="disabledDate"  type="date" value-format="YYYY-MM-DD" placeholder="选择日期"> </el-date-picker>
            </el-form-item>
            <el-form-item label="备注" style="margin-right: 16px;">
                <el-input v-model="add_form.remarkContent" type="textarea" class="my-textarea2" :rows="4" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="材料清单" style="margin-right: 16px;" prop="taskFiles">
                <p class="text_12 grey upload-tip">（支持上传pdf、word、excel，单个文件大小不超过10MB）</p>
                <div class="upload-file-box">
                    <div class="flex_between mb_16" v-for="(item,index) in add_form.taskFiles" :key="index" style="width:100%;">
                        <p class="flex_start text_16 blue" style="width:calc(100% - 24px)"><img src="@/assets/pc/jky/file.png" class="file-icon" alt=""><span class="ellipsis1 file-name">{{item.name}}</span></p>
                        <img @click="removeFile(index)" src="@/assets/pc/del-file.png" class="file-close-icon pointer" alt="">
                    </div>
                </div>
                <el-upload
                    :file-list="add_form.taskFiles"
                    action="Fake Action"
                    show-file-list="false"
                    multiple
                    :http-request="uploadFile"
                    :on-preview="handlePictureCardPreview"
                    :on-remove="handleRemove"
                    :before-upload='beforeUpload'
                >
                    <!-- :on-change="changeFile" -->

                    <el-button type="plain" class="upload-plain">本地上传</el-button>
                </el-upload>
            </el-form-item>
        </el-form>
        <!-- 指标设置 -->
        <div v-show="current_tab == 2" class="mb_24">
            <div class="flex_between mb_12">
                <p class="text_16_bold">单位列表</p>
                <div class="flex_end">
                    <el-upload
                        :file-list="quota_file"
                        action="Fake Action"
                        show-file-list="false"
                        multiple
                        :http-request="uploadTemplate"
                        :on-preview="handlePictureCardPreview"
                        :on-remove="handleRemove"
                        :before-upload='beforeUpload'
                    >
                        <!-- :on-change="changeFile" -->
                        <el-button type="primary" class="green_btn btn_80_32">导入</el-button>
                    </el-upload>
                    <p class="download-btn" @click="downloadClick">模板下载</p>
                </div>
            </div>
            <el-table :data="add_form.taskTargets" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F8F9FF"}'>
                <el-table-column prop="unitCode" label="代码" show-overflow-tooltip width="80"></el-table-column> 
                <el-table-column prop="unitName" label="单位名称" />
                <el-table-column prop="targetNumber" label="指标总数" fixed="right"> 
                    <template #default='scope'>
                        <el-input-number v-model="scope.row.targetNumber" :min="1" :precision="0" class="input_40" />
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="flex_end">
            <el-button class="plain-btn btn_96_48" type="plain" @click="emit('close')">取消</el-button>
            <el-button class="primary-btn btn_96_48" type="primary" @click="submitClick">发布</el-button>
        </div>
    </div>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import {uploadFileFunc} from '@/utils/pc-upload.js'
    import {ElMessage,ElLoading,ElMessageBox} from 'element-plus'
    import download from '@/utils/download.js'
    import { templateImport, taskAdd, taskEdit, taskDetail } from '@/api/pc/jky'
    const formObj = function(obj = {}){
        this.taskName = obj.taskName || ''
        this.applicationDeadline = obj.applicationDeadline || ''
        this.remarkContent = obj.remarkContent || ''
        this.taskFiles = obj.taskFiles || []
        this.taskTargets = obj.taskTargets || []
    }
    const tab_list = ref([{id:1,title:'基本信息'},{id:2,title:'指标设置'}])
    const current_tab = ref(1)
    const props = defineProps({
        id: 0, // 默认值
    });
    const addRef = ref(null)
    const rules = ref({
        taskName:[{required: true,message: '请输入课题任务名称',trigger: 'blur'}],
        applicationDeadline:[{required: true,message: '请选择申请截止时间',trigger: 'blur'}]
    })
    const quota_file = ref([])

    const disabledDate = (date) => {
        return date < new Date() - 8.64e7
    }

    const emit = defineEmits(['close'])
    const add_form = ref(new formObj({}))
    const quota_list = ref([])
    
    const changeTab = (item) => {
        current_tab.value = item.id
    }
    function getQueryParams(src,type) {
        const url = new URL(src);
        const params = url.searchParams.get(type);
        return params
    }
    const getExtensionString = (str) => {
        var index = str.lastIndexOf('.')
        // 如果找到了第一个"."并且它不在字符串的起始处或结尾处
        if (index != -1 && index != 0 && index + 1 != str.length) {
            return str.substr(index + 1)
        } else {
            return ''
        }
    }
    const beforeUpload=(file)=>{
        let type = getExtensionString(file.name)
        let allowType=['doc','docx','pdf','xls','xlsx','txt']
        if (!allowType.includes(type)) {
            ElMessage.warning('请上传以下格式的文件：' + allowType.join('、'));
            return false;
        }
        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
            ElMessage.warning('文件大小不能超过10MB');
            return false;
        }
        return true;
    }
    const uploadFile = (file) => {
        uploadFileFunc(file,(url) => {
            add_form.value.taskFiles.push({
                url:url,
                name:file.file.name,
                fileId:getQueryParams(url,'fileId')
            })
            console.log(add_form.value.taskFiles,'上传文件')

        })
    }
    const uploadTemplate = async (file) => {
        console.log(file.file,'fileyyyyyy')
        // 创建 FormData 对象
        const formData = new FormData();
        formData.append('file', file.file); // 直接添加文件对象
        let res = await templateImport(formData)
        if(res){
            add_form.value.taskTargets = res.data
        }
    }
    const removeFile = (index) => {
        add_form.value.taskFiles.splice(index, 1)
    }
    const getDetail = async () => {
        let res = await taskDetail(props.id)
        if(res){
            add_form.value = new formObj(res.data)
            add_form.value.taskFiles.forEach(item => {
                item.name = item.fileName
            })
        }
    }
    const downloadClick = async () => {
        download.excel('/jky/task-target','模板')
    }
    const submitClick = async () => {
        if (!addRef.value) return
        await addRef.value.validate(async(valid, fields) => {
            if (valid) {
                console.log(add_form.value,'add_form.value')
                return
                let res = await (props.id && props.id != 0 ? taskEdit(add_form.value, props.id) : taskAdd(add_form.value))
                if(res){
                    ElMessage.success('发布成功')
                    emit('close')
                }
            } else {
                console.log(current_tab.value,'current_tab.value')
                if(current_tab.value == 1) return
                ElMessage.error('切换到基本信息页面，填写必填信息')
                console.log('error submit!', fields)
            }
        })
        
    }
    const closeClick = () => {
        emit('close')
    }
    onMounted(() => {
        if(props.id && props.id != 0){
            getDetail()
        }
    })
</script>
<style lang="scss" scoped>
.publish-add-main{
    .upload-file-box{border: 1px solid #E2E3E6;border-radius: 8px;padding: 14px 16px 0;flex:1;min-height: 48px;margin-right: 8px;width: calc(100% - 104px );}
    .file-icon{width: 17px;height: auto;margin-right: 8px;}
    .file-close-icon{width: 24px;height: auto;}
    .file-name{width: calc(100% - 40px);}
    .upload-tip{position: absolute;top: -29px;left: 74px;}
    .tab-ul{border-bottom: 1px solid #E2E3E6;margin-bottom:20px;display:inline-flex;
        li{margin-right: 32px;color: #94959C;padding: 6px 0;cursor: pointer;}
        .active-tab{font-weight: bold;color: #508CFF;border-bottom: 2px solid #508CFF;}
    }
    .download-btn{font-weight: 400; font-size: 16px; color: #508CFF; line-height: 24px; text-align: left; font-style: normal; text-decoration-line: underline;margin-left: 16px;cursor: pointer;}
}
</style>
<style lang="scss">
    .el-upload-list{display: none;}
    .el-dialog__headerbtn{top: 14px;right: 14px;}
    .el-dialog .el-table__body-wrapper{max-height: 300px;overflow: auto;}
</style>
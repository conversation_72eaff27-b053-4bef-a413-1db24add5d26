<template>
    <div class="allocate-main">
        <el-select class="select_48" v-model="teacherId" placeholder="请选择" @change="changeExtend">
            <el-option v-for="item in stage_list" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
        <div class="flex_center mt_24">
            <el-button class="plain-btn btn_96_48" type="plain" @click="emit('close')">取消</el-button>
            <el-button class="primary-btn btn_96_48" type="primary" @click="submitClick">确认</el-button>
        </div>
    </div>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    const teacherId = ref('')
    onMounted(() => {
    })
</script>
<style lang="scss" scoped>
.allocate-main{

}
</style>
<script setup>
import { ref, reactive, onMounted } from "vue"
import { useRouter } from "vue-router"
import defaultAvatar from "@/assets/layouts/default-avatar.png"
import { getHistoryMessageApi } from "@/api/mobile/message"
import dayjs from "dayjs"

const router = useRouter()
const activeTab = ref(0)
const handleClickTab = (tab) => {
  activeTab.value = tab.name
  page.pageNum = 1
  error.value = false
  finished.value = false
  refreshLoading.value = false
  getMessageList()
}

const loading = ref(false)
const finished = ref(false)
const error = ref(false)
const messageList = ref([])

const page = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 打开筛选日期弹窗
const filterDateSheetVisible = ref(false)
const activeFilterDate = ref([])
const activeFilterDateForHtml = ref("")
const minDate = ref()
const maxDate = ref()
const openFilterDateSheet = () => {
  // 获取年月日
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1
  const day = now.getDate()
  maxDate.value = new Date(year, month, day)
  minDate.value = new Date(year, month - 6, day)
  filterDateSheetVisible.value = true
}

const handleFilterDateConfirm = (value) => {
  activeFilterDate.value = value
  activeFilterDateForHtml.value = `${dayjs(value?.[0]).format("YYYY-MM-DD")}至${dayjs(value?.[1]).format("YYYY-MM-DD")}`
  filterDateSheetVisible.value = false
  page.pageNum = 1
  finished.value = false
  getMessageList()
}

const filterDateSheetRef = ref()
const resetFilterDate = () => {
  filterDateSheetRef.value?.reset()
  activeFilterDate.value = []
  activeFilterDateForHtml.value = ""
  filterDateSheetVisible.value = false
  page.pageNum = 1
  finished.value = false
  getMessageList()
}


const getMessageList = async () => {
  loading.value = true
  try {

    const res = await getHistoryMessageApi({
      pageNum: page.pageNum,
      pageSize: page.pageSize,
      beginCreateTime: activeFilterDate.value?.[0] && dayjs(activeFilterDate.value?.[0]).format('YYYY-MM-DD') || '',
      endCreateTime: activeFilterDate.value?.[1] && dayjs(activeFilterDate.value?.[1]).format('YYYY-MM-DD') || ''
    })

    if (res) {
      if (page.pageNum === 1) {
        messageList.value = res.data.list
      } else {
        messageList.value = [...messageList.value, ...res.data.list]
      }

      page.total = res.data.total
      refreshLoading.value = false
      loading.value = false

      if (messageList.value.length >= page.total) {
        finished.value = true
      } else {
        page.pageNum++
      }

    }

  } catch (error) {
    console.log(error)
  }
}

const refreshLoading = ref(false)
const onRefresh = () => {
  page.pageNum = 1
  error.value = false
  finished.value = false
  refreshLoading.value = true
  getMessageList()
}

const toDetail = (item) => {
  router.push({
    path: "/message/detail",
    query: { id: item.noticeId }
  })
}

onMounted(() => {
  getMessageList()
})
</script>

<template>
  <div class="message">
    <div class="back-area m-pt10 m-pb10 flex-between-center">
      <div class="flex-start-center pointer" @click="router.back()">
        <van-icon name="arrow-left" size="14" />
        <span class="m-text-14-20-400 grey1">返回</span>
      </div>
      <div class="m-text-16-24-400 dark1">公告通知</div>
      <div style="width: 42px;"></div>
    </div>
    <div class="flex-between-center sort-area pt12 pl24 pb12">
      <div class="flex-start-center">
        <div class="sort-item flex-start-center" @click="openFilterDateSheet">
          <span class="m-text-14-22-400 grey1 pr5">{{ activeFilterDateForHtml || "筛选日期区间" }}</span>
          <img src="@/assets/mobile/personal-space/arrow-down.png" alt="" width="8" height="6" />
        </div>
        <img class="m-ml12" src="@/assets/mobile/personal-space/reset.png" alt="" width="18" height="16"
          @click="resetFilterDate" />
      </div>
    </div>
    <section class="message-list">
      <van-pull-refresh v-model="refreshLoading" @refresh="onRefresh">
        <van-list v-model:loading="loading" :finished="finished" :finished-text="'没有更多了'" :error="error"
          @load="getMessageList" :immediate-check="false" :error-text="'加载失败，请稍后再试'">
          <div class="message-item pointer" v-for="item in messageList" :key="item.id" @click="toDetail(item)">
            <div class="flex-between-end item-top">
              <div class="flex-start-center">
                <img :src="defaultAvatar" alt="头像" class="avatar m-mr8" width="32" height="32">
                <div class="flex-column-center-start">
                  <span class="name grey1 m-text-14-20-600">{{ item.userName }}</span>
                  <span class="time grey5 m-text-12-18-400">{{ item.postTime }}</span>
                </div>
              </div>
              <div class="flex-end-center pb3">
                <div class="notice-range">{{ item.noticeRangeLabel }}</div>
              </div>
            </div>
            <div class="item-bottom">
              <img src="@/assets/mobile/message/notification.png" alt="通知" class="notification m-mr4" width="12"
                height="14">
              <span class="title-container m-text-14-20-400 flex-start-center">
                <span class="title">{{ item.noticeTitle }}</span>
                <img v-if="!item.isViewed" class="new-icon" src="@/assets/mobile/message/new.png" alt="" width="14" height="14">
              </span>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </section>

    <van-calendar ref="filterDateSheetRef" v-model:show="filterDateSheetVisible" type="range"
      @confirm="handleFilterDateConfirm" safe-area-inset-bottom :min-date="minDate" :max-date="maxDate"
      allow-same-day />
  </div>
</template>

<style lang="scss" scoped>
@use "@/views/mobile/styles/custom-global-mobile-style.scss";

.message {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding-top: 44px;

  .header-area {
    width: 100%;
    height: 57px;
    padding: 12px 20px 14px;
    background-color: #fff;

    :deep(.van-tabs) {
      width: 100%;

      .van-tabs__nav {
        background: transparent;
      }

      .van-tab {
        color: #6D6F75;
        font-size: 14px;

        &--active {
          background: linear-gradient(270deg, #508cff 0%, #535dff 100%), #508cff;
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent !important;
          font-weight: 600 !important;
        }
      }

      .van-tabs__wrap {
        height: 30px;
      }

      .van-tabs__line {
        background: linear-gradient(270deg, #508cff 0%, #535dff 100%), #508cff;
        border-radius: 3px;
      }
    }
  }

  .back-area {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background-color: #fff;
    padding: 0 16px;
  }

  .message-list {
    height: 100%;
    overflow-y: auto;
    padding: 0 12px 12px;

    .message-item {
      width: 100%;
      padding: 10px 14px 14px;
      background-color: #fff;
      border-radius: 6px;
      margin-bottom: 12px;

      .item-top {
        padding-bottom: 5px;
        border-bottom: 1px solid #e2e3e4;
      }

      .item-bottom {
        width: 100%;
        padding-top: 14px;
        display: flex;
        align-items: flex-start;

        .notification {
          margin-top: 3px; // 微调图标位置以与文本第一行居中
          flex-shrink: 0;
        }

        .title-container {
          width: calc(100% - 16px);
        }

        .title {
          max-width: calc(100% - 14px);
          padding-right: 5px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .notice-range {
        padding: 1px 10px;
        font-size: 14px;
        line-height: 18px;
        color: #508CFF;
        background-color: #DFEAFF;
        border-radius: 10px;
      }
    }
  }

  ::-webkit-scrollbar {
    display: none;
  }
}
</style>
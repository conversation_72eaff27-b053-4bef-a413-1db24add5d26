<template>
      <div>
         <el-dialog title="" v-model="loginVisibleShow" width="456px" :close-on-click-modal='false'>
           
                <div class="header">
                  <img src="@/assets/pc/logo.png" alt="" class="header-bg">
                  <img src="@/assets/pc/dialog-del.png" alt="" class="dialog-del" @click="goCancel"/>
                </div>
                
                <div class="tab flex_around">
                    <div :class="[activeIndex==1||activeIndex==4?'active':'']" @click="changeTab(1)">账号登录</div>
                    <div :class="[activeIndex==2?'active':'']" @click="changeTab(2)">微信登录</div>
                </div>
             <div class="login" v-show="activeIndex==1">
                <div class="form">
                  <el-form
                    label-position="top"
                    label-width="auto"
                    :model="loginForm"
                    :rules="loginRules"
                    ref="loginRef"
                >
                
                    <el-form-item label="用户名" prop='username'>
                       <el-input v-model="loginForm.username" placeholder="请输入用户名" />
                    </el-form-item>
                    <el-form-item label="密码" prop='password'>
                        <el-input v-model="loginForm.password" placeholder="请输入密码"  show-password />
                        <div class="flex_between" style="width:100%">
                            <div class="forget-password" style="text-align:left" @click="changeTab(4)">验证码登录</div>
                            <div class="forget-password" @click="changeTab(3)">忘记密码</div>
                        </div>
                    </el-form-item>
                   </el-form>
                </div>
                <el-button class="confirm-btn" @click="confirm">确认</el-button>
            </div>
                 <div class="login" v-show="activeIndex==4">
                <div class="form">
                  <el-form
                    label-position="top"
                    label-width="auto"
                    :model="codeForm"
                    :rules="codeRules"
                    ref="codeRef"
                >
                
                    <el-form-item label="手机号" prop='mobile'>
                       <el-input v-model="codeForm.mobile" placeholder="请输入用户名" />
                    </el-form-item>
                    <el-form-item label="验证码" prop='code'>
                        <el-input v-model="codeForm.code" placeholder="请输入验证码"  >
                            <template #append>
                                <p @click="sendYzCode" style="cursor:pointer">{{codeSeconds==60?'发送验证码':(codeSeconds+'秒')}}</p>
                            </template>
                          </el-input>
                         <div class="forget-password" style="text-align:left" @click="changeTab(1)">密码登录</div>
                    </el-form-item>
                   </el-form>
                </div>
                <el-button class="confirm-btn" @click="confirmCode">确认</el-button>
            </div>
            <div class="qrcode flex_center flex_column" v-if="activeIndex==2">
                
                 <div class="qrcodeImg">
                       <img :src="qrBase64" alt="" style="width:200px;height:200px">
                       <div class="expired flex_column flex_center" @click="reloadQR" v-show="qrloginStatus=='EXPIRED'">
                            <p style="font-size:18px">二维码已过期</p>
                            <el-icon style="font-size:20px;color:#508CFF;margin-top:24px"><RefreshRight /></el-icon>
                            <p style="font-size: 16px;color: #508CFF;">点击刷新</p>
                       </div>
                   </div>
                <p>微信扫一扫，立即登录</p>
            </div>
            <div class="login forget" v-show="activeIndex==3">
                <div class="tab">
                    <div>忘记密码</div>
                </div>
                <div class="form">
                  <el-form
                    label-position="top"
                    label-width="auto"
                    ref="resetRef"
                    :model="resetForm"
                    :rules="resetRules"
                >
                
                    <el-form-item label="手机号" prop='mobile'>
                       <el-input v-model="resetForm.mobile" placeholder="请输入手机号" />
                    </el-form-item>
                    <el-form-item label="密码" prop='newPassword'>
                        <el-input v-model="resetForm.newPassword" placeholder="请输入密码"   />
                       
                    </el-form-item>
                     <el-form-item label="确认密码" prop='confirmnewPassword'>
                        <el-input v-model="resetForm.confirmnewPassword" placeholder="请再次输入密码"  />
                     </el-form-item>
                      <el-form-item label="验证码" prop='code'>
                             <el-input v-model="resetForm.code" placeholder="请输入验证码">
                                 <template #append>
                                    <p @click="sendCode">{{seconds==60?'发送验证码':(seconds+'秒')}}</p>
                                 </template>
                             </el-input>
                       </el-form-item>
                   </el-form>
                </div>
                <el-button class="confirm-btn" @click="confirmReset">确认</el-button>
            </div>
            <!-- 1 -->
         </el-dialog>
      </div>
</template>
<script setup>
import {reactive, ref} from 'vue'
import {storeToRefs} from 'pinia'
// import { useUserStore } from "@/store/modules/user"
import { getToken, } from "@/utils/cache/cookies"
import {sendCaptcha,resetPassword,getQrLoginCode,sendCaptchaToLogin} from '@/api/pc/index.js'
import {useUserInfoStore} from "@/store/modules/pc"
import { ElMessage } from 'element-plus'
let status=ref('EXPIRED')
// let userStore=useUserStore()
let useUserInfo=useUserInfoStore()
let { loginVisibleShow,qrloginStatus } = storeToRefs(useUserInfo)
let dialogTableVisible=ref(false)
let loginRef=ref()
let inter=''
let seconds=ref(60)
let resetRef=ref()
let interval=''
let authorizeKey=ref('')
let qrBase64=ref('')
let msg=ref('发送验证码')
let codeForm=reactive({
    mobile:'',
    code:''
})
let codeRef=ref('')
let codeInter=''
let codeSeconds=ref(60)
let codeRules={
    mobile:[ {
      required: true,
      message: '请输入手机号',
      trigger: 'change',
    }],
    code:[ {
      required: true,
      message: '请输入验证码',
      trigger: 'change',
    }]
}
let goCancel=()=>{
    loginVisibleShow.value=false
    activeIndex.value=1
    clearInterval(interval)
}
let activeIndex=ref(1)
let reloadQR=async()=>{
  qrloginStatus.value='PENDING'
 let res=await getQrLoginCode()
       if(res){
        authorizeKey.value=res.data.key
        qrBase64.value='data:image/jpeg;base64,'+res.data.image
        interval=setInterval(()=>{
              useUserInfo.qrlogin(authorizeKey.value)
              if(getToken()&&getToken()!='null'){
                activeIndex.value=1
                // ElMessage.success('登录成功')
                loginVisibleShow.value=false
                clearInterval(interval)
              }
              if(qrloginStatus.value=='EXPIRED'){
                  //登录失效
                 clearInterval(interval)
              }
        },1500)
    }
}
let changeTab=async(arg)=>{
    if(arg==activeIndex.value&&arg==2){
        return
    }
    activeIndex.value=arg
    clearInterval(interval)
    if(arg==2){
      reloadQR()
    }
    codeRef.value?.resetFields()
    loginRef.value?.resetFields()
    resetRef.value?.resetFields()
}

let loginForm=reactive({
    username:'',
    password:''
})
let loginRules={
    username:[ {
      required: true,
      message: '请输入用户名',
      trigger: 'change',
    }],
    password:[ {
      required: true,
      message: '请输入密码',
      trigger: 'change',
    }]
}
let validateconfirmnewPassword=(rule,value,callback)=>{
  if(resetForm.newPassword!=resetForm.confirmnewPassword){
    callback(new Error('两次密码请输入一致'))
  }else{
     callback()
  }
}
let validatePassword=(rule,value,callback)=>{
  if(resetForm.newPassword.length<6){
    callback(new Error('密码不低于6位。'))
  }else{
     callback()
  }
}
let resetRules={
      code:[{
      required: true,
      message: '请输入验证码',
      trigger: 'change',
    }],
    newPassword:[ {
      required: true,
      message: '请输入密码',
      trigger: 'change',
    }, { validator: validatePassword, trigger: 'blur' }],
     mobile:[ {
      required: true,
      message: '请输入手机号',
      trigger: 'change',
    }],
    confirmnewPassword:[{required: true, message: '请输入确认密码',trigger: 'change',},
     { validator: validateconfirmnewPassword, trigger: 'blur' }
    ]
}
let resetForm=reactive({
    mobile:'',
    newPassword:'',
    confirmnewPassword:'',
    code:''
})
let confirmCode=async()=>{
      codeRef.value.validate(async(valid)=>{
        if(valid){
            useUserInfo.codeLogin(codeForm)
            loginVisibleShow.value=false
             codeRef.value?.resetFields()
        }
    })
}
let confirmReset=()=>{
    
        resetRef.value.validate(async(valid)=>{
        if(valid){
             let res=await resetPassword(resetForm)
             if(res){
                ElMessage.success('重置成功')
                Object.assign(resetForm,{
                    mobile:'',
                    newPassword:'',
                    confirmnewPassword:'',
                    code:''
                })
                 resetRef.value.resetFields()
                 activeIndex.value=1
             }
        }
    })
}
let sendYzCode=async()=>{
       codeRef.value.validateField(['mobile'],async(vaild)=>{
          if(vaild){
              if(codeSeconds.value!=60){
                    return
                }else{
                    let res=await sendCaptcha({
                        mobile:codeForm.mobile
                    })
                    if(res){
                          ElMessage.success('发送成功')
                    }
                }
                codeInter=setInterval(()=>{
                    if(codeSeconds.value>0){
                        codeSeconds.value=codeSeconds.value-1
                    }else{
                        codeSeconds.value=60

                        clearInterval(codeInter)
                    }
                    
                },1000)
          }
    })
}
let sendCode=async()=>{
    resetRef.value.validateField(['mobile'],async(vaild)=>{
          if(vaild){
              if(seconds.value!=60){
                    return
                }else{
                    let res=await sendCaptcha({
                        mobile:resetForm.mobile
                    })
                    if(res){
                          ElMessage.success('发送成功')
                    }
                }
                inter=setInterval(()=>{
                    if(seconds.value>0){
                        seconds.value=seconds.value-1
                    }else{
                        seconds.value=60

                        clearInterval(inter)
                    }
                    
                },1000)
          }
    })
    
}
let confirm=async()=>{
    if (!loginRef.value) return
  await loginRef.value.validate((valid, fields) => {
    if (valid) {
      
      useUserInfo.login(loginForm)
      loginVisibleShow.value=false
    } else {
      console.log('error submit!', fields)
    }
  })
}
</script>
<style scoped lang='scss'>
.header{
    position: relative;
   .header-bg{
    width: 100%;
  }
  .dialog-del{
    position: absolute;
    top:24px;
    right: 24px;
    height: 16px;
    width: 16px;
    cursor: pointer;
  }
}
:deep(.el-dialog__header){
    display: none;
}
:deep(.el-overlay-dialog .el-dialog){
    padding: 0;
    border-radius: 7px;
}
:deep(.el-dialog__body){
    padding: 0;
    border-radius:8px ;
}
.tab{
    font-size: 18px;
    color: #6D6F75;
    padding: 24px 0  8px 0;
    z-index: 12;
    div{
        cursor: pointer;
        padding-bottom: 6px;
        // &::after{
        //     content: '';
        //     display: block;
        //     width: 74px;
        //     height: 12px;
           
        //     transform: translateY(-6px);
        //      z-index: 1
        // }
    }
    .active{
        font-size: 18px;
        color: #3071FF;
        font-weight: bold;
         background: url(@/assets/pc/bottom-tab.png) no-repeat;
         background-size: 74px 12px;
         background-position: center 100%;
     
    }
}
.form{
    padding: 8px 24px 0 24px;
    :deep(.el-form-item__content .el-form-item__error){
        position: absolute;
        right: 0;
        left:auto;
        top:-22px
    }
    .forget-password{
        font-size: 12px;
        color: #3071FF;
        text-align: right;
        width: 100%;
        cursor: pointer;
    }
  
}
  .confirm-btn{
        width: 184px;
        height: 48px;
        background: #3071FF;
        font-size: 18px;
        color: #FFFFFF;
        cursor: pointer;
        border-radius: 12px;
        margin-left: 50%;
        margin-bottom: 32px;
        transform: translateX(-50%);
    }
    .forget{
        .tab{
            margin-left: 24px;
            font-size: 24px;
            color: #2B2C33;
            font-weight: bold;
            background: url(@/assets/pc/bottom-tab.png) no-repeat;
            background-size: 74px 12px;
            background-position: left 84%;
        }
        .sendcode{
            
        }
        :deep(.el-input-group__append){
           background: #fff;
          color: #3071FF;
          cursor: pointer;
        }
    }
    .qrcode{
        padding: 10px 0 56px 0;
        img{
            width: 200px;
            height: 200px;
        }
        p{
            font-size: 16px;
            color: #508CFF;
            margin-top: 16px;
        }
            .qrcodeImg{
                width: 200px;
                height: 200px;
                position: relative;
                .expired{
                    width: 200px;
                height: 200px;
                position: absolute;
                top:0;
                left:0;
                cursor: pointer;
                background: #FFFFFF;
                opacity: 0.9;
            }
    }
    }
    :deep(.el-input-group__append){
        background: #fff;
        color: #3071FF;
    }

 
</style>
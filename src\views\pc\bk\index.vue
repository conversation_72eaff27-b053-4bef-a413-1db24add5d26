<template>
    <div class="content-box  question mt10" >
        <div class="flex_between top-desc">
            <h3>备课资源</h3>
            <div class="ipt flex_start">
                 <input type="text" class="flex_1" placeholder="搜索课程" v-model="form.name" @keyup.enter="searchByName">
                 <el-icon style="font-size:20px;color: #B4B6BE" @click="searchByName"><Search /></el-icon>
            </div>
            <div class="action flex_start">
                  <div class="flex_start go-bk" @click="$router.push('/bk/res/group')">
                      <img src="@/assets/pc/setting.png" alt="">
                      <p>备课组管理</p>
                  </div>
                  <div class="flex_center go-upload" @click="goUpload">
                        <img src="@/assets/pc/upquestion.png" alt="">
                        <p>上传</p>
                  </div>
            </div>
        </div>
        <div class="container flex_start_0">
             <div class="left">
                  <!-- <div class="location flex_start">
                         <el-icon style="font-size:16px"><Location /></el-icon>
                         <p>小学语文</p>
                  </div> -->
                    <el-tree
                    style="max-width: 600px"
                    :load="loadNode"
                    lazy
                    :props="defaultProps"
                    @node-click="handleNodeClick"
                />
             </div>
             <div class="right flex_1 flex_column flex_start_0">
                   <div class="flex_between action">
                          <div class="flex_start go-create">
                              <!-- <img src="@/assets/pc/add-bk.png" alt="">
                              <p>创建备课资源</p> -->
                          </div>
                          <div class="flex_start select">
                            <el-select
                                v-model="form.belongToTenantId"
                                placeholder="全部"
                                size="large"
                                style="width: 160px;margin-right:16px"
                                @change="changeResCategory"
                                clearable
                            >
                                <el-option
                                    v-for="item in options"
                                    :key="item.tenantId"
                                    :label="item.tenantName"
                                    :value="item.tenantId"
                                />
                            </el-select>
                               <el-select
                                 clearable
                                v-model="form.category"
                                placeholder="全部"
                                size="large"
                                @change="changeResCategory"
                                style="width: 160px"
                            >
                                <el-option
                                    label="全部"
                                    value=""
                                />
                                <el-option
                                    v-for="item in ResourceCategory"
                                    :key="item.id"
                                    :label="item.title"
                                    :value="item.id"
                                />
                    </el-select>
                          </div>
                   </div>
                   <ul class="flex_between bk-list flex_wrap">
                        <li v-for="item in tableData" :key="item.id" ><Resource :row='item'  @delAction='delAction' @joinBasket='joinBasket'/></li>
                      
                   </ul>
                    <el-pagination
                        background
                        v-if="total!=0"
                        layout="total,prev, pager, next"
                        :total="total"
                        v-model:page-size='form.pageSize'
                        class="mt-4"
                        @current-change='currentChange'
                 />
                 <div  class="basket">
                     <img src="@/assets/pc/bk-basket.png" alt="" @click="drawerShow=true;getBasket()" />
                     <div>
                        {{basketNum}}
                     </div>
                 </div>
                 
             </div>
              
        </div>

      <el-drawer
        v-model="drawerShow"
        title="I have a nested table inside!"
        direction="rtl"
        size="480"
    >
        <template #header>
            <div class="flex_start header">
                <h3>备课篮</h3>
                <div class="flex_start clear" @click="clearAll">
                    <img src="@/assets/pc/clear-bk.png" alt="">
                    <p>清空</p>
                </div>
            </div>
        </template>
        <ul class="drawer-list">
            <li class="flex_between" v-for="item in baskets" :key="item.id">
                <div class="drawer-left flex_start">
                    <img :src="item.url" alt="">
                    <h3>{{item.resourceName}}</h3>
                </div>
                <img src="@/assets/pc/del-bk.png" alt="" class="del-bk" @click="removeBasket(item)">
            </li>
        </ul>
        <div class="drawer-edit flex_center" @click="goEditProcess">
            编辑授课流程
        </div>
    </el-drawer>
    </div>
    <!-- <choicePersonDialog /> -->
</template>
<script setup>
import {onActivated, onMounted, reactive, ref} from 'vue'
import {Search } from '@element-plus/icons-vue'
import Resource from '@/views/pc/component/bk/resource.vue'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'
import {useRouter} from 'vue-router'
import choicePersonDialog from '@/views/pc/component/bk/choicePersonDialog.vue'
import { ElMessage,ElMessageBox } from 'element-plus'
let userInfo=useUserInfoStoreHook()
import {questionList,subjectList,editionList,volumeList,chapterTree,resources,userTenant} from '@/api/pc/index.js'
import {bkResource,basketAdd,basketDel,basketList,basketDelAll} from '@/api/pc/bk.js'

let options=ref([])
let {questionCategory,questionDegree,stages,ResourceCategory}=storeToRefs(userInfo)
let router=useRouter()
let tableData=ref([])
let basketNum=ref(0)
let treeData=ref([
       {
            value:'10',
            label:'幼儿园',
            type:'stage',
        },
        {
            value:'20',
            label:'小学',
             type:'stage',
        },
        {
            value:'30',
            label:'初中',
             type:'stage',
        },{
            value:'40',
            label:'高中',
             type:'stage',
        }
])
let drawerShow=ref(false)
let total=ref(0)
let baskets=ref([])
let form=reactive({
    pageNum:1,
    pageSize:4,
    name:'',
    tenantId:'',
    category:'',
    stage:'',
    subjectId:'',
    editionId:'',
    volumeId:'',
    chapter1Id:'',
    chapter2Id:'',
    orderBy:'',
    orderRule:'',
    belongToTenantId:""
})
let goUpload=()=>{
    router.push('/bk/res/upload')
}

let delAction=()=>{
    getList()
}
let goEditProcess=()=>{
     router.push('/bk/res/add')
}
let removeBasket=async(row)=>{
  let res=await basketDel({
    resourceId:row.id
  })
  if(res){
    basketNum.value=basketNum.value-1
    baskets.value=baskets.value.filter(item=>{
        return item.resourceId!=row.resourceId
    })
    getList()
  }
}
let getBasket=async()=>{
    let res=await basketList()
    if(res){
       
         res.data.forEach(item=>{
            let obj=JSON.parse(import.meta.env.VITE_FILE)
            item.url=obj[item.coverFileId]?(import.meta.env.VITE_BASE_URL+'/fs/file?fileId='+obj[item.coverFileId]):(import.meta.env.VITE_BASE_URL+'/fs/file?fileId='+item.coverFileId)
        })
        baskets.value=res.data
        basketNum.value=res.data.length
    }
}
let currentChange=(val)=>{
   Object.assign(form,{
    pageNum:val
   })
   getList()
}
let clearAll=()=>{
       ElMessageBox.confirm(
            '确认清空备课篮吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
            let res=await basketDelAll()
            if(res){
                ElMessage.success('清空成功')
               baskets.value=[]
             basketNum.value=0
                 getList()
            }
        })
        .catch(() => {
        }) 
}
let joinBasket=async(row)=>{
   
    let res=await basketAdd({
        resourceId:row.id
    })
    if(res){
        ElMessage.success('已添加到备课篮')
        row.inSet=true
        basketNum.value=basketNum.value+1
    }
  
}
let changeResCategory=()=>{
 Object.assign(form,{
    pageNum:1
   })
   getList()
}
let searchByName=(e)=>{
 Object.assign(form,{
    pageNum:1
   })
   getList()
}
let getUserTenant=async()=>{
    let res=await userTenant()
    if(res){
      
         res.data.unshift({
            isAdmin:false,
            tenantId:'0',
            tenantName:"个人"
        })
        res.data.unshift({
            isAdmin:false,
            tenantId:'',
            tenantName:"全部"
        })
        
        options.value=res.data
    }
}
let loadNode=async(node,resolve)=>{
   
    if(node.level==0){
        resolve([...treeData.value])
    } else if(node.level==1){
       let res1=await subjectList({
        stage:node.data.value
       })
       if(res1){
         resolve([...res1.data])
       }
    }else if(node.level==2){
       
        let res1=await editionList({
             stage:node.parent.data.value,
            subjectId:node.data.value
        })
        if(res1){
         resolve([...res1.data])
        }
    }else if(node.level==3){
        let res1=await volumeList({
            editionId:node.data.value
        })
        if(res1){
          resolve([...res1.data])
        }
    }else if(node.level==4){
        let res1=await chapterTree({
            volumeId:node.data.value
        })
        if(res1){
          resolve([...res1.data])
        }
    }else if(node.level==5){
         resolve(node.data.children?node.data.children:[])
    }else{
        resolve([])
    }

}
const handleNodeClick=(val1,val2)=>{
   
    Object.assign(form,{
        stage:"",
        subjectId:'',
        editionId:'',
        volumeId:'',
        chapter1Id:'',
        chapter2Id:'',
        pageSize:4,
        pageNum:1
    })
    if(val2.level==1){
         Object.assign(form,{
          stage:val1.value
         })
    }else if(val2.level==2){
         Object.assign(form,{
            subjectId:val1.value
         })
    }else if(val2.level==3){
         Object.assign(form,{
            editionId:val1.value
         })
    }else if(val2.level==4){
         Object.assign(form,{
            volumeId:val1.value
         })
    }else if(val2.level==5){
         Object.assign(form,{
            chapter1Id:val1.value
         })
    }else if(val2.level==6){
         Object.assign(form,{
            chapter2Id:val1.value
         })
    }
     getList()
}
let defaultProps={
      children: 'children',
}
let getList=async()=>{
    let res=await bkResource(form)
    if(res){
        res.data.list.forEach(item=>{
            let obj=JSON.parse(import.meta.env.VITE_FILE)
            item.url=obj[item.coverFileId]?(import.meta.env.VITE_BASE_URL+'/fs/file?fileId='+obj[item.coverFileId]):(import.meta.env.VITE_BASE_URL+'/fs/file?fileId='+item.coverFileId)
        })
        tableData.value=res.data.list
        total.value=res.data.total
    }
}
onMounted(()=>{
    getUserTenant()
    getList()
    getBasket()
    
})
</script>
<style lang="scss" scoped>
.content-box{
    background: #fff;
    padding: 24px;
    border-radius: 8px;
    .top-desc{
        width: 100%;
        h3{
            font-size: 20px;
            color: #2B2C33;
            font-weight: bold;
        }
        .ipt{
            width: 40%;
            border-radius: 24px;
            border: 1px solid #E2E3E6;
            padding: 10px 16px;
            cursor: pointer;
            input{
                border:none
            }
        }
        .action{
            .go-bk{
                font-size: 16px;
                color: #508CFF;
                margin-right: 24px;
                cursor: pointer;
                img{
                   width: 15px;
                    height: auto;
                    margin-right: 4px;
                }
            }
            .go-upload{
                width: 96px;
                height: 40px;
                background: #508CFF;
                border-radius: 8px;
                border: 1px solid #508CFF;
                font-size: 16px;
                color: #FFFFFF;
                font-weight: bold;
                cursor: pointer;
                img{
                    width: 16px;
                    height: auto;
                    margin-right: 4px;
                }
            }
        }
    }
    .container{
        margin-top: 16px;
        .left{
            border-right: 1px solid #E2E3E6;
            padding-left: 16px;
            width: 30%;
            .location{
                padding-bottom: 16px;
                font-size: 14px;
                color: #6D6F75;
                border-bottom: 1px solid  #E2E3E6;
            }
        }
        .right{
            padding-left: 24px;
            position: relative;
            .action{
             .go-create{
                font-size: 16px;
                color: #508CFF;
                font-weight: bold;
                img{
                    width: 16px;
                    height: auto;
                    margin-right: 8px;
                }
                p{
                    line-height: 16px;
                }
              }
            }
            .bk-list{
                li{
                    width: 49%;
                    margin-top: 16px;
                }
            }
           
            .basket{
               position: absolute;
                right: -130px;
                width: 110px;
                bottom: 40px;
                cursor: pointer;
                img{
                    width: 108px;
                    height: auto;
                }
                div{
                    width: 20px;
                    height: 20px;
                    background: #E72C4A;
                    border: 1px solid #F5F7FB;
                    font-size: 12px;
                    color: #fff;
                    position: absolute;
                    border-radius: 200px;
                    bottom: 40px;
                    right: 0;
                    text-align: center;
                    line-height: 20px;

                }
            }
        }
    }
  
}
.header{
    h3{
        font-size: 20px;
       color: #2B2C33;
       font-weight: bold;
       margin-right: 16px;
    }
    .clear{
        font-size: 16px;
        color: #B4B6BE;
        cursor: pointer;
        img{
            width: 16px;
            height: auto;
            margin-right: 8px;
        }
    }
}
.drawer-list{
    height: 80%;
    overflow: auto;
    li{
        margin-bottom: 40px;
        &:last-child{
            margin-bottom: 0;
        }
       .drawer-left{
        img{
            width: 48px;
            height: auto;
            margin-right: 16px;
        }
        h3{
            font-size: 16px;
            color: #2B2C33;
            font-weight: bold;
            cursor: pointer;
            &:hover{
                color: #508CFF;
            }
        }
       } 
       .del-bk{
        width: 20px;
        cursor: pointer;
        height: auto;
        margin-left: 16px;
       }
    }
}
.drawer-edit{
    width: 184px;
    height: 48px;
    background: #508CFF;
    border-radius: 12px;
    color: #fff;
    margin: auto;
    margin-top: 36px;cursor: pointer;
}
 :deep(.el-tree){
    height: calc(100vh - 340px);
    overflow: auto;
 }
  :deep(.el-tree>.el-tree-node>.el-tree-node__content){
     font-weight: 600;
    font-size: 16px;
    color: #2B2C33;
    padding: 8px 0;

     
  }
  :deep(.el-tree>.el-tree-node .el-tree-node__children){
    font-size: 16px;color: #2B2C33;
  }
  :deep(.el-tree-node__content){
    height: 40px;
  }
  :deep(.el-tree-node__label){
    width: 165px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  :deep(.el-tree>.el-tree-node .is-current>.el-tree-node__content){
     background: #DFEAFF;
     font-weight: 600;
    font-size: 16px;
    color: #508CFF;
  }
  :deep(.el-tree-node__content:hover){
    background: #DFEAFF;
  }
  :deep(.el-tree .el-icon){
    margin-right: 8px;
  }
  :deep(.el-select__wrapper){
    min-height: 40px;
    border-radius: 200px;
  }
</style>
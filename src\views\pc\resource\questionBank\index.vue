<template>
    <div class="content-box  question mt10 flex_start_0" >
        <div class="left">
            
            <div class="title flex_between">
                <p>题库</p>
                <div class="flex_start my-tj" @click="currentInfo.type=='teacher'&&goMyTj()">
                      <img src="@/assets/pc/tj.png" alt="" />
                      <p>我的题集</p>
                </div>
            </div>
             <el-tree
                style="max-width: 600px"
                :load="loadNode"
                lazy
                ref='treeRef'
                :props="defaultProps"
                 node-key='value'
                 :default-expanded-keys="expandedKeys"
                @node-click="handleNodeClick"
            />
        </div>
        <div class="right flex_1">
            <div class="select_form flex_between">
                <div class="flex_start">
                   <el-input
                        v-model="form.keyword"
                        style="width: 240px;margin-right:16px"
                        placeholder="搜索题目名称"
                        :suffix-icon="Search"
                         @change="changeQuestionCategory"
                    />
                    <el-select
                        v-model="form.belongToTenantId"
                        placeholder="全部"
                        size="large"
                          @change="changeQuestionCategory"
                        style="width: 160px;margin-right:16px"
                    >
                        <el-option
                            v-for="item in tenantoptions"
                            :key="item.tenantId"
                            :label="item.tenantName"
                            :value="item.tenantId"
                        />
                    </el-select>
                    <el-select
                        v-model="form.questionCategory"
                        placeholder="全部"
                        size="large"
                        @change="changeQuestionCategory"
                        style="width: 160px"
                    >
                         <el-option
                            label="全部"
                            value=""
                        />
                        <el-option
                            v-for="item in questionCategory"
                            :key="item.id"
                            :label="item.title"
                            :value="item.id"
                        />
                    </el-select>
                </div>
                 <el-button class="up-question" @click="goUpload" v-if="currentInfo.type=='teacher'">
                     <img src="@/assets/pc/upquestion.png" alt="">
                        上传题目
                 </el-button>

            </div>
            <div class="question-item" v-for='item in tableData' :key="item.id">
                 <Question :row='item' @emitDel='emitDel' @emitJoinBasket='emitJoinBasket'/>
            </div>
              <el-empty description="暂无数据" v-show="total==0" style="margin-top:10px"/>
              <el-pagination
                background
                v-show="total!=0"
                layout="total,prev, pager, next"
                :total="total"
                v-model:page-size='form.pageSize'
                class="mt-4"
                @current-change='currentChange'
            />
        </div>
        <div class="tj" @click="goTj"  v-if="currentInfo.type=='teacher'">
            <img src="@/assets/pc/tjl.png" alt="" />
            <div>
                {{count}}
            </div>
        </div>
    </div>
</template>
<script setup>
import {onActivated, onMounted, reactive, ref, nextTick} from 'vue'
import {Search } from '@element-plus/icons-vue'
import Question from '@/views/pc/component/resource/question.vue'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'
import {questionList,subjectList,editionList,volumeList,chapterTree,questionDel,basketCount,basketAdd,userTenant} from '@/api/pc/index.js'
import {useRouter} from 'vue-router'
import { ElMessage,ElMessageBox } from 'element-plus'
let userInfoStore=useUserInfoStoreHook()
let {questionCategory,questionDegree,stages,currentInfo,userInfo}=storeToRefs(userInfoStore)
let router=useRouter()
let tenantoptions=ref([])
let form=reactive({
    stage:"",
    keyword:'',
    belongToTenantId:'',
    questionCategory:'',
    isViewJustMine:'',
    subjectId:'',
    editionId:'',
    volumeId:'',
    chapter1Id:'',
    chapter2Id:'',
    pageSize:10,
    pageNum:1
})
let total=ref(0)
let tableData=ref([])
let defaultProps={
      children: 'children',
}
let count=ref(0)
let options=ref([])
let treeData=ref([
       {
            value:'10',
            label:'幼儿园',
            type:'stage',
        },
        {
            value:'20',
            label:'小学',
             type:'stage',
        },
        {
            value:'30',
            label:'初中',
             type:'stage',
        },{
            value:'40',
            label:'高中',
             type:'stage',
        }
])
let expandedKeys=ref([])
let treeRef=ref()

let loadNode=async(node,resolve)=>{
    if(node.level==0){
  
       return resolve(treeData.value)
    } else if(node.level==1){
        
       let res1=await subjectList({
        stage:node.data.value
       })
       if(res1){
         resolve([...res1.data])
       }
    }else if(node.level==2){
       
        let res1=await editionList({
             stage:node.parent.data.value,
            subjectId:node.data.value
        })
        if(res1){
         resolve([...res1.data])
        }
    }else if(node.level==3){
        let res1=await volumeList({
            editionId:node.data.value
        })
        if(res1){
          resolve([...res1.data])
        }
    }else if(node.level==4){
        let res1=await chapterTree({
            volumeId:node.data.value
        })
        if(res1){
          resolve([...res1.data])
        }
    }else if(node.level==5){
         resolve(node.data.children?node.data.children:[])
    }else{
        resolve([])
    }
    
}
let getUserTenant=async()=>{
    let res=await userTenant()
    if(res){
      
        
        res.data.unshift({
            isAdmin:false,
            tenantId:'',
            tenantName:"全部"
        },{
            isAdmin:false,
            tenantId:'-1',
            tenantName:"个人"
        })
        
        tenantoptions.value=res.data
    }
}
const emitJoinBasket=async(row)=>{
     let res=await basketAdd({
        questionIds:[row.id]
     })
     if(res){
        getBasketCount()
        tableData.value.forEach(item=>{
            if(item.id==row.id){
                item.isInQuestionBasket=true

            }
        })
     }
   
}
const goTj=()=>{
    router.push('/resource/question/set')
}
let getBasketCount=async()=>{
    let res=await basketCount()
    if(res){
        // console.log(res,'res')
        count.value=res.data
    }
}
let emitDel=(id)=>{
     ElMessageBox.confirm(
         '确认删除该题目吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
            
          let res=await questionDel({
            questionId:id
          })
          if(res){
            ElMessage.success("删除成功")
            getList()
          }
              
        })
        .catch(() => {
      
        })
}
let currentChange=(val)=>{
    Object.assign(form,{
        pageNum:val
    })
    getList()
}
let goMyTj=()=>{
    router.push('/resource/question/questionset')
}
let changeQuestionCategory=(val)=>{
    if(form.belongToTenantId==-1){
        Object.assign(form,{
            pageNum:1,
            isViewJustMine:1
        })
    }else{
     Object.assign(form,{
        pageNum:1,
        isViewJustMine:''
     })
    }
    
    getList()
}
const handleNodeClick=(val1,val2)=>{
   
    Object.assign(form,{
        stage:"",
        keyword:"",
        belongToTenantId:'',
        subjectId:'',
        editionId:'',
        volumeId:'',
        chapter1Id:'',
        chapter2Id:'',
        pageSize:10,
        pageNum:1
    })
    if(val2.level==1){
         Object.assign(form,{
          stage:val1.value
         })
    }else if(val2.level==2){
         Object.assign(form,{
            subjectId:val1.value
         })
    }else if(val2.level==3){
         Object.assign(form,{
            editionId:val1.value
         })
    }else if(val2.level==4){
         Object.assign(form,{
            volumeId:val1.value
         })
    }else if(val2.level==5){
         Object.assign(form,{
            chapter1Id:val1.value
         })
    }else if(val2.level==6){
         Object.assign(form,{
            chapter2Id:val1.value
         })
    }
     getList()
}
let getList=async()=>{
    let params={
        stage:form.stage,
        keyword:form.keyword,
        belongToTenantId:form.belongToTenantId,
        questionCategory:form.questionCategory,
        isViewJustMine:form.isViewJustMine,
        subjectId:form.subjectId,
        editionId:form.editionId,
        volumeId:form.volumeId,
        chapter1Id:form.chapter1Id,
        chapter2Id:form.chapter2Id,
        pageSize:form.pageSize,
        pageNum:form.pageNum
    }
    if(!params.stage) delete params.stage;
    if(!params.keyword) delete params.keyword;
    if(!params.belongToTenantId) delete params.belongToTenantId;
    if(!params.questionCategory) delete params.questionCategory;
    if(!params.isViewJustMine) delete params.isViewJustMine;
    if(!params.subjectId) delete params.subjectId;
    if(!params.editionId) delete params.editionId;
    if(!params.volumeId) delete params.volumeId;
    if(!params.chapter1Id) delete params.chapter1Id;
    if(!params.chapter2Id) delete params.chapter2Id;

    let res=await questionList(params)
    if(res){
        res.data.list.forEach(item=>{
            if(item.questionDegree==30){
                item.degree=1
            }else if(item.questionDegree==60){
                item.degree=2
            }else if(item.questionDegree==90){
                item.degree=3
            }else{
                 item.degree=0
            }
        })
        tableData.value=res.data.list
        total.value=res.data.total
    }
}
let goUpload=()=>{
  router.push('/resource/question/upload')
}
onMounted(()=>{
    getUserTenant()
    expandedKeys.value=[userInfo.value.stage,userInfo.value.subjectId]
    Object.assign(form,{
        subjectId:userInfo.value.subjectId,
        stage:userInfo.value.stage
    })
    nextTick(()=>{
       
        treeRef.value.setCurrentKey(userInfo.value.subjectId)
    })
})
onActivated(()=>{
    getBasketCount()
    getList()
})
</script>
<style lang="scss" scoped>
.content-box{
    margin-bottom: 10px;
}
.left{
    width: 374px;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 24px 24px 16px 24px;
   .title{
        padding-bottom: 16px;
        border-bottom: 1px solid  #E2E3E6;
        p{
            font-size: 20px;
            color: #2B2C33;
            font-weight: bold;
        }
        .my-tj{
            cursor: pointer;
            img{
                width: 16px;
                height: auto;
                margin-right: 4px;
            }
            p{
                font-size: 16px;
                color: #508CFF;
            }
        }
  }
 
  :deep(.el-tree>.el-tree-node>.el-tree-node__content){
     font-weight: 600;
    font-size: 16px;
    color: #2B2C33;
    padding: 8px 0;

     
  }
  :deep(.el-tree>.el-tree-node .el-tree-node__children){
    font-size: 16px;color: #2B2C33;
  }
  :deep(.el-tree-node__content){
    height: 40px;
  }
  :deep(.el-tree-node__label){
    width: 165px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  :deep(.el-tree>.el-tree-node .is-current>.el-tree-node__content){
     background: #DFEAFF;
     font-weight: 600;
    font-size: 16px;
    color: #508CFF;
  }
  :deep(.el-tree-node__content:hover){
    background: #DFEAFF;
  }
  :deep(.el-tree .el-icon){
    margin-right: 8px;
  }
}
.right{
    margin-left: 10px;
  .select_form{
    :deep(.el-input){
        height: 40px;
       
    }
    :deep(.el-input__wrapper){
         border-radius: 8px;
    }
    :deep(.el-select__wrapper){
         border-radius: 8px;
    }
     .up-question{
        width: 116px;
        height: 40px;
        background: #508CFF;
        border-radius: 8px;
        border: 1px solid #508CFF;
        font-size: 16px;
        color: #FFFFFF;
        img{
            width: 16px;
            height: auto;
            margin-right: 4px;
        }
     }
  }
  .question-item{
    margin-top: 10px;
  }
}
.tj{
    position: fixed;
    bottom:100px;
    right: 20px;
    cursor: pointer;
    img{
         width: 108px;
         height: auto;
    }
    div{
        width: 20px;
        height: 20px;
        background: #E72C4A;
        border: 1px solid #F5F7FB;
        font-size: 12px;
        color: #fff;
        position: absolute;
        border-radius: 200px;
        bottom: 30px;
        right: 0;
        text-align: center;
        line-height: 20px;

    }
}
</style>
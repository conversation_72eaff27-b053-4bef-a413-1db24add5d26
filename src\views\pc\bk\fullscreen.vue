<template>
  <div id="test">
    <div class="right flex_column">
      <div class="bar_view">
        <div class="test-view">
          <div class="top flex_center" @click="screen">
            <img
              class="exit"
              src="@/assets/pc/back.png"
            />
            <p class="text_16">退出</p>
          </div>
          <ul class="left">
            <li>
              <ul
                class="wd-ul flex_column_center"
              >
              
                <li
                 
                  :class="['flex_column_center',current==item.fileId?'active':'']"
                  v-for="(item, index) in bkList"
                  :key="index"
                  @click="getCurrent(item, index)"
                >
                <h4 style="font-size: 16px;color: #2B2C33;">{{item.processName}}</h4>
                  <img
                    class="wd-img"
                   
                    :src="item.coverFileUrl"
                  />
                 

                  <p class="name text_14">{{ item.fileName }}</p>
                </li>
              </ul>
            </li>
          </ul>
        </div>
        <img
          class="flex_center select-icon"
          @click="fadeOut()"
           src="@/assets/pc/showopen.png"
        />
      </div>
      <div class="flex_start_0 flex_1">
        <div class="flex_1" style="height: 100vh; overflow: hidden">
          <Preview :row="fileinfo"></Preview>
        </div>
        <div class="default-img" v-if="showDefault">
          <img src="@/assets/pc/defaultclass.png" />
          <div class="default-info flex_column_center">
            <p>{{ name }}</p>
            <!-- <p>{{ packageTitle }}</p> -->
            <p>授课人：{{ userInfo.user?.name }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import {onMounted, ref} from 'vue'
import {packageDetail} from '@/api/pc/bk.js'
import Preview from '@/views/pc/component/resource/preview.vue'
import {useRoute,useRouter} from 'vue-router'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'
import {previewUrl} from '@/api/pc/common.js'
let userInfoStore=useUserInfoStoreHook()
let {userInfo}=storeToRefs(userInfoStore)
let router=useRouter()
let route=useRoute()
let current=ref('')
let bkList=ref([])
let packageId=ref('')
let packageTitle=ref('')
let fileinfo=ref({})
let uploadName=ref('')
let showDefault=ref(true)
let name=ref('')
 let  streamVideoType=['mp4','flv','dash','hls']
 let  streamAudioType=['mp3','wav','aac','webm']
let fadeOut=()=>{
 var sidebarEl = document.querySelector(".test-view");

      if (sidebarEl.className.indexOf("move_left") > -1) {
        sidebarEl.classList.add("move_right");
        sidebarEl.classList.remove("move_left");
        document.querySelector(".select-icon").style =
          "transform: rotate(0deg)";
      } else {
        sidebarEl.classList.add("move_left");
        sidebarEl.classList.remove("move_right");
        document.querySelector(".select-icon").style =
          "transform: rotate(180deg);right: 50px;";
      }
}
let screen=()=>{
  router.go(-1)
}
const getExtensionString = (str) => {
    var index = str.lastIndexOf('.')
    // 如果找到了第一个"."并且它不在字符串的起始处或结尾处
    if (index != -1 && index != 0 && index + 1 != str.length) {
        return str.substr(index + 1)
    } else {
        return ''
    }
}
let getCurrent=async(item)=>{
    console.log(item)
    showDefault.value=false
    current.value=item.fileId
    if(item.coverFileId==import.meta.env.VITE_PDF||item.coverFileId==import.meta.env.VITE_DOC||item.coverFileId==import.meta.env.VITE_EXCEL||item.coverFileId==import.meta.env.VITE_PPT){
        item.fileType=1
    }
    let res=await previewUrl({
        fileId:item.fileId
    })
    if(res){
       item.fileUrl=res.data.previewUrl
       let type=getExtensionString(res.data.fileName)
       if(streamVideoType.indexOf(type)>-1){
            item.fileType=2
       }
       if(streamAudioType.indexOf(type)>-1){
            item.fileType=3
       }
       fileinfo.value=item
    }
  
}
let init=async()=>{
    let res=await packageDetail({
        packageId:packageId.value
      })  
    if(res){
        res.data.process=res.data.process?JSON.parse(res.data.process):[]
        res.data.process.forEach(item=>{
            let obj=JSON.parse(import.meta.env.VITE_FILE)
            item.coverFileUrl=obj[item.coverFileId]?(import.meta.env.VITE_BASE_URL+'/fs/file?fileId='+obj[item.coverFileId]):(import.meta.env.VITE_BASE_URL+'/fs/file?fileId='+item.coverFileId)
        })
        name.value=res.data.name
        bkList.value=res.data.process
        console.log(res.data)
    }
}
onMounted(()=>{
 packageId.value=route.query.id
 init()
})
//  id: 0,
//       : [],
//       current: "",
//       fileinfo: {},
//       fullscreen: true,
//       showMore: false,
//       showDefault: true,
//       name: "",
//       uploadName: "",
//       packageTitle: "",
</script>
<style lang="scss" scoped>
// @import "@/style/element.scss";
.el-icon-d-arrow-left {
  color: #fff;
  font-size: 32px;
}
.default-info {
  position: absolute;
  top: 0%;
  color: #4e5060;
  font-size: 50px;
  padding: 0 130px;
  width: 100%;
  height: 100vh;
  justify-content: center;
  p:nth-child(1) {
    font-size: 80px;
    font-weight: 600;
    color: #2b2c33;
    margin-bottom: 64px;
  }
  p:nth-child(2) {
    font-size: 32px;
    font-weight: 400;
    margin-bottom: 32px;
    color: #2b2c33;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  p:nth-child(3) {
    font-size: 32px;
    font-weight: 400;
    color: #2b2c33;
  }
}
::v-deep .filepreview {
  margin: 0 !important;
}

::v-dep #office-iframe {
  height: calc(100vh - 10px);
}

::v-deep #custom-mount-box {
  height: calc(100vh - 10px) !important;
}

::v-deep .custom-mount {
  height: calc(100vh - 10px) !important;
}

::v-deep .video-js {
  width: 100vw !important;
  height: calc(100vh - 10px) !important;
}

::v-deep .preview-video-box {
  height: calc(100vh - 10px) !important;
}

@keyframes move_left {
  from {
    width: 112px;
    overflow: auto;
    padding: 20px 10px;
  }

  to {
    width: 0;
    overflow: hidden;
    padding: 0;
  }
}

@keyframes move_right {
  from {
    width: 0;
    overflow: hidden;
    padding: 0;
  }

  to {
    width: 114px;
    overflow: auto;
    padding: 20px 10px;
  }
}
.move_left {
  display: none;
}
.move_right {
  display: block;
}
.default-img {
  position: fixed;
  z-index: 10;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.test-view {
  background: #f2fbff;
  border-radius: 0px 12px 12px 0px;
  width: 112px;
  height: calc(100vh - 140px);

  .left {
    overflow-y: auto;
    height: calc(100vh - 220px);
    .name {
      font-size: 14px;
     color: #2B2C33;
     text-align: center;
    }
  }
}

.wd-ul {
  padding: 22px 8px;
}

.wd-ul > li {
  width: 100%;
  cursor: pointer;
  color: #2B2C33;
  /* border-radius: 10px; */
  padding: 22px 0;
  &.active {
 background: #4260FD;
  color: #fff;
  .name {
    color: #fff;
  }
  h4{
    color: #fff !important;
  }
}
}

.wd-img {
  width: 32px;
  height: 32px;
  margin: 16px 0 11px;
}

.name {
  word-break: break-all;
}
.title {
  background: #3798fc;
  box-shadow: 0px 4px 11px rgba(182, 182, 182, 0.25);
  border-radius: 4px;
  padding: 10px 0;
  text-align: center;
  font-size: 16px;
  color: #fff;
}

.right {
  flex: 1;
  background: #ffffff;
  background: #fff;
  position: relative;
  height: 100vh;
}
.top {
  cursor: pointer;
  padding: 16px 0;
  color: #2B2C33;
  border-bottom: 1px solid #E2E3E6;
  margin: 0 8px;
  .exit {
    width: 32px;
    height: 32px;
    margin-left: 4px;
  }
}


.bar_view {
  width: 112px;
  height: calc(100vh - 140px);

  position: fixed;
  left: 0;
  top: 40px;
  z-index: 100;
}
.select-icon {
  width: 44px;
  height: 44px;
  cursor: pointer;
  position: absolute;
  right: -20px;
  top: 50%;
  cursor: pointer;
}
.wd-ul:hover {
  background: #f9f9f9;
  cursor: pointer;
//   color: #4260FD !important;
  .name {
    // color:  #4260FD !important;
  }
}
</style>

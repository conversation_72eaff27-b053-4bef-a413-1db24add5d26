<template>
    <div>
        <div class="content-box  upload mt10" >
          <div class="flex_start title" @click="$router.go(-1)">
            <el-icon style="margin-right: 4px;"><ArrowLeft /></el-icon>
            返回列表
          </div>
               <el-form
                ref="addRef"
                :model="form"
                :rules="rules"
                label-width="auto"
                class="demo-ruleForm"
              style="margin-top:20px"
            >
            <el-form-item label="标题" prop="title" label-position='top'>
                     <el-input v-model="form.title" placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="通知内容" prop="content" label-position='top'>
                     <WangEditor :html='form.content' @emitHtml='emitHtml' />
                </el-form-item>
                 <el-form-item label='发布范围' label-position='top' prop='clubGroupId'>
                       <el-radio-group v-model="form.clubGroupId"  size='large'>
                            <el-radio :value="-1" >全社</el-radio>
                            <el-radio :value="item.value" v-for="item in groups" :key="item.value">{{item.label}}</el-radio>
                          
                        </el-radio-group>
                 </el-form-item>
              
            </el-form>
        </div>
        <div class="btn-list flex_center">
             <el-button class="primary" @click="goSubmit" :loading='loading'>提交</el-button>
           
             <el-button class="cancel" @click="goCancel" >取消</el-button>
        </div>
    </div>
  
</template>
<script setup>
import WangEditor from '@/views/pc/component/wangeditor.vue'
import {noticeAdd,groupList} from '@/api/pc/index.js'
import {onMounted, ref} from 'vue'
import {useRouter,useRoute} from 'vue-router'
import {ElMessageBox,ElMessage} from 'element-plus'
let form=ref({
    content:'',
    title:'',
    clubGroupId:''
})
let route=useRoute()
let router=useRouter()
let loading=ref(false)
let groups=ref([])
let addRef=ref()
let rules=ref({
    content:[{required: true,message: '请输入发布内容',trigger: 'change'}],
     title:[{required: true,message: '请输入标题',trigger: 'change'}],
     clubGroupId:[{required: true,message: '请选择发布范围',trigger: 'change'}],
})

let getgroupList=async()=>{
    let res=await groupList()
    if(res){
        
         groups.value=res.data
    }
}
let emitHtml=(val)=>{
    // console.log(val)
    form.value.content=val
}
let goSubmit=async(type)=>{
   if (!addRef.value) return
  await addRef.value.validate((valid, fields) => {
    if (valid) {
       ElMessageBox.confirm(
           '确认发布内容吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
            loading.value=true
            let params={
                content: form.value.content,
                title:form.value.title,
                noticeRange:form.value.clubGroupId==-1?'CLUB':'CLUB_GROUP',
                clubGroupId:form.value.clubGroupId==-1?'':form.value.clubGroupId
            }
             noticeAdd(params).then(data=>{
                  loading.value=false
                   ElMessage({
                        type: 'success',
                        message: '保存成功',
                    })
                    router.go(-1)
             }).catch(err=>{
                 loading.value=false
             })
            
        })
        .catch(() => {
      
        })
    } else {
      console.log('error submit!', fields)
    }
  })
}

let goCancel=()=>{
     router.back()
}

onMounted(()=>{
    getgroupList()
    // getDetail()
})
</script>
<style lang="scss" scoped>
.upload{
    background: #fff;
    border-radius: 4px;
    padding: 18px 24px 6px 24px;
    .title{
        font-weight: bold;
        font-size: 20px;
         color: #2B2C33;
         cursor: pointer;
    }
    .label{
            font-size: 18px;
            color: #2B2C33;
            font-weight: bold;
            margin-right: 10px;
        }
    .editor{
        margin-top: 20px;
    }
     .tag-list{
            li{
                padding: 4px 10px;
                border-radius: 100px;
                font-size: 16px;
                border: 1px solid #E2E3E6;
                color: #2B2C33;
                cursor: pointer;
                margin-right: 10px;
                cursor: pointer;
                img{
                    width: 16px;
                    height: auto;
                    margin-right: 8px;  
                }
               p{
                line-height: 100%;
               }
               &.active{
                background: linear-gradient( 270deg, #535DFF 0%, #508CFF 100%);
                color: #fff;
               }
            }
            
        }
    // .theme{
    //     margin-top: 30px;
       
    //     .add{
    //              width: 30px;
    //              height: auto;cursor: pointer;
    //         }
    // }

}
.btn-list{
    margin-top: 24px;
    .primary{
        width: 120px;
        height: 44px;
        background: #508CFF;
        border-radius: 4px;font-weight: 500;
        font-size: 18px;
        color: #FFFFFF;
        outline: none;border:none; cursor: pointer;margin-right: 24px;
    }
    .save{
        width: 120px;
        height: 44px;
        background: #fff;border: 1px solid #508CFF; cursor: pointer;
        border-radius: 4px;font-weight: 500;
        font-size: 18px;
        color: #508CFF; cursor: pointer;
        outline: none;margin-right: 24px;
    }
    .cancel{
        cursor: pointer;
        width: 120px;
        height: 44px;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E2E3E6;
        font-size: 18px;
        color: #94959C;
    }
}
.app-scrollbar .app-container-grow{
    flex-grow: 0 !important;
}
:deep(.el-form-item__label){
    font-weight: bold;
    font-size: 18px;
}
:deep(.el-input__wrapper),:deep(.el-input__wrapper),:deep(.el-date-editor.el-input){
    height: 40px;
}
</style>
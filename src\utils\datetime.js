import dayjs from "dayjs"

const INVALID_DATE = "N/A"

/** 格式化日期时间 */
export const formatDateTime = (datetime = "", template = "YYYY-MM-DD HH:mm:ss") => {
  const day = dayjs(datetime)
  return day.isValid() ? day.format(template) : INVALID_DATE
}

// 计算出时间戳的具体数据：比如将85400转化为 n天n时n分n秒
export function formateLeftTime (lefttime) {
  

  var day;
  var hour;
  var min;
  var second;

  day = parseInt(lefttime / (24 * 60 * 60)) // 计算整数天数
  var afterDay = lefttime - day * 24 * 60 * 60 // 取得算出天数后剩余的秒数
  hour = parseInt(afterDay / (60 * 60)) // 计算整数小时数
  var afterHour = lefttime - day * 24 * 60 * 60 - hour * 60 * 60 // 取得算出小时数后剩余的秒数
  min = parseInt(afterHour / 60) // 计算整数分
  second = parseInt(lefttime - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60) // 取得算出分后剩余的秒数

  if (day < 10) {
    day =  day;
  }

  if (hour < 10) {
    hour = '0' + hour
  };

  if (min < 10) {
    min = '0' + min;
  }

  if (second < 10) {
    second = '0' + second;
  }


  var restStamp = {
    day: day,
    hour: hour,
    min: min,
    second: second
  }
  return restStamp
}
import { request } from "@/utils/service"

export function eventGroups(params) {
  return request({
    url: "/xzs/mileage/event-group/option",
    method: "get",
    params
  })
}
export function mileages(params) {
  return request({
    url: "/xzs/mileage/event-type/setting",
    method: "get",
    params
  })
}
//
export function currentWeekProfile(params) {
    return request({
      url: "/xzs/mileage/me/current-weekly-profile",
      method: "get",
      params
    })
  }
  export function currentWeekProfileRank(params) {
    return request({
      url: "/xzs/mileage/me/current-weekly-rank",
      method: "get",
      params
    })
  }
  // 
  export function dailyProfileLog(params) {
    return request({
      url: "/xzs/mileage/me/event-log",
      method: "get",
      params
    })
  }
  export function currentDailyProfile(params) {
    return request({
      url: "/xzs/mileage/me/current-daily-task",
      method: "get",
      params
    })
  }
  //里程周报
  export function weeks(params) {
    return request({
      url: "/xzs/mileage/util/week",
      method: "get",
    })
  }
  export function months(params) {
    return request({
      url: "/xzs/mileage/util/month",
      method: "get",
    })
  }
  export function weekCapacityAnalysis(params) {
    return request({
      url: "/xzs/mileage/me/weekly-report/event-group/mine-vs-avg",
      method: "get",
      params
    })
  }
  export function monthlyCapacityAnalysis(params) {
    return request({
      url: "/xzs/mileage/me/monthly-report/event-group/mine-vs-avg",
      method: "get",
      params
    })
  }
  //
  export function weekTrendAnalysis(params) {
    return request({
      url: "/xzs/mileage/me/weekly-report/daily-points",
      method: "get",
      params
    })
  }

  export function monthlyTrendAnalysis(params) {
    return request({
      url: "/xzs/mileage/me/monthly-report/daily-points",
      method: "get",
      params
    })
  }
  export function weeklyReport(params) {
    return request({
      url: "/xzs/mileage/me/weekly-report/event-group-points",
      method: "get",
      params
    })
  }
 //
 export function monthlyReport(params) {
  return request({
    url: "/xzs/mileage/me/monthly-report/event-group-points",
    method: "get",
    params
  })
}
 export function weeklyCount(params) {
  return request({
    url: "/xzs/mileage/me/weekly-report/event-type-count",
    method: "get",
    params
  })
}
//
export function monthlyCount(params) {
  return request({
    url: "/xzs/mileage/me/monthly-report/event-type-count",
    method: "get",
    params
  })
}
export function weeklyRank(params) {
  return request({
    url: "/xzs/mileage/me/weekly-report/rank",
    method: "get",
    params
  })
}
export function monthlyRank(params) {
  return request({
    url: "/xzs/mileage/me/monthly-report/rank",
    method: "get",
    params
  })
}
//数据统计模块社长身份
export function  DirctorMileageWeekRank(){
  return request({
    url: "/xzs/mileage/stat/club/current-weekly-ranking-list",
    method: "get",
  })
}
export function  DirctorMileageMonthRank(){
  return request({
    url: "/xzs/mileage/stat/club/current-monthly-ranking-list",
    method: "get",
  })
}
export function  DirctorMileageHistoryRank(){
  return request({
    url: "/xzs/mileage/stat/club/history-ranking-list",
    method: "get",
  })
}
// 
//  /xzs/mileage/stat/club/current-weekly-group-ranking-list
//  
// /xzs/mileage/stat/club/history-group-ranking-list
export function  DirctorGroupWeekRank(){
  return request({
    url: "/xzs/mileage/stat/club/current-weekly-group-ranking-list",
    method: "get",
  })
}
export function  DirctorGroupMonthRank(){
  return request({
    url: "/xzs/mileage/stat/club/current-monthly-group-ranking-list",
    method: "get",
  })
}
export function  DirctorGroupHistoryRank(){
  return request({
    url: "/xzs/mileage/stat/club/history-group-ranking-list",
    method: "get",
  })
}


export function  DirctorHonnerWeek(){
  return request({
    url: "/xzs/mileage/stat/club/current-weekly-group-honour",
    method: "get",
  })
}
export function  DirctorHonnerMonth(){
  return request({
    url: "/xzs/mileage/stat/club/current-monthly-group-honour",
    method: "get",
  })
}
export function  DirctorHonnerHistory(){
  return request({
    url: "/xzs/mileage/stat/club/history-group-honour",
    method: "get",
  })
}
///xzs/mileage/stat/club/current-weekly-event-group-points
export function  DirctorMileageDistributionWeek(){
  return request({
    url: "/xzs/mileage/stat/club/current-weekly-event-group-points",
    method: "get",
  })
}
export function  DirctorMileageDistributionMonth(){
  return request({
    url: "/xzs/mileage/stat/club/current-monthly-event-group-points",
    method: "get",
  })
}
export function  DirctorMileageDistributionHistory(){
  return request({
    url: "/xzs/mileage/stat/club/history-event-group-points",
    method: "get",
  })
}

export function  DirctorMileageThirdtyPoints(){
  return request({
    url: "/xzs/mileage/stat/club/recent-day30-points",
    method: "get",
  })
}
//导师统计视图
export function  TutorMileageWeekRank(){
  return request({
    url: "/xzs/mileage/stat/club-group/current-weekly-ranking-list",
    method: "get",
  })
}
export function  TutorMileageMonthRank(){
  return request({
    url: "/xzs/mileage/stat/club-group/current-monthly-ranking-list",
    method: "get",
  })
}
export function  TutorMileageHistoryRank(){
  return request({
    url: "/xzs/mileage/stat/club-group/history-ranking-list",
    method: "get",
  })
}
export function  TutorMileageDistributionWeek(){
  return request({
    url: "/xzs/mileage/stat/club-group/current-weekly-event-group-points",
    method: "get",
  })
}
export function  TutorMileageDistributionMonth(){
  return request({
    url: "/xzs/mileage/stat/club-group/current-monthly-event-group-points",
    method: "get",
  })
}
export function  TutorMileageDistributionHistory(){
  return request({
    url: "/xzs/mileage/stat/club-group/history-event-group-points",
    method: "get",
  })
}

export function TutorMileageThirdtyPoints(){
  return request({
    url: "/xzs/mileage/stat/club-group/recent-day30-points",
    method: "get",
  })
}
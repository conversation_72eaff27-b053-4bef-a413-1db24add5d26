<script setup>
import { ref, onMounted, nextTick, onBeforeUnmount, watch, reactive, computed, onActivated, onDeactivated } from "vue"
import { useRouter, useRoute } from "vue-router"
import dayjs from "dayjs"
import { showConfirmDialog, showSuccessToast } from "vant"
import {
  getAllDiaryList<PERSON>pi,
  getDirectorDiary<PERSON>pi,
  hideDiary<PERSON>pi,
  deleteDiary<PERSON><PERSON>,
  getMyGroupApi,
  getRecommendDiaryApi,
  getDirectorRecommendApi,
  getTutorDiaryApi,
  getTutorRecommendApi,
  getWxMpRecommendApi,
  getUserDiaryApi
} from "@/api/mobile/diarySquare"
import {
  recommendDiaryApi,
  cancelRecommendDiaryApi,
  likeDiaryApi,
  favoriteDiaryApi
} from "@/api/mobile/memberOperation"
import { useUserStore } from "@/store/modules/user"
import DefaultAvatar from "@/assets/layouts/default-avatar.png"
const router = useRouter()
const userStore = useUserStore()
const route = useRoute()

const isViceDirector = computed(() => userStore.roles?.includes("VICE_DIRECTOR"))
const isMember = computed(() => userStore.roles?.includes("MEMBER"))
const isDirector = computed(() => userStore.roles?.includes("DIRECTOR"))
const weekMap = {
  "Monday": "周一",
  "Tuesday": "周二",
  "Wednesday": "周三",
  "Thursday": "周四",
  "Friday": "周五",
  "Saturday": "周六",
  "Sunday": "周日"
}

const myGroupList = ref([])
const getMyGroupList = () => {
  myGroupList.value = [
    {
      label: `${route.query.userName}的日记`,
      value: -6
    }
  ]
  activeTab.value = -6
}
const activeTab = ref(0)
const handleClickTab = (tab) => {
  activeTab.value = tab.name
  page.pageNum = 1
  finished.value = false
  // sortActions.value = sortActions.value.filter((item) => item.id !== "TUTOR_READ" && item.id !== "TUTOR_UNREAD")

  // if (tab.name !== -1 && tab.name !== -2 && tab.name !== -3 && tab.name !== -4) {

  //   if (userStore.userInfo?.xingZhiShe?.clubGroupId === tab.name || activeTab.value === 0) {

  //     sortActions.value = sortActions.value.concat([
  //       {
  //         id: "TUTOR_READ",
  //         name: "导师已阅"
  //       },
  //       {
  //         id: "TUTOR_UNREAD",
  //         name: "导师未阅"
  //       }
  //     ])
  //   }
  // }

  activeSortAction.value = "TIME_DESC"
  activeSortName.value = "时间排序(倒序)"
  getList()
  // 切换tab时滚动位置归0
  const listArea = document.querySelector('.list-area')
  if (listArea) {
    listArea.scrollTop = 0
  }
  scrollPosition.value = 0
  sessionStorage.removeItem('diarySquareScrollPosition')
}

// 打开排序弹窗
const sortSheetVisible = ref(false)
const activeSortAction = ref("TIME_DESC")
const activeSortName = ref("时间排序(倒序)")
const openSortSheet = () => {
  sortSheetVisible.value = true
}
const sortActions = ref([
  {
    id: "TIME_DESC",
    name: "时间排序(倒序)"
  },
  {
    id: "TIME_ASC",
    name: "时间排序(正序)"
  },
  {
    id: "VIEWS",
    name: "阅读数排序"
  },
  {
    id: "SCORE",
    name: "得星数排序"
  },
  {
    id: "EVALUATIONS",
    name: "评价数排序"
  },
  {
    id: "COMMENTS",
    name: "评论数排序"
  },
  {
    id: "LIKES",
    name: "点赞数排序"
  }
])
const selectSortAction = (item) => {
  activeSortAction.value = item.id
  activeSortName.value = item.name
  sortSheetVisible.value = false
  page.pageNum = 1
  finished.value = false
  getList()
}

// 打开筛选日期弹窗
const filterDateSheetVisible = ref(false)
const activeFilterDate = ref([])
const activeFilterDateForHtml = ref("")
const minDate = ref()
const maxDate = ref()
const openFilterDateSheet = () => {
  // 获取年月日
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1
  const day = now.getDate()
  maxDate.value = new Date(year, month, day)
  minDate.value = new Date(year, month - 6, day)
  filterDateSheetVisible.value = true
}

const handleFilterDateConfirm = (value) => {
  activeFilterDate.value = [dayjs(value?.[0]).format("YYYY-MM-DD"), dayjs(value?.[1]).format("YYYY-MM-DD")]
  activeFilterDateForHtml.value = `${dayjs(value?.[0]).format("YYYY-MM-DD")}至${dayjs(value?.[1]).format("YYYY-MM-DD")}`
  filterDateSheetVisible.value = false
  page.pageNum = 1
  finished.value = false
  getList()
}

const filterDateSheetRef = ref()
const resetFilterDate = () => {
  filterDateSheetRef.value?.reset()
  activeFilterDate.value = []
  activeFilterDateForHtml.value = ""
  filterDateSheetVisible.value = false
  page.pageNum = 1
  finished.value = false
  getList()
}

const page = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 获取列表
const list = ref([])
const loading = ref(false)
const finished = ref(false)
const scrollPosition = ref(0)

const getList = async () => {
  try {
    loading.value = true

    const orderBy = activeSortAction.value.startsWith("TIME")
      ? activeSortAction.value.split("_")[0]
      : (activeSortAction.value === "TUTOR_READ" || activeSortAction.value === "TUTOR_UNREAD") ? "" : activeSortAction.value
    const orderRule = activeSortAction.value.startsWith("TIME") ? activeSortAction.value.split("_")[1] : ""

    const query = {
      pageNum: page.pageNum,
      pageSize: page.pageSize,
      orderBy,
      orderRule,
      beginRecordDate: activeFilterDate.value?.[0] ? dayjs(activeFilterDate.value?.[0]).format("YYYY-MM-DD") : "",
      endRecordDate: activeFilterDate.value?.[1] ? dayjs(activeFilterDate.value?.[1]).format("YYYY-MM-DD") : "",
    }

    const res = await getUserDiaryApi(route.query.userId, query)

    if (res) {
      res.data.list.forEach(item => {
        item.score = item.score === -1 || item.store === null ? 0 : item.score
      })
      if (page.pageNum === 1) {
        list.value = res.data.list
      } else {
        list.value = [...list.value, ...res.data.list]
      }
      page.total = res.data.total
      if (list.value?.length >= page.total) {
        finished.value = true
      } else {
        page.pageNum++
      }
    }

    loading.value = false
  } catch (err) {
    console.log(err)
  }
}

// 隐藏
const handleHidden = (id, isNormal) => {
  showConfirmDialog({
    title: "隐藏",
    message: "确定隐藏该日记吗？",
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    confirmButtonColor: "#508CFF",
    cancelButtonColor: "#C7CFDF",
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      try {
        const res = await hideDiaryApi(id, {
          isSetNormal: isNormal
        })
        if (res) {
          showSuccessToast("隐藏成功")
          list.value.find((item) => item.id === id).isHidden = !list.value.find((item) => item.id === id).isHidden
        }
      } catch (err) {
        console.log(err)
      }
    })
    .catch(() => { })
}

const clickLike = async (row, isLike) => {
  try {
    if (!row.isLikeCapable) {
      return
    }
    const res = await likeDiaryApi(row.id)
    if (res) {
      const item = list.value.find((item) => item.id === row.id)
      item.isLike = !item.isLike
      item.likes = Number(item.likes) + (isLike ? 1 : -1)
      showSuccessToast(isLike ? "点赞成功" : "取消点赞成功")
    }
  } catch (err) {
    console.log(err)
  }
}

const clickFavorite = async (id, isFavorite) => {
  try {
    const res = await favoriteDiaryApi(id, {
      favorite: isFavorite
    })
    if (res) {
      list.value.find((item) => item.id === id).isFavorite = !list.value.find((item) => item.id === id).isFavorite
      list.value.find((item) => item.id === id).favorites =
        Number(list.value.find((item) => item.id === id).favorites) + (isFavorite ? 1 : -1)
      showSuccessToast(isFavorite ? "收藏成功" : "取消收藏成功")
    }
  } catch (err) {
    console.log(err)
  }
}

// 存储溢出状态的对象
const textOverflowMap = ref({})
// 存储已展开项的对象
const expandedItems = ref({})

// 检查文本是否溢出
const isTextOverflow = (id) => {
  return textOverflowMap.value[id] || false
}

// 添加保存滚动位置的方法
const saveScrollPosition = () => {
  const listArea = document.querySelector('.list-area')
  if (listArea) {
    scrollPosition.value = listArea.scrollTop
    // 同时保存到 sessionStorage，以防页面刷新
    sessionStorage.setItem('userDiarySquareScrollPosition', scrollPosition.value.toString())
  }
}

// 添加恢复滚动位置的方法
const restoreScrollPosition = () => {
  nextTick(() => {
    const listArea = document.querySelector('.list-area')
    if (listArea) {
      // 优先使用 ref 中保存的位置，如果没有则尝试从 sessionStorage 获取
      const savedPosition = scrollPosition.value || Number(sessionStorage.getItem('userDiarySquareScrollPosition'))
      if (savedPosition) {
        listArea.scrollTop = savedPosition
      }
    }
  })
}

// 修改 goToDetail 方法
const goToDetail = (id) => {
  // 保存滚动位置
  saveScrollPosition()

  const orderBy = activeSortAction.value.startsWith("TIME")
    ? activeSortAction.value.split("_")[0]
    : (activeSortAction.value === "TUTOR_READ" || activeSortAction.value === "TUTOR_UNREAD") ? "" : activeSortAction.value
  const orderRule = activeSortAction.value.startsWith("TIME") ? activeSortAction.value.split("_")[1] : ""

  const query = {
    orderBy,
    orderRule,
    beginRecordDate: activeFilterDate.value?.[0] ? dayjs(activeFilterDate.value?.[0]).format("YYYY-MM-DD") : "",
    endRecordDate: activeFilterDate.value?.[1] ? dayjs(activeFilterDate.value?.[1]).format("YYYY-MM-DD") : ""
  }

  sessionStorage.setItem("m-diaryLocation", JSON.stringify(query))
  sessionStorage.setItem("m-diaryId", JSON.stringify(id))
  sessionStorage.setItem("m-diaryUserId", JSON.stringify(route.query.userId))
  router.push({
    path: "/personalSpace/detail",
    query: {
      id,
      from: "userRecord",
      activeTab: activeTab.value,
      userId: route.query.userId
    }
  })
}

// 添加新的计算方法
const isLastLineOverflow = (id) => {
  const contentText = document.querySelector(`[data-id="${id}"] .content-text`)
  if (!contentText) return false

  // 获取行高
  const lineHeight = parseInt(window.getComputedStyle(contentText).lineHeight)
  // 内容实际高度
  const actualHeight = contentText.scrollHeight
  // 三行的理论高度
  const threeLineHeight = lineHeight * 3

  // 如果实际高度大于三行高度，说明最后一行是被截断的
  return actualHeight > threeLineHeight
}

// 修改原有的 checkTextOverflow 方法
const checkTextOverflow = () => {
  nextTick(() => {
    const items = document.querySelectorAll(".list-item")
    items.forEach((item) => {
      const id = item.getAttribute("data-id")
      const contentText = item.querySelector(".content-text")
      if (contentText) {
        // 检查元素是否溢出
        const isOverflow = contentText.scrollHeight > contentText.clientHeight
        textOverflowMap.value[id] = isOverflow
      }
    })
  })
}

watch(
  () => list.value,
  () => {
    nextTick(() => {
      checkTextOverflow()
    })
  },
  { deep: true }
)

const handleRecommend = (diary) => {
  showConfirmDialog({
    title: "提示",
    message: `是否${diary.isRecommended ? "取消" : "设为"}推荐`,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    confirmButtonColor: "#508CFF"
  }).then(async () => {
    try {
      let res

      if (diary.isRecommended) {
        res = await cancelRecommendDiaryApi(diary.id, {
          recommendType: "1"
        })
      } else {
        res = await recommendDiaryApi(diary.id, {
          recommendType: "1"
        })
      }
      if (res) {
        showSuccessToast(diary.isRecommended ? "取消推荐成功" : "设为推荐成功")
        finished.value = false
        saveScrollPosition()
        await restoreGetList()
      }
    } catch (err) {
      console.log(err)
    }
  })
}

const handleWxMpRecommend = (diary) => {
  showConfirmDialog({
    title: "提示",
    message: `是否${diary.isWxMpRecommended ? "取消" : "设为"}公众号推荐`,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    confirmButtonColor: "#508CFF"
  }).then(async () => {
    try {
      let res

      if (diary.isWxMpRecommended) {
        res = await cancelRecommendDiaryApi(diary.id, {
          recommendType: "2"
        })
      } else {
        res = await recommendDiaryApi(diary.id, {
          recommendType: "2"
        })
      }
      if (res) {
        showSuccessToast(diary.isWxMpRecommended ? "取消公众号推荐成功" : "设为公众号推荐成功")
        finished.value = false
        saveScrollPosition()
        await restoreGetList()
      }
    } catch (err) {
      console.log(err)
    }
  })
}

const restoreGetList = async () => {
  try {
    // 保存当前的页码和每页数量
    const currentPageNum = page.pageNum
    const currentPageSize = page.pageSize
    // 修改为获取所有当前已加载的数据
    page.pageSize = page.pageNum * page.pageSize
    page.pageNum = 1

    loading.value = true
    finished.value = false

    const orderBy = activeSortAction.value.startsWith("TIME")
      ? activeSortAction.value.split("_")[0]
      : (activeSortAction.value === "TUTOR_READ" || activeSortAction.value === "TUTOR_UNREAD") ? "" : activeSortAction.value
    const orderRule = activeSortAction.value.startsWith("TIME") ? activeSortAction.value.split("_")[1] : ""

    const query = {
      pageNum: page.pageNum,
      pageSize: page.pageSize,
      orderBy,
      orderRule,
      beginRecordDate: activeFilterDate.value?.[0] ? dayjs(activeFilterDate.value?.[0]).format("YYYY-MM-DD") : "",
      endRecordDate: activeFilterDate.value?.[1] ? dayjs(activeFilterDate.value?.[1]).format("YYYY-MM-DD") : "",
    }

    const res = await getUserDiaryApi(route.query.userId, query)

    if (res) {
      res.data.list.forEach(item => {
        item.score = item.score === -1 || item.score === null ? 0 : item.score
      })
      if (page.pageNum === 1) {
        list.value = res.data.list
      } else {
        list.value = [...list.value, ...res.data.list]
      }

      page.total = res.data.total
      page.pageNum = currentPageNum
      page.pageSize = currentPageSize

      if (list.value?.length >= page.total) {
        finished.value = true
      } else {
        page.pageNum++
      }

      // 恢复滚动位置
      restoreScrollPosition()
      loading.value = false
    }

  } catch (err) {
    console.log(err)
  }
}

onMounted(() => {
  list.value = []
  page.pageNum = 1
  finished.value = false
  loading.value = false
  getMyGroupList()
  getList()
  // 在列表加载后检查文本溢出
  nextTick(() => {
    checkTextOverflow()
    window.addEventListener("resize", checkTextOverflow)
  })
  
  // 判断来源，如果是从个人空间或日记广场进入，则重置滚动位置为0
  const fromRoute = route.query.from
  if (fromRoute === 'personalSpace' || fromRoute === 'diarySquare') {
    scrollPosition.value = 0
    sessionStorage.removeItem('userDiarySquareScrollPosition')
    nextTick(() => {
      const listArea = document.querySelector('.list-area')
      if (listArea) {
        listArea.scrollTop = 0
      }
    })
  }
})

onActivated(() => {
  if (list.value.length > 0) {
    refreshListStatus()
  }
})


// 添加 deactivated 钩子
onDeactivated(() => {
  // 保存滚动位置
  saveScrollPosition()
})

// 修改 refreshListStatus 方法，避免重置列表
const refreshListStatus = async () => {
  try {
    // 保存当前列表状态
    list.value = []

    const currentPageNum = page.pageNum
    const currentPageSize = page.pageSize

    page.pageSize = page.pageNum * page.pageSize
    page.pageNum = 1

    // 获取最新数据
    const query = {
      pageNum: page.pageNum,
      pageSize: page.pageSize, // 获取与当前列表相同数量的数据
      orderBy: activeSortAction.value.startsWith("TIME")
        ? activeSortAction.value.split("_")[0]
        : (activeSortAction.value === "TUTOR_READ" || activeSortAction.value === "TUTOR_UNREAD") ? "" : activeSortAction.value,
      orderRule: activeSortAction.value.startsWith("TIME") ? activeSortAction.value.split("_")[1] : "",
      beginRecordDate: activeFilterDate.value?.[0] ? dayjs(activeFilterDate.value?.[0]).format("YYYY-MM-DD") : "",
      endRecordDate: activeFilterDate.value?.[1] ? dayjs(activeFilterDate.value?.[1]).format("YYYY-MM-DD") : ""
    }

    const res = await getUserDiaryApi(route.query.userId, query)

    if (res) {
      list.value = res.data.list

      page.pageNum = currentPageNum
      page.pageSize = currentPageSize
      restoreScrollPosition()
    }
  } catch (err) {
    console.log(err)
  }
}

onBeforeUnmount(() => {
  window.removeEventListener("resize", checkTextOverflow)
})

// 添加新的方法来处理展开/收起
const toggleContent = (id, event) => {
  event.stopPropagation()
  expandedItems.value[id] = !expandedItems.value[id]
}

// 添加一个解码HTML实体的函数
const decodeHtmlEntities = (text) => {
  if (!text) return ''
  const textArea = document.createElement('textarea')
  textArea.innerHTML = text
  return textArea.value
}
</script>

<template>
  <div class="diary-square">
    <header class="header-area flex-center-center">
      <span class="m-text-14-20-600 white">{{ route.query.userName }}的日记</span>
    </header>
    <div class="flex-between-center sort-area">
      <div class="sort-item flex-start-center" @click="openSortSheet">
        <span class="m-text-14-22-400 grey1 pr5">{{ activeSortName || "时间排序" }}</span>
        <img src="@/assets/mobile/personal-space/arrow-down.png" alt="" width="8" height="6" />
      </div>
      <div class="flex-between-center">
        <div class="sort-item flex-start-center" @click="openFilterDateSheet">
          <span class="m-text-14-22-400 grey1 pr5">{{ activeFilterDateForHtml || "筛选日期" }}</span>
          <img src="@/assets/mobile/personal-space/arrow-down.png" alt="" width="8" height="6" />
        </div>
        <img class="m-ml12" src="@/assets/mobile/personal-space/reset.png" alt="" width="18" height="16"
          @click="resetFilterDate" />
      </div>
    </div>
    <section class="list-area">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="getList"
        :immediate-check="false">
        <div class="list-item flex-start-start m-mb12" v-for="item in list" :key="item.id" :data-id="item.id"
          @click="goToDetail(item.id)">
          <div class="left-box">
            <img :src="item.userAvatarUrl || DefaultAvatar" alt="" width="32" height="32" style="border-radius: 50%;" />
          </div>
          <div class="right-box flex-1 m-pl5">
            <div class="right-top flex-between-center">
              <span class="flex-start-center">
                <span @click="goToDetail(item.id)" class="m-text-14-20-600 grey1 m-pr8">{{ item.userName }}</span>
                <span class="m-text-12-18-400 grey3">{{ item.recordDate }}</span>
              </span>
              <van-rate v-if="item.userClubRole === 'LEADER' || item.userClubRole === 'MEMBER'" v-model="item.score" :size="14" color="#FFB160" void-icon="star"
                void-color="#C7CFDF" :count="6" />
            </div>
            <div class="m-text-14-20-600 m-pt2 m-pb2"><span class="m-text-14-20-600" v-if="item.title">标题：</span>{{
              item.title }}</div>
            <div class="right-middle">
              <div class="content-wrapper">
                <p class="m-text-14-20-400 grey1 content-text" v-html="decodeHtmlEntities(item.content)"></p>
              </div>
            </div>
            <div class="right-bottom flex-between-start">
              <div class="flex-start-center flex-wrap"
                v-if="item.userClubRole !== 'DIRECTOR' && item.topics?.length > 0">
                <div v-for="label in item.topics" :key="label.id"
                  class="label m-text-12-18-400 grey1 m-mr8 flex-start-center m-mb5">
                  <img src="@/assets/mobile/personal-space/label.png" alt="" width="12" height="12" />
                  <span class="m-ml4">{{ label.name }}</span>
                </div>
              </div>
              <div v-else></div>
              <div class="flex-column-end-end">
                <div class="flex-end-center" v-if="item.isHideCapable">
                  <van-button v-if="item.isHideCapable && !item.isHidden" class="inner-btn hidden-btn"
                    @click.stop="handleHidden(item.id, false)">
                    <template #icon>
                      <img src="@/assets/mobile/personal-space/hidden.png" alt="" width="12" height="8" />
                    </template>
                    <span class="m-text-12-18-400 blue1">隐藏</span>
                  </van-button>
                  <van-button v-if="item.isHideCapable && item.isHidden" class="inner-btn has-hidden-btn"
                    @click.stop="handleHidden(item.id, true)">
                    <template #icon>
                      <img src="@/assets/mobile/personal-space/has-hidden.png" alt="" width="12" height="8" />
                    </template>
                    <span class="m-text-12-18-400 grey4">已隐藏</span>
                  </van-button>
                </div>
              </div>
            </div>
            <div class="flex-start-center">

              <van-button v-if="item.isRecommended !== null" class="inner-btn recommend-btn m-mb8 m-mr8"
                :class="{ 'has-recommend': item.isRecommended }" @click.stop="handleRecommend(item)">
                {{ item.isRecommended ? "取消栏目推荐" : "栏目推荐" }}
              </van-button>
              <van-button v-if="item.isWxMpRecommended !== null" class="inner-btn wx-recommend-btn m-mb8"
                :class="{ 'has-recommend': item.isWxMpRecommended }" @click.stop="handleWxMpRecommend(item)">
                {{ item.isWxMpRecommended ? "取消公众号推荐" : "公众号推荐" }}
              </van-button>
            </div>
            <span class="m-text-12-18-400 grey3 flex-end-center" v-if="item.lastUpdateTime">最后修改时间:
              <span class="m-text-12-18-400 grey3">{{ item.lastUpdateTime }}</span>
            </span>
            <div class="bottom flex-start-center m-pt8">
              <div class="count-item view-count flex-start-center m-mr24">
                <img v-if="item.isViewed" class="m-mr6" src="@/assets/mobile/personal-space/view-active.png" alt=""
                  width="14" height="10" />
                <img v-else class="m-mr6" src="@/assets/mobile/personal-space/view.png" alt="" width="14" height="10" />
                <span class="m-text-12-18-400 grey3">{{ item.views }}</span>
              </div>
              <div class="count-item like-count flex-start-center m-mr24"
                @click.stop="clickFavorite(item.id, !item?.isFavorite)">
                <img v-if="!item?.isFavorite" class="m-mr6" src="@/assets/mobile/personal-space/like.png" alt=""
                  width="14" height="13" />
                <img v-else class="m-mr6" src="@/assets/mobile/personal-space/like-active.png" alt="" width="14"
                  height="13" />
                <span class="m-text-12-18-400 grey3">{{ item?.favorites || 0 }}</span>
              </div>
              <div class="count-item comment-count flex-start-center m-mr24">
                <img v-if="item.isCommented" class="m-mr6" src="@/assets/mobile/personal-space/comment-active.png"
                  alt="" width="14" height="14" />
                <img v-else class="m-mr6" src="@/assets/mobile/personal-space/comment.png" alt="" width="14"
                  height="14" />
                <span class="m-text-12-18-400 grey3">{{ item.comments }}</span>
              </div>
              <div class="count-item good-count flex-start-center" @click.stop="clickLike(item, !item?.isLike)">
                <img v-if="!item.isLike" class="m-mr6" src="@/assets/mobile/personal-space/good.png" alt="" width="14"
                  height="14" />
                <img v-else class="m-mr6" src="@/assets/mobile/personal-space/good-active.png" alt="" width="14"
                  height="14" />
                <span class="m-text-12-18-400 grey3">{{ item.likes }}</span>
              </div>
            </div>
          </div>
        </div>
      </van-list>
    </section>

    <van-action-sheet v-model:show="sortSheetVisible" cancel-text="取消" close-on-click-action :actions="sortActions"
      @select="selectSortAction" safe-area-inset-bottom />

    <van-calendar ref="filterDateSheetRef" v-model:show="filterDateSheetVisible" type="range"
      @confirm="handleFilterDateConfirm" safe-area-inset-bottom :min-date="minDate" :max-date="maxDate"
      allow-same-day />
  </div>
</template>

<style lang="scss" scoped>
@use "@/views/mobile/styles/custom-global-mobile-style.scss";

.diary-square {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .header-area {
    width: 100%;
    height: 57px;
    background: linear-gradient(270deg, #508cff 0%, #535dff 100%), #508cff;
    padding: 12px 20px 14px;

    :deep(.van-tabs) {
      flex: 1;
      padding-right: 6px;

      .van-tabs__nav {
        background: transparent;
      }

      .van-tab {
        color: #e2e3e6;
        font-size: 14px;

        &--active {
          color: #ffffff !important;
          font-weight: 600 !important;
        }
      }

      .van-tabs__wrap {
        height: 30px;
      }

      .van-tabs__line {
        background: #fff;
        border-radius: 3px;
        display: none;
      }
    }

    :deep(.van-checkbox) {
      .van-checkbox__label {
        font-size: 14px;
        color: #fff;
      }

      .van-checkbox__icon {
        .van-icon {
          border: 1px solid #c2c9ce;
          background-color: #fff;
        }

        &--checked {
          .van-icon {
            color: #508cff;
          }
        }
      }
    }
  }

  .sort-area {
    padding: 16px 15px 12px;
  }

  .list-area {
    position: relative;
    flex: 1;
    overflow-y: auto;

    .list-item {
      width: 100%;
      background-color: #fff;
      padding: 15px;

      .right-middle {
        position: relative;

        .content-wrapper {
          display: block;
        }

        .content-text {
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
          word-break: break-all;
          margin: 0;

          :deep(img) {
            max-width: 100% !important;
            height: auto !important;
            object-fit: contain;
          }

          :deep(p) {
            img {
              margin: 8px 0;
            }
          }
        }
      }

      .right-bottom {
        padding-top: 8px;

        .label {
          padding: 0 7px;
          border-radius: 19px;
          border: 1px solid #e2e3e6;
          height: 24px;
          line-height: 24px;
        }

        .clocking-time {
          padding: 0 11px;
          border-radius: 19px;
          background-color: #fff5e7;
          height: 24px;
          line-height: 24px;
        }

      }

      .recommend-btn {
        width: auto;
        height: 24px;
        line-height: 24px;
        border-radius: 19px;
        background: #fff;
        color: #FF9B00;
        font-size: 12px;
        border: 1px solid #E2E3E6;
        padding: 0 26px;

        &.has-recommend {
          background: linear-gradient(180deg, #FFC33B 0%, #FFA919 100%);
          color: #fff;
          border: none;
        }
      }

      .wx-recommend-btn {
        width: auto;
        height: 24px;
        line-height: 24px;
        border-radius: 19px;
        background: #fff;
        color: #60AE24;
        font-size: 12px;
        border: 1px solid #E2E3E6;
        padding: 0 26px;

        &.has-recommend {
          background: linear-gradient(180deg, #8CE049 0%, #6DBB31 100%);
          color: #fff;
          border: none;
        }
      }

      .bottom {
        .count-item {}
      }
    }
  }

  .inner-btn {
    width: 56px;
    height: 24px;
    line-height: 24px;
    border-radius: 4px;
    border: 1px solid #e2e3e6;
    padding: 0 4px;

    &.delete-btn {
      margin-right: 6px;
    }

    &.has-hidden-btn {
      width: 68px;
    }
  }

  ::-webkit-scrollbar {
    display: none;
  }
}
</style>

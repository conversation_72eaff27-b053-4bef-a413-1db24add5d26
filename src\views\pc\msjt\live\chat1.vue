<template>
    <div class="chat-wrapper">
        <div class="live-title">评论 <!-- <span class="count">1.5万条</span> --></div>
        <div class="send-message-box" id="send-message-box1">
            <div class="loading" @click="loadMore">{{last_page==0?'-点击加载更多-':'已经到顶啦~'}}</div>
            <div id="send-message-box" ref="message-list">
                <div class="message-list" v-for="message in msgList" :key="message.userName">
                    <img class="message-avatar" :src="message.userAvatar">
                    <div class="message-item">
                        <div class="message-name">
                            <p class="" style="margin-bottom: 6px;font-size: 13px;">{{message.userName}}</p>
                            <!-- <span>{{message.datetime}}</span> -->
                        </div>
                        <div class="message-container" v-if="message.messageType=='TEXT'">
                            <div class="triangle"></div>
                            <template v-for="(item, index) in contentList(message.messageContent)">
                                <span :key="index" class="message-text"
                                      v-if="item.name === 'text'">{{ item.text }}</span>
                                <img v-else-if="item.name === 'img'" :src="item.src" width="20px" height="20px" />
                            </template>
                        </div>
                        <div class="message-img-box" v-if="message.messageType=='IMG'" >
                            <img class="message-img" :src="message.messageContent" alt="聊天图片"  @click="openImg(message.messageContent)">
                        </div>
                        <!-- <div>{{message.content}}</div> -->
                    </div>
                </div>
            </div>  
        </div>
        <div class="send-foot-bar" :class="{'disabled':forbiddenWords}">
            <textarea name="" id="" cols="30" rows="1" v-model="messageContent" :placeholder="forbiddenWords?'你已被禁言，如有疑问请联系课程管理员':'留下你的评论吧'" @focus="focus = true"  @blur="focus = false" @keyup.enter="handleEnter" :disabled="forbiddenWords" maxlength="500"></textarea>
            <div class="flex_between">
                <div class="operate" id="operate">
                    <img id="emojis" :src="basic_static+'images/emoji.png'" alt="发表情" @click="showEmojis()">
                    <label for="add_img"><img class="add_img" :src="basic_static+'images/image.png'" alt="发图片">
                    </label>
                    <input type="file" id="add_img" style="display:none" @change="add_file" data-type="IMG"/>

                    <div class="emojis-box" v-if="emojis_show">
                        <div class="emojis">
                            <div v-for="item in emojiName" class="emoji cursor" :key="item" @click="chooseEmoji(item)">
                                <img :src="emojiUrl + emojiMap[item]" style="width:30px;height:30px"/>
                            </div>
                        </div>
                        <div class="emojis-target"></div>
                    </div>
                </div>
                <div class="send-btn" @click="sendTextMessage('TEXT')" @keyup.enter="handleEnter">发送</div>
                <div class="confirmDialog" v-if="confirmDialog">
                    <div class="flex_between">
                        <span>发送</span>
                        <!-- <span style="cursor:pointer;font-size: 28px;line-height: 20px;" @click="concelSendMessage">×</span> -->
                    </div>
                    <div class="confirmDialogFileName">{{fileName}}</div>
                    <div class="flex_end">
                        <span class="confirmDialogSendBtn" @click="sendTextMessage">发送</span>
                        <span class="confirmDialogConcelBtn" @click="concelSendMessage">取消</span>
                    </div>
                </div>
            </div>
        </div>

        <div v-if="open_img_show&&!ispc" @click="openImg(open_img)" class="img-box-marker" style="position:absolute;bottom:0;top:0;right:0;left:0;width:100%;height:100%;z-index:1000000;cursor:pointer;padding:5%;background: rgba(31,31,31,.4);display:flex;flex-direction:row;align-items:center;">
            <!-- <img :src="basic_static+'images/close_dialog.png'"  style="height: auto;width: 100%;object-fit: cover;" alt=""> -->
            <img :src="open_img"  style="height: auto;max-width: 100%;max-height:100%; object-fit: cover;width:auto;margin: 0 auto;z-index:1000000;" alt="">
        </div>
        <div v-if="open_img_show&&ispc" @click="openImg(open_img)" class="img-box-marker" style="position:fixed;bottom:0;top:0;right:0;left:0;width:100%;height:100%;z-index:1000000;cursor:pointer;padding:5%;background: rgba(31,31,31,.4);display:flex;flex-direction:row;align-items:center;">
            <!-- <img :src="basic_static+'images/close_dialog.png'"  style="height: auto;width: 100%;object-fit: cover;" alt=""> -->
            <img :src="open_img"  style="height: auto;max-width: 100%;max-height:100%; object-fit: cover;width:auto;margin: 0 auto;z-index:1000000;" alt="">
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import TIM from '@tencentcloud/chat'; // 请替换为实际的 TIM SDK 导入路径
// import COS from 'your-cos-sdk'; // 请替换为实际的 COS SDK 导入路径
// import moment from 'moment';
import { getQueryVariable } from '@/utils/index.js'; // 请替换为实际的工具函数导入路径
// 定义 props
const props = defineProps({
    im_info: {
        type: Object,
        default: () => ({})
    }
});
// 响应式数据
const live_id = ref('');
const last_id = ref('');
const last_page = ref(0);
const tim = ref(null);
const basic_static = ref('');
const hasLogin = ref(1);
const hasInGroup = ref(1);
const messageContent = ref('');
const addImgContent = ref('');
const messageType = ref('');
const userName = ref('');
const userAvatar = ref('');
const msgList = ref([]);
const myProfile = ref({});
const emojis_show = ref(false);
const emojiMap = {}; // 请根据实际情况初始化
const emojiName = []; // 请根据实际情况初始化
const emojiUrl = ''; // 请根据实际情况初始化
const tweblive = ref(null);
const confirmDialog = ref(false);
const fileName = ref('');
const login_mess = ref({});
const user_type = ref('');
const open_img = ref('');
const open_img_show = ref(false);
const forbiddenWords = ref(false);
const focus = ref(false);
const ispc = ref(false);
const tabSelected = ref(1);

// 初始化 TIM
const initTim = () => {
    const options = {
        SDKAppID: props.im_info.app_id // 接入时需要将0替换为您的即时通信 IM 应用的 SDKAppID
    };
    tim.value = TIM.create(options);
    tim.value.setLogLevel(0);
    tim.value.registerPlugin({'cos-js-sdk': COS});
};

// 初始化监听器
const initListener = () => {
    console.log('监听事件');
    tim.value.on(TIM.EVENT.MESSAGE_RECEIVED, onMessageReceived);
    tim.value.on(TIM.EVENT.SDK_READY, onSdkReady);
};

// 登录
const enter = () => {
    const promise = tim.value.login({userID: props.im_info.user_id, userSig: props.im_info.user_sign});
    promise.then((imResponse) => {
        console.log('登录成功');
        if (imResponse.data.repeatLogin === true) {
            console.log(imResponse.data.errorInfo);
        }
        hasLogin.value = 1;
    }).catch((imError) => {
        console.warn('login error:', imError);
    });
};

// 登出
const leave = () => {
    const promise = tim.value.logout();
    promise.then((imResponse) => {
        console.log('退出成功', imResponse.data);
    }).catch((imError) => {
        console.warn('logout error:', imError);
    });
};

// 加入群组
const inGroup = () => {
    const promise = tim.value.joinGroup({ groupID: props.im_info.group_id, type: TIM.TYPES.GRP_AVCHATROOM });
    promise.then((imResponse) => {
        console.log('成功加入群组');
        switch (imResponse.data.status) {
            case TIM.TYPES.JOIN_STATUS_WAIT_APPROVAL:
                break;
            case TIM.TYPES.JOIN_STATUS_SUCCESS:
                console.log(imResponse.data.group);
                hasInGroup.value = 1;
                break;
            case TIM.TYPES.JOIN_STATUS_ALREADY_IN_GROUP:
                hasInGroup.value = 1;
                break;
            default:
                break;
        }
    }).catch((imError) => {
        console.warn('joinGroup error:', imError);
    });
};

// SDK 准备就绪
const onSdkReady = () => {
    const isSDKReady = name === TIM.EVENT.IM_READY ? true : false;
    if (isSDKReady) {
        getMyProfile();
    }
};

// 获取个人信息
const getMyProfile = () => {
    tim.value.getMyProfile().then((res) => {
        console.log('个人信息', res);
        updateMyProfile();
    }).catch(() => {
    });
};

// 更新个人信息
const updateMyProfile = () => {
    const param = {
        nick: userName.value,
        avatar: userAvatar.value,
        selfSignature: '我的个性签名'
    };
    tim.value.updateMyProfile(param).then((res) => {
        console.log('更新个人信息', res);
        myProfile.value = res.data;
    }).catch(() => {
    });
};

// 收到消息
const onMessageReceived = (event) => {
    console.log('收到消息', event);
    if (event.name === "onMessageReceived") {
        event.data.map((item) => {
            if (item.from === myProfile.value.userID) {
                return;
            } else {
                if (msgList.value.some((msg) => msg.msg_seq === item.clientSequence)) {
                    return;
                }
                if (item.payload.data === 'ADMIN') {
                    const forbidden = JSON.parse(item.payload.description);
                    console.log(forbidden);
                    if (forbidden.data.state === 1) {
                        forbiddenWords.value = true;
                    } else {
                        forbiddenWords.value = false;
                    }
                } else {
                    if (item.payload.description) {
                        msgList.value.push({
                            datetime: moment(item.time * 1000).format('MM-DD HH:mm:ss'),
                            messageContent: item.payload.description,
                            messageType: item.payload.data,
                            userName: canIUseNick(item.nick) ? item.nick : item.from,
                            userAvatar: item.avatar ? item.avatar : basic_static.value + 'images/nav/student.png'
                        });
                    }
                }
                console.log('消息列表');
                console.log(msgList.value);
                scrollBottom();
            }
        });
    }
};

// 取消发送消息
const concelSendMessage = () => {
    confirmDialog.value = false;
    messageContent.value = '';
    addImgContent.value = '';
    messageType.value = '';
    fileName.value = '';
};

// 发送文本消息
const sendTextMessage = (type) => {
    if (!user_type.value || user_type.value === '99') {
        showDialog();
        return;
    }
    if (type && type === 'TEXT') {
        messageType.value = 'TEXT';
        if (messageContent.value === '' || messageContent.value.trim().length === 0) {
            messageContent.value = '';
            return;
        }
    }

    const messageobj = {
        to: props.im_info.group_id,
        conversationType: TIM.TYPES.CONV_GROUP,
        payload: {
            data: messageType.value,
            description: messageType.value === 'IMG' ? addImgContent.value : messageContent.value,
            extension: ''
        }
    };
    msgList.value.push({
        datetime: Date.now() / 1000,
        messageContent: messageType.value === 'IMG' ? addImgContent.value : messageContent.value,
        messageType: messageType.value,
        userName: userName.value,
        userAvatar: userAvatar.value
    });
    console.log('消息列表', msgList.value);
    concelSendMessage();

    const message = tim.value.createCustomMessage(messageobj);
    const promise = tim.value.sendMessage(message);
    promise.then((imResponse) => {
        console.log('发送成功', imResponse);
    }).catch((imError) => {
        console.warn('发送失败', imError);
    });
};

// 添加文件
// 添加文件
const add_file = (e) => {
    const filelist = e.target.files;
    messageType.value = e.target.dataset.type;
    const str = e.target.value;
    const index = str.lastIndexOf('.');
    const type = str.substr(index).toLowerCase();
    const o = str.lastIndexOf("\\");
    const name = str.substr(o + 1);
    const allowType = [];

    if (messageType.value === 'IMG') {
        allowType = ['.png', '.jpg', '.jpeg'];
        if (allowType.indexOf(type) < 0) {
            alert('请上传.png或.jpg或.jpeg格式图片');
            e.target.value = '';
            return;
        }
    }

    for (let i = 0; i < filelist.length; i++) {
        fileName.value += filelist[i].name + ';';
        getCos(filelist[i], type, filelist[i].name);
    }
    e.target.value = '';
};

// 上传图片
const upload_img = (file, type, name) => {
    const key = 'filecos/' + getdate() + randomString(32, '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ') + type;
    const cos = new COS({
        SecretId: cos.value.temp_keys.credentials.tmpSecretId,
        SecretKey: cos.value.temp_keys.credentials.tmpSecretKey,
        XCosSecurityToken: cos.value.temp_keys.credentials.sessionToken,
        StartTime: cos.value.temp_keys.startTime,
        ExpiredTime: cos.value.temp_keys.expiredTime
    });

    cos.putObject({
        Bucket: cos.value.bucket,
        Region: cos.value.region,
        Key: key,
        StorageClass: 'STANDARD',
        Body: file,
        onProgress: (progressData) => {
            if (progressData.percent === 1) {
                // 上传完成
            }
        }
    }, (err, data) => {
        if (err) {
            if (err.statusCode === 403) {
                alert('暂不支持该格式！');
            }
            return;
        }
        if (data.statusCode === 200) {
            if (messageType.value === 'IMG') {
                addImgContent.value = 'https://' + data.Location;
                confirmDialog.value = true;
            }
        }
    });
};

// 获取 COS 配置
const getCos = (file, type, name) => {
    const request_url = basic_api_url + '/base/cos/';
    $.ajax({
        url: request_url,
        type: 'POST',
        async: false,
        data: { api_token: api_token },
        dataType: 'JSON',
        success: (res) => {
            if (res.state === 0) {
                cos.value = res.data;
                Setcookie('uploadcos', JSON.stringify(res.data));
                upload_img(file, type, name);
            } else {
                // RequestFailProcess(res);
            }
        },
        error: () => {
            alert('网络错误，请重试');
        }
    });
};

// 滚动到底部
const scrollBottom = () => {
    const messagebox = document.getElementById('send-message-box1');
    if (messagebox.scrollHeight > messagebox.clientHeight) {
        messagebox.scrollTop = messagebox.scrollHeight;
    }
};

// 加载更多消息
const loadMore = () => {
    if (last_page.value !== 1 && !loading.value) {
        loading.value = true;
        setTimeout(() => {
            getCommentList();
        }, 500);
    }
};

// 滚动加载
const scrollTop = () => {
    const messagebox = document.getElementById('send-message-box');
    const offsetTop = messagebox.offsetTop;
    const toTop = offsetTop - $(window).scrollTop();
    
    if (toTop < 10 && last_page.value !== 1 && !loading.value) {
        loading.value = true;
        setTimeout(() => {
            getCommentList();
        }, 500);
    }
};

// 获取评论列表
const getCommentList = (type) => {
    const data = {
        live_id: live_id.value || props.im_info.live_id,
        last_id: last_id.value,
        limit: 10
    };

    // $.ajax({
    //     url: basic_api_url + '/xkzb/im/live_message_list/',
    //     type: 'POST',
    //     data,
    //     async: false,
    //     dataType: 'JSON',
    //     success: (res) => {
    //         if (res.state === 0) {
    //             const msg_list = res.data.msg_list;
    //             for (let i = 0; i < msg_list.length; i++) {
    //                 const obj = {};
    //                 obj.userAvatar = msg_list[i].user_type === 1 
    //                     ? basic_static.value + 'images/nav/teacher.png' 
    //                     : basic_static.value + 'images/nav/student.png';
    //                 obj.userName = msg_list[i].user_title;
    //                 obj.messageContent = msg_list[i].content;
    //                 obj.messageType = msg_list[i].type_title;
    //                 obj.msg_seq = msg_list[i].msg_seq;
    //                 msgList.value.unshift(obj);
    //             }

    //             last_id.value = res.data.last_id;
    //             last_page.value = res.data.last_page;
    //             loading.value = false;

    //             if (type && type === 'init') {
    //                 scrollBottom();
    //             }
    //         } else {
    //             // RequestFailProcess(res);
    //         }
    //     },
    //     error: () => {
    //         alert('网络错误，请重试');
    //     }
    // });
};

// Cookie 操作
const getCookie = (c_name) => {
    if (document.cookie.length > 0) {
        let c_start = document.cookie.indexOf(c_name + '=');
        if (c_start !== -1) {
            c_start = c_start + c_name.length + 1;
            let c_end = document.cookie.indexOf(';', c_start);
            if (c_end === -1) c_end = document.cookie.length;
            return unescape(document.cookie.substring(c_start, c_end));
        }
    }
    return '';
};

const Setcookie = (name, value) => {
    const expdate = new Date();
    expdate.setTime(expdate.getTime() + 30 * 24 * 60 * 60 * 1000);
    document.cookie = name + '=' + value + ';expires=' + expdate.toGMTString() + ';path=/';
};

// 随机字符串生成
const randomString = (length, chars) => {
    let result = '';
    for (let i = length; i > 0; --i) {
        result += chars[Math.floor(Math.random() * chars.length)];
    }
    return result;
};

// 获取当前日期
const getdate = () => {
    const myDate = new Date();
    const year = myDate.getFullYear();
    const mon = myDate.getMonth() + 1;
    const date = myDate.getDate();
    return `${year}/${mon}/${date}/`;
};

// 选择表情
const chooseEmoji = (item) => {
    messageContent.value += item;
};

// 显示表情框
const showEmojis = () => {
    emojis_show.value = !emojis_show.value;
    document.onclick = () => {
        const e = e || window.event;
        const elem = e.srcElement || e.target;
        while (elem) {
            if (elem.id === 'emojis') {
                return;
            }
            elem = elem.parentNode;
        }
        emojis_show.value = false;
    };
};

// 打开图片查看器
const openImg = (url) => {
    open_img.value = url;
    open_img_show.value = !open_img_show.value;
};

// 处理换行
const handleLine = () => {
    messageContent.value += '\n';
};

// 处理 Enter 键
const handleEnter = () => {
    sendTextMessage('TEXT');
};

// 判断是否使用昵称
const canIUseNick = (nick) => {
    if (nick && nick !== '""' && nick !== "''") {
        return true;
    }
    return false;
};

// 判断是否是 PC 端
const isPC = () => {
    const userAgentInfo = navigator.userAgent;
    const Agents = ['Android', 'iPhone', 'SymbianOS', 'Windows Phone', 'iPad', 'iPod'];
    let flag = true;
    for (let v = 0; v < Agents.length; v++) {
        if (userAgentInfo.indexOf(Agents[v]) > 0) {
            flag = false;
            break;
        }
    }
    return flag;
};

// 计算属性：内容列表
const contentList = (text) => {
    return decodeText(text);
};

// 生命周期钩子
onMounted(() => {
    live_id.value = getQueryVariable('live_id');
    getCommentList('init');
    basic_static.value = basic_static;
    
    // if (getLocalStorage('login_mess')) {
    //     login_mess.value = JSON.parse(getLocalStorage('login_mess'));
    //     user_type.value = login_mess.value.user_type;
        
    //     if (user_type.value === 1) {
    //         userName.value = login_mess.value.teacher_info.teacher_title;
    //         userAvatar.value = basic_static.value + 'images/nav/teacher.png';
    //     } else if (user_type.value === 2) {
    //         userName.value = login_mess.value.student_info.student_title;
    //         userAvatar.value = basic_static.value + 'images/nav/student.png';
    //     } else if (user_type.value === 3) {
    //         userName.value = login_mess.value.parent_info.parent_title;
    //         userAvatar.value = basic_static.value + 'images/nav/student.png';
    //     }
    // }
    
    ispc.value = isPC();
    
    timer2 = setTimeout(() => {
        if (props.im_info.app_id === '') {
            // 处理未获取到 app_id 的情况
        } else {
            clearTimeout(timer2);
            initTim();
            initListener();
            enter();
            inGroup();
        }
    }, 1000);

    window.addEventListener('scroll', scrollTop, true);
});

onBeforeUnmount(() => {
    // $(document).off('click');
    leave();
    window.removeEventListener('scroll', scrollTop);
});

// 监听 props
watch(
    () => props.im_info,
    (newVal, oldVal) => {
        if (newVal.im_block_state === 1) {
            forbiddenWords.value = true;
        } else {
            forbiddenWords.value = false;
        }
    },
    { deep: true }
);



// 定义 emits
const emits = defineEmits([]);

// 导入外部依赖
const decodeText = (text) => {
    // 这里需要根据实际的 decodeText 函数实现
    return [];
};

// 模拟全局变量
const basic_api_url = ''; // 请替换为实际的 API URL
const api_token = ''; // 请替换为实际的 API Token
const cos = ref(null);
const loading = ref(false);
let timer2 = null;
</script>

<style>
    .cursor {cursor: pointer;}
    .chat-wrapper {box-sizing :border-box;padding:0 10px 10px 10px;width :100%;height :100%;margin: 0 auto;position: relative;display: flex;flex-direction: column;border: 0.966549px solid #D3D7DB;border-bottom: none;;position: relative;}
    .live-title {color: #28B28B;font-size: 16px;border-bottom:3px solid #28B28B;height: 44px;line-height: 44px;width: 100%;padding: 0 10px;}
    .live-title .count{font-size: 13px;color: #BDBDBD;}

    .send-message-box{overflow: scroll;z-index: 1;width :100%;box-sizing :border-box;-webkit-overflow-scrolling: touch;    flex-grow: 1;}
  .send-message-box::-webkit-scrollbar {
  /*滚动条整体样式*/
  width : 10px;  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  }
  .send-message-box::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  background: #F2F2F2;
  }
  .send-message-box::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  border-radius: 10px;
  background   : #fff;
  }
  .emojis-box .emojis::-webkit-scrollbar {
  /*滚动条整体样式*/
  width : 8px;  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  }
  .emojis-box .emojis::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 8px;
  background: #F2F2F2;
  }
  .emojis-box .emojis::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  border-radius: 8px;
  background   : #fff;
  }
  .send-foot-bar textarea::-webkit-scrollbar {
  /*滚动条整体样式*/
  width : 6px;  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  }
  .send-foot-bar textarea::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 6px;
  background: #e8e4e4;
  }
  .send-foot-bar textarea::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  border-radius: 6px;
  background   : #fff;
  }

 .emojis-box {}

 
    .message-list {color: #333333;display: flex;margin-bottom: 10px;}
    .message-list .message-avatar {display :inline-block;min-width: 30px; max-width :30px;height: 30px;border-radius: 50%;}
    .message-list .message-item {font-size: 14px;padding: 0 8px;position: relative;line-height: 25px;word-wrap: break-word;white-space: normal;width:90%;margin-left: 6px;
    }
    .message-list .message-name{font-size: 13px;line-height: 23px;color: #828282;display: flex;margin-bottom: 6px;}
	.message-container {display :inline-block;position :relative;background-color: #fff;border-radius: 3px;padding: 5px 12px 5px 10px;color:#333333;border-radius: 0px 8px 8px 8px;}
    .message-container .triangle {width: 0;height: 0;border-left: 5px solid transparent;border-right: 5px solid transparent;border-bottom: 6px solid #F6F9FB;position: absolute;left: 8px;top: -6px;
    }
    .message-container img{display: inline;}
	.message-container .message-text {font-size :15px;line-height: 18px;white-space:normal;word-break :break-all;word-wrap: break-word;}
	.message-img-box{width: 60%;height: 60px;}
	.message-img{max-width: 100%;max-height: 100%; cursor: pointer;cursor:pointer;width: auto;height: auto;}

    .send-foot-bar {width: 100%;position: relative;border: 0.5px solid #D3D7DB;background: #F5F9FB;padding: 6px 5px 3px 0;border-radius: 4px;}
    .send-foot-bar .operate{display: flex;flex-direction: row;align-items: center;position: relative;}
    .send-foot-bar .operate>img{cursor: pointer;}
    .send-foot-bar img#emojis {height: 28px;width: 28px;padding: 4px;}
    .send-foot-bar img.add_img {height: 28px;width: auto;padding: 4px;cursor: pointer;}
    .send-foot-bar textarea {width: 100%;outline: none;resize: none;line-height: 22px;height :44px;box-sizing :border-box;position: relative;border-radius: 2px;padding: 0 10px 0 10px;font-size: 15px;border: none;background: #F5F9FB;}
    .send-foot-bar .send-btn {display: flex;background: #28B28B;border-radius: 13px;color: #ffffff;font-size: 14px;padding: 0 10px;flex-shrink: 0;cursor: pointer;height: 26px;line-height: 26px;
    }
    .send-foot-bar.disabled img#emojis,.send-foot-bar.disabled .add_img{display: none;}
    .send-foot-bar.disabled .send-btn{cursor:not-allowed;background: #BDBDBD;}
	
	/*表情弹框*/
    .emojis-box{width: auto !important;max-width: 400px;background: #FFF; min-width: 195px;border-radius: 4px;border: 1px solid #EBEEF5;padding: 12px;z-index: 2000;color: #606266;line-height: 1.4;text-align: justify;font-size: 14px;-webkit-box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);word-break: break-all;position: absolute;top: -190px;left: 0;}
    .emojis-box .emojis {display :flex;flex-direction: row;flex-wrap :wrap;height: 160px;box-sizing :border-box;overflow-y: scroll;}
    .emojis-box .emoji {height: 40px;width: 40px;box-sizing: border-box;}
    .emojis-box .emojis-target{filter: drop-shadow(0 2px 12px rgba(0, 0, 0, .03));position: absolute;top: 182px;left: 16px;z-index: 3000;}
    .emojis-box .emojis-target:after{content: " ";border-width: 8px;display: block;width: 0;height: 0;
    border-color: transparent;border-style: solid;bottom: 1px;margin-left: -6px;border-top-color: #fff;border-bottom-width: 0;}
    
	/*确认弹框*/
	.confirmDialog{
		padding:10px 20px;position:absolute;right:10px;bottom:0;background:#fff;border:1px solid #E9E9E9;z-index: 10;
	}
	.confirmDialogFileName{padding:10px;background:#eeeeee;margin:20px 0;}
	.confirmDialogSendBtn{background:#28B28B;padding:4px 15px;color:#fff;cursor:pointer;}
	.confirmDialogConcelBtn{background:#fff;padding:4px 15px;color:#4f4f4f;cursor:pointer;}

	.loading{text-align: center;color: #828282;font-size: 12px;padding: 8px;cursor: pointer;}
    /* textarea {
        resize none
    } */
    /* @media screen and (max-width:750px){
		.chat-wrapper{border: none !important;background: #F5F7F7;}
		.send-foot-bar{padding: 0.32rem 0.3rem 0.64rem;background: #F7FAF9;border: 0;border-radius: 8px;;position: fixed;bottom: 0;left: 0;right: 0;z-index: 10;background: #fff;}
	    .send-foot-bar img {height: 0.58rem;padding: 0.08rem;width: auto;}
	    .send-foot-bar textarea {height: 0.8rem; line-height: 0.8rem;font-size: 0.3rem;margin: 0 0.1rem 0 0.24rem;border-radius: 8px;}
	    .send-foot-bar .send-btn {font-size: 0.28rem; padding: 0.2rem 0.28rem;height: 0.8rem; background: #28B28B; border-radius: 8px; line-height: 0.4rem; }
	    .confirmDialog{left: 0;}
	} */

</style>
@use "./variables.css";
@use "./transition.scss";
@use "./element-plus-variables.css";
@use "./element-plus.scss";
@use "./mixins.scss";
@use "./fonts/index.scss";
@use "./custom-global-style.scss";

.app-container {
  padding: 24px;
}

html {
  height: 100%;
  // 灰色模式
  &.grey-mode {
    filter: grayscale(1);
  }
  // 色弱模式
  &.color-weakness {
    filter: invert(0.8);
  }
}

body {
  height: 100%;
  color: var(--v3-body-text-color);
  background-color: #F5F6FA;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial,
    sans-serif;
  @extend %scrollbar;
}
// 
#app {
  height: 100%;
 
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

a,
a:focus,
a:hover {
  color: inherit;
  outline: none;
  text-decoration: none;
}

div:focus {
  outline: none;
}

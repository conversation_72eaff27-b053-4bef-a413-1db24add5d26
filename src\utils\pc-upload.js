import { uploadFile,uploadHtmlFile,uploadHeaderFile } from '@/api/pc/index.js'
import {ElLoading} from 'element-plus'
let loadingInstance=''
export function uploadFileFunc(file, callback) {
    loadingInstance = ElLoading.service({ fullscreen: true,text:"上传中" })
    uploadFile({
        file:file
    }).then(data => {
        loadingInstance.close()
        callback(data.data)
    }).catch(()=>{
        loadingInstance.close()
    })
}
export function uploadHtmlFileFunc(file, callback) {
    loadingInstance = ElLoading.service({ fullscreen: true,text:"上传中" })
    uploadHtmlFile({
        file:file
    }).then(data => {
        loadingInstance.close()
        callback(data.data)
    }).catch(()=>{
        loadingInstance.close()
    })
}
export function uploadHeaderFileFunc(file, callback) {
    loadingInstance = ElLoading.service({ fullscreen: true,text:"上传中" })
    uploadHeaderFile({
        file:file
    }).then(data => {
        loadingInstance.close()
        callback(data.data)
    }).catch(()=>{
        loadingInstance.close()
    })
}
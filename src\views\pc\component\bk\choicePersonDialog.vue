<template>
 <el-dialog
    :model-value="choicePersonDialogShow"
    title="选择人员"
    width="456"
    :before-close="cancel"
  >
    <el-form>
        <el-form-item>

             <el-select placeholder='请选择' v-model="form.tenantId" style="border-radius:200px">
                     <el-option :label="item.tenantName" :value="item.tenantId" v-for="item in userInfo?.userTenants" :key="item.tenantId" />
             </el-select>
        </el-form-item>
          <el-form-item>
                <div class="ipt flex_1 flex_start">
                    <input type="text" placeholder="请输入" class="flex_1" v-model="form.name" @keyup.enter="searchMember">
                   <el-icon style="font-size:20px;cursor:pointer" @click="searchMember"><Search /></el-icon>
                </div>
                  <el-table
                    ref="multipleTableRef"
                    :data="tableData"
                    row-key="id"
                    style="margin-top:24px"
                     height="300px"
                     :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F8F9FF"}'
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" :selectable='selectable' width="55"   reserve-selection/>
                    <el-table-column label="姓名" width="120" show-overflow-tooltip="true">
                        <template #default="scope">{{ scope.row.name }}</template>
                    </el-table-column>
                    <el-table-column prop="mobile" label="手机号" />
                    <el-table-column prop="subjectName" label="学科" />
                </el-table>
                 <!-- <div class="flex_between pagination">
                         <p>共3条</p>
                         <el-pagination
                            background
                            layout="total,prev, pager, next"
                            :total="total"
                            v-model:page-size='form.pageSize'
                            class="mt-4"
                            @current-change='currentChange'
                        />
                 </div> -->
        </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="confirm">
                 确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
 import {reactive, ref,defineProps} from 'vue'
 import {packageMember,packageBatchShare} from '@/api/pc/bk.js'

 import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'
import {useRouter} from 'vue-router'
import { ElMessage,ElMessageBox } from 'element-plus'
let userInfoStore=useUserInfoStoreHook()
let {userInfo}=storeToRefs(userInfoStore)
 let props=defineProps(['choicePersonDialogShow','idList'])
 let emits=defineEmits('closeDialog')
 let dialogVisible=ref(true)
 let tableData=ref([])
 let form=reactive({
    tenantId:'',
    name:''

 })
 let multipleTableRef=ref()
 let userIdList=ref([])

 let total=ref(1)
 let handleSelectionChange=(val)=>{
   let arr=[]
   val.forEach(item=>{
         arr.push(item.id)
   })
   userIdList.value=arr
 }
 let selectable=(val)=>{
      return true
 }
 let searchMember=async()=>{
  if(form.tenantId){
    let res=await packageMember(form)
    if(res){
      tableData.value=res.data
    }
  }else{
    ElMessage.warning('请选择单位')
    return
  }
 }
 let confirm=async()=>{
   if(userIdList.length==0){
    ElMessage.warning('请选择分享对象')
    return
   }else{
     let res=await packageBatchShare({
      idList:props.idList,
      userIdList:userIdList.value
     })
     if(res){
       ElMessage.success('分享成功')
       cancel()
     }
   }
 }
 let cancel=()=>{
  Object.assign(form,{
    tenantId:'',
    name:''
  })
  tableData.value=[]
   multipleTableRef.value.clearSelection()
   emits('closeDialog')
 }
 let currentChange=()=>{

 }
</script>
<style scoped lang='scss'>
:deep(.el-select__wrapper){
    border-radius: 200px;    min-height: 40px !important;
}
    .ipt{border: 1px solid #E2E3E6;
       
        height: 40px;
        background: #fff;
        border-radius: 100px;
         padding: 4px 16px;
        input{
           border: none;
        }
    }
    .pagination{
        width: 100%;
        p{
        font-size: 14px;
        color: #6D6F75;
        margin-top: 16px;
    }
    }
</style>

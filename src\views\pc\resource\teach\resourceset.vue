<template>
    <div  class="content-box  question-set mt10" >
        <div class="flex_between">
            <h3>资源审核</h3>
            <div class="ipt-box flex_start">
                    <input type="text" class="flex_1" v-model="searchForm.resourceName" placeholder="搜索资源名称"  @change="goSearch"/>
                    <el-icon @click="goSearch"><Search /></el-icon>
            </div>
            <p></p>
        </div>
      <div class="flex_between" style="margin-top: 24px;">
        <div class="flex_start date-search">
          <img src="@/assets/pc/date.png" alt="" />
            <el-date-picker
              @change="goSearch"
                v-model="searchForm.beginApproveTime"
                type="date"
                placeholder="开始日期"
                value-format="YYYY-MM-DD"
            />
            <p>-</p>
             <el-date-picker
             @change="goSearch"
                v-model="searchForm.endApproveTime"
                type="date"
                placeholder="结束日期"
                 value-format="YYYY-MM-DD"
            />
         </div>
         <el-select placeholder="请选择"   @change="goSearch" clearable v-model="searchForm.approveStatus" style="width:160px;height:40px">
            <el-option value='1' label='待审核'></el-option>
            <el-option value='2' label='已通过'></el-option>
            <el-option value='3' label='已驳回'></el-option>
         </el-select>
      </div>
       
        <el-table :data="tableData" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F9F9F9"}'>
            <el-table-column prop="resourceName" label="资源名称" show-overflow-tooltip>
               <template  #default='scope'>
                 <el-link type="primary" @click="goPreview(scope.row)">{{scope.row.resourceName}}</el-link>
                   <!-- <el-button type="priamry" text></el-button> -->
                </template>
           </el-table-column>
            <el-table-column prop="questionNumber" label="资源类型">
                <template  #default='scope'>
                    {{scope.row.category==1?'课件':scope.row.category==2?'教案':scope.row.category==3?'音视频':'其他文档资源'}}
                </template>
           </el-table-column>
            <el-table-column prop="tenantTypeName" label="申请级别" />
              <el-table-column prop="address" label="状态">
                <template #default='scope'>
                    <div class="common-status flex_start pass" v-if="scope.row.approveStatus==2">
                        <img src="@/assets/pc/pass.png" alt="">
                         <p>已通过</p>
                    </div>
                   
                    <div class="common-status flex_start reject"  v-if="scope.row.approveStatus==3">
                        <img src="@/assets/pc/reject.png" alt="">
                         <p>已驳回</p>
                    </div>
                     <div class="common-status flex_start checking"  v-if="scope.row.approveStatus==1">
                        <img src="@/assets/pc/checking.png" alt="">
                         <p>待审核</p>
                    </div>
                </template>
            </el-table-column>
             <el-table-column prop="applyUserName" label="申请人" show-overflow-tooltip="true"  />
              <el-table-column prop="approveTime" label="审核时间"  />
            <el-table-column prop="address" label="操作">
                <template #default='scope'>
                    <div class="flex_start" style="height:100%"  v-if="scope.row.approveStatus==1">
                         <el-button type="primary" link style="font-size:16px" @click="goCheck(2,scope.row)">通过</el-button>
                          <p class="line"></p>
                         <el-button type="primary" link style="font-size:16px;color: #E72C4A;" @click="goCheck(3,scope.row)">驳回</el-button>
                    </div>
                    <div v-else>
                             -
                    </div>
                    <!-- <div>
                        -
                    </div> -->
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                background
                layout="total,prev, pager, next"
                :total="total"
                class="mt-4"
                :current-page='searchForm.pageNum'
                @current-change='currentChange'
            />
    </div>
</template>
<script setup>
import {ref,reactive, onMounted} from 'vue'
import {resourceCheckList,resourceCheck}  from '@/api/pc/index.js'
import {useRouter} from 'vue-router'
import {ElMessageBox,ElMessage} from 'element-plus'
let tableData=ref([{}])
let router=useRouter()
let searchForm=reactive({
    resourceName:'',
    approveStatus:'',
    beginApproveTime:'',
    endApproveTime:'',
    pageSize:10,
    pageNum:1
})
let total=ref(0)
let currentChange=(val)=>{
    Object.assign(searchForm,{
        pageNum:val
    })
    getList()
}
let goPreview=(row)=>{
    router.push('/resource/teach/preview?id='+row.resourceId+'&type=preview')
}
let goCheck=(type,row)=>{
     ElMessageBox.confirm(
    type==2?'确认通过吗?':"确认驳回吗",
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async() => {
        let res=await resourceCheck({
            processId:row.id,
            status:type
        })
        if(res){
            ElMessage({
                type: 'success',
                message: type==2?'通过成功':'驳回成功',
            })
             getList()
        }
    
    })
    .catch(() => {
      
    })
}
let goSearch=()=>{
  Object.assign(searchForm,{
        pageNum:1,
        beginApproveTime:searchForm.beginApproveTime?searchForm.beginApproveTime:'',
        endApproveTime:searchForm.endApproveTime?searchForm.endApproveTime:''
    })
    getList()
}
let goDownload=(row)=>{
      router.push('/resource/question/preview?id='+row.id)
}
let goEdit=(row)=>{
    router.push('/resource/question/set?id='+row.id)
}
let getList=async()=>{
  let res=await resourceCheckList(searchForm) 
  if(res){
     tableData.value=res.data.list
     total.value=res.data.total
  }
}
onMounted(()=>{
    getList()
})
</script>
<style lang='scss' scoped>
.question-set{
    padding: 16px 24px;
    background: #FFFFFF;
    border-radius: 12px;
    .ipt-box{
        cursor: pointer;
        width: 50%;
        border-radius: 24px;
        border: 1px solid #E2E3E6;
        margin: auto;
        height: 48px;
        padding: 0 16px;
        input{
            border: none;
        }
    }
    .date-search{
      
        img{
            width: 18px;
            height: auto;
            margin-right: 8px;
        }
        p{
            margin: 0 4px;
            color: #94959C;
        }
    }
    :deep(.el-input__wrapper),:deep(.el-date-editor.el-input){
        height: 40px;
    }
    :deep(.el-table .cell){
        height: 48px;
        line-height: 48px;
        font-size: 16px;
    }
    .common-status{
        font-size: 16px;
        img{
            width: 14px;
            height: auto;
            margin-right: 4px;
        }
    }
    .pass{
       color: #23D486;
    }
    .reject{
         color: #E72C4A;
    }
    .checking{
        color: #4278FD;
    }
    .line{
        width: 1px;
        height: 18px;
        background: #B4B6BE;
        margin: 0 12px;
    }
}
:deep(.el-select__wrapper){
    height: 40px;
}
</style>


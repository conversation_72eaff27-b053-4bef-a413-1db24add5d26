<template>
    <div class="preview-container">
        <!-- <video src="https://test.tq-edu.com/api/hasgktltsxt/dxs/fs/file?fileId=1915228381811646466" controls></video> -->
        <div class="video" id="video" style="height:100%;width:100%;" v-show="row.fileType==2">

        </div>
         <div class="audio flex_center" style="height:100%" v-show="row.fileType==3">
            <!-- {{audioUrl}} -->
                <audio ref="audioPlayer" controls id="audioID">
                    <source :src="audioUrl" type="audio/ogg">
                    <source :src="audioUrl" type="audio/mpeg">
                     您的浏览器不支持音频播放功能
                </audio>

        </div>
        <div class="iframe" v-show="row.fileType==1">
             <iframe :src="row.previewUrl?row.previewUrl:row.fileUrl" frameborder="0" width="100%" height="100%"></iframe>
        </div>

    </div>
</template>
<script setup>
import Player, { Events } from 'xgplayer'; // 引入西瓜视频模块
import 'xgplayer/dist/index.min.css'; // 引入西瓜视频样式
import {useRoute} from 'vue-router'
// import Vue3AudioPlayer from 'vue3-audio-player';
// import 'vue3-audio-player/dist/style.css'
import {onMounted, ref,defineProps, watch, nextTick} from 'vue'
let player=''
let route=useRoute()
let props=defineProps(['row'])
let audioUrl=ref('')
let config={
    id: 'video', // 占位dom元素
    width: '100%', 
    height: '100%', // 视频宽高尺寸
    url: 'https://sf1-cdn-tos.huoshanstatic.com/obj/media-fe/xgplayer_doc_video/mp4/xgplayer-demo-360p.mp4', // 视频源
    poster: "http://ashuai.work/static/img/avantar.png", // 视频封面

  
    autoplay: false, // 是否自动播放，不自动播放，浏览器有限制规则
    autoplayMuted: false, // 是否自动播放（静音播放）
    videoInit: true, // 是否默认初始化video，默认初始化，默认true
    playsinline: true, // 是否启用内联播放模式，仅移动端生效
    defaultPlaybackRate: 1, // 默认播放速度（可选：0.5/0.75/1/1.5/2等）
    volume: 0.72, // 播放音量（可选：0 ~ 1）
    loop: false, // 是否循环播放，默认不循环播放
    startTime: 0, // 点播模式下，初始起播时间
    videoAttributes: {}, // video扩展属性，暂且不配置
    lang: 'zh-cn', // 播放器初始显示语言，设置为中文
    fluid: true, // 是否流式布局（宽高优先于流失布局，默认16:9）注掉上方宽高看效果
    fitVideoSize: 'fixed', // 保持容器宽/高，不做适配，按照容器来
    videoFillMode: 'auto', // 宽高不够自动底色填充（fill拉伸填充等...）
    seekedStatus: 'play', // 跳转后继续播放
   
    thumbnail: null, // 进度条预览图配置，普通业务用不到
    marginControls: false, // 是否开启画面和控制栏分离模式，不开启空间多一些
    domEventType: 'default', // 响应的事件类型，不用指定，用默认的即可
  
    icons: {}, // 使用默认的icon图标
    i18n: [], // 使用默认的中文
    // 自定义一些颜色
    // commonStyle: {
    //     progressColor: 'green', // 整个进度条颜色
    //     playedColor: 'chocolate', // 已播放的进度条颜色
    //     volumeColor: 'pink', // 音量大小竖向滑块颜色
    // },
    controls: true, // 是否使用底部控制栏，默认使用
    miniprogress: true, // 是否使用mini进度条（当底部控制栏隐藏时生效）
    screenShot: false, // 关闭截图功能
    rotate: true, // 是否使用视频旋转插件，默认不使用
    download: false, // 是否使用下载按钮，一般不用，一般自定义控制
    pip: false, // 使用使用画中画模式，默认不用
    mini: false, // 是否使用小屏幕控件
    cssFullscreen: true, // 是否使用网页样式全屏按钮开关
    playbackRate: [0.5, 1, 1.5, 2, 3], //传入倍速可选数组
    playbackRate: true, //false，禁用倍速播放（即控制栏不显示）
    keyShortcut: false, // 是否开启快捷键模式
   
}
const init = () => {
    player = new Player({
        ...config
    });
    if(player){
          player.on(Events.PLAY, (ev) => {
        console.log('-播放开始-', ev);
        })
        player.on(Events.PAUSE, (ev) => {
            console.log('-播放结束-', ev);
        })
        player.on('loadedmetadata', (ev) => {
            console.log('-媒体数据加载好了-', ev);
        })
        player.on(Events.SEEKED, (ev) => {
            console.log('-跳着播放-', ev);
        })
    }
    nextTick(()=>{
        if(route.path=='/fullscreen'){
            document.getElementById('video').style.paddingTop='0'
        }
        
    })
    // 等各种监听事件
}
watch(()=>props.row,(newval)=>{
    
    if(newval.fileType=='2'){
        if(newval.from='wisdom'){
            config.url=newval.fileUrl
       config.poster=''
        }else{
           config.url=import.meta.env.VITE_BASE_URL+'/fs/file?fileId='+newval.fileId
            config.poster=import.meta.env.VITE_BASE_URL+'/fs/file?fileId='+newval.coverFileId
        }
      
       init()
    }
    if(newval.fileType=='3'){
        audioUrl.value=import.meta.env.VITE_BASE_URL+'/fs/file?fileId='+newval.fileId
       
         document.querySelector('#audioID').setAttribute("src",audioUrl.value )
    }
    
},{
    deep:true,
    immediate:true
})
onMounted(()=>{
    // init()
    //
})
</script>
<style scoped>

.preview-container,.video{
    width: 100%;
    height: 100%;
}
:deep(.xgplayer){
    height: 100% !important;
    /* max-height: 100vh; */
}
:deep(.xgplayer-poster){
    background-size: cover;
}
.iframe{
    height: 100%;
}
</style>
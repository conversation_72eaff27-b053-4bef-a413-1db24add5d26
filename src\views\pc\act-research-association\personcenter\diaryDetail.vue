

<template>
    <div class="diary-detail mt10 flex_start_0 content-box">
           <div class="left flex_1">
             
            <div class="flex_start title" @click="goBack">
            <el-icon style="margin-right: 4px;"><ArrowLeft /></el-icon>
           返回
          </div>
                <div class="flex_start_0 diary">
                       <el-avatar :src="detailRow.userAvatarUrl || DefaultAvatar" class="avatar head" :size="32" @click="goPreview(detailRow)" />
                    <!-- <img src="@/assets/pc/teacher.png" alt="" class="head" /> -->
                    <div class="flex_1">
                        <div class="flex_between">
                          <div class="info">
                            <div class="flex_start ">
                                <p class="teacher">{{detailRow.userName}}</p>
                                <div class="flex_start stars" v-show="detailRow.userClubRole=='MEMBER'||detailRow.userClubRole=='LEADER'">
                                    <div v-for="item in 6" :key="item" class="star-box">
                                        <img src="@/assets/pc/star.png" alt="" v-if="item>detailRow.score">
                                        <img src="@/assets/pc/stared.png" alt="" v-else>
                                    </div>
                                </div>
                             </div>
                             <p class="date">{{detailRow.recordDate}}</p>
                           </div>
                            <div class="changeArticle flex_start">
                               <button :class="['flex_center',exchangeForm.prevId?'exist':'']" style="margin-right:16px" @click="exchangeForm.prevId&&goOther('prev')">
                                   <img src="@/assets/pc/prev.png" alt="" style=" margin-right: 4px;" v-show="!exchangeForm.prevId">
                                   <img src="@/assets/pc/preved.png" alt="" style=" margin-right: 4px;" v-show="exchangeForm.prevId">
                                  上一篇
                               </button>
                                <button :class="['flex_center',exchangeForm.nextId?'exist':'']" @click="exchangeForm.nextId&&goOther('next')">
                                 
                                  下一篇
                                   <img src="@/assets/pc/next.png" alt="" style=" margin-left: 4px;" v-show="!exchangeForm.nextId">
                                    <img src="@/assets/pc/nexted.png" alt="" style=" margin-left: 4px;" v-show="exchangeForm.nextId">
                               </button>
                           </div>
                        </div>
                        
                           <div class="flex_start diary-title" v-show="detailRow.title">
                            <h3>标题：</h3>
                            <p>{{detailRow.title}}</p>
                        </div>
                    <div class="rich">
                        <div class="rich-html" ref="richRef" v-html="detailRow.content">
                          
                        </div>
                    </div>
                
                    <div class="bottom">
                        <ul class="flex_start tag-list">
                            <li class="flex_start" v-for="item in detailRow.topic" :key="item.id">
                                <img src="@/assets/pc/tag.png" alt="">
                                <p>{{item.name}}</p>
                            </li>
                        </ul>
                    </div>
                </div>
               </div>
               <ul class="flex_center action-list">
                        <li class="flex_start">
                            <img src="@/assets/pc/eyed.png" alt="">
                            <p>浏览&nbsp;{{detailRow.views}}</p>
                        </li>
                        <li class="flex_start" @click="gofavorite(detailRow)">
                            <img src="@/assets/pc/comment-star.png" alt="" v-show="!detailRow.isFavorite">
                            <img src="@/assets/pc/comment-stared.png" alt="" v-show="detailRow.isFavorite">
                            <p>收藏&nbsp;{{detailRow.favorites}}</p>
                        </li>
                        <li class="flex_start">
                           
                            <img src="@/assets/pc/comment.png" alt=""  v-show="!detailRow.isCommented" />
                             <img src="@/assets/pc/commented.png" alt="" v-show="detailRow.isCommented" />
                            <p>评论&nbsp;{{detailRow.comments}}</p>
                        </li>
                        <li class="flex_start" @click="goThumb(detailRow)">
                            <img src="@/assets/pc/thumb.png" alt="" v-show="!detailRow.isLike">
                            <img src="@/assets/pc/thumbed.png" alt="" v-show="detailRow.isLike">
                            <p>点赞&nbsp;{{detailRow.likes}}</p>
                        </li>
               </ul>
               <div class="comment">
                    <!-- <div class="sort flex_start">
                        <p :class="commentForm.orderBy=='HEAT'?'active':''" @click="changeTab('HEAT')">按热度</p>
                        <p :class="commentForm.orderBy=='TIME'?'active':''"  @click="changeTab('TIME')">按时间</p>
                     </div> -->
                      <el-empty description="暂无评论" v-show="comments.length==0" style="height:calc(100vh - 400px)"/>
                      <div class="comment-list" v-infinite-scroll="load" style="overflow:auto"  v-show="comments.length!=0">
                         <div class="comment-item flex_between_0" v-for="item in comments" :key="item.id">
                                <div class="flex_1">
                                <div class="flex_start_0 level_1_comment">
                                    <img :src="item.userAvatarUrl || DefaultAvatar" class="avatar teacher-logo"  @click="goPreview(item)" alt="">
                                   <!-- <el-avatar :src="item.userAvatarUrl || DefaultAvatar" class="avatar teacher-logo" :size="32" @click="goPreview(item)" /> -->
                                    <div>
                                        <div class="flex_start_0">
                                            <p class="teacher-name">{{item.userName}}</p>
                                             <p :class="item.userClubRole=='LEADER'?'job-leader':item.userClubRole=='TUTOR'?'job-tutor':item.userClubRole=='DIRECTOR'?'job-director':''">
                                                {{item.userClubRole=='LEADER'?'组长':item.userClubRole=='TUTOR'?'导师':item.userClubRole=='DIRECTOR'?'社长':''}}
                                            </p>
                                            <p class="teacher-write flex_1">：{{item.content}}</p>
                                        </div>
                                        <p class="from">{{item.formattedCommentTime}}&nbsp;&nbsp; 来自{{item.userSourceName}}</p>
                                    </div>
                                  </div>
                                  <div class="child-comment">
                                    <div class="level_1_comment level_2_comment" v-for="citem in item.children" :key="citem.id">
                                       
                                            <div class="flex_start_0">
                                                <p class="teacher-name">{{citem.userName}}{{citem.replyToUserName?'&nbsp;回复&nbsp;':''}}{{citem.replyToUserName}}</p>
                                              <p :class="item.userClubRole=='LEADER'?'job-leader':item.userClubRole=='TUTOR'?'job-tutor':item.userClubRole=='DIRECTOR'?'job-director':''">
                                                {{item.userClubRole=='LEADER'?'组长':item.userClubRole=='TUTOR'?'导师':item.userClubRole=='DIRECTOR'?'社长':''}}
                                            </p>
                                                <p class="teacher-write flex_1"  @click="replyChildComment(item,citem)">：{{citem.content}}</p>
                                            </div>
                                            <p class="from">{{item.formattedCommentTime}}&nbsp;&nbsp; 来自{{item.userSourceName}}</p>
                                        
                                    </div>
                                  </div>
                                
                               </div>
                             
                              <div class="thumb-list flex_start_0">
                                    <div class="flex_start item">
                                        <img src="@/assets/pc/comment.png" alt="">
                                        <p>{{item.comments}}</p>
                                    </div>
                                    <div class="flex_start item">
                                        <img src="@/assets/pc/thumb.png" alt="" v-show="!item.isLike">
                                        <img src="@/assets/pc/thumbed.png" alt="" v-show="item.isLike">
                                        <p>{{item.likes}}</p>
                                    </div>
                                    <div style="width:13px">
                                        <img src="@/assets/pc/del.png" alt="" v-show="item.isDeleteCapable" class="deleteCapable" @click="goDelComment(item)" />
                                    </div>
                                      <div style="width:13px;margin-left:16px">
                                        <img src="@/assets/pc/edit.png" alt=""  v-show="item.isUpdateCapable" class="deleteCapable" @click="goEditComment(item)" />
                                    </div>
                              </div>
                         </div>
                     </div>
               </div>
            </div>
            <div class="right">
               
                <div class="title">点赞列表</div>
                    <ul>
                        <li class="thumb-item" v-for="(item,index) in likes" :key="index">
                            <div class="flex_start">
                                 <img :src="item.avatarUrl || DefaultAvatar" class="avatar header" alt="" >
                                  <!-- <el-avatar :src="item.avatarUrl || DefaultAvatar" class="avatar header" :size="32"  /> -->
                                 <p class="name">{{item.name}}</p>
                                  <img src="@/assets/pc/thumbed.png" alt="" class="thumb">
                            </div>
                            <p class="date">{{item.actionTime}}</p>
                        </li>
                    </ul>
            </div>
    </div>
      <el-dialog
        v-model="commentDialogVisible"
        title="编辑评论"
        width="500"
        :before-close="cancelEditComment"
    >
        <el-input v-model="editCommentForm.content" placeholder="请输入"></el-input>
        <template #footer>
        <div class="dialog-footer" style="margin-top:24px">
            <el-button  @click="cancelEditComment">
              取消
            </el-button>
            <el-button type="primary" @click="confirmEditComment">确认</el-button>
            
        </div>
        </template>
    </el-dialog>
      <el-image-viewer
        v-if="showPreview"
        :url-list="previewurlList"
        show-progress
        :initial-index="0"
        @close="showPreview = false"
      />
</template>
<script setup>
import {ref,onMounted, reactive,nextTick} from 'vue'
import {recordDetail,recordView,commentList,sendComment,likeList,thumbSubmit,favoriteSubmit,myCollectLocation,memberDiaryLocation,delComment,editComment} from '@/api/pc/index.js'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'
import {ElMessageBox,ElMessage} from 'element-plus'
let userInfo=useUserInfoStoreHook()
let {personcenterCurrentInfo}=storeToRefs(userInfo)
import DefaultAvatar from "@/assets/layouts/default-avatar.png"
import {useRoute,useRouter} from 'vue-router'
let route=useRoute()
let commentDialogVisible=ref(false)
let router=useRouter()
let sortType=ref(1)
let recordId=''
let detailRow=ref({})
let commentForm=reactive({
    pageSize:14,
    pageNum:1,
   // orderBy:'HEAT', //
})
let exchangeForm=ref({
    nextId:'',
    prevId:''
})
let maxPage=0
let comments=ref([])
let likes=ref([])
let previewurlList=ref([])
let showPreview=ref(false)
let goBack=()=>{
    router.go(-1)
}
let editCommentForm=reactive({
    commentId:'',
    content:''
})

let confirmEditComment=async()=>{
   if(editCommentForm.content==''){
     cancelEditComment()
   }else{
      let res=await editComment(editCommentForm)
      if(res){
           ElMessage.success('编辑成功')
           comments.value.forEach(item=>{
             if(item.id==editCommentForm.commentId){
                item.content=editCommentForm.content
             }
           })
           cancelEditComment()
      }
   }
}
let goPreview=(row)=>{
    if(row.userAvatarUrl){
         previewurlList.value=[row.userAvatarUrl]
        nextTick(()=>{
             showPreview.value=true
        })
    }
}
let cancelEditComment=()=>{
  commentDialogVisible.value=false
  Object.assign(editCommentForm,{
    commentId:'',
    content:''
  })
}
let goEditComment=(row)=>{
   commentDialogVisible.value=true
   Object.assign(editCommentForm,{
    commentId:row.id,
    content:row.content
  })
}
let goDelComment=(row)=>{
    ElMessageBox.confirm(
    '确认删除该条评论吗?',
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async() => {
        let res=await delComment({
            commentId:row.id
        })
        if(res){
          comments.value=comments.value.filter(item=>{
                return item.id!=row.id
            })
            ElMessage({
                type: 'success',
                message: '删除成功',
            })
        }
    
    })
    .catch(() => {
      
    })
}
let goOther=(type)=>{
    comments.value=[]
   if(type=='next'){
      recordId=exchangeForm.value.nextId
   }else{
      recordId=exchangeForm.value.prevId
   }
//    router.replace('/actResearchAssociation/square/detail?recordId='+recordId)
   init()
}
let getLoaction=async()=>{
    let res=''
    if(personcenterCurrentInfo.value.activeIndex==1){
       res=await memberDiaryLocation({
           recordId:recordId,
           ...personcenterCurrentInfo.value
       })
    }else{
       
         res=await myCollectLocation({
           recordId:recordId,
           ...personcenterCurrentInfo.value
       })
    }
    if(res){
        exchangeForm.value=res.data
    }
}
let load=()=>{
  if(commentForm.pageNum<maxPage){
    
        Object.assign(commentForm,{
           pageNum:commentForm.pageNum+1
        })
        // alert(11)
         getComment()
    }
}
let goThumb=async(row)=>{
     let res=await thumbSubmit({
        recordId:row.id,
     })
     if(res){
        // sessionStorage.setItem('detailAction',JSON.stringify({
        //     isLike:!detailRow.value.isLike,
        //     id:row.id,
        //     isFavorite:detailRow.value.isFavorite,
        //     isViewed:true
        // }))
        if(detailRow.value.isLike){
           detailRow.value.isLike=false
           detailRow.value.likes=detailRow.value.likes-1
        }else{
             detailRow.value.isLike=true
             detailRow.value.likes=detailRow.value.likes+1
        }
     
     }
}
let gofavorite=async(row)=>{
   let res=await favoriteSubmit({
      recordId:row.id,
      favorite:!detailRow.value.isFavorite
   })
    if(res){
         sessionStorage.setItem('detailAction',JSON.stringify({
            isFavorite:!detailRow.value.isFavorite,
             isLike:detailRow.value.isLike,
            id:row.id,
            isViewed:true
        }))
       if(detailRow.value.isFavorite){
           detailRow.value.isFavorite=false
           detailRow.value.favorites=detailRow.value.favorites-1
        }else{
             detailRow.value.isFavorite=true
           detailRow.value.favorites=detailRow.value.favorites+1
        }
     }
}
let getDetail=async()=>{
    let res=await recordDetail({
        recordId:recordId
    })
    if(res){
       
        res.data.score=res.data.score?res.data.score:0
        detailRow.value=res.data
        //   sessionStorage.setItem('detailAction',JSON.stringify({
        //     isFavorite:detailRow.value.isFavorite,
        //      isLike:detailRow.value.isLike,
        //     id:detailRow.value.id,
        //     isViewed:true
        // }))
    }
}
// let changeTab=(row)=>{
//      comments.value=[]
//      Object.assign(commentForm,{
//          orderBy:row,
//          pageNum:1
//     })
//     getComment()
// }
let sendView=()=>{
   recordView({
        recordId:recordId
    })
    
}

let getLike=async()=>{
    let res=await likeList({
        recordId:recordId
    })
    if(res){
         likes.value=res.data
    }
}
let getComment=async()=>{
    Object.assign(commentForm,{
        recordId:recordId
    })
    let res=await commentList(commentForm)
    if(res){
          res.data.list.forEach(item=>{
            item.children=item.children?item.children:[]
        })
     
        comments.value=comments.value.concat([...res.data.list])
        //   total.value=res.data.total
         maxPage=res.data.pages
    }
}
let init=()=>{
  sendView()
  getDetail()
  getComment()
  getLike()
  getLoaction()
}
onMounted(()=>{
  recordId=route.query.recordId
//   console.log(window.matchMedia('(max-width:70px)'))
init()
})
</script>
<style lang="scss" scoped>

.title{
        font-weight: bold;
        font-size: 20px;
         color: #2B2C33;cursor: pointer;
         margin-bottom: 16px;
    }
       .diary-title{
        margin-top: 16px;
        h3{
            font-size: 18px;
        }
        p{
             font-size: 16px;
            font-weight: bold;
        }
    }
.right{
  padding: 16px 6px 16px 16px;
  border-radius: 4px;
  background: #fff;
  width: 180px;
  .title{
    font-size: 16px;
    color: #2B2C33;
    font-weight: bold;
  }
  ul{
    max-height: calc(100vh - 200px);
    overflow: auto;
  }
  .thumb-item{
    margin-top: 16px;
    div{
        .header{
            width: 24px;
            height: auto;
            border-radius: 200px;
        }
        .name{
            font-size: 14px;
            color: #2B2C33;
            margin: 0 8px;
            font-weight: bold;
            line-height: 22px;
        }
        .thumb{
            width: 14px;
            height: auto;
        }
    }
    .date{
        font-size: 12px;
        color: #94959C;
        margin-top:6px
    }
  }
}
.left{
    padding: 16px 19px;
    background: #fff;  margin-right: 10px;border-radius:4px;margin-bottom:10px;
    .diary{
    margin-top: 10px;
  
   
    .head{
        width: 52px;
        height: 52px;
        margin-right: 10px;
    }
    .info{
        .teacher{
            font-size: 16px;
            color: #2B2C33;
            font-weight: bold;
            margin-right: 24px;
        }
        .stars{
            .star-box{
                margin-right: 4px;
            }
            img{
                width: 24px;
                height: auto;
            }
        }
    }
    .date{
        font-size: 14px;
        color: #94959C;
        margin-top: 4px;
    }
   
    .rich{
        position: relative;
        .rich-html{
        margin-top:8px;
       
        font-size: 16px;
        color: #2B2C33;
     
         line-height: 24px;
         :deep(img){
            width: 100%;
         }
    }
    

    }
    .bottom{
        .tag-list{
            li{
                padding: 4px 10px;
                border-radius: 100px;
                font-size: 16px;
                border-radius: 19px;
                border: 1px solid #E2E3E6;
                color: #2B2C33;
                margin: 10px 10px 0 0;cursor: pointer;
                img{
                    width: 16px;
                    height: auto;
                    margin-right: 8px;
                    
                }
            }
        }
    }
     }
     .action-list{
        margin: 24px 0;
        li{
            font-size: 16px;
             color: #94959C;
             padding: 8px 52px;
             border-right: 1px solid #E2E3E4;;
          
            cursor: pointer;
            &:last-child{
                margin-right: 0;
                padding: 8px 0px 8px 52px;
                border-right: none;
            }
            &:first-child{
                padding: 8px 52px 8px 0px;
            }
            img{
                width: 18px;
                height: auto;
                margin-right: 4px;
            }
        }
    }
    .comment{
        margin-left: 62px;
        .sort{
            font-size: 14px;
            color: #2B2C33;
            p{
                margin-right: 30px;cursor: pointer;
                &.active{
                    color: #4260FD;
                }
            }
        }
        .comment-list{
            height: calc(100vh - 400px);
            .comment-item{
                margin-top: 24px;
                 .level_1_comment{
                    .teacher-logo{
                        width: 34px;
                        height: 34px;
                        border-radius: 200px;
                        margin-right: 10px;
                    }
                    .teacher-name{
                        font-size: 15px;
                        color: #5C76D4;
                        font-weight: bold;
                    }
                    .teacher-write{
                        cursor: pointer;
                        white-space:pre-wrap;
                        word-break: break-all;
                    }
                   .job-leader{
                        width: 34px;
                        height: 14px;
                       background: #C197FF;
                        border-radius: 2px;
                        color: #fff;
                        font-size: 11px;
                        line-height: 14px;
                        text-align: center;
                        margin: 0 8px;
                    }
                     .job-tutor{
                        width: 34px;
                        height: 14px;
                        background: #83BCFE;
                        border-radius: 2px;
                        color: #fff;
                        font-size: 11px;
                        line-height: 14px;
                        text-align: center;
                        margin: 0 8px;
                    }
                    .job-director{
                        width: 34px;
                        height: 14px;
                        border-radius: 2px;
                        color: #fff;
                        font-size: 11px;
                        line-height: 14px;
                        text-align: center;
                        margin: 0 8px;
                        background:  #FFA132;
                    }
                    .from{
                        font-size: 14px;
                        margin-top: 4px;
                        color: #94959C;
                        line-height: 20px;
                    }
                 }
                 .child-comment{
                    margin-top: 16px;
                 }
                 .level_2_comment{
                         margin-left: 44px;
                         padding-left: 10px;
                         border-left: 1px solid #e2e3e6;
                         padding-bottom: 10px;
                         &:last-child{
                            padding-bottom: 0;
                         }
                 }
            }
        }
        .thumb-list{
            margin-left: 16px;
            align-items: flex-start;
            .item{
                font-size: 14px;
               color: #94959C;
               margin-right: 16px;
               cursor: pointer;
               &:last-child{
                margin-right: 0;
               }
                img{
                    width: 14px;
                    height: auto;
                    margin-right: 6px;
                }
            }
             .deleteCapable{
                    cursor: pointer;
                    width: 13px;
                    height: auto;
                   transform: translateY(2px);
                 }
        }
    }
}
</style>
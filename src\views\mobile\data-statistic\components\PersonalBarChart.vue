<script setup>
import { ref, watch, onMounted } from "vue"
import { EchartsUI, useEcharts } from "@/components/Echart"

const chartRef = ref()
const { renderEcharts } = useEcharts(chartRef)

const props = defineProps({
  chartData: {
    type: Object,
    default: () => ({
      data: [],
      color: []
    })
  }
})

watch(
  () => props.chartData,
  () => {
    paintChart()
  },
  { deep: true }
)

onMounted(() => {
  paintChart()
})


const paintChart = () => {
  const data = props.chartData.data
  const xAxisData = data.map(item => `${item.teacherName}\n${item.groupName !== null && item.groupName !== undefined ? '（' + item.groupName + '）' : ''}`)
  const diaryCountData = data.map(item => item.diaryCount)
  const sixStarCountData = data.map(item => item.sixStarCount)
  const recommendCountData = data.map(item => item.recommendCount)
  renderEcharts({
    //你的代码
    backgroundColor: '#fff',
    grid: {
      top: '5%',
      left: '1%',
      right: '8%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: [{
      type: 'category',
      data: xAxisData,
      axisLine: {
        show: true,
        lineStyle: {
          color: "#8DA2B5",
          width: 1,
          type: "solid"
        }
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: true,
        textStyle: {
          color: '#8DA2B5',
          fontSize: 12
        },
        interval: 0,
        rotate: 0,
        width: 100,
        overflow: 'truncate'
      },
    }],
    yAxis: [{
      nameTextStyle: {
        color: '#8DA2B5'
      },
      type: 'value',
      axisLabel: {
        formatter: '{value}',
        textStyle: {
          color: '#8DA2B5',
          fontSize: 12
        }
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#8DA2B5",
          width: 1,
          type: "solid"
        },
      },
      splitLine: {
        lineStyle: {
          color: "#8DA2B5",
          type: "dashed"
        }
      }
    }],
    series: [{
      name: '日记数量',
      type: 'bar',
      data: diaryCountData,
      barWidth: 8, //柱子宽度
      barGap: '50%', // 增加柱子之间的间距
      itemStyle: {
        borderRadius: [8, 8, 0, 0],
        color: (params) => {
          return {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: '#88C0FF'
            }, {
              offset: 1,
              color: '#508CFF'
            }]
          }
        }
      }
    }, {
      name: '较好',
      type: 'bar',
      data: sixStarCountData,
      barWidth: 8,
      itemStyle: {
        borderRadius: [8, 8, 0, 0],
        color: (params) => {
          return {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: '#A4F3F5'
            }, {
              offset: 1,
              color: '#55C2F5'
            }]
          }
        }
      }
    }, {
      name: '一般',
      type: 'bar',
      data: recommendCountData,
      barWidth: 8,
      itemStyle: {
        borderRadius: [8, 8, 0, 0],
        color: (params) => {
          return {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: '#FFD390'
            }, {
              offset: 1,
              color: '#FF8988'
            }]
          }
        }
      }
      }],
    dataZoom: [
      {
        type: 'slider',
        show: false,
        zoomLock: false,
        orient: 'horizontal',
        bottom: 0,
        height: 20,
        start: 0,
        end: Math.min(100, Math.max(300 / data.length, 30))
      },
      {
        type: 'inside',
        orient: 'horizontal',
        zoomLock: false
      }
    ]
  })
}
</script>

<template>
  <EchartsUI ref="chartRef" :height="'178px'" />
</template>

<style scoped></style>

<script setup>
import { reactive, ref, onMounted } from "vue"
import { useUserStore } from "@/store/modules/user"
import { getCaptchaApi, resetPassword<PERSON><PERSON>, getPhone<PERSON>aptcha } from "@/api/login"
import { showToast } from "vant"
// import { router } from '../../router'
import { useRouter } from "vue-router"

const router = useRouter()

const userStore = useUserStore()
const resetRef = ref()
const activeIndex = ref(3)

// 特殊字符定义，与后端保持一致
const specificCharacters = ['!', '@', '#', '$', '%', '^', '&', '*', ',', '.']

// 检查是否包含特殊字符
const isSpecificCharacter = (c) => {
  return specificCharacters.includes(c)
}

// 强密码校验函数
const validateStrongPassword = (password) => {
  // 校验长度是否是 8-30 位
  if (password.length < 8 || password.length > 30) {
    return "密码长度必须在8-30位之间"
  }

  // 大写字母、小写字母、数字、特殊字符
  let hasUpperCase = false
  let hasLowerCase = false
  let hasDigit = false
  let hasSpecificCharacter = false

  for (let ch of password) {
    if (ch >= 'A' && ch <= 'Z') {
      hasUpperCase = true
    }
    if (ch >= 'a' && ch <= 'z') {
      hasLowerCase = true
    }
    if (ch >= '0' && ch <= '9') {
      hasDigit = true
    }
    if (isSpecificCharacter(ch)) {
      hasSpecificCharacter = true
    }
  }

  if (!hasUpperCase) {
    return "密码必须包含大写字母"
  }
  if (!hasLowerCase) {
    return "密码必须包含小写字母"
  }
  if (!hasDigit) {
    return "密码必须包含数字"
  }
  if (!hasSpecificCharacter) {
    return "密码必须包含特殊字符（!@#$%^&*,.）"
  }

  return null // 校验通过
}
const changeTab = (arg) => {
  activeIndex.value = arg
}
const loginForm = reactive({
  username: "",
  password: "",
  confirmPassword: "",
  code: ""
})
const resetForm = reactive({
  username: "",
  password: "",
  confirmPassword: "",
  code: ""
})

const resetRules = ref({
  password: [
    {
      required: true,
      message: "请输入新密码",
      trigger: "change"
    },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback()
          return
        }
        const errorMsg = validateStrongPassword(value)
        if (errorMsg) {
          callback(new Error(errorMsg))
        } else {
          callback()
        }
      },
      trigger: "blur"
    }
  ],
  confirmPassword: [
    {
      required: true,
      message: "请输入确认密码",
      trigger: "change"
    },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback()
          return
        }
        if (value !== resetForm.password) {
          callback(new Error("两次密码不一致"))
        } else {
          callback()
        }
      },
      trigger: "blur"
    }
  ]
})

const confirmReset = async () => {
  if (!resetRef.value) return
  await resetRef.value.validate(async (valid, fields) => {
    if (valid) {
      const res = await resetPasswordApi({
        mobile: resetForm.username,
        newPassword: resetForm.password,
        code: resetForm.code
      })
      if (res) {
        showToast('修改密码成功')
        router.push('/login')
        // 移除sessionStorage中的m
        sessionStorage.removeItem('m')
        resetForm.code = ''
        resetForm.confirmPassword = ''
        resetForm.password = ''
        resetForm.username = ''
      }
    } else {
      console.log('验证错误!', fields)
    }
  })
}

const captcha = ref({})
const getCaptcha = async () => {
  const res = await getCaptchaApi()
  if (res) {
    captcha.value = res.data
  }
}

onMounted(() => {
  resetForm.username = sessionStorage.getItem('m') || ''
  getCaptcha()
})
</script>

<template>
  <div class="login-container">
    <div class="header">
      <img src="@/assets/layouts/mobile-login-bg.png" alt="" class="header-bg" />
    </div>
    <div class="tabs flex-center-center">
      <div class="tab">
        <span class="active blue1">修改密码</span>
      </div>
    </div>
    <div class="login forget" v-show="activeIndex == 3">
      <div class="form">
        <el-form label-position="top" label-width="auto" :model="resetForm" :rules="resetRules" ref="resetRef">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="resetForm.username" disabled placeholder="请输入用户名" />
          </el-form-item>
          <el-form-item label="新密码" prop="password">
            <el-input v-model="resetForm.password" placeholder="请输入新密码" type="password" show-password />
            <div class="tip">密码长度8-30位，必须包含大写字母、小写字母、数字及特殊字符</div>
          </el-form-item>
          <el-form-item label="确认新密码" prop="confirmPassword">
            <el-input v-model="resetForm.confirmPassword" placeholder="请再次输入密码" type="password" show-password />
          </el-form-item>
          <el-form-item label="验证码" prop="code">
            <el-input v-model="resetForm.code" placeholder="请输入验证码">
              <template #append>
                <img
                  @click="getCaptcha"
                  :src="'data:image/jpeg;base64,' + captcha.img"
                  alt=""
                  class="captcha-img"
                  width="100"
                  height="50"
                />
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <!-- <div class="back-login flex-start-center" @click="changeTab(1)">
          <el-icon>
            <ArrowLeft />
          </el-icon>
          <span class="back-login-text">返回登录</span>
        </div> -->
      </div>
      <el-button class="confirm-btn" @click="confirmReset">确认</el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.login-container {
  background-color: #fff;
  height: 100%;
  overflow-y: auto;
  
}
::-webkit-scrollbar {
  display: none;
}

.header {
  position: relative;

  .header-bg {
    width: 100%;
  }

  .dialog-del {
    position: absolute;
    top: 24px;
    right: 24px;
    height: 16px;
    width: 16px;
    cursor: pointer;
  }
}

.tab {
  font-size: 18px;
  color: #6d6f75;
  padding: 24px 0 40px 0;
  z-index: 12;

  div {
    cursor: pointer;
    padding-bottom: 6px;
  }

  .active {
    font-size: 18px;
    color: #3071ff;
    font-weight: bold;
    background: url(@/assets/layouts/title-bg.png) no-repeat;
    background-size: 74px 12px;
    background-position: center 100%;
  }
}

.form {
  padding: 8px 24px 0 24px;

  /* :deep(.el-form-item__content .el-form-item__error) {
    position: absolute;
    right: 0;
    left: auto;
    top: -22px;
  } */

  .forget-password {
    font-size: 12px;
    color: #3071ff;
    text-align: right;
    width: 100%;
    cursor: pointer;
  }

  :deep(.el-input-group__append) {
    padding: 0;
  }
}

.tip {
  font-size: 12px;
  line-height: 18px;
  color: #6d6f75;
  margin-top: 4px;
}

:deep(.el-form) {
  .el-form-item {
    &__label {
      font-size: 16px;
      color: #2b2c33;
      font-weight: bold;
    }
  }
}

:deep(.el-input) {
  .el-input__inner {
    height: 48px;
    border-radius: 8px;
  }
}

.confirm-btn {
  width: 184px;
  height: 48px;
  background: linear-gradient(90deg, #6f9eff 0%, #3071ff 100%);
  box-shadow: 0px 4px 6px 0px rgba(66, 96, 253, 0.24);
  font-size: 18px;
  color: #ffffff;
  cursor: pointer;
  border-radius: 12px;
  margin-left: 50%;
  margin-bottom: 32px;
  transform: translateX(-50%);
  margin-top: 12px;
}

.forget {
  .tab {
    margin-left: 24px;
    font-size: 24px;
    color: #2b2c33;
    font-weight: bold;
    background: url(@/assets/pc/bottom-tab.png) no-repeat;
    background-size: 74px 12px;
    background-position: left 84%;
  }

  .sendcode {
  }

  :deep(.el-input-group__append) {
    background: #fff;
    color: #3071ff;
    cursor: pointer;
  }
}

.back-login {
  font-size: 12px;
  color: #3071ff;
  margin-bottom: 16px;
  cursor: pointer;

  .back-login-text {
    margin-left: 4px;
  }
}
</style>

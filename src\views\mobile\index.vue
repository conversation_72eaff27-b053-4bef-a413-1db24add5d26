<script setup>
import { ref, onMounted, computed, nextTick, watch, reactive, onBeforeUnmount, onActivated } from "vue"
import { getProfileApi, getLatestActivityApi, getSixStarRecordApi, getActiveUserApi, getClubApi, getRecommendBooks<PERSON>pi } from "@/api/pc/home"
import { getUnreadMessageApi } from '@/api/mobile/message'
import { getMileageFlagApi } from '@/api/mobile/mileage'
import { useRouter } from "vue-router"
import { useUserStore } from "@/store/modules/user"
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay } from 'swiper/modules'
import 'swiper/css'
import { getToken } from '@/utils/cache/cookies'
import { useUserInfoStoreHook } from '@/store/modules/pc'
const userInfoStore = useUserInfoStoreHook()

const router = useRouter()
const userStore = useUserStore()

// console.log('mobile首页测试', 'userStore.userInfo', userStore.userInfo, userStore.roles, 'userInfoStore', userInfoStore.userInfo, userInfoStore.roles)
const profile = ref({})

const getProfile = async () => {
  try {
    const res = await getProfileApi()
    if (res) {
      profile.value = res.data
    }
  } catch (err) {
    console.log(err)
  }
}

// 最新活动
const latestActivity = ref([])
const getLatestActivity = async () => {
  try {
    const res = await getLatestActivityApi()
    if (res) {
      latestActivity.value = res.data
    }
  } catch (err) {
    console.log(err)
  }
}

// 六星日记
const sixStarDiary = ref([])
const getSixStarDiary = async () => {
  try {
    const res = await getSixStarRecordApi()
    if (res) {
      sixStarDiary.value = res.data
    }
  } catch (err) {
    console.log(err)
  }
}

// 好书推荐
const recommendBooks = ref([])
const showAllBooks = ref(false)
const getRecommendBooks = async () => {
  try {
    const res = await getRecommendBooksApi()
    if (res) {
      recommendBooks.value = res.data
    }
  } catch (err) {
    console.log(err)
  }
}

// 切换书籍显示状态
const toggleBooksDisplay = () => {
  showAllBooks.value = !showAllBooks.value
}

// 显示的书籍列表
const displayedBooks = computed(() => {
  if (showAllBooks.value || recommendBooks.value.length <= 3) {
    return recommendBooks.value
  }
  return recommendBooks.value.slice(0, 3)
})

// 是否需要显示展开/收起按钮
const needToggleBooks = computed(() => recommendBooks.value.length > 3)


const openUrl = (url) => {
  window.location.href = url
}

const logout = () => { 
  userStore.logout()
  router.replace('/login')
}

// 添加计算属性来判断是否需要滚动
const needScrollDiary = computed(() => sixStarDiary.value?.length > 5)
const needScrollActivity = computed(() => latestActivity.value?.length > 5)

// 修改 swiper 配置
const swiperOptions = {
  modules: [Autoplay],
  direction: 'vertical',
  loop: true,
  autoplay: {
    delay: 0,
    disableOnInteraction: false, // 改为 false，用户交互后继续自动播放
    pauseOnMouseEnter: false, // 鼠标进入时暂停
  },
  speed: 3000,
  slidesPerView: 5,
  spaceBetween: 16,
  cssMode: false,
  allowTouchMove: false,  // 禁用触摸滑动
  simulateTouch: false,   // 不模拟触摸事件
}

const unreadMessageCount = ref(0)
const getUnreadMessage = async () => {
  try {
    const res = await getUnreadMessageApi()
    if (res) {
      unreadMessageCount.value = res.data
    }
  } catch (err) {
    console.log('err', err)
  }
}

// 里程标志
const mileageFlag = ref({})
const getMileageFlag = async () => {
  try {
    const res = await getMileageFlagApi()
    if (res) {
      mileageFlag.value = res.data
    }
  } catch (err) {
    console.log(err)
  }
}

onMounted(() => {
  getProfile()
  getLatestActivity()
  getSixStarDiary()
  getRecommendBooks()
  getMileageFlag()

  if (getToken()) {
    getUnreadMessage()
  }
})
</script>

<template>
  <div class="home">
    <header class="home-header">
      <div class="top flex-between-center">
        <div class="flex-start-center">
          <img src="@/assets/mobile/home/<USER>" alt="" width="16" height="16" />
          <span class="m-text-16-24-600 m-pl5 dark1">行知研习社</span>
        </div>
        <div class="flex-end-center pointer">
          <div class="id-name" v-if="userStore?.roles?.length > 0">
            <span class="m-text-14-20-400 user-role m-pr4">{{ userStore?.rolesName?.[0] }}</span>
            <!-- <span class="m-text-14-20-400 user-role m-pr4">{{ userStore?.roles[0] === 'DIRECTOR' ? '社长' :
              userStore?.roles[0] === 'VICE_DIRECTOR' ? '副社长' : userStore?.roles[0] === 'MEMBER' ? '成员' :
              userStore?.roles[0] === 'TUTOR' ? '导师' : userStore?.roles[0] === 'LEADER' ? '组长' : '' }}</span> -->
            <span class="m-text-14-20-400 dark1 m-pl4">{{ userStore?.userInfo?.user?.name }}</span>
          </div>
          <img v-if="mileageFlag?.isActiveStar" src="@/assets/mobile/data-statistic/active-star.png" alt="" width="24" height="24" />
          <img v-if="mileageFlag?.isAwesomeAuthor" src="@/assets/mobile/data-statistic/good-creator.png" alt="" width="24" height="24" />
          <div class="flex-end-center m-pl8" @click="logout" v-if="userStore?.roles?.length > 0">
            <span class="m-text-14-20-400 grey3 m-pr4">退出登录</span>
            <img src="@/assets/mobile/home/<USER>" alt="" width="14" height="14" />
          </div>
          <div class="flex-end-center m-pl8" @click="logout" v-else>
            <span class="m-text-14-20-400 grey3">登录</span>
          </div>
        </div>
      </div>
      <van-swipe :height="168" :show-indicators="false" :autoplay="3000">
        <van-swipe-item :key="1">
          <img class="header-bg" src="@/assets/mobile/home/<USER>" alt="" />
        </van-swipe-item>
        <van-swipe-item :key="2">
          <img class="header-bg" src="@/assets/mobile/home/<USER>" alt="" />
        </van-swipe-item>
      </van-swipe>
    </header>
    <section class="home-statistics">
      <div class="statistics">
        <div class="statistics-item flex-center-center">
          <div class="statistics-item-title">
            <img src="@/assets/mobile/home/<USER>" alt="" />
          </div>
          <div class="statistics-item-value">
            <span class="score statistics-item-value-number m-text-18-26-400">{{ profile.numberOfAllianceUnit || 0
              }}</span>
            <span class="statistics-item-value-text m-text-12-18-400 grey3">联盟单位</span>
          </div>
        </div>
        <div class="statistics-item flex-center-center">
          <div class="statistics-item-title">
            <img src="@/assets/mobile/home/<USER>" alt="" />
          </div>
          <div class="statistics-item-value">
            <span class="club statistics-item-value-number m-text-18-26-400">{{ profile.numberOfClub || 0
              }}</span>
            <span class="statistics-item-value-text m-text-12-18-400 grey3">分社数量</span>
          </div>
        </div>
        <div class="statistics-item flex-center-center">
          <div class="statistics-item-title">
            <img src="@/assets/mobile/home/<USER>" alt="" />
          </div>
          <div class="statistics-item-value">
            <span class="comment statistics-item-value-number m-text-18-26-400">{{ profile.numberOfMemberOrLeader || 0
              }}</span>
            <span class="statistics-item-value-text m-text-12-18-400 grey3">加入成员</span>
          </div>
        </div>
      </div>
    </section>
    <section class="home-latest-activity">
      <div class="home-title flex-start-center m-mb10">
        <img class="home-title-icon" src="@/assets/mobile/home/<USER>" alt="" />
        <span class="m-text-16-24-600 m-pl6 m-pr8">最新活动</span>
        <img src="@/assets/mobile/home/<USER>" alt="" width="30" height="17" />
      </div>
      <div class="latest-activity">
        <div class="latest-activity-content">
          <swiper v-if="needScrollActivity" v-bind="swiperOptions" class="scroll-container">
            <swiper-slide v-for="(item, index) in latestActivity" :key="index">
              <div class="activity-item pointer" @click="openUrl(item.url)">
                <div class="activity-item-title m-text-14-20-400">{{ item.title }}</div>
              </div>
            </swiper-slide>
          </swiper>
          <div v-else class="scroll-container">
            <div v-for="(item, index) in latestActivity" :key="index" class="activity-item pointer"
              @click="openUrl(item.url)">
              <div class="activity-item-title m-text-14-20-400">{{ item.title }}</div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="home-diary">
      <div class="home-title flex-start-center m-mb10">
        <img class="home-title-icon" src="@/assets/mobile/home/<USER>" alt="" />
        <span class="m-text-16-24-600 m-pl6 m-pr8">六星日记</span>
        <img src="@/assets/mobile/home/<USER>" alt="" width="30" height="17" />
      </div>
      <div class="latest-activity">
        <div class="latest-activity-content">
          <swiper v-if="needScrollDiary" v-bind="swiperOptions" class="scroll-container">
            <swiper-slide v-for="(item, index) in sixStarDiary" :key="index">
              <div class="activity-item flex-start-center pointer" @click="openUrl(item.url)">
                <div class="activity-item-title m-text-14-20-400">{{ item.title }}</div>
              </div>
            </swiper-slide>
          </swiper>
          <div v-else class="scroll-container">
            <div v-for="(item, index) in sixStarDiary" :key="index" class="activity-item flex-start-center pointer"
              @click="openUrl(item.url)">
              <div class="activity-item-title m-text-14-20-400">{{ item.title }}</div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="home-recommend-books">
      <div class="home-title flex-start-center m-mb10">
        <img class="home-title-icon" src="@/assets/mobile/home/<USER>" alt="" />
        <span class="m-text-16-24-600 m-pl6 m-pr8">好书推荐</span>
      </div>
      <div class="book-content">
        <div class="book-item" v-for="(item, index) in displayedBooks" :key="index">
          <div class="book-item-content flex-start-start">
            <img :src="item.cover" alt="" width="58" height="82">
            <div class="book-item-title flex-column-start-start">
              <span class="m-text-14-20-600 m-pb8">{{ item.title }}</span>
              <span class="m-text-12-18-400 grey3">{{ item.introduction }}</span>
            </div>
          </div>
        </div>
        <div class="toggle-button-wrapper" v-if="needToggleBooks">
          <div class="toggle-button" @click="toggleBooksDisplay">
            <span>{{ showAllBooks ? '收起全部' : '显示全部' }}</span>
            <el-icon>
              <ArrowDown v-if="!showAllBooks" />
              <ArrowUp v-else />
            </el-icon>
          </div>
        </div>
      </div>
    </section>
    <section class="home-active" v-if="false">
      <div class="home-title flex-start-center m-mb10 m-ml14">
        <img class="home-title-icon" src="@/assets/mobile/home/<USER>" alt="" />
        <span class="m-text-16-24-600 m-pl6 m-pr8">成员活跃度</span>
      </div>
      <div class="active-content">
        <div class="member-active-list">
          <div class="member-active-item" v-for="(item, index) in memberActiveList" :key="item.id"
            :class="{ 'first': index === 0, 'second': index === 1, 'third': index === 2 }">
            <div class="member-active-item-top flex-start-center m-mb8">
              <img src="@/assets/layouts/default-avatar.png" alt="" width="24" height="24" />
              <span class="m-text-14-20-400 m-pl6 grey1">{{ item.userName }}</span>
              <img v-if="index === 0" class="m-ml6" src="@/assets/pc/home/<USER>" alt="" width="12" height="16" />
              <img v-if="index === 1" class="m-ml6" src="@/assets/pc/home/<USER>" alt="" width="12" height="16" />
              <img v-if="index === 2" class="m-ml6" src="@/assets/pc/home/<USER>" alt="" width="12" height="16" />
              <span v-if="index <= 2 && item.userName.length < 3" class="m-text-12-18-400 m-ml6 grey1 rank-text"
                :class="{ 'first': index === 0, 'second': index === 1, 'third': index === 2 }">{{ index === 0 ? '排名第一' :
                index === 1 ? '排名第二' : index === 2 ? '排名第三' : '' }}</span>
            </div>
            <div class="member-active-item-bottom flex-start-center">
              <span class="flex-start-center mr16">
                <span class="m-text-12-18-400 blue1 m-pl2 m-pr6">{{ item.totalRecords || 0 }}篇</span>
                <span class="m-text-12-18-400 grey3"> 日记</span>
              </span>
              <span class="flex-start-center">
                <span class="m-text-12-18-400 red1 m-pl3 m-pr6">{{ item.totalLikes || 0 }}次</span>
                <span class="m-text-12-18-400 grey3"> 获赞</span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <div class="message-entry" @click="$router.push('/message')">
      <div class="entry">
        <img src="@/assets/mobile/message/entry.png" alt="" width="48" height="48">
        <span v-if="unreadMessageCount > 0" class="message-entry-count">{{ unreadMessageCount }}</span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use './styles/custom-global-mobile-style.scss';

.home {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  padding-bottom: 16px;

  &-header {
    .top {
      padding: 12px 16px;

      .user-role {
        padding: 2px 5px;
        color: #fff;
        background-color: #FFA532;
        border-radius: 12px;
      }
    }

    .header-bg {
      width: 100%;
      height: 168px;
    }
  }

  &-statistics {
    padding: 16px 14px;

    .statistics {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 8px;

      &-item {
        width: 100%;
        height: 100%;
        background-color: #fff;
        border-radius: 4px;
        padding: 14px 8px;

        img {
          width: 32px;
          height: 32px;
        }

        &-value {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: center;
          padding-left: 8px;

          &-number {
            background-image: linear-gradient(to right, #ff8a00, #ff5630);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;

            &.diary {
              // 可以为特定类型设置不同的渐变色
              background-image: linear-gradient(90deg, #b496fc 0%, #6e46fd 100%);
            }

            &.club {
              background-image: linear-gradient(90deg, #7DE0C8 0%, #27CB96 100%);
            }

            &.comment {
              background-image: linear-gradient(90deg, #83beff 0%, #4b8afe 100%);
            }

            &.evaluations {
              background-image: linear-gradient(90deg, #7de0c8 0%, #27cb96 100%);
            }

            &.like {
              background-image: linear-gradient(90deg, #ff9bb7 0%, #ff6262 100%);
            }

            &.view {
              background-image: linear-gradient(90deg, #83beff 0%, #4b8afe 100%);
            }
          }
        }
      }
    }
  }

  .home-title {
    padding: 0 6px;

    .home-title-icon {
      width: 17px;
      height: 17px;
    }
  }

  .home-latest-activity {
    padding: 0 14px 16px;

    .latest-activity {
      width: 100%;
      height: 212px;
      background-image: url('@/assets/mobile/home/<USER>');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
      padding: 24px 16px;

      &-content {

        .activity-item {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          &-title {
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .home-diary {
    padding: 0 14px 16px;

    .latest-activity {
      width: 100%;
      height: 212px;
      background-image: url('@/assets/mobile/home/<USER>');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
      padding: 24px 16px;

      &-content {

        .activity-item {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          &-title {
            width: calc(100% - 14px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .home-recommend-books {
    padding: 0 14px 16px;

    .book-content {
      width: 100%;
      background-color: #fff;
      position: relative;
      /* padding-bottom: 16px; */
      margin-bottom: 16px;
      
      .book-item {
        padding: 16px;
        border-bottom: 1px dashed #E2E3E6;

        &:last-child {
          border-bottom: none;
        }

        &-content {
          img {
            width: 58px;
            height: 82px;
          }
          
          .book-item-title {
            padding-left: 12px;
          }
        }
      }
      
      .toggle-button-wrapper {
        width: 100%;
        display: flex;
        justify-content: center;
        padding: 8px 0;
        background-color: #fff;
        
        .toggle-button {
          color: #4b8afe;
          font-size: 12px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          line-height: 18px;
        }
      }
    }
  }
  
  .home-link {
    padding: 0 14px 16px;

    .link-content {
      width: 100%;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 7px;

      .link-item {
        width: 100%;
        height: 100%;
        background-color: #fff;
        border-radius: 4px;
        padding: 16px 14px;
      }
    }
  }

  .home-active {
    padding-bottom: 16px;

    .active-content {
      width: 100%;
      background-color: #fff;
      
      .member-active-list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        padding: 16px;

        .member-active-item {
            padding: 12px 10px;
            border-radius: 4px;
            margin-bottom: 11px;
            background-color: #F8F8F8;
        
            &:last-child {
              margin-bottom: 0;
            }
        
            &.first {
              background-color: #FFF7F1;
            }
        
            &.second {
              background-color: #EEF8FF;
            }
        
            &.third {
              background-color: #FFF9E9;
            }
        
            &-top {
              display: flex;
              align-items: center;
        
              .rank-text {
                display: inline-block;
                width: 58px;
                height: 16px;
                line-height: 16px;
                text-align: center;
                background-size: 100% 100%;
                background-repeat: no-repeat;
        
                &.first {
                  color: #DA4D12;
                  background-image: url("@/assets/pc/home/<USER>");
                }
        
                &.second {
                  color: #2E95D3;
                  background-image: url("@/assets/pc/home/<USER>");
                }
        
                &.third {
                  color: #CD7747;
                  background-image: url("@/assets/pc/home/<USER>");
                }
              }
            }
        
            &-bottom {}
          }
      }
    }
  }

  .message-entry {
    position: fixed;
    bottom: calc(80px + env(safe-area-inset-bottom));
    right: 16px;
    z-index: 1000;

    .entry {
      position: relative;

      .message-entry-count {
        position: absolute;
        top: 0px;
        right: 0px;
        background-color: #E72C4A;
        color: #fff;
        border-radius: 50%;
        width: 18px;
        height: 18px;
        text-align: center;
        font-size: 12px;
        line-height: 18px;
      }
    }
  }
}

.scroll-container {
  width: 100%;
  height: 100%;

  :deep(.swiper-slide) {
    height: auto !important;
    transition-timing-function: linear !important;
  }

  :deep(.swiper-wrapper) {
    transition-timing-function: linear !important;
  }
}

.latest-activity {
  &-content {
    height: 164px;
    overflow: hidden;
  }
}

.swiper {
  touch-action: pan-y;
}
</style>

import { request } from "@/utils/service"
///fs/file
export function createConversations() {
  return request({
    url: "/ai/conversations/teaching/create",
    method: "post",
    
  })
}

export function getExampleQuestion() {
    return request({
      url: "/ai/conversations/teaching/faq",
      method: "get",
      
    })
  }
 
  export function getAllList(data) {
    return request({
      url: "/ai/conversations/"+data.id+"/teaching/messages/",
      method: "get",
      
    })
  }
  export function clearAll(data) {
    return request({
      url: "/ai/conversations/"+data.id+"/teaching/clear-context/",
      method: "delete",
      
    })
  }
  export function audioToText(data) {
    return request({
      url: "/ai/asr",
      method: "post",
      data
    })
  }
//   export function sendChat(data) {
//     return request({
//       url: "/ai/conversations/teaching/chat",
//       method: "post",
//        data
//     })
//   }
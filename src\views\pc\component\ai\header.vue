<template>
  <div class="flex_between ai-header">
      <div class="flex_start left">
        <img src="@/assets/layouts/logo.png" alt="">
        <p>AI助教</p>
      </div>
       <el-icon style="color:#fff;font-size:24px;cursor:pointer" @click="close">
          <Close />
    </el-icon>
  </div>
</template>

<script setup>
import {useRouter} from 'vue-router'
const router = useRouter()
const close = () => {
  router.back()
}
</script>

<style lang='scss' scoped>
.ai-header{
    background: linear-gradient( 90deg, #508CFF 0%, #95B8FF 100%);
    padding: 18px 24px;
    .left{
        font-size: 20px;
        color: #FFFFFF;
        font-weight: bold;
     img{
        width: auto;
        height: 18px;
        margin-right: 8px;
     }
    }
}
</style>
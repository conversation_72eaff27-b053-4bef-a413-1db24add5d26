import { request } from "@/utils/service"

// 解析指标模板 导入模板
export function templateImport(data) {
    return request({
      url: "/jky/task-target",
      method: "post",
      headers: {
        "Content-Type": "multipart/form-data"
      },
      data
    })
}

export function taskList(data) {
    return request({
      url: "/jky/task-master",
      method: "get",
      params:data
    })
  }

export function taskAdd(data) {
    return request({
      url: "/jky/task",
      method: "post",
      data
    })
}

export function taskEdit(data,taskId) {
  return request({
    url: `/jky/task/${taskId}`,
    method: "put",
    data
  })
}

export function taskDel(taskMasterId) {
  return request({
    url: `/jky/task-master/${taskMasterId}`,
    method: "delete",
  })
}

export function taskDetail(taskId) {
  return request({
    url: `/jky/task/${taskId}`,
    method: "get",
  })
}
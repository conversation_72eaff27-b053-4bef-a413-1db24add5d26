<template>
    <div class="editor-container">
        <div style="border: 1px solid #ccc">
      <Toolbar
        style="border-bottom: 1px solid #ccc"
        :editor="editorRef"
        :defaultConfig="toolbarConfig"
        :mode="mode"
      />
      <Editor
        style="height: 310px; overflow-y: hidden;"
        v-model="valueHtml"
        :defaultConfig="editorConfig"
        :mode="mode"
        @onCreated="handleCreated"
         @onChange="onChange"
      />
     
    </div>
    </div>
</template>

<script setup>
  import '@wangeditor/editor/dist/css/style.css' // 引入 css
  import {uploadHtmlFileFunc} from '@/utils/pc-upload.js'
  import { onBeforeUnmount, ref, shallowRef, onMounted,defineProps, watch,defineEmits } from 'vue'
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
        let mode='default'
      // 编辑器实例，必须用 shallowRef
      const editorRef = shallowRef()
      let props=defineProps(['html'])
      let emits=defineEmits(['emitHtml'])
      // 内容 HTML
      const valueHtml = ref('')

      // 模拟 ajax 异步获取内容
      onMounted(() => {
        
      })
       watch(()=>props.html,(newval)=>{
        valueHtml.value=newval
       },{
        immediate:true
       })
      const toolbarConfig = {
        excludeKeys: ['insertVideo', 'group-video','fullScreen']
      }
      let onChange=(editor)=>{
        // console.log(editor.getHtml())
        if(editor.getHtml()=='<p><br></p>'){
           emits('emitHtml','')
        }else{
          emits('emitHtml',editor.getHtml())
        }
            
        } 
      const editorConfig = { 
          placeholder: '请输入内容...', 
         MENU_CONF: {},
       
       }
      editorConfig.MENU_CONF['uploadImage'] = {
        // 自定义上传
        async customUpload(file, insertFn) {
         
          uploadHtmlFileFunc(file,(url)=>{
           
            insertFn(url)
          })
          // insertFn(url, alt, href)
        },
      }
      // 组件销毁时，也及时销毁编辑器
      onBeforeUnmount(() => {
        const editor = editorRef.value
        if (editor == null) return
         editor.destroy()
       })

      const handleCreated = (editor) => {
        editorRef.value = editor // 记录 editor 实例，重要！
      }

</script>

<style lang="scss" scoped>
.editor-container{
    margin-top: 6px;
}
:deep(.w-e-text-placeholder){
  top:10px
}
:deep(.w-e-text-container){
  font-size: 20px;
}
:deep(.w-e-modal button){
  line-height: 16px;
}
:deep(.w-e-modal){
  height: 260px;
  overflow: auto;
}
</style>
<template>
    <div class="top flex_center flex_column">
          <div class="date">
             <el-select v-model="weekObj.label" placeholder="请选择" style="width:248px" @change="changeWeek">
               <el-option :label="item.label" :value="item.label" v-for="item in weekList" :key="item.label"></el-option>
            </el-select>
          </div>
          <div class="statistic flex_start_0">
               <div class="left">
                  <div class="tip flex_center">
                      <p class="flex_start">里程概览</p>
                  </div>
                  
                  <div class="flex_between">
                       <div class="flex_center flex_column item">
                           <div class="flex_center">
                                <h2>{{weeklyReportRow[0]?weeklyReportRow[0].points:0}}</h2>
                                <img src="@/assets/pc/score/up.png" alt="" v-show="weeklyReportRow[0]?.compareNumber&&weeklyReportRow[0].compareNumber>0" />
                                <img src="@/assets/pc/score/down.png" alt="" v-show="weeklyReportRow[0]?.compareNumber&&weeklyReportRow[0].compareNumber<0" />
                                <p :class="weeklyReportRow[0]?.compareNumber&&weeklyReportRow[0].compareNumber>0?'up':''">{{weeklyReportRow[0]?weeklyReportRow[0].compareNumber:'0'}}%</p>
                           </div>
                           <p class="item-title">周总步数</p>
                       </div>
                       <div class="line"></div>
                       <div class="flex_center flex_column item">
                           <div class="flex_center">
                                <h3>{{weeklyReportRow[1]?weeklyReportRow[1].points:0}}</h3>
                                   <img src="@/assets/pc/score/up.png" alt="" v-show="weeklyReportRow[1]?.compareNumber&&weeklyReportRow[1].compareNumber>0" />
                                <img src="@/assets/pc/score/down.png" alt="" v-show="weeklyReportRow[1]?.compareNumber&&weeklyReportRow[1].compareNumber<0" />
                                <p :class="weeklyReportRow[1]?.compareNumber&&weeklyReportRow[1].compareNumber>0?'up':''">{{weeklyReportRow[1]?weeklyReportRow[1].compareNumber:'0'}}%</p>
                           </div>
                           <p class="item-title">自我学习进步</p>
                       </div>
                        <div class="line"></div>
                       <div class="flex_center flex_column item">
                           <div class="flex_center">
                                  <h3>{{weeklyReportRow[2]?weeklyReportRow[2].points:0}}</h3>
                                  <img src="@/assets/pc/score/up.png" alt="" v-show="weeklyReportRow[2]?.compareNumber&&weeklyReportRow[2].compareNumber>0" />
                                  <img src="@/assets/pc/score/down.png" alt="" v-show="weeklyReportRow[2]?.compareNumber&&weeklyReportRow[2].compareNumber<0" />
                                  <p :class="weeklyReportRow[2]?.compareNumber&&weeklyReportRow[2].compareNumber>0?'up':''">{{weeklyReportRow[2]?weeklyReportRow[2].compareNumber:'0'}}%</p>
                           </div>
                           <p class="item-title">互动交流进步</p>
                       </div>
                  </div>
                  <div class="flex_between" style="margin-top:40px">
                       <div class="flex_center flex_column item">
                           <div class="flex_center">
                               
                                  <h3>{{weeklyReportRow[3]?weeklyReportRow[3].points:0}}</h3>
                                   <img src="@/assets/pc/score/up.png" alt="" v-show="weeklyReportRow[3]?.compareNumber&&weeklyReportRow[3].compareNumber>0" />
                                  <img src="@/assets/pc/score/down.png" alt="" v-show="weeklyReportRow[3]?.compareNumber&&weeklyReportRow[3].compareNumber<0" />
                                  <p :class="weeklyReportRow[3]?.compareNumber&&weeklyReportRow[3].compareNumber>0?'up':''">{{weeklyReportRow[3]?weeklyReportRow[3].compareNumber:'0'}}%</p>
                           
                           </div>
                           <p class="item-title">阶段成长进步</p>
                       </div>
                        <div class="line"></div>
                       <div class="flex_center flex_column item">
                            <div class="flex_center">
                                  <h3>{{weeklyReportRow[4]?weeklyReportRow[4].points:0}}</h3>
                                  <img src="@/assets/pc/score/up.png" alt="" v-show="weeklyReportRow[4]?.compareNumber&&weeklyReportRow[4].compareNumber>0" />
                                  <img src="@/assets/pc/score/down.png" alt="" v-show="weeklyReportRow[4]?.compareNumber&&weeklyReportRow[4].compareNumber<0" />
                                  <p :class="weeklyReportRow[4]?.compareNumber&&weeklyReportRow[4].compareNumber>0?'up':''">{{weeklyReportRow[4]?weeklyReportRow[4].compareNumber:'0'}}%</p>
                           </div>
                           <p class="item-title">行知成果进步</p>
                       </div>
                        <div class="line"></div>
                       <div class="flex_center flex_column item">
                            <div class="flex_center">
                                  <h3>{{weeklyReportRow[5]?weeklyReportRow[5].points:0}}</h3>
                                  <img src="@/assets/pc/score/up.png" alt="" v-show="weeklyReportRow[5]?.compareNumber&&weeklyReportRow[5].compareNumber>0" />
                                  <img src="@/assets/pc/score/down.png" alt="" v-show="weeklyReportRow[5]?.compareNumber&&weeklyReportRow[5].compareNumber<0" />
                                  <p :class="weeklyReportRow[5]?.compareNumber&&weeklyReportRow[5].compareNumber>0?'up':''">{{weeklyReportRow[5]?weeklyReportRow[5].compareNumber:'0'}}%</p>
                           </div>
                           <p class="item-title">退步</p>
                       </div>
                  </div>
               </div>
               <div class="right flex_1 flex_between flex_column">
                  <div class="group flex_center">
                    <div class="tip flex_center">
                      <p class="flex_start">社团排名</p>
                  </div>
                  <div class="flex_start" v-show="rankRow.clubRank">
                     第<h3>{{rankRow.clubRank}}</h3>名
                  </div>
                    <div class="flex_start" v-show="!rankRow.clubRank">
                    暂无排名
                  </div>
                  </div>
                  <div class="group flex_center">
                     <div class="tip flex_center">
                      <p class="flex_start">小组排名</p>
                    </div>
                  <div class="flex_start" v-show="rankRow.clubGroupRank">
                     第<h3>{{rankRow.clubGroupRank}}</h3>名
                  </div>
                   <div class="flex_start" v-show="!rankRow.clubGroupRank">
                    暂无排名
                  </div>
                  </div>
               </div>
          </div>
    </div>
    <div class="echart-box flex_between_0">
        <div class="left">
              <div class="model-title">能力分析</div>
              <div class="echart-ability" id="echart-ability"></div>
        </div>
        <div class="right">
             <div class="model-title">活跃趋势分析</div>
             <div class="echart-ability" id="echart-active"></div>
        </div>
    </div>
    <div class="tab-box">
         <div class="model-title">数据统计</div>
         <div class="tab flex_between">
              <div v-for="(item,index) in tabList" @click="changeCountTab(item)"  :class="[item.label==activeCountLabel?'active':'','flex_center','flex_column']" :key='index'>
                  <p>{{item.label}}</p>
              </div>
         </div>
         <ul class="action-list flex_start flex_wrap">
             <li class="action-item" v-for="item in detailList" :key="item.value">
                 <p class="title">{{item.label}}</p>
                 <div class="flex_start">
                     <h3 class="number">{{item.count?item.count.split('').splice(0,item.count.split('').length-1).join(''):'0'}}</h3>
                     <p class="unit">{{item.count?item.count.split('').splice(item.count.split('').length-1).join(''):'个'}}</p>
                 </div>
                 <img :src="imgMap[item.value]" alt="" class="tag">
             </li>
         </ul>
    </div>
</template>



<script setup>
import {onMounted, ref} from 'vue'
import * as echarts from 'echarts';
import {currentWeekProfile,weeks,weekCapacityAnalysis,weekTrendAnalysis,weeklyReport,weeklyCount,weeklyRank} from '@/api/pc/mileage.js'
let imgMap=ref({
    '10001':new URL('@/assets/pc/score/1-1.png/',import.meta.url),
    '10002':new URL('@/assets/pc/score/1-2.png/',import.meta.url),
    '20001':new URL('@/assets/pc/score/2-1.png/',import.meta.url),
    '20002':new URL('@/assets/pc/score/2-2.png/',import.meta.url),
     '20003':new URL('@/assets/pc/score/2-3.png/',import.meta.url),
    '20004':new URL('@/assets/pc/score/2-4.png/',import.meta.url),
    '20005':new URL('@/assets/pc/score/2-5.png/',import.meta.url),
    '20006':new URL('@/assets/pc/score/2-6.png/',import.meta.url),
     '30001':new URL('@/assets/pc/score/3-1.png/',import.meta.url),
    '30002':new URL('@/assets/pc/score/3-2.png/',import.meta.url),
    '40001':new URL('@/assets/pc/score/4-1.png/',import.meta.url),
     '40002':new URL('@/assets/pc/score/4-1.png/',import.meta.url),
      '40003':new URL('@/assets/pc/score/4-1.png/',import.meta.url),
    '40004':new URL('@/assets/pc/score/4-2.png/',import.meta.url),
     '40005':new URL('@/assets/pc/score/4-3.png/',import.meta.url),
    '50001':new URL('@/assets/pc/score/5-1.png/',import.meta.url),
   
})
let weekObj=ref({
    weekOfYear:'',
    year:'',
    label:''
})
let tabList=ref([
 
])
let detailList=ref([])
//排名
let rankRow=ref({})
let getweeklyRank=async()=>{
    let res=await weeklyRank(weekObj.value)
    if(res){
        rankRow.value=res.data
    }
}
//活动概览
let weeklyReportRow=ref([])
let getweeklyReport=async()=>{
    let res=await weeklyReport(weekObj.value)
    if(res){
       weeklyReportRow.value=[{
        label:'周总步数',
        points:res.data.totalPoints,
        value:'',
        weekOnWeek:res.data.totalPointsWeekOnWeek,
       },...res.data.eventGroups]
    //    console.log(weeklyReportRow.value,'23')
       weeklyReportRow.value=weeklyReportRow.value.map(item=>{
          return {
              points:item.points,
              compareNumber:item.weekOnWeek?item.weekOnWeek:"0"
          }
       })
       console.log( weeklyReportRow.value)
    }
}
let activeCountLabel=ref('')
let weeklyCountRow=ref({})
let getweeklyCount=async()=>{
    let res=await weeklyCount(weekObj.value)
    if(res){
         tabList.value=res.data.eventGroups
         detailList.value=res.data.eventGroups[0].eventTypes
         activeCountLabel.value=res.data.eventGroups[0].label
    }
}
let changeCountTab=(row)=>{
    activeCountLabel.value=row.label
    detailList.value=row.eventTypes
}
let echartsAbility=''
let getcurrentWeekProfile=async()=>{
    let res=await currentWeekProfile(weekObj.value)
    if(res){
        console.log(res)
      
    }
}
let getweekCapacityAnalysis=async()=>{
    let res=await weekCapacityAnalysis(weekObj.value)
    if(res){
         initEcharts1(res.data.eventGroups)
       
    }
}
let getweekTrendAnalysis=async()=>{
    let res=await weekTrendAnalysis(weekObj.value)
     if(res){
         initEcharts2(res.data.dailyPointsList)
       
    }
}
let initEcharts1=(arr)=>{
    // console.log(arr)
    echartsAbility=echarts.init(document.getElementById('echart-ability'));
    var legendData = ['你的步数', '社团平均步数']; //图例
    let my=arr.map(item=>item.myPoints)
    let avg=arr.map(item=>item.avgPoints)
    let max=Math.max(...[...my,...avg])>0?Math.max(...[...my,...avg]):10

    var indicator = [{
        text: '自我学习进步',
        max: max,
    },
    {
        text: '行知成果\n进步',
        max: max
    },
    {
        text:'阶段成长进步',
        max: max
    },
    {
        text: '互动交流\n进步',
        max: max,
        //  axisLabel: {show: true, textStyle: {fontSize: 18, color: '#333'}}
    }
    ];
    var dataArr = [{
            value:my,
            name: legendData[0],
            itemStyle: {
                normal: {
                    lineStyle: {
                        color: '#4A99FF',
                        // shadowColor: '#4A99FF',
                        // shadowBlur: 10,
                    },
                    // shadowColor: '#4A99FF',
                    // shadowBlur: 10,
                },
            },
            areaStyle: {
                    normal: { // 单项区域填充样式
                        color: {
                            type: 'linear',
                            x: 0, //右
                            y: 0, //下
                            x2: 1, //左
                            y2: 1, //上
                            colorStops: [{
                                offset: 0,
                                color: '#508CFF'
                            }, {
                                offset: 0.5,
                                color: '#508CFF'
                            }, {
                                offset: 1,
                                color: '#4A99FF'
                            }],
                            globalCoord: false
                        },
                        opacity: 0.3// 区域透明度
                    }
                }
        },
        {
            value: avg,
            name: legendData[1],
            itemStyle: {
                normal: {
                    lineStyle: {
                        color: '#4BFFFC',
                        // shadowColor: '#4BFFFC',
                        // shadowBlur: 10,
                    },
                    // shadowColor: '#4BFFFC',
                    // shadowBlur: 10,
                    // color:'rgba(157,214,252,.6)'
                },
            },
            areaStyle: {
                    normal: { // 单项区域填充样式
                        color: {
                            type: 'linear',
                            x: 0, //右
                            y: 0, //下
                            x2: 1, //左
                            y2: 1, //上
                            colorStops: [{
                                offset: 0,
                                color: '#FF8850'
                            }, {
                                offset: 0.5,
                                color: '#FF8850'
                            }, {
                                offset: 1,
                                 color: '#FF8850'
                            }],
                            globalCoord: false
                        },
                        opacity: 0.3 // 区域透明度
                    }
                }
        }
    ];
    var colorArr = ['#508CFF', '#FEAC12']; //颜色
    var option = {
        backgroundColor: '#fff',
        color: colorArr,
        // grid:{
           
        //     left:'70px',
        //     right:'70px'
        // },
        legend: {
            orient:'vertical',
            icon: 'circle', //图例形状
            data: legendData,
            bottom:10,
            right:0,
            itemWidth: 14, // 图例标记的图形宽度。[ default: 25 ]
            itemHeight: 14, // 图例标记的图形高度。[ default: 14 ]
            itemGap: 21, // 图例每项之间的间隔。[ default: 10 ]横向布局时为水平间隔，纵向布局时为纵向间隔。
            textStyle: {
                fontSize: 14,
                color: '#2B2C33',
            },
        },
        radar: {
            // shape: 'circle',
            name: {
                textStyle: {
                    color: '#333333',
                    fontSize: 14,
                    lineHeight:22,
                    fontWeight:'bold',
                    
                },
            },
            indicator: indicator,
            splitArea: { // 坐标轴在 grid 区域中的分隔区域，默认不显示。
                show: true,
                areaStyle: { // 分隔区域的样式设置。
                    color: [ '#F5F8FC','#E2EAF7'], // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
                }
            },
            axisLine: { //指向外圈文本的分隔线样式
                lineStyle: {
                    color: '#E8E9F0'
                }
            },
           
        },
        series: [{
            type: 'radar',
            symbolSize: 8,
            // symbol: 'angle',
            data: dataArr,
            
        }]
    };
    echartsAbility.setOption(option)
}
var echartsActive=''
let initEcharts2=(arr)=>{
    echartsActive=echarts.init(document.getElementById('echart-active'));
    const colorList = ["#9E87FF", '#73DDFF', '#fe9a8b', '#F56948', '#9E87FF']
    let dateArr=arr.map(item=>item.date)
    let pointsArr=arr.map(item=>item.points)
    var  option = {
    backgroundColor: '#fff',
    // title: {
    //     text: '全国6月销售统计',
    //     textStyle: {
    //         fontSize: 12,
    //         fontWeight: 400
    //     },
    //     left: 'center',
    //     top: '5%'
    // },
    // legend: {
    //     icon: 'circle',
    //     top: '5%',
    //     right: '5%',
    //     itemWidth: 6,
    //     itemGap: 20,
    //     textStyle: {
    //         color: '#556677'
    //     }
    // },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            label: {
                show: true,
                backgroundColor: '#fff',
                color: '#556677',
                borderColor: 'rgba(0,0,0,0)',
                shadowColor: 'rgba(0,0,0,0)',
                shadowOffsetY: 0
            },
            lineStyle: {
                width: 0
            }
        },
        backgroundColor: '#fff',
        textStyle: {
            color: '#5c6c7c'
        },
        padding: [10, 10],
        extraCssText: 'box-shadow: 1px 0 2px 0 rgba(163,163,163,0.5)'
    },
    grid: {
        top: '15%',
        bottom:'10%'
    },
    xAxis: [{
        type: 'category',
        data: dateArr,
        axisLine: {
            lineStyle: {
                color: '#DCE2E8'
            }
        },
        axisTick: {
            show: false
        },
        axisLabel: {
            interval: 0,
            textStyle: {
                color: '#8DA2B5'
            },
            // 默认x轴字体大小
            fontSize: 12,
            // margin:文字到x轴的距离
            margin: 15
        },
        axisPointer: {
            label: {
                // padding: [11, 5, 7],
                padding: [0, 0, 10, 0],
                /*
                */
                // 这里的margin和axisLabel的margin要一致!
                margin: 15,
                // 移入时的字体大小
                fontSize: 12,
              
            }
        },
        boundaryGap: false
    }],
    yAxis: [{
        type: 'value',
        axisTick: {
            show: false
        },
        axisLine: {
            show: false,
            lineStyle: {
                color: '#DCE2E8'
            }
        },
        axisLabel: {
            textStyle: {
                color: '#8DA2B5'
            }
        },
        splitLine: {
            show: true,
            lineStyle:{
                type:'dashed'
            }
        }
    }, ],
    series: [{
            name: '分数',
            type: 'line',
            data: pointsArr,
            symbolSize: 6,
            symbol: 'circle',
            smooth: true,
            yAxisIndex: 0,
            showSymbol: true,
         
            lineStyle: {
                width: 2,
                color:'#508CFF',
               
            },
            itemStyle: {
                normal: {
                    color: '#508CFF',
                    borderColor: '#508CFF'
                }
            },
              areaStyle: {
            normal: {
                color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [{
                            offset: 0,
                            color: 'rgba(162,219,254,.5)'
                        },
                        {
                            offset: 1,
                            color:'rgba(162,219,254,.5)'
                        }
                    ],
                    false
                ),
                shadowColor:'rgba(162,219,254,.5)',
                shadowBlur: 10
            }
        },
        }
    ]
    };
     echartsActive.setOption(option)
}
let weekList=ref([])
let getWeek=async()=>{
    let res=await weeks()
    if(res){
        weekList.value=res.data.weeks
        weekObj.value=res.data.current
         getweekCapacityAnalysis()
         getweekTrendAnalysis()
         getweeklyReport()
         getweeklyCount()
         getweeklyRank()
    }
}
let changeWeek=(val)=>{
     weekObj.value.label=val
    weekList.value.forEach(item=>{
         if(item.label==val){
            weekObj.value.weekOfYear=item.weekOfYear
            weekObj.value.year=item.year
              getweekCapacityAnalysis()
              getweekTrendAnalysis()
              getweeklyReport()
              getweeklyCount()
              getweeklyRank()
         }
    })
}
onMounted(()=>{
    getWeek()
    getcurrentWeekProfile()
  
    // initEcharts1()
    // initEcharts2()
})
</script>
<style lang='scss' scoped>
@import '@/css/mileagePublic.scss';

.echart-box{
    // transform: translateY(-20px);
    padding: 0 40px ;
    background: #fff;

    .left{
      width: 48%;
     
    }
    .right{
      width: 48%;
    }
     .echart-ability{
        height: 352px;
        margin-bottom:60px;
      }
}

.action-item{
    position: relative;
 
}
</style>
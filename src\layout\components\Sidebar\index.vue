<script setup>
import { computed } from "vue"
import { useRoute } from "vue-router"
import { usePermissionStore } from "@/store/modules/permission"
import SidebarItem from "./SidebarItem.vue"
import { useDevice } from "@/hooks/useDevice"
import { getCssVar } from "@/utils/css"
const { isMobile } = useDevice()
const route = useRoute()
const permissionStore = usePermissionStore()

const activeMenu = computed(() => {
  const {
    meta: { activeMenu },
    path
  } = route
  return activeMenu ? activeMenu : path
})
const noHiddenRoutes = computed(() => permissionStore.routes.filter((item) => !item.meta?.hidden))
const sidebarMenuItemHeight = computed(() => {
  return "var(--v3-navigationbar-height)"
})
const sidebarMenuHoverBgColor = computed(() => {
  return "#fff"
})
const tipLineWidth = computed(() => {
  return "0px"
})
const activeTextColor = computed(() => {
  return "#508CFF"
})
</script>

<template>
  <div class="sidebar-container">
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        background-color="transparent"
        :unique-opened="true"
        :collapse-transition="false"
        :mode="'horizontal'"
        :text-color="'#fff'"
        :active-text-color="'#508CFF'"
      >
        <SidebarItem v-for="route in noHiddenRoutes" :key="route.path" :item="route" :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<style lang="scss" scoped>
%tip-line {
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: v-bind(tipLineWidth);
    height: 100%;
    background-color: var(--v3-sidebar-menu-tip-line-bg-color);
  }
}

.has-logo {
  .el-scrollbar {
    height: calc(100% - var(--v3-header-height));
  }
}

.el-scrollbar {
  height: 100%;
  :deep(.scrollbar-wrapper) {
    // 限制水平宽度
    overflow-x: hidden !important;
  }
  // 滚动条
  :deep(.el-scrollbar__bar) {
    &.is-horizontal {
      // 隐藏水平滚动条
      display: none;
    }
  }
}

.el-menu {
  border: none;
  width: 100% !important;
}

.el-menu--horizontal {
  height: v-bind(sidebarMenuItemHeight);
}

:deep(.el-menu-item),
:deep(.el-sub-menu__title),
:deep(.el-sub-menu .el-menu-item),
:deep(.el-menu--horizontal .el-menu-item) {
  height: v-bind(sidebarMenuItemHeight);
  line-height: v-bind(sidebarMenuItemHeight);
  padding: 0 12px;
  &.is-active {
    color: v-bind(activeTextColor) !important;
    background-color: v-bind(sidebarMenuHoverBgColor);
  }

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

:deep(.el-sub-menu) {
  &.is-active {
    > .el-sub-menu__title {
      color: v-bind(activeTextColor) !important;
    }
  }
}

:deep(.el-menu-item.is-active) {
  @extend %tip-line;
}

.el-menu--collapse {
  :deep(.el-sub-menu.is-active) {
    .el-sub-menu__title {
      @extend %tip-line;
    }
  }
}
</style>

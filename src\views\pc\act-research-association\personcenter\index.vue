<template>
    <div class="content-box flex_start_0 mt10">
      
       <div class="left">
           <div class="person-column">
           
              <p class="title">个人空间</p>
              <ul class="list">
                <li :class="['flex_between',item.id==activeIndex?'active':'']" v-for="item in diaryList" :key="item.id" @click="changeActive(item)">
                    <div class="flex_start">
                         <img src="@/assets/pc/p1.png" alt="" v-show="item.id==1&&item.id!=activeIndex">
                         <img src="@/assets/pc/p1-1.png" alt="" v-show="item.id==1&&item.id==activeIndex">
                         <img src="@/assets/pc/p2.png" alt="" v-show="item.id==2&&item.id!=activeIndex">
                         <img src="@/assets/pc/p2-2.png" alt="" v-show="item.id==2&&item.id==activeIndex">
                         <img src="@/assets/pc/p3.png" alt="" v-show="item.id==3&&item.id!=activeIndex">
                         <img src="@/assets/pc/p3-3.png" alt="" v-show="item.id==3&&item.id==activeIndex">
                         <img src="@/assets/pc/p4.png" alt="" v-show="item.id==4">
                        
                        <img src="@/assets/pc/p5.png" alt="" v-show="item.id==5&&item.id!=activeIndex">
                         <img src="@/assets/pc/p5-5.png" alt="" v-show="item.id==5&&item.id==activeIndex">
                        <p>{{item.title}}</p>
                    </div>
                    <el-icon><ArrowRight /></el-icon>
                </li>
              </ul>
           </div>
           <div class="friend-link">
       
              <!-- <FriendLink /> -->
           </div>
       </div>
      
       <div class="flex_1 right">
           <div v-if="$route.path.indexOf('mileage')>-1">
              <router-view></router-view>
           </div>
           <Personaldiary v-else :role="userInfo.xingZhiShe" :total='total' :pageSize='searchForm.pageSize' @emitCurrentChange='emitCurrentChange' @emitParentDetail='emitParentDetail' @emitParentDel='emitParentDel' :activeIndex='activeIndex' @needLoad='needLoad' :url='url' :countForm='countForm' @emitParentIsLike='emitParentIsLike' :tableData='tableData' @emitsParentForm='emitsParentForm' />
            
       </div>
       
    </div>
</template>
<script setup>
import {onActivated, onMounted, reactive, ref} from 'vue'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import Personaldiary from './personaldiary.vue'
import {useRouter} from 'vue-router'
import {recordProfile,memberDiary,leaderDiary,leaderRecommendedDiary,tutorRecommendedDiary,myCollect,} from '@/api/pc/index.js'
import FriendLink from '@/views/pc/component/act-research-association/friendLink.vue'

import {storeToRefs} from 'pinia'
let userInfoStore=useUserInfoStoreHook()
let {personcenterCurrentInfo,userInfo}=storeToRefs(userInfoStore)
let diaryList=ref([{id:'1',title:'个人日记'},{id:'5',title:'我的收藏'}])
let activeIndex=ref(1)
let router=useRouter()
let countForm=ref({
    totalComments:0,
    totalEvaluations:0,
    totalLikes:0,
    totalScore:0,
    totalViews:0,
})
let roles=ref([])
let searchForm=reactive({
    pageNum:1,
    pageSize:10,
    orderBy:'',
    beginRecordDate:'',
    endRecordDate:'',
})
let url=ref('/actResearchAssociation/personcenter/detail')
let tableData=ref([])
let total=ref(0)
let maxPage=0
let emitParentDel=(id)=>{
      getList(activeIndex.value)
    // console.log(id,'11')
    //  tableData.value= tableData.value.filter(item=>{
    //     return item.id!=id
    //  })
}
let emitParentDetail=(recordId)=>{
    // alert(recordId)
    personcenterCurrentInfo.value={
        activeIndex:activeIndex.value,
         orderBy:searchForm.orderBy,
         beginRecordDate:searchForm.beginRecordDate,
         endRecordDate:searchForm.endRecordDate
    }
    
}
router.beforeEach((to,from)=>{
//    console.log(from)
    if(from.name=='ActResearchAssociation-upload'){
          Object.assign(searchForm,{
            pageNum:1
          })
         getList(activeIndex.value)
    }
})
let needLoad=(val)=>{
    // console.log('3')
    if(searchForm.pageNum<maxPage){
        Object.assign(searchForm,{
           pageNum:searchForm.pageNum+1
        })
         getList(activeIndex.value)
    }
}
let getCount=async()=>{
    let res=await recordProfile()
    if(res){
        countForm.value={
            totalComments:res.data.totalComments?res.data.totalComments:0,
            totalEvaluations:res.data.totalEvaluations?res.data.totalEvaluations:0,
            totalLikes:res.data.totalLikes?res.data.totalLikes:0,
            totalScore:res.data.totalScore?res.data.totalScore:0,
            totalViews:res.data.totalViews?res.data.totalViews:0,
            totalRecords:res.data.totalRecords?res.data.totalRecords:0
        }
    }
}
let emitParentIsLike=(islike,id,type)=>{
    // console.log(islike,id)
    if(type=='thumb'){
       tableData.value.forEach(item=>{
        if(item.id==id){
            item.likes=islike?(item.likes-1):(item.likes+1)
            item.isLike=!islike
        }
      })
    }else if(type=='favorite'){
       tableData.value.forEach(item=>{
        if(item.id==id){
            item.favorites=islike?(item.favorites-1):(item.favorites+1)
            item.isFavorite=!islike
        }
      })  
    }
   
    getCount()
}
// 
let changeActive=(row)=>{
    tableData.value=[]
    Object.assign(searchForm,{
        pageNum:1
    })
    activeIndex.value=row.id
    getList(row.id)
    // getCount()
}
let emitsParentForm=(val)=>{
    tableData.value=[]
    Object.assign(searchForm,{
        pageNum:1,
       orderBy:val.orderBy,
       beginRecordDate:(val.daterange&&val.daterange[0])?val.daterange[0]:'' ,
       endRecordDate:(val.daterange&&val.daterange[1])?val.daterange[1]:'' ,
    })
    getList(activeIndex.value)
}
function getWeekday(date) {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const dayIndex = new Date(date).getDay();
    return weekdays[dayIndex];
}
let getList=async(arg)=>{
    let res=''
    let params={
        pageNum:searchForm.pageNum,
        pageSize:searchForm.pageSize,
        beginRecordDate:searchForm.beginRecordDate,
        endRecordDate:searchForm.endRecordDate, 
    }
    if(searchForm.orderBy){
        params.orderBy=searchForm.orderBy
    }
   if(arg==1){
       res=await memberDiary(params)
   }
   if(arg==2){
       res=await leaderDiary(params)
   }
   if(arg==3){
       res=await leaderRecommendedDiary(params)
   }
   if(arg==4){
       res=await tutorRecommendedDiary(params)
   }
   if(arg==5){
       res=await myCollect(params)
   }
   if(res){
      res.data.list.forEach(item=>{
            item.week=getWeekday(item.recordDate)
        })
      tableData.value=res.data.list
      total.value=res.data.total
      maxPage=res.data.pages
   }

}
let emitCurrentChange=(val)=>{
    
    Object.assign(searchForm,{
        pageNum:val
    })
    getList(activeIndex.value)

}
    onActivated(()=>{
        getCount()
        getList(activeIndex.value)
    // let obj=sessionStorage.getItem('detailAction')?JSON.parse(sessionStorage.getItem('detailAction')):{}
    // if(obj.id){
    //     tableData.value.forEach(item=>{
    //          if(item.id==obj.id){
    //             item.isFavorite=obj.isFavorite
    //             item.isLike=obj.isLike
    //             item.isViewed=obj.isViewed
    //             item.views=item.views+1
    //             if(obj.isFavorite){
    //                 item.favorites=item.favorites+1
    //             }
    //             if(obj.isLike){
    //                   item.likes=item.likes+1
    //             }
    //             sessionStorage.removeItem('detailAction')
    //             return 
    //          }
    //     })
    // }

})
onMounted(()=>{
//   getList(1)
})
</script>
<style lang="scss" scoped>
.mt10{
    margin-top: 10px;
}
.left{
    width: 180px;
    .person-column{
        border-radius: 8px;
        background: #fff;
        padding-bottom: 16px;
        .title{
             padding: 18px 0  24px 18px;
             font-weight: bold;
             font-size: 20px;
             color: #2B2C33;
        }
        .list{
            li{
                cursor: pointer;
                padding: 10px 8px  8px 18px;
                font-size: 16px;
                color: #2B2C33;
                margin-bottom: 6px;
                p{
                    line-height: 16px;
                }
                &.active{
                    background: #DFEAFF;color: #508CFF;
                }
                img{
                    width: 14px;
                    margin-right: 8px;
                    height: auto;
                }
            }
        }
    }
    .friend-link{
        margin-top: 10px;
    }
}
.right{
    margin-left: 10px;
   
}
</style>
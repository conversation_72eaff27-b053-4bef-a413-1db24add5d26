import { ElMessage } from "element-plus";
let errMsg='当前浏览器不支持音频录制'
export function isAudioRecordingSupported() {
    // 检查getUserMedia支持
    const hasGetUserMedia = !!(
      navigator.mediaDevices &&
      navigator.mediaDevices.getUserMedia
    );
    
    // 检查MediaRecorder支持
    const hasMediaRecorder = typeof MediaRecorder !== 'undefined';
    
    return hasGetUserMedia && hasMediaRecorder;
  }
  
  // 使用示例
//   if (isAudioRecordingSupported()) {
//     console.log('浏览器支持音频录制');
//   } else {
//     console.log('浏览器不支持音频录制');
//   }
  async function canAccessMicrophone() {
    try {
      // 尝试获取音频流（仅检测，不实际录制）
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // 获取成功后立即释放
      stream.getTracks().forEach(track => track.stop());
      
      return true;
    } catch (error) {
      console.error('无法访问麦克风:', error);
      
      // 处理常见错误类型
      if (error.name === 'NotAllowedError') {
        console.log('用户拒绝了麦克风权限');
        errMsg='用户拒绝了麦克风权限'
      } else if (error.name === 'NotFoundError') {
        console.log('未找到麦克风设备');
        errMsg='未找到麦克风设备'
      } else if (error.name === 'NotSupportedError') {
        console.log('当前URL不支持录音（需HTTPS）');
        errMsg='当前URL不支持录音（需HTTPS）'
      } else if (error.name === 'NotReadableError') {
        console.log('麦克风被占用');
        errMsg='麦克风被占用'
      } else if (error.name === 'SecurityError') {
        console.log('安全限制，无法访问麦克风');
        errMsg='安全限制，无法访问麦克风'
      }
      
      return false;
    }
  }
  export async function initAudioRecorder() {
    // 1. 基础API检测
    if (!isAudioRecordingSupported()) {
        ElMessage.error('当前浏览器不支持音频录制')
        return false;
    }
    
    // 2. 尝试获取麦克风权限
    const hasPermission = await canAccessMicrophone();
    
    if (hasPermission) {
      // 3. 权限获取成功，初始化录音器
        return true
      
      // 设置录音事件处理...
    } else {
      // 4. 权限被拒绝，提供替代方案
      ElMessage.error(errMsg)
      return false
    }
  }  


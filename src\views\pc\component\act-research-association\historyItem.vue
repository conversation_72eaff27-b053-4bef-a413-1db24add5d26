<template>
     <el-dialog
        v-model="historyItemVisible"
        title=""
        width="850"
        class="noticedetail-dialog"
        :before-close="handleClose"
        :close-on-click-modal='false'
    >
    
     <div class="header">
           <div class="flex_between_0">
              <div class="flex_start_0 left flex_1">
                    <img src="@/assets/pc/notice1.png" alt="">
                    <div>
                        <p class="title">{{noticeDetailRow.title}}</p>
                        <p class="name">{{noticeDetailRow.userName}}&nbsp;&nbsp;{{noticeDetailRow.postTime}} &nbsp;&nbsp;发布</p>
                    </div>
              </div>
              <img src="@/assets/pc/notice2.png" alt="" class="right"/>
           </div>
     </div>
     <p class="html" v-html="noticeDetailRow.content"></p>
  </el-dialog>
</template>
<script setup>
import {ref,defineProps} from 'vue'
import { useUserInfoStore } from "@/store/modules/pc"
import {storeToRefs} from 'pinia'
let userStore=useUserInfoStore()
let {historyListVisible,historyItemVisible,historyItemForm}=storeToRefs(userStore)
let props=defineProps(['noticeDetailRow'])
let handleClose=()=>{
      historyItemVisible.value=false
    if(historyItemForm.value=='list'){
      historyListVisible.value=true
    }else{ 
         historyListVisible.value=false
    }
  
}
</script>
<style lang='scss' scoped>
.header{
    .left{
        margin-right: 20px;
       img{
        width: 41px;
        height:32px;
        margin-right: 12px;
       }
       .title{
        font-size: 24px;
        padding-bottom: 11px;
        color: #3D76E3;
        line-height: 33px;
        font-weight: bold;
        background: url('@/assets/pc/line.png') no-repeat;
        background-position:0 100% ;
        background-size: 100% 6px;
       
       }
       .name{
        font-size: 18px;
        color: #2B2C33;
        font-weight: bold;
        margin-top: 11px;
       }
    }
    .right{
        width: 120px;
        height: auto;
    }
}
.html{
    height: 400px;
    overflow: auto;
    margin-top: 24px;
    font-size: 16px;
    color: #2B2C33;
 }
</style>
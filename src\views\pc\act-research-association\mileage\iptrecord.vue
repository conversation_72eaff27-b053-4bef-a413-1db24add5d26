<template>
    <div class="record-container">
        <div class="flex_between">
             <div class="flex_start">
                 <el-select placeholder="全部进步类型" style="width:160px;margin-right:16px">
                     <el-option value='' label=''></el-option>
                 </el-select>
                 <el-select placeholder="全部行为类型" style="width:160px;margin-right:16px">
                     <el-option value='' label=''></el-option>
                 </el-select>
                 <el-date-picker
                    v-model="searchForm.range"
                    type="daterange"
                    style="width:248px"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                />
             </div>
             <el-button class="export">导出</el-button>
        </div>
         <el-table :data="tableData" style="width: 100%;margin-top:16px" :header-cell-style='{background:"#f9f9f9"}' stripe>
            <el-table-column prop="date" label="里程类型" align="center" />
            <el-table-column prop="name" label="行为类型" align="center" />
            <el-table-column prop="address" label="里程步数" align="center"/>
             <el-table-column prop="address" label="社员" align="center"/>
             <el-table-column prop="address" label="录入人" align="center"/>
             <el-table-column prop="address" label="录入日期" align="center"/>
        </el-table>
        <el-pagination background style="margin-bottom:10px" layout="prev, pager, next" :page-size='searchFrom.pageSize' :total="total" @current-change='handlelCurrentChange' />
    </div>
</template>
<script setup>
import {reactive, ref} from 'vue'
let searchForm=reactive({
    range:[]
})
let tableData=ref([
    {name:'11'}, {name:'11'}, {name:'11'}, {name:'11'},
])
let searchFrom=reactive({
    pageSize:10
})
let total=ref(0)
let handlelCurrentChange=()=>{

}


</script>
<style lang='scss' scoped>
 .record-container{
    padding: 18px 18px 10px 18px;
    border-radius: 4px;
    background: #fff;
  :deep(.el-select__wrapper){
    height: 40px;
  }
  :deep(.el-range-editor.el-input__wrapper){
    height: 40px;
  }
  .export{
    width: 132px;
    height: 40px;
    background: #508CFF;
    border-radius: 8px;
    font-size: 16px;
color: #FFFFFF;

    border-radius: 4px;
  }
 }
</style>
<template>
     <div class="content-box  upload mt10" >
        <div class="select-form">
             <div class="flex_start title" @click="goCancel">
                <el-icon style="margin-right: 4px;"><ArrowLeft /></el-icon>
               {{resourceId?'编辑资源':'上传资源'}} 
            </div>
           
            <el-form :inline="true" ref='selectRef' :model="formInline" :rules="rules" class="demo-form-inline flex_between flex_wrap" label-width='50px' label-position='top' style="margin-top:24px">
                <el-form-item label="资源名称" style="width:45%" prop='title'>
                    <el-input v-model="formInline.title" placeholder="请输入资源名称" ></el-input>
                </el-form-item>
                <el-form-item label="学段" style="width:45%" prop='stage'>
                   <el-select
                        v-model="formInline.stage"
                        placeholder="请选择学段"
                        clearable
                        @change="changeStage"
                      
                    >
                        <el-option :label="item.title" :value="item.id" v-for="item in stages" :key="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="学科" style="width:45%" prop='subjectId'>
                    <el-select
                        v-model="formInline.subjectId"
                        placeholder="请选择学科"
                        clearable
                       
                        @change="changeSubject"
                    >
                         <el-option :label="item.label" :value="item.value" v-for="item in subjects" :key="item.value" />
                    </el-select>
                </el-form-item>
                 <el-form-item label="教材" style="width:45%">
                    <el-select
                        v-model="formInline.editionId"
                        placeholder="请选择教材"
                        clearable
                        
                        @change="changeEdition"
                    >
                      <el-option :label="item.label" :value="item.value" v-for="item in editions" :key="item.value" />
                    </el-select>
                </el-form-item>
                 <el-form-item label="册" style="width:45%">
                    <el-select
                        v-model="formInline.volumeId"
                        placeholder="请选择册"
                        clearable
                        
                        @change='changeVolume'
                    >
                         <el-option :label="item.label" :value="item.value" v-for="item in volumes" :key="item.value" />
                    </el-select>
                </el-form-item>
                 <el-form-item label="章/节" style="width:45%">
                       <el-cascader :options="chapters"  v-model="formInline.chapterIds" :props="{
                        checkStrictly: true,
                       
                        }" clearable />
                 
                </el-form-item>
                
                   
                
            </el-form>
        </div>
        <div class="btn-list flex_end">
             <el-buton class="cancel" @click="goCancel">返回</el-buton>
             <el-buton class="submit" @click="goEdit" >确认</el-buton>
           
        </div>
    </div>
</template>
<script setup>

import {ref,reactive,onMounted} from 'vue'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {subjectList,editionList,volumeList,chapterTree,questionAdd,questionEdit,resourceAdminEdit,userTenant} from '@/api/pc/index.js'
import {jyfxDetailNew,jyfxEdit} from '@/api/pc/wisdom.js'
import {ElMessage,ElMessageBox} from 'element-plus'
import {storeToRefs} from 'pinia'
import {useRouter,useRoute} from 'vue-router'
let router=useRouter()
let route=useRoute()
let resourceId=ref('')
let userInfoStore=useUserInfoStoreHook()
let {stages,userInfo}=storeToRefs(userInfoStore)
let subjects=ref([])
let editions=ref([])
let volumes=ref([])
let chapters=ref([])
let selectRef=ref()
let tenantoptions=ref([])
let formInline=reactive({
    stage:'',
    subjectId:'',
    editionId:'',
    volumeId:'',
    chapterIds:[],
    chapter1Id:'',
    chapter2Id:'',
    title:"",
})
let id=2
let changeStage=async()=>{
     editions.value=[]
     volumes.value=[]
     chapters.value=[]
    Object.assign(formInline,{
        subjectId:'',
        editionId:'',
        volumeId:'',
        chapterIds:[]
    })
    let res=await subjectList({
        stage:formInline.stage
    })
    if(res){
        subjects.value=res.data
    }
}
onMounted(async()=>{
  
    resourceId.value=route.query.id
   
   if(resourceId.value){
      let res=await jyfxDetailNew({
        resourceId:resourceId.value
      })  
      if(res){
      
        let chapterIds=[]
      
        //上面的基础信息
        Object.assign(formInline,{
            title:res.data.title,
            stage:res.data.stageId.toString(),
            subjectId:res.data.subjectId,
            editionId:res.data.editionId?res.data.editionId:"",
            volumeId:res.data.bookId?res.data.bookId.toString():"",
            chapterIds:[],
            chapter1Id:'',
            chapter2Id:'',
            belongToName:res.data.createTitle,
        })
      
        if(res.data.chapterId){
            chapterIds[0]=res.data.chapterId?res.data.chapterId.toString():""
        }
        if(res.data.childChapterId){
            chapterIds[1]=res.data.childChapterId?res.data.childChapterId.toString():''

        }
       
        Object.assign(formInline,{
            chapterIds:chapterIds
        })
          let res0=await subjectList({
                stage:formInline.stage
            })
            if(res0){
                subjects.value=res0.data
            }
           let res1=await editionList({
                stage:formInline.stage,
                subjectId:formInline.subjectId,
            })
            if(res1){
                editions.value=res1.data
            }
            if(formInline.editionId){
                let res2=await volumeList({
                    editionId:formInline.editionId
                })
                if(res2){
                    volumes.value=res2.data
                }
            }
             if(formInline.volumeId){
                let res3=await chapterTree({
                    volumeId:formInline.volumeId
                })
                if(res3){
                    chapters.value=res3.data
                }
             }
          
      } 
   } else{
    Object.assign(formInline,{
        belongToName:userInfo.value.user?.name
    })
   }
})
let changeSubject=async()=>{
      volumes.value=[],
      chapters.value=[]
       Object.assign(formInline,{
        editionId:'',
        volumeId:'',
        chapterIds:[]
    })
    let res=await editionList({
        stage:formInline.stage,
        subjectId:formInline.subjectId,
    })
    if(res){
        editions.value=res.data
    }
}
let changeEdition=async()=>{
    chapters.value=[]
    
     Object.assign(formInline,{
        volumes:'',
        chapterIds:[]
    })
     let res=await volumeList({
        editionId:formInline.editionId
     })
     if(res){
        volumes.value=res.data
     }
}
let changeVolume=async()=>{
     Object.assign(formInline,{
        chapterIds:[]
    })
    let res=await chapterTree({
        volumeId:formInline.volumeId
    })
    if(res){
        chapters.value=res.data
    }
}

let rules={
    stage:[{ required: true, message: '请选择学段', trigger: 'blur' }],
    subjectId:[{ required: true, message: '请选择学科', trigger: 'blur' }],
    title:[{ required: true, message: '请输入资源名称', trigger: 'blur' }],
  
   
}

let goCancel=()=>{
    router.go(-1)
}

let goEdit=()=>{
   
    
    let p1=new Promise((resolve,reject)=>{
         selectRef.value.validate((valid, fields) => {
            if (valid) {
               resolve()
            } else {
              reject()
            }
        })
    })
     
    Promise.all([p1]).then(data=>{
           let params={
                resourceId:resourceId.value,
                title:formInline.title,
                stageId:formInline.stage,
                subjectId:formInline.subjectId,
                editionId:formInline.editionId,
                bookId:formInline.volumeId,
                chapterId:formInline.chapterIds[1]?formInline.chapterIds[1]:formInline.chapterIds[0]?formInline.chapterIds[0]:'',
                
           }
          ElMessageBox.confirm(
            '确认提交编辑吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
            let res=await jyfxEdit(params)
            if(res){
              
                ElMessage.success('编辑成功')
                router.go(-1)
            }
              
        })
        .catch(() => {
      
        })
       
       
    }).catch(err=>{
        console.log(err)
    })

}

</script>
<style lang="scss" scoped>
 .select-form{
    border-radius: 8px;
    padding: 24px 24px 0 24px;
    background: #fff;
    .upitem{
        margin-top: 10px;
    }
    .add-item{
        background: #FFFFFF;
        border-radius: 12px;
        border: 1px dashed #508CFF;
        font-size: 16px;
        padding: 20px 0;
        cursor: pointer;
        margin-top: 16px;
color: #4260FD;
        img{
            width: 24px;
            height: auto;
            margin-right: 10px;
            
        }
    }
    .title{
        font-size: 20px;
        color: #2B2C33;
        font-weight: bold;
        cursor: pointer;
    }
    :deep(.el-form--inline .el-form-item){
        margin-right: 0;
        margin-bottom: 24px;
    }
    :deep(.el-form-item--label-top .el-form-item__label){
        font-size: 16px;
        color: #2B2C33;
        font-weight: bold;
    }
    :deep(.el-select__wrapper){
        height: 48px;
    }
    :deep(.el-radio.el-radio--large .el-radio__label){
        font-size: 16px;color: #2B2C33;
    }
    :deep(.el-radio.el-radio--large .el-radio__inner){
        height: 24px;
        width: 24px;
        &::after{
            transform: translate(-50%,-50%) scale(2.4);
        }
    }
    :deep(.el-radio__input.is-checked .el-radio__inner){
        background: rgb(36,198,152);
        border-color: rgb(36,198,152);
    }
    :deep(.el-cascader){
        width: 100%;
    }
    :deep(.el-input__wrapper){
        height: 48px;
    }
 }

 .btn-list{
    padding: 16px 0 24px 0;
    .cancel{
        font-weight: bold;
        font-size: 18px;
        color: #6D6F75;
        line-height: 26px;
        padding: 11px 42px;
        border-radius: 12px;
        border: 1px solid #E6E6E6;
        background: #fff; cursor: pointer;
    }
    .submit{
        padding: 11px 46px;
         line-height: 26px;
        background: #508CFF;
        border-radius: 12px;
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        margin-left: 16px;
        cursor: pointer;
    }
 }
</style>
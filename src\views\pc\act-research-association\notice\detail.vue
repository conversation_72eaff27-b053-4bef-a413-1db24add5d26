<template>
        <div class="content-box  notice-detail mt10" >
          <div class="flex_start title" @click="router.go(-1)">
            <el-icon style="margin-right: 4px;"><ArrowLeft /></el-icon>
            公告详情
          </div>
          <div class="flex_start_0 diary">
    
          <img :src="detailRow.userAvatarUrl || DefaultAvatar" alt="" class="head" />
           <!-- <el-avatar :src="detailRow.userAvatarUrl || DefaultAvatar" class="avatar head" :size="32"  style="margin-right:8px" /> -->
          <div class="flex_1">
                 <div class="flex_between">
                    <div class="info">
                        <div class="flex_start ">
                            <p class="teacher">{{detailRow.userName}}</p>
                            
                        </div>
                        <p class="date">{{detailRow.postTime}}</p>
                    </div>
                    
                 </div>
                <div class="rich-open-html" ref="richRef" v-html="detailRow.content"></div>
               
          </div>
  </div>
        </div>
</template>
<script setup>
import WangEditor from '@/views/pc/component/wangeditor.vue'
import {noticeDetail,noticeView,noticeUnViewCount} from '@/api/pc/index.js'
import {onMounted, ref,watch} from 'vue'
import {useRouter,useRoute} from 'vue-router'
import DefaultAvatar from "@/assets/layouts/default-avatar.png"
import { useUserInfoStore } from "@/store/modules/pc"
import {storeToRefs} from 'pinia'
let userStore=useUserInfoStore()
let {noticeUnView}=storeToRefs(userStore)
let clubNoticeId=''
let detailRow=ref({
    content:'',
})
let route=useRoute()
let router=useRouter()
let getDetail=async()=>{
  let res=await noticeDetail({
    clubNoticeId:clubNoticeId
  }) 
  if(res){
     detailRow.value=res.data
  }
}
let view=async()=>{
   let res=await noticeView({
    clubNoticeId:clubNoticeId
  })
  if(res){
    getnoticeUnViewCount()
  }
}
let getnoticeUnViewCount=async()=>{
   let res=await noticeUnViewCount()
   if(res){
    // console.log(res)
    noticeUnView.value=res.data
   }
}
watch(()=>route,function(newval){
    clubNoticeId=route.query.id
    getDetail()
},{
    deep:true
})
onMounted(()=>{
    clubNoticeId=route.query.id
    getDetail()
    view()
})
</script>
<style lang="scss" scoped>
.notice-detail{
    background: #fff;
    border-radius: 4px;
    padding: 18px 24px 24px 24px;
    margin-bottom: 10px;
    .title{
        font-weight: bold;
        font-size: 20px;
         color: #2B2C33;
         cursor: pointer;
    }
}
.diary{
  margin-top: 20px;
    background: #fff;
  
    min-height: calc(100vh - 400px);
    .head{
        width: 52px;
        height: 52px;
        margin-right: 10px;
        border-radius: 200px;
    }
    .info{
        .teacher{
            font-size: 16px;
            color: #2B2C33;
            font-weight: bold;
            margin-right: 24px;
        }
        .stars{
            .star-box{
                margin-right: 4px;
            }
            img{
                width: 24px;
                height: auto;
            }
        }
    }
    .date{
        font-size: 14px;
        color: #94959C;
        margin-top: 4px;
    }
 
    .rich-open-html{
       margin-top: 8px;
            line-height: 24px;
            font-size: 16px;
            color: #2B2C33;
            
        }
  
}

</style>
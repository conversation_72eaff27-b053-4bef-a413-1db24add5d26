<template>
      <ul>
         <li class="module" v-for="item in tableData" :key="item.eventGroupValue">
                    <p class="module-title">{{item.eventGroupLabel}}</p>
                    <el-table :data="item.eventTypeSettings" style="width: 100%">
                        <el-table-column prop="eventTypeLabel" label="行为类型" align="center" />
                        <el-table-column prop="name" label="奖励步数" align="center">
                            <template #default='scope'>
                                 <el-input placeholder="请输入" v-model="scope.row.rewardPoints"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column prop="address" label="周期" align="center" >
                           <template #default='scope'>
                                 <el-select placeholder="请选择" v-model="scope.row.cycleTypeValue">
                                    <el-option :label='item.label' :value='item.value' v-for="item in frequency" :key="item.value"></el-option>
                                 </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="address" label="上限" align="center">
                             <template #default='scope'>
                                  <el-input-number v-model="scope.row.upperLimit" :min="1"  />
                             </template>
                        </el-table-column>
                        <el-table-column prop="triggeringConditionDesc" label="触发条件" align="center" />
                    </el-table>
                </li>
            </ul>
</template>
<script setup>
import {onActivated, onMounted, reactive, ref} from 'vue'
// import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {useRouter} from 'vue-router'
import {mileages} from '@/api/pc/mileage.js'
// import {storeToRefs} from 'pinia'
// let userInfoStore=useUserInfoStoreHook()
// let {personcenterCurrentInfo,userInfo}=storeToRefs(userInfoStore)
let router=useRouter()
let tableData=ref([])
let frequency=ref([{value:1,label:'每日'},{value:7,label:'每周'},{value:30,label:'每月'},])
let getMileages=async()=>{
    let res=await mileages()
    if(res){
        console.log(res)
        tableData.value=res.data.eventGroups
    } 
}
onMounted(()=>{
   getMileages()
})
</script>
<style lang='scss' scoped>
 .module{
    padding: 16px 18px;
    border-radius: 4px;
    background: #fff;
    margin-bottom: 10px;
    &:last-child{
        margin-bottom: none;
    }
    .module-title{
        background: linear-gradient( 270deg, #88C0FF 0%, #508CFF 100%);
        border-radius: 4px 4px 0px 0px;
        width: 100%;
        padding: 9px 0;
        font-size: 16px;
        color: #FFFFFF;
        text-align: center;
        font-weight: bold;
    }
    :deep(.el-table thead .cell){
        height: 40px;
        line-height: 40px;
    }
    :deep(.el-table thead .el-table__cell){
        padding: 0;
        background: #DFEAFF;
        font-size: 14px;
        color: #3E73D9;
        font-weight: bold;
        border-right: 2px solid #fff;
        &:last-child{
            border-right: none;
        }
    }
    :deep(.el-input__inner){
        height: 38px;
    }
    :deep(.el-select__wrapper){
        height: 38px;
    }
    :deep(.el-table tbody .cell){
        
        font-size: 14px;
        color: #2B2C33;
        line-height: 20px;
        white-space: pre-wrap;
        height: auto;
    } 
   }
</style>
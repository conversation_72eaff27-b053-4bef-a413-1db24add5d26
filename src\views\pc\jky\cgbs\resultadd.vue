<!-- 成果报送 -->
<template>
    <div class="result-add-main content-box">
        <div class="flex_between">
            <ul class="flex_start tab-ul">
                <li v-for="item in tab_list" :key="item.id" :class="current_tab === item.id? 'active-tab' : ''" @click="current_tab = item.id">{{item.title}} <span v-if="item.id != 3" class="red2" style="font-weight:400;">*</span> </li>
            </ul>
            <!-- <div class="flex_end">
                <el-button class="plain-blue-btn_solid btn_132_40" type="plain" @click="">暂存</el-button>
                <el-button class="primary-btn btn_132_40" type="primary" @click="submitClick">报送</el-button>
                <el-button class="primary-btn btn_132_40" type="primary" @click="submitClick">修改</el-button>
                <img class="result-icon" src="@/assets/pc/jky/unqualified.png" alt="">
                <img class="result-icon" src="@/assets/pc/jky/qualified.png" alt="">
                <img class="result-icon" src="@/assets/pc/jky/good.png" alt=""> 
            </div> -->
            <div class="flex_end">
                <el-button class="plain-blue-btn_solid btn_132_40" type="plain" @click="">专家指定</el-button>
                <el-button class="red_btn btn_132_40" type="plain" @click="">驳回</el-button>
                <el-button class="primary-btn btn_132_40" type="primary" @click="submitClick">上报</el-button>
                <el-button class="primary-btn btn_132_40" type="primary" @click="submitClick">评分</el-button>
            </div>
        </div>
        <div v-if="current_tab === 1">
            <p class="text_18_bold flex_center mb_20">基本信息 </p>
            <BaseInfo></BaseInfo>
            <p class="text_18_bold flex_center mb_20">研究成果</p>
            <p class="sub-title text_16_bold">文章发表</p>
            <el-table :data="article_list" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F9F9F9"}'>
                <el-table-column prop="reviewUserNames" label="篇名">
                    <template #default='scope'>
                        <el-input v-model="scope.row.reviewUserNames" class="input_40" placeholder="请输入"></el-input>
                    </template>
                </el-table-column>
                <el-table-column prop="reviewUserNames" label="发表刊名与出版单位">
                    <template #default='scope'>
                        <el-input v-model="scope.row.reviewUserNames" class="input_40" placeholder="请输入"></el-input>
                    </template>
                </el-table-column>
                <el-table-column prop="reviewUserNames" label="发表刊期与出版时间">
                    <template #default='scope'>
                        <el-date-picker v-model="scope.row.recordDate" class="my-date-picker_40" type="date" value-format="YYYY-MM-DD" placeholder="请选择日期"> </el-date-picker>
                    </template>
                </el-table-column> 
                <el-table-column label="操作" width="100">
                    <template #default='scope'>
                        <div class="flex_start" style="height:100%">
                            <el-button type="primary" link style="font-size:16px;color: #E72C4A;font-weight:400;" @click="deleteClick(scope.row)">删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="add-btn flex_center text_16_bold"><img src="@/assets/pc/jky/add.png" alt="">新增</div>

            <p class="sub-title text_16_bold">研究课及获奖</p>
            <el-table :data="article_list" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F9F9F9"}'>
                <el-table-column prop="reviewUserNames" label="研究课课题及获奖名称">
                    <template #default='scope'>
                        <el-input v-model="scope.row.reviewUserNames" class="input_40" placeholder="请输入"></el-input>
                    </template>
                </el-table-column>
                <el-table-column prop="reviewUserNames" label="上课地点及主办单位">
                    <template #default='scope'>
                        <el-input v-model="scope.row.reviewUserNames" class="input_40" placeholder="请输入"></el-input>
                    </template>
                </el-table-column>
                <el-table-column prop="reviewUserNames" label="上课时间">
                    <template #default='scope'>
                        <el-date-picker v-model="scope.row.recordDate" class="my-date-picker_40" type="date" value-format="YYYY-MM-DD" placeholder="请选择日期"> </el-date-picker>
                    </template>
                </el-table-column> 
                <el-table-column label="操作" width="100">
                    <template #default='scope'>
                        <div class="flex_start" style="height:100%">
                            <el-button type="primary" link style="font-size:16px;color: #E72C4A;font-weight:400;" @click="deleteClick(scope.row)">删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table> 
            <div class="add-btn flex_center text_16_bold"><img src="@/assets/pc/jky/add.png" alt="">新增</div>

            <p class="text_18_bold flex_center mb_20">研究成果简述</p>
            <WangEditor :html='add_form.questionContent' @emitHtml='emitContentHtml'  />
            <div class="result-content text_14">
                现实需求层面
                需基于具体社会现象或行业发展困境，揭示研究问题的紧迫性23。如民间委托理财纠纷中保底条款引发的市场秩序问题，或教育领域资源分配不均等现实矛盾25。
                理论缺口层面
                应指出现有研究的局限性，如汉语哲学研究揭示的西方哲学范式与中国传统思想的不适配性1，或英语阅读教学理论对课外阅读机制研究的不足5。
            </div>
        </div>
        <div v-else-if="current_tab === 2">
            <p class="sub-title text_16_bold">成果报告</p>
            <FileUpload tip="注意：文件名称为课题名称"></FileUpload>
        </div>
        <div v-else>
            <p class="sub-title text_16_bold">文章发表</p>
            <FileUpload tip="注意：文件名称为刊物名称+文章标题"></FileUpload>
            <p class="sub-title text_16_bold mt_24">文章获奖</p>
            <FileUpload tip="注意：文件名称为获奖名称等级+文章标题"></FileUpload>
            <p class="sub-title text_16_bold mt_24">公开课</p>
            <FileUpload tip="注意：文件名称为教案名称"></FileUpload>
            <p class="sub-title text_16_bold mt_24">讲座（汇报）</p>
            <FileUpload tip="注意：文件名称为讲座名称"></FileUpload> 
        </div>
    </div>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import BaseInfo from '@/views/pc/component/jky/BaseInfo.vue'
    import WangEditor from '@/views/pc/component/wangeditor.vue'
    import FileUpload from '@/views/pc/component/jky/Upload.vue'

    const tab_list = ref([{id:1,title:'鉴定表'},{id:2,title:'成果报告'},{id:3,title:'附件'}])
    const current_tab = ref(2)
    const article_list = ref([{}])
    const add_form = ref({})

    onMounted(() => {
    })
</script>
<style lang="scss" scoped>
.result-add-main{background: #fff;padding:24px;margin:24px auto;border-radius: 12px;
    .tab-ul{ border-bottom: 1px solid #E2E3E6; margin-bottom:20px; display:inline-flex;
        li{ margin-right: 32px; color: #94959C; padding: 6px 0; cursor: pointer; }
        .active-tab{ font-weight: bold; color: #508CFF; border-bottom: 2px solid #508CFF; }
    }
    .sub-title{margin-bottom: 14px;position: relative;padding-left: 8px;}
    .sub-title:before{content: "";display: inline-block;width: 2px;height: 16px;border-radius: 1px;margin-right: 10px;background: #508CFF;position: absolute;top: 56%;left:0;transform: translateY(-50%);}
    .add-btn{background: #F0F5FF;border-radius: 12px;padding: 8px 0;color: #508CFF;margin:16px 0; 
        img{width: 16px;margin-right: 8px;} 
    }
    :deep(.el-table--fit){border-radius: 12px;border: 1px solid #E6E6E6;}
    .result-content{padding: 16px;background: #F2F5F9;border-radius: 8px;}
    .result-icon{width: 104px;}
}
</style>
import { request } from "@/utils/service"

export function taskList(data) {
  return request({
    url: "/jky/task-master",
    method: "get",
    params:data
  })
}

export function taskAdd(data) {
    return request({
      url: "/jky/task-master",
      method: "post",
      data
    })
}

export function taskEdit(data,taskMasterId) {
  return request({
    url: `/jky/task-master/${taskMasterId}`,
    method: "put",
    data
  })
}

export function taskDel(taskMasterId) {
  return request({
    url: `/jky/task-master/${taskMasterId}`,
    method: "delete",
  })
}

export function taskDetail(taskMasterId) {
  return request({
    url: `/jky/task-master/${taskMasterId}`,
    method: "get",
  })
}

export function schList(params) {
    return request({
      url: `/jky/task-master/${params.taskMasterId}/task-school`,
      method: "get",
      params
    })
}

export function expertSelect(data) {
    return request({
      url: `/jky/expert-group`,
      method: "get",
      data
    })
}

export function expertUser(data) {
    return request({
      url: `/jky/expert-group/${data.expertGroupId}/user`,
      method: "get",
      data
    })
}

export function reviewSection(taskMasterId) {
  return request({
    url: `/jky/task-master/${taskMasterId}/review-section`,
    method: "get",
  })
}

export function review(data) {
  return request({
    url: `/jky/task-master/${data.taskMasterId}/review`,
    method: "post",
    data
  })
}

export function reviewTemplate(taskMasterId) {
  return request({
    url: `/jky/task-master/${taskMasterId}/task-review-template`,
    method: "get",
  })
}

export function result(taskMasterId,params) {
  return request({
    url: `/jky/task-master/${taskMasterId}/task-school/result`,
    method: "get",
    params
  })
}



// 学校接受任务
export function schTask(params) {
  return request({
    url: `/jky/task-school`,
    method: "get",
    params
  })
}
export function schTaskExamine(taskSchoolId) {
  return request({
    url: `/jky/task-school/${taskSchoolId}`,
    method: "get",
  })
}

export function userOption() {
  return request({
    url: `/jky/task-school/user/option`,
    method: "get",
  })
}

export function schAllocationDetail(taskSchoolId) {
  return request({
    url: `/jky/task-school/${taskSchoolId}/review`,
    method: "get",
  })
}

export function schAllocate(taskSchoolId,data) {
  return request({
    url: `/jky/task-school/${taskSchoolId}/review`,
    method: "post",
    data
  })
}

// 专家评审
// 学校专家
export function reviewSchList(params) {
  return request({
    url: `/jky/task-school/review/me`,
    method: "get",
    params
  })
}
export function reviewSchDetail(params) {
  return request({
    url: `/jky/task-school/review/section`,
    method: "get",
    params
  })
}
export function reviewSchAdd(params,data) {
  return request({
    url: `/jky/task-school/review/section?taskMasterId=${params.taskMasterId}&schoolId=${params.schoolId}&reviewSectionId=${params.reviewSectionId}&schoolReviewUserId=${params.schoolReviewUserId}&fileId=${params.fileId}`,
    method: "post",
    data
  })
}

// 区专家
export function reviewAreaList(params) {
  return request({
    url: `/jky/task-master/review/me`,
    method: "get",
    params
  })
}
export function reviewAreaDetail(params) {
  return request({
    url: `/jky/task-master/review/section`,
    method: "get",
    params
  })
}
<template>
    <div class="judges-main">
        <div class="flex_between">
            <div class="flex_start">
                <el-select class="select_40 mr_24" placeholder="全部组别" @change="changeExtend">
                    <el-option v-for="item in expert_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
                <el-select class="select_40 mr_24" placeholder="所在单位" @change="changeExtend">
                    <el-option v-for="item in expert_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
                <el-select class="select_40" placeholder="申报类型" @change="changeExtend">
                    <el-option v-for="item in expert_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
            </div>
            <el-button type="primary" class="green_btn btn_132_40">重新分配</el-button>
        </div>
        <el-table :data="material_list" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F9F9F9"}'>
            <el-table-column type="index" label="序号" width="80"  />
            <el-table-column prop="taskTitle" label="申报编码" min-width="90" show-overflow-tooltip></el-table-column>
            <el-table-column prop="taskTitle" label="学段" show-overflow-tooltip></el-table-column>
            <el-table-column prop="createTime" label="学科" show-overflow-tooltip />
            <el-table-column prop="deadline" label="课题名称" min-width="90" show-overflow-tooltip />
            <el-table-column prop="deadline" label="所在单位" min-width="90" show-overflow-tooltip />
            <el-table-column prop="deadline" label="支持人" show-overflow-tooltip />
            <el-table-column prop="deadline" label="是否青年专项" show-overflow-tooltip min-width="120" />
            <el-table-column label="评分小组" fixed="right" width="180">
                <template #default='scope'>
                    <el-select v-model="scope.row.expertGroup" class="select_40_2" placeholder="请选择" @change="changeExtend" style="width:100%;">
                        <el-option v-for="item in expert_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination background layout="total,prev, pager, next" :total="total" class="mt-4" :current-page='search_form.pageNum' @current-change='currentChange' />
        <div class="flex_center">
            <el-button class="plain-btn btn_96_48" type="plain" @click="emit('close')">取消</el-button>
            <el-button class="primary-btn btn_96_48" type="primary" @click="submitClick">确认</el-button>
        </div>
    </div> 
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    const formObj = function(obj = {}){
        this.name = obj.name || ''
        this.pageNum = obj.pageNum || 1
        this.pageSize = obj.pageSize || 10
    }
    const search_form = ref(new formObj({}))
    const total = ref(0)
    const material_list = ref([{}])
    onMounted(() => {
    })
</script>
<style lang="scss" scoped>
.judges-main{}
</style>

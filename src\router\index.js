import { createRouter } from "vue-router"
import { history, flatMultiLevelRoutes } from "./helper"
import routeSettings from "@/config/route"

import { useUserInfoStore } from "@/store/modules/pc"
import {storeToRefs} from 'pinia'
import {ElLoading} from 'element-plus'

const Layout = () => import("@/layout/index.vue")

const MAX_MOBILE_WIDTH = 750
const _isMobile = () => {
  const rect = document.body.getBoundingClientRect()
  return rect.width - 1 < MAX_MOBILE_WIDTH
}


/**
 * 常驻路由
 * 除了 redirect/403/404/login 等隐藏页面，其他页面建议设置 Name 属性
 */
export const constantRoutes = [
  {
    path: "/publicwisdom",
    name:'publicwisdom',
    component: () => import("@/views/pc/publicwisdom.vue"),
  },
  {
    path: "/wxland",
    name:'wxland-login',
    component: () => import("@/views/mobile/wxland.vue"),
    meta: {
      title: "微信登录",
    },
  },
  {
    path: "/fullscreen",
    component: () => import("@/views/pc/bk/fullscreen.vue"),
    name: "bk-package-screen",
    meta: {
      title: "备课中心__课例包",
      keepAlive:true,
      model:2
    },
  },
  {
    path: "/helpteach",
    component: () => import("@/views/pc/ai/helpTeach/index.vue"),
    name: "ai-helpteach",
    meta: {
      title: "备课中心__AI助教",
      keepAlive:true,
    
    },
  },
  {
    path: "/bkzs",
    component: () => import("@/views/pc/ai/bkzs/index.vue"),
    name: "ai-bkzs",
    meta: {
      title: "备课中心__备课助手",
      keepAlive:true,
    
    },
  },
  {
    path: "/redirect",
    component: Layout,
    meta: {
      hidden: true
    },
    children: [
      {
        path: ":path(.*)",
        component: () => import("@/views/redirect/index.vue")
      }
    ]
  },
  {
    path: "/403",
    component: () => import("@/views/error-page/403.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: "/404",
    component: () => import("@/views/error-page/404.vue"),
    meta: {
      hidden: true
    },
    alias: "/:pathMatch(.*)*"
  },
  {
    path: "/login",
    component: () => import("@/views/login/index.vue"),
    meta: {
      hidden: true
    }
  },
  // 大首页

 

  {
     path:'/',
     component: Layout,
     redirect: "/index",
     children:[
      {
        path:'/index',
        component: () => import("@/views/pc/index.vue"), 
        name:'index',
        meta:{
          title:'首页'
        }
      }
     ]
  },
  // {
  //   path: "/",
  //   component: Layout,
  //   redirect: "/actResearchAssociation/home",
  //   children: [
  //     {
  //       path: "/actResearchAssociation/home",
  //       component: () => import("@/views/pc/home/<USER>"),
  //       name: "Home",
  //       meta: {
  //         title: "首页",
  //         model:0
  //       }
  //     }
  //   ]
  // },
  {

    path: "/actResearchAssociation",
    component: Layout,
    redirect:'/actResearchAssociation/personcenter',
    meta: {
      title: "行知研习社",
    },
    children: [
      {
        path: "/actResearchAssociation/home",
        component: () => import("@/views/pc/home/<USER>"),
        name: "Home",
        meta: {
          title: "首页",
          model:0
        }
      },
      {
        path: "/actResearchAssociation/statistic",
        component: () => import("@/views/pc/act-research-association/statistic/index.vue"),
        name: "ActResearchAssociation-statistic",
        meta: {
          title: "天天向上学习平台_数据统计",
          model:1,
          keepAlive:false
        },
        children:[
            {
              path: "/actResearchAssociation/statistic/joiner",
              component: () => import("@/views/pc/act-research-association/statistic/joiner.vue"),
              name: "ActResearchAssociation-statistic-joiner",
              meta: {
                title: "天天向上学习平台_社员情况统计",
                model:4,
                keepAlive:false
              },
            },
            {
              path: "/actResearchAssociation/statistic/diary",
              component: () => import("@/views/pc/act-research-association/statistic/diary.vue"),
              name: "ActResearchAssociation-statistic-diary",
              meta: {
                title: "天天向上学习平台_日记情况统计",
                model:4,
                keepAlive:false
              },
            },
            {
              path: "/actResearchAssociation/statistic/mileage",
              component: () => import("@/views/pc/act-research-association/statistic/mileage.vue"),
              name: "ActResearchAssociation-statistic-mileage",
              meta: {
                title: "天天向上学习平台_日记情况统计",
                model:4,
                keepAlive:false
              },
            },
            {
              path: "/actResearchAssociation/statistic/mileagetutor",
              component: () => import("@/views/pc/act-research-association/statistic/mileageTutor.vue"),
              name: "ActResearchAssociation-statistic-mileagetutor",
              meta: {
                title: "天天向上学习平台_日记情况统计",
                model:4,
                keepAlive:false
              },
            },
        ]
      },
    
      {
        path: "/actResearchAssociation/personcenter",
        component: () => import("@/views/pc/act-research-association/personcenter/index.vue"),
        name: "ActResearchAssociation",
        meta: {
          title: "行知研习社",
          model:1,
          keepAlive:true
        },
      },
      {
        path: "/actResearchAssociation/personcenter/upload",
        component: () => import("@/views/pc/act-research-association/personcenter/upload.vue"),
        name: "ActResearchAssociation-upload",
        meta: {
          title: "行知研习社__上传日记",
          model:1
        },
      },
      {
        path: "/actResearchAssociation/personcenter/detail",
        component: () => import("@/views/pc/act-research-association/personcenter/diaryDetail.vue"),
        name: "ActResearchAssociation-detail",
        meta: {
          title: "行知研习社__日记详情",
          model:1
        },
      },
      {
        path: "/actResearchAssociation/personcenter/mileage",
        component: () => import("@/views/pc/act-research-association/personcenter/index.vue"),
        name: "ActResearchAssociation-personcenter-mileage",
        meta: {
          title: "行知研习社__行知里程",
          model:1
        },
        redirect:'/actResearchAssociation/personcenter/mileage/index',
        children:[
          {
            path: "/actResearchAssociation/personcenter/mileage/index",
            component: () => import("@/views/pc/act-research-association/personcenter/mileage/index.vue"),
            name: "ActResearchAssociation-personcenter-mileage-index",
            meta: {
              title: "行知研习社__行知里程",
              model:1
            },
            redirect:'/actResearchAssociation/personcenter/mileage/my',
            children:[
              {
                path: "/actResearchAssociation/personcenter/mileage/my",
                component: () => import("@/views/pc/act-research-association/personcenter/mileage/my.vue"),
                name: "ActResearchAssociation-personcenter-mileage-my",
                meta: {
                  title: "行知研习社__行知里程",
                  model:1
                },
              },
              {
                path: "/actResearchAssociation/personcenter/mileage/week",
                component: () => import("@/views/pc/act-research-association/personcenter/mileage/week.vue"),
                name: "ActResearchAssociation-personcenter-mileage-week",
                meta: {
                  title: "行知研习社__里程周报",
                  model:1
                },
              },
              {
                path: "/actResearchAssociation/personcenter/mileage/month",
                component: () => import("@/views/pc/act-research-association/personcenter/mileage/month.vue"),
                name: "ActResearchAssociation-personcenter-mileage-month",
                meta: {
                  title: "行知研习社__里程月报",
                  model:1
                },
              },
              {
                path: "/actResearchAssociation/personcenter/mileage/personmy",
                component: () => import("@/views/pc/act-research-association/personcenter/mileage/personmy.vue"),
                name: "ActResearchAssociation-personcenter-mileage-personmy",
                meta: {
                  title: "行知研习社__我的里程",
                  model:1
                },
              }
            ]
          }
        ]
      },
      {
        path: "/actResearchAssociation/square/index",
        component: () => import("@/views/pc/act-research-association/square/index.vue"),
        name: "ActResearchAssociation-square",
        meta: {
          title: "行知研习社__日记广场",
          keepAlive:true,
          model:2
        },
      },
      {
        path: "/actResearchAssociation/square/detail",
        component: () => import("@/views/pc/act-research-association/square/detail.vue"),
        name: "ActResearchAssociation-square-detail",
        meta: {
          title: "行知研习社__日记广场",
          keepAlive:false,
          model:2
        },
      },
      {
        path: "/actResearchAssociation/square/persondiary",
        component: () => import("@/views/pc/act-research-association/square/persondiary.vue"),
        name: "ActResearchAssociation-square-persondiary",
        meta: {
          title: "行知研习社__日记广场",
          keepAlive:false,
          model:2
        },
      },
      {
        path: "/actResearchAssociation/square/user",
        component: () => import("@/views/pc/act-research-association/square/index.vue"),
        name: "ActResearchAssociation-square-userRecord",
        meta: {
          title: "行知研习社__个人日记广场",
          keepAlive:true,
          model:2
        },
      },
      {
        path: "/actResearchAssociation/theme",
        component: () => import("@/views/pc/act-research-association/theme/index.vue"),
        name: "ActResearchAssociation-theme-index",
        meta: {
          title: "行知研习社__主题交流",
          keepAlive:true,
          model:3
        },
      },
      {
        path: "/actResearchAssociation/theme/detail",
        component: () => import("@/views/pc/act-research-association/theme/detail.vue"),
        name: "ActResearchAssociation-theme-detail",
        meta: {
          title: "行知研习社__主题交流",
          keepAlive:false,
          model:3
        },
      },
      {
        path: "/actResearchAssociation/theme/upload",
        component: () => import("@/views/pc/act-research-association/theme/upload.vue"),
        name: "ActResearchAssociation-theme-upload",
        meta: {
          title: "行知研习社__主题交流",
          keepAlive:false,
          model:3
        },
      },
      {
        path: "/actResearchAssociation/theme/edit",
        component: () => import("@/views/pc/act-research-association/theme/upload.vue"),
        name: "ActResearchAssociation-theme-edit",
        meta: {
          title: "行知研习社__主题交流",
          keepAlive:false,
          model:3
        },
      },
      {
        path: "/actResearchAssociation/notice",
        component: () => import("@/views/pc/act-research-association/notice/index.vue"),
        name: "ActResearchAssociation-notice-index",
        meta: {
          title: "行知研习社__活动通知",
          keepAlive:true,
          model:5
        },
      },
      {
        path: "/actResearchAssociation/notice/detail",
        component: () => import("@/views/pc/act-research-association/notice/detail.vue"),
        name: "ActResearchAssociation-notice-detail",
        meta: {
          title: "行知研习社__活动通知",
          keepAlive:false,
          model:4
        },
      },
      {
        path: "/actResearchAssociation/notice/upload",
        component: () => import("@/views/pc/act-research-association/notice/upload.vue"),
        name: "ActResearchAssociation-notice-upload",
        meta: {
          title: "行知研习社__活动通知",
          keepAlive:false,
          model:5
        },
      },
     
      {
        path: "/actResearchAssociation/mileage",
        component: () => import("@/views/pc/act-research-association/mileage/index.vue"),
        name: "ActResearchAssociation-mileage",
        meta: {
          title: "行知研习社__里程管理",
          keepAlive:false,
          model:6
        },
        redirect:'/actResearchAssociation/mileage/ruleset',
        children:[
          {
            path: "/actResearchAssociation/mileage/ruleset",
            component: () => import("@/views/pc/act-research-association/mileage/ruleset.vue"),
            name: "ActResearchAssociation-mileage-ruleset",
            meta: {
              title: "行知研习社__规则设置",
              keepAlive:false,
              model:6
            },
          },
          {
            path: "/actResearchAssociation/mileage/inline",
            component: () => import("@/views/pc/act-research-association/mileage/inline.vue"),
            name: "ActResearchAssociation-mileage-inline",
            meta: {
              title: "行知研习社__线下录入",
              keepAlive:false,
              model:6
            },
            children:[
              {
                path: "/actResearchAssociation/mileage/inline/ipt",
                component: () => import("@/views/pc/act-research-association/mileage/ipt.vue"),
                name: "ActResearchAssociation-mileage-inline-ipt",
                meta: {
                  title: "行知研习社__里程录入",
                  keepAlive:false,
                  model:6
                },
              },
              {
                path: "/actResearchAssociation/mileage/inline/iptrecord",
                component: () => import("@/views/pc/act-research-association/mileage/iptrecord.vue"),
                name: "ActResearchAssociation-mileage-inline-iptrecord",
                meta: {
                  title: "行知研习社__录入记录",
                  keepAlive:false,
                  model:6
                },
              }
            ]
          },
        ]
      },
    ]
  },
  {
    path: "/resource/question",
    component: Layout,
    redirect:'/resource/question/index',
    meta: {
      title: "行知研习社",
    },
    children:[
      {
        path: "/resource/question/index",
        component: () => import("@/views/pc/resource/questionBank/index.vue"),
        name: "question",
        meta: {
          title: "天天向上学习平台_题库",
          model:2,
          keepAlive:true
        },
      },
      {
        path: "/resource/question/upload",
        component: () => import("@/views/pc/resource/questionBank/upload.vue"),
        name: "question-upload",
        meta: {
          title: "天天向上学习平台_题库",
          model:2,
          keepAlive:false
        },
      },
      {
        path: "/resource/question/questionset",
        component: () => import("@/views/pc/resource/questionBank/questionset.vue"),
        name: "question-questionset",
        meta: {
          title: "天天向上学习平台_题库",
          model:2,
          keepAlive:false
        },
      },
      {
        path: "/resource/question/set",
        component: () => import("@/views/pc/resource/questionBank/set.vue"),
        name: "question-set",
        meta: {
          title: "天天向上学习平台_题库",
          model:2,
          keepAlive:true
        },
      },
      {
        path: "/resource/question/preview",
        component: () => import("@/views/pc/resource/questionBank/preview.vue"),
        name: "question-preview",
        meta: {
          title: "天天向上学习平台_题库",
          model:2,
          keepAlive:false
        },
      },
      {
        path: "/resource/question/preview",
        component: () => import("@/views/pc/resource/questionBank/preview.vue"),
        name: "question-preview",
        meta: {
          title: "天天向上学习平台_题库",
          model:2,
          keepAlive:false
        },
      },
      {
        path: "/resource/teach/index",
        component: () => import("@/views/pc/resource/teach/index.vue"),
        name: "teach",
        meta: {
          title: "天天向上学习平台_资源中心",
          model:1,
          keepAlive:true
        },
      },
      {
        path: "/resource/teach/upload",
        component: () => import("@/views/pc/resource/teach/upload.vue"),
        name: "teach-upload",
        meta: {
          title: "天天向上学习平台_资源中心",
          model:1,
          keepAlive:false
        },
      },
      {
        path: "/resource/teach/preview",
        component: () => import("@/views/pc/resource/teach/preview.vue"),
        name: "teach-preview",
        meta: {
          title: "天天向上学习平台_资源中心",
          model:1,
          keepAlive:false
        },
      },
      {
        path: "/resource/teach/resourceset",
        component: () => import("@/views/pc/resource/teach/resourceset.vue"),
        name: "teach-resourceset",
        meta: {
          title: "天天向上学习平台_资源中心",
          model:1,
          keepAlive:false
        },
      },
      {
        path: "/resource/wisdom/index",
        component: () => import("@/views/pc/resource/wisdom/index.vue"),
        name: "wisdom-index",
        meta: {
          title: "天天向上学习平台_智慧课堂",
          model:3,
          keepAlive:true
        },
      },
      {
        path: "/resource/wisdom/detail",
        component: () => import("@/views/pc/resource/wisdom/preview.vue"),
        name: "wisdom-detail",
        meta: {
          title: "天天向上学习平台_智慧课堂",
          model:3,
          keepAlive:false
        },
      },
      {
        path: "/resource/wisdom/edit",
        component: () => import("@/views/pc/resource/wisdom/detail.vue"),
        name: "wisdom-update",
        meta: {
          title: "天天向上学习平台_智慧课堂",
          model:3,
          keepAlive:false
        },
      },
    ]
  },
  {
    path:'/bk',
    component: Layout,
    redirect:'/bk/res/index',
    meta: {
      title: "行知研习社",
    },
    children:[
      {
        path: "/bk/res/index",
        component: () => import("@/views/pc/bk/index.vue"),
        name: "bk-res-index",
        meta: {
          title: "备课中心__备课资源",
          keepAlive:true,
          model:1
        },
      },
      {
        path: "/bk/res/group",
        component: () => import("@/views/pc/bk/group.vue"),
        name: "bk-res-group",
        meta: {
          title: "备课中心__备课组管理",
          keepAlive:false,
          model:1
        },
      },
      {
        path: "/bk/res/upload",
        component: () => import("@/views/pc/resource/teach/upload.vue"),
        name: "bk-res-upload",
        meta: {
          title: "备课中心__创建课件资源",
          keepAlive:false,
          model:1
        },
      },
      {
        path: "/bk/res/edit",
        component: () => import("@/views/pc/resource/teach/upload.vue"),
        name: "bk-res-edit",
        meta: {
          title: "备课中心__编辑课件资源",
          keepAlive:false,
          model:1
        },
      },
      {
        path: "/bk/res/preview",
        component: () => import("@/views/pc/resource/teach/preview.vue"),
        name: "bk-res-preview",
        meta: {
          title: "备课中心__课件资源详情",
          keepAlive:false,
          model:1
        },
      },
      {
        path: "/bk/res/add",
        component: () => import("@/views/pc/bk/addRes.vue"),
        name: "bk-res-add",
        meta: {
          title: "备课中心__创建课件资源",
          keepAlive:false,
          model:1
        },
      },
      {
        path: "/bk/res/editres",
        component: () => import("@/views/pc/bk/addRes.vue"),
        name: "bk-res-editres",
        meta: {
          title: "备课中心__编辑课件资源",
          keepAlive:false,
          model:1
        },
      },
      {
        path: "/bk/package/index",
        component: () => import("@/views/pc/bk/package.vue"),
        name: "bk-package-index",
        meta: {
          title: "备课中心__课例包",
          keepAlive:true,
          model:2
        },
      },
      {
        path: "/bk/package/detail",
        component: () => import("@/views/pc/bk/packagedetail.vue"),
        name: "bk-package-detail",
        meta: {
          title: "备课中心__课例包",
          keepAlive:false,
          model:2
        },
      },
      {
        path: "/bk/package/groupbk",
        component: () => import("@/views/pc/bk/groupbk.vue"),
        name: "bk-package-groupbk",
        meta: {
          title: "备课中心__集体备课",
          keepAlive:false,
          model:3
        },
      },
    ]
  },
  {
    path:'/jky',
    component: Layout,
    redirect:'/jky/lxpj/index',
    meta: {
      title: "教科研",
    },
    children:[
      {
          path: "/jky/lxpj/index",
          component: () => import("@/views/pc/jky/lxpj/index.vue"),
          name: "jky-lxpj-index",
          meta: {
            title: "教科研_立项评鉴",
            keepAlive:false,
            model:1
          },
      },
      {
        path: "/jky/lxpj/detail/:id",
        component: () => import("@/views/pc/jky/lxpj/detail.vue"),
        name: "jky-lxpj-detail",
        meta: {
          title: "教科研_立项评鉴_详情",
          keepAlive:false,
          model:1
        },
      },
      {
        path: "/jky/lxpj/schtask/:id",
        component: () => import("@/views/pc/jky/lxpj/schtask.vue"),
        name: "jky-lxpj-schtask",
        meta: {
          title: "教科研_立项评鉴_详情",
          keepAlive:false,
          model:1
        },
      },
      {
        path: "/jky/lxpj/judges/:id",
        component: () => import("@/views/pc/jky/lxpj/judges.vue"),
        name: "jky-lxpj-judges",
        meta: {
          title: "教科研_立项评鉴_评委名单 ",
          keepAlive:false,
          model:1
        },
      },
      {
        path: "/jky/lxpj/application/:id",
        component: () => import("@/views/pc/jky/lxpj/application.vue"),
        name: "jky-lxpj-application",
        meta: {
          title: "教科研_立项评鉴_申请表",
          keepAlive:false,
          model:1
        },
      },
      {
        path: "/jky/lxpj/check/:id",
        component: () => import("@/views/pc/jky/lxpj/check.vue"),
        name: "jky-lxpj-check",
        meta: {
          title: "教科研_立项评鉴_审核",
          keepAlive:false,
          model:1
        },
      },
      {
        path: "/jky/lxsq/index",
        component: () => import("@/views/pc/jky/lxsq/index.vue"),
        name: "jky-lxsq-index",
        meta: {
          title: "教科研_立项申请",
          keepAlive:false,
          model:2
        },
      },
      {
        path: "/jky/lxsq/application/:id",
        component: () => import("@/views/pc/jky/lxpj/application.vue"),
        name: "jky-lxsq-application",
        meta: {
          title: "教科研_立项申请_申请表",
          keepAlive:false,
          model:2
        },
      },
      {
        path: "/jky/lxcs/index",
        component: () => import("@/views/pc/jky/lxcs/index.vue"),
        name: "jky-lxcs-index",
        meta: {
          title: "教科研_立项初审",
          keepAlive:false,
          model:4
        },
      },
      {
        path: "/jky/lxcs/detail/:id",
        component: () => import("@/views/pc/jky/lxcs/detail.vue"),
        name: "jky-lxcs-detail",
        meta: {
          title: "教科研_立项初审_详情",
          keepAlive:false,
          model:4
        },
      },
      {
        path: "/jky/lxcs/application/:id",
        component: () => import("@/views/pc/jky/lxpj/application.vue"),
        name: "jky-lxcs-application",
        meta: {
          title: "教科研_立项初审_详情",
          keepAlive:false,
          model:4
        },
      },
      {
        path: "/jky/lxcp/index",
        component: () => import("@/views/pc/jky/lxcp/index.vue"),
        name: "jky-lxcp-index",
        meta: {
          title: "教科研_立项初评",
          keepAlive:false,
          model:5
        },
      },
      {
        path: "/jky/lxcp/detail/:id",
        component: () => import("@/views/pc/jky/lxcp/detail.vue"),
        name: "jky-lxcp-detail",
        meta: {
          title: "教科研_立项初评_详情",
          keepAlive:false,
          model:5
        },
      },
      {
        path: "/jky/lxcp/detailExpert/:id",
        component: () => import("@/views/pc/jky/lxcp/detailExpert.vue"),
        name: "jky-lxcp-detailExpert",
        meta: {
          title: "教科研_立项初评_详情",
          keepAlive:false,
          model:5
        },
      },
      {
        path: "/jky/lxcp/application/:id",
        component: () => import("@/views/pc/jky/lxpj/application.vue"),
        name: "jky-lxcp-application",
        meta: {
          title: "教科研_立项初评_详情",
          keepAlive:false,
          model:5
        },
      },
      {
        path: "/jky/cgpj/index",
        component: () => import("@/views/pc/jky/cgpj/index.vue"),
        name: "jky-cgpj-index",
        meta: {
          title: "教科研_成果评鉴",
          keepAlive:false,
          model:3
        },
      },
      {
        path: "/jky/cgpj/detail/:id",
        component: () => import("@/views/pc/jky/cgpj/detail.vue"),
        name: "jky-cgpj-detail",
        meta: {
          title: "教科研_成果评鉴详情",
          keepAlive:false,
          model:3
        },
      },
      {
        path: "/jky/cgpj/schtask/:id",
        component: () => import("@/views/pc/jky/cgpj/schtask.vue"),
        name: "jky-cgpj-schtask",
        meta: {
          title: "教科研_成果评鉴详情",
          keepAlive:false,
          model:3
        },
      },
      {
        path: "/jky/cgpj/judgeslist/:id",
        component: () => import("@/views/pc/jky/cgpj/judgeslist.vue"),
        name: "jky-cgpj-judgeslist",
        meta: {
          title: "教科研_成果评鉴_评委名单",
          keepAlive:false,
          model:3
        },
      },
      {
        path: "/jky/cgbs/index",
        component: () => import("@/views/pc/jky/cgbs/index.vue"),
        name: "jky-cgbs-index",
        meta: {
          title: "教科研_成果报送",
          keepAlive:false,
          model:7
        },
      },
      {
        path: "/jky/cgbs/resultadd/:id",
        component: () => import("@/views/pc/jky/cgbs/resultadd.vue"),
        name: "jky-cgbs-resultadd",
        meta: {
          title: "教科研_成果报送",
          keepAlive:false,
          model:7
        },
      },
      {
        path: "/jky/cgcp/index",
        component: () => import("@/views/pc/jky/cgcp/index.vue"),
        name: "jky-cgcp-index",
        meta: {
          title: "教科研_成果初评",
          keepAlive:false,
          model:6
        },
      },
      {
        path: "/jky/cgcp/bslist/:id",
        component: () => import("@/views/pc/jky/cgcp/bslist.vue"),
        name: "jky-cgcp-bslist",
        meta: {
          title: "教科研_成果初评_报送列表",
          keepAlive:false,
          model:6
        },
      },
      {
        path: "/jky/cgcp/resultadd/:id",
        component: () => import("@/views/pc/jky/cgbs/resultadd.vue"),
        name: "jky-cgcp-resultadd",
        meta: {
          title: "教科研_成果初评_报送材料",
          keepAlive:false,
          model:6
        },
      },
    ]
    // children:[
    //     {
    //         path: "/jky/publish/index",
    //         component: () => import("@/views/pc/jky/publish/index.vue"),
    //         name: "jky-publish-index",
    //         meta: {
    //           title: "教科研_任务发布",
    //           keepAlive:false,
    //           model:1
    //         },
    //     },
    //     {
    //         path: "/jky/publish/detail/:id",
    //         component: () => import("@/views/pc/jky/publish/detail.vue"),
    //         name: "jky-publish-detail",
    //         meta: {
    //           title: "教科研_任务发布_详情",
    //           keepAlive:false,
    //           model:1
    //         },
    //     },
    //     {
    //         path: "/jky/accept/index",
    //         component: () => import("@/views/pc/jky/accept/index.vue"),
    //         name: "jky-accept-index",
    //         meta: {
    //           title: "教科研_学校接受任务", 
    //           keepAlive:false,
    //           model:2
    //         },
    //     },
    //     {
    //         path: "/jky/review/index",
    //         component: () => import("@/views/pc/jky/review/index.vue"),
    //         name: "jky-review-index",
    //         meta: {
    //           title: "教科研_专家评审", 
    //           keepAlive:false,
    //           model:3
    //         },
    //     },
    //     {
    //       path: "/jky/review/area",
    //       component: () => import("@/views/pc/jky/review/index.vue"),
    //       name: "jky-review-area",
    //       meta: {
    //         title: "教科研_专家评审", 
    //         keepAlive:false,
    //         model:3
    //       },
    //     },
    //     {
    //       path: "/jky/review/sch",
    //       component: () => import("@/views/pc/jky/review/index.vue"),
    //       name: "jky-review-index",
    //       meta: {
    //         title: "教科研_专家评审", 
    //         keepAlive:false,
    //         model:4
    //       },
    //     },
    //     {
    //         path: "/jky/review/area/detail/:taskMasterId/:reviewUserId/:schoolId",
    //         component: () => import("@/views/pc/jky/review/areaDetail.vue"),
    //         name: "jky-review-area-detail",
    //         meta: {
    //           title: "教科研_专家评审_详情", 
    //           keepAlive:false,
    //           model:3
    //         },
    //     },
    //     {
    //       path: "/jky/review/sch/detail/:taskMasterId/:schoolReviewUserId/:schoolId",
    //       component: () => import("@/views/pc/jky/review/detail.vue"),
    //       name: "jky-review-sch-detail",
    //       meta: {
    //         title: "教科研_专家评审_详情", 
    //         keepAlive:false,
    //         model:4
    //       },
    //   },
    // ]
  },
  {
    path:'/msjt',
    component: Layout,
    redirect:'/msjt/live/index',
    meta: {
      title: "教科研",
    },
    children:[
        {
            path: "/msjt/live/index",
            component: () => import("@/views/pc/msjt/live/index.vue"),
            name: "msjt-live-index",
            meta: {
              title: "名师讲堂_直播间",
              keepAlive:true,
              model:1
            },
        },
        {
            path: "/msjt/live/add/:id",
            component: () => import("@/views/pc/msjt/live/add.vue"),
            name: "msjt-live-add",
            meta: {
              title: "名师讲堂_新增直播",
              keepAlive:false,
              model:1
            },
        },
        {
            path: "/msjt/live/detail/:id",
            component: () => import("@/views/pc/msjt/live/detail.vue"),
            name: "msjt-live-detail",
            meta: {
              title: "名师讲堂_直播详情",
              keepAlive:false,
              model:1
            },
        },
        {
            path: "/msjt/check/index",
            component: () => import("@/views/pc/msjt/check/index.vue"),
            name: "msjt-check-index",
            meta: {
              title: "名师讲堂_直播审核",
              keepAlive:true,
              model:2
            },
        },
        {
          path: "/msjt/check/add/:id",
          component: () => import("@/views/pc/msjt/live/add.vue"),
          name: "msjt-check-add",
          meta: {
            title: "名师讲堂_新增直播",
            keepAlive:false,
            model:2
          },
        },
        {
            path: "/msjt/apply/index",
            component: () => import("@/views/pc/msjt/apply/index.vue"),
            name: "msjt-apply-index",
            meta: {
              title: "名师讲堂_申请列表",
              keepAlive:true,
              model:3
            },
        },
        {
          path: "/msjt/apply/add/:id",
          component: () => import("@/views/pc/msjt/live/add.vue"),
          name: "msjt-apply-add",
          meta: {
            title: "名师讲堂_新增直播",
            keepAlive:false,
            model:3
          },
        },
    ]
  }
]

export const mobileConstantRoutes = [
  {
    path: "/publicwisdom",
    name:'publicwisdom',
    component: () => import("@/views/pc/publicwisdom.vue"),
  },
  {
    path: "/qrland",
    name:'qrland',
    component: () => import("@/views/mobile/land.vue"),
    meta: {
      title: "扫码确认",
    },
  },
  {
    path: "/wxland",
    name:'wxland-login',
    component: () => import("@/views/mobile/wxland.vue"),
    meta: {
      title: "微信登录",
    },
  },
  {
    path: "/redirect",
    component: Layout,
    meta: {
      hidden: true
    },
    children: [
      {
        path: ":path(.*)",
        component: () => import("@/views/redirect/index.vue")
      }
    ]
  },
  {
    path: "/403",
    component: () => import("@/views/error-page/403.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: "/404",
    component: () => import("@/views/error-page/404.vue"),
    meta: {
      hidden: true
    },
    alias: "/:pathMatch(.*)*"
  },
  {
    path: "/login",
    component: () => import("@/views/login/index.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: '/reset',
    component: () => import("@/views/login/reset.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: "/wisdom",
    component: () => import("@/views/mobile/wisdom.vue"),
    meta: {
      hidden: true,
      title:'智慧课堂'
    }
  },
  {
    path: "/",
    component: Layout,
    redirect: "/home",
    meta: {
      title: "行知研习社",
    },
    children: [
      {
        path: "/home",
        component: () => import("@/views/mobile/index.vue"),
        name: "Home",
        meta: {
          title: "行知研习社",
          showBar: true
        }
      }
    ]
  },
  {
    path: "/personalSpace",
    component: Layout,
    redirect: "/personalSpace",
    meta: {
      title: "个人空间",
      keepAlive: true
    },
    children: [
      {
        path: "/personalSpace",
        component: () => import("@/views/mobile/personal-space/index.vue"),
        name: "PersonalSpace",
        meta: {
          title: "行知研习社",
          showBar: true,
          keepAlive: true
        }
      },
      {
        path: "/personalSpace/add",
        component: () => import("@/views/mobile/personal-space/add/index.vue"),
        name: "DiaryAdd",
        meta: {
          title: "行知研习社",
          showBar: false
        }
      },
      {
        path: "/personalSpace/publish",
        component: () => import("@/views/mobile/personal-space/publish/index.vue"),
        name: "DiaryPublish",
        meta: {
          title: "行知研习社",
          showBar: false
        }
      },
      {
        path: "/personalSpace/detail",
        component: () => import("@/views/mobile/personal-space/detail/index.vue"),
        name: "DiaryDetail",
        meta: {
          title: "日记详情",
          showBar: false
        }
      }
    ]
  },
  {
    path: "/diarySquare",
    component: Layout,
    meta: {
      title: "日记广场",
      keepAlive: true
    },
    children: [
      {
        path: "/diarySquare",
        component: () => import("@/views/mobile/diary-square/index.vue"),
        name: "DiarySquare",
        meta: {
          title: "日记广场",
          showBar: true,
          keepAlive: true
        }
      }
    ]
  },
  {
    path: "/userRecord",
    component: Layout,
    meta: {
      title: "用户记录",
      keepAlive: true
    },
    children: [
      {
        path: "/userRecord",
        component: () => import("@/views/mobile/user-record/index.vue"),
        name: "UserRecord",
        meta: {
          title: "用户记录",
          showBar: false,
          keepAlive: true
        }
      }
    ]
  },
  {
    path: "/topicConversation",
    component: Layout,
    meta: {
      title: "主题交流"
    },
    children: [
      {
        path: "/topicConversation",
        component: () => import("@/views/mobile/topic-conversation/index.vue"),
        name: "TopicConversation",
        meta: {
          title: "主题交流",
          showBar: true
        }
      },
      {
        path: "/topicConversation/detail",
        component: () => import("@/views/mobile/topic-conversation/detail/index.vue"),
        name: "TopicConversationDetail",
        meta: {
          title: "主题交流详情",
          showBar: false
        }
      },
      {
        path: "/topicConversation/publish",
        component: () => import("@/views/mobile/topic-conversation/publish/index.vue"),
        name: "TopicConversationPublish",
        meta: {
          title: "发布讨论",
          showBar: false
        }
      }
    ]
  },
  {
    path: "/message",
    component: Layout,
    meta: {
      title: "消息通知"
    },
    children: [
      {
        path: "/message",
        component: () => import("@/views/mobile/message/index.vue"),
        name: "Message",
        meta: {
          title: "消息通知",
          showBar: false
        }
      },
      {
        path: "/message/detail",
        component: () => import("@/views/mobile/message/detail/index.vue"),
        name: "MessageDetail",
        meta: {
          title: "消息通知详情"
        }
      }
    ]
  },
  {
    path: "/dataStatistic",
    component: Layout,
    meta: {
      title: "数据统计"
    },
    children: [
      {
        path: "/dataStatistic",
        component: () => import("@/views/mobile/data-statistic/index.vue"),
        name: "DataStatistic",
        meta: {
          title: "数据统计",
          showBar: true
        }
      }
    ]
  }
]

/**
 * 动态路由
 * 用来放置有权限 (Roles 属性) 的路由
 * 必须带有 Name 属性
 */
export const dynamicRoutes = [
  // {
  //   path: "/permission",
  //   component: Layout,
  //   redirect: "/permission/page",
  //   name: "Permission",
  //   meta: {
  //     title: "权限",
  //     svgIcon: "lock",
  //     roles: ["admin", "editor"], // 可以在根路由中设置角色
  //     alwaysShow: true // 将始终显示根菜单
  //   },
  //   children: [
  //     {
  //       path: "page",
  //       component: () => import("@/views/permission/page.vue"),
  //       name: "PagePermission",
  //       meta: {
  //         title: "页面级",
  //         roles: ["admin"] // 或者在子导航中设置角色
  //       }
  //     },
  //     {
  //       path: "directive",
  //       component: () => import("@/views/permission/directive.vue"),
  //       name: "DirectivePermission",
  //       meta: {
  //         title: "按钮级" // 如果未设置角色，则表示：该页面不需要权限，但会继承根路由的角色
  //       }
  //     }
  //   ]
  // }
]

const setRoutes = _isMobile() ? mobileConstantRoutes : constantRoutes

export const router = createRouter({
  history,
  routes: routeSettings.thirdLevelRouteCache ? flatMultiLevelRoutes(setRoutes) : setRoutes
})

/** 重置路由 */
export function resetRouter() {
  // 注意：所有动态路由路由必须带有 Name 属性，否则可能会不能完全重置干净
  try {
    router.getRoutes().forEach((route) => {
      const { name, meta } = route
      if (name && meta.roles?.length) {
        router.hasRoute(name) && router.removeRoute(name)
      }
    })
  } catch {
    // 强制刷新浏览器也行，只是交互体验不是很好
    window.location.reload()
  }
}
let fullscreenLoading=null
router.beforeEach((to,from,next)=>{
  console.log('to',to,'from',from)
    let userStore=useUserInfoStore()
    let {userInfo,loginVisibleShow,squareUserCenterScrollTop,squareCommonScrollTop,squareScrollTop}=storeToRefs(userStore)
    let is_live_detail = to.path && to.path.indexOf('/msjt/live/detail')
    if(!userInfo.value.user&&to.path!='/actResearchAssociation/home'&&is_live_detail == -1){
      loginVisibleShow.value=true
    }
    if(!_isMobile()){
      fullscreenLoading = ElLoading.service({ fullscreen: true,text:'加载中' })
    }
    if(from.name=='ActResearchAssociation-square-userRecord'){
      squareCommonScrollTop.value=squareUserCenterScrollTop.value
    }else{
      squareCommonScrollTop.value=squareScrollTop.value
    }
    if(to.fullPath=='/login'){
      if(from?.name?.indexOf('login')>-1){
        //各种登录形式不允许继续重定向了
        next()
      }else{
        next('/login?redirect_uri='+from.fullPath)
      }
     
    }else{
      next()
    }
    
    
})
router.afterEach(() => {
  if(!_isMobile()){
    
    setTimeout(()=>{
      fullscreenLoading.close()
    },500)
  }
 
})
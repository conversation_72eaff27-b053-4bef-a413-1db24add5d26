.top{
    z-index: 100;
     background:#fff url(@/assets/pc/score/topbg.png) no-repeat;
     padding: 32px 24px 114px 24px;
     background-size: 100% 100%;
    .date{
        background: url(@/assets/pc/score/datebg.png) no-repeat;
        background-size: 100% 100%;
        padding: 17px 40px 35px 178px;
        :deep(.el-select__wrapper.is-focused){
             box-shadow: none;
        }
         :deep(.el-select__wrapper){
            box-shadow: none;
            height: 38px;
         }
         :deep(.el-select__selected-item span){
            font-size: 16px;
           color: #508CFF;
         }
    }
    .statistic{
        width: 100%;
        margin-top: 44px;
        .tip{
                   width: 120px;
                    height: 38px;
                    position: absolute;
                    top:0;
                    left:50%;
                    background: #fff;
                    transform: translate(-50%,-50%);
                     border-radius: 8px;
                p{
                    font-size: 16px;
                    color: #508CFF;
                    line-height: 100%;
                   
                    text-align: center;
                    &::before{
                        content:'';
                        display: block;
                        width: 3px;
                        height: 16px;
                        background: #508CFF;
                        border-radius: 2px;
                        margin-right: 8px;
                    }
                }
            }
        .left{
            width: 78%;
            background: #258DFF;
            border-radius: 8px;
            padding: 24px 8px 34px 8px;
            position: relative;
            
            .item{
                width: 32%;
                h2{
                    font-size: 40px;
                     color: #FFFFFF;
                }
                h3{
                     font-size: 24px;
                     color: #FFFFFF;
                }
                img{
                    width: 20px;
                    height: auto;
                    margin:  0 0 0 4px;
                }
                p{
                    font-size: 14px;
                    color: #72E9B4;
                    margin:  0 0 0 4px;
                    &.up{
                         color:#FFCF49; 
                        
                    }
                }
                .item-title{
                  font-size: 16px;
                   color: #FFFFFF;
                   margin-top: 4px;
                }
            }
            .line{
                height: 32px;
                width: 2px;
                background: #fff;
            }
        }
        .right{
            margin-left: 24px;
            .group{
                  width: 100%;
                  position: relative;
                  background: #258DFF;
                 border-radius: 8px;
                  color: #fff;
                 font-size: 16px;
                 line-height: 22px;
                 height: 42%;
                 padding-top: 10px;
                 h3{
                    font-size: 32px;
                    margin:0 13px;
                    color: #FFFFFF;
                 }
            }
        }
    }
}

.model-title{
    padding:8px 24px 8px 96px;
    display: inline-block;
    background: url(@/assets/pc/score/tabbg.png) no-repeat;
    background-size: 100% 100%;
    font-size: 20px;
    color: #FFFFFF;
    line-height: 28px;;
    transform: translateY(-30px);
}
.tab-box{
    padding: 0 40px 24px 40px;
    background: #fff;
    .tab{
        border-bottom: 1px solid #E2E3E6;;
        div{
            // padding-bottom: 19px;
            font-size: 15px;
            color: #2B2C33;
            line-height: 21px;
            cursor: pointer;
            p{
                margin-bottom: 4px;
                 padding: 4px 12px 13px 12px;
            }
            &::after{
                content:'';
                width: 100%;
                display:block;
                height: 3px;
                background: #fff;
                border-radius: 2px;
            
            }
        }
        .active{
            
            color: #fff;
            p{
                 
                background: url(@/assets/pc/score/tab-bg.png) no-repeat;
               background-size: 100% 100%;
              
            }
           &::after{
             content:'';
             width: 100%;
             display:block;
             height: 3px;
              background: linear-gradient(#68AFFF 0%, #508CFF 100%), #508CFF;
             border-radius: 2px;
          
           }
        }
    }
    .action-list{
        margin-top:8px;
        .action-item{
            background: #F3F7FF;
            border-radius: 8px;
            width: 18%;
            position: relative;
            margin-right: 2.5%;
            padding: 14px 16px;
            margin-top: 16px;
            &:nth-child(5n+5){
                margin-right: 0;
            }
        .title{
            font-size: 14px;
            color: #94959C;
            line-height: 20px;
        }
        .tag{
            width: 17px;
            height: auto;
            position: absolute;
           right: 14px;
           bottom: 20px;
        }
        div{
            margin-top: 6px;
           .number{
                font-size: 24px;
                color: #2B2C33;
                line-height: 35px;
                margin-right: 2px;
           }
          .unit{
            font-size: 14px;
            color: #2B2C33;
          }
          
        }
      }
       
      
    }
}
<template>
    <div class="diary-detail flex_start_0 content-box">
        <div class="left">
            <ThemeLeft />
            
            
        </div>
           <div class="right flex_1">
                <div class="flex_start_0 diary">
                    <!-- <img src="@/assets/pc/teacher.png" alt="" class="head" /> -->
                     <el-avatar :src="detailRow.userAvatarUrl || DefaultAvatar" class="avatar head" :size="32" @click="goPreview(detailRow)" />
                    <div class="flex_1">
                        <div class="info">
                            <div class="flex_start ">
                                <p class="teacher">{{detailRow.userName}}</p>
                               
                            </div>
                            <p class="date">{{detailRow.postTime}}</p>
                        </div>
                    <div class="rich">
                        <div class="rich-html" ref="richRef" v-html="detailRow.content">
                         
                        </div>
                        
                    </div>
                
                    <div class="bottom">
                        <ul class="flex_start tag-list">
                            <li class="flex_start">
                                <img src="@/assets/pc/tag.png" alt="">
                                <p>{{detailRow.clubTopicName}}</p>
                            </li>
                        </ul>
                    </div>
                </div>
               </div>
             
               <div class="comment">
                   <div class="write-own-comment" v-if="detailRow.postType==1" v-pcpermission='["LEADER","TUTOR","MEMBER"]'>
                     <div class="ipt flex_start_0">
                            <img src="@/assets/pc/teacher.png" alt="">
                            <div class="flex_1 icon-box">
                                  <el-input type="textarea" rows="5" resize="none" show-word-limit maxlength="350"  v-model="commentForm.content" placeholder="发表你的评论"  >
                                     <template #prepend v-if="commentForm.replyName">回复{{commentForm.replyName}}:</template>
                                 </el-input>
                                 <el-button @click="goSendComment">评论</el-button>
                               
                            </div>
                        </div>
                   </div>
                    <div class="write-own-comment" v-if="detailRow.postType==2">
                     <div class="ipt flex_start_0">
                            <img src="@/assets/pc/teacher.png" alt="">
                            <div class="flex_1 icon-box">
                                  <el-input type="textarea" rows="5" resize="none" show-word-limit maxlength="350"  v-model="commentForm.content" placeholder="发表你的评论"  >
                                     <template #prepend v-if="commentForm.replyName">回复{{commentForm.replyName}}:</template>
                                 </el-input>
                                 <el-button @click="goSendComment">评论</el-button>
                                
                            </div>
                        </div>
                   </div>
                      <div class="comment-list" v-show="comments.length!=0" v-infinite-scroll="load" style="overflow:auto">
                         <div class="comment-item flex_between_0" v-for="item in comments" :key="item.id">
                            <div>
                                <div class="flex_start_0 level_1_comment">
                                     <img :src="item.userAvatarUrl || DefaultAvatar" alt="" class="teacher-logo"  @click="goPreview(item)">
                                      <!-- <el-avatar :src="item.userAvatarUrl || DefaultAvatar" class="avatar teacher-logo" :size="32" @click="goPreview(item)" /> -->
                                    <div>
                                        <div class="flex_start_0">
                                            <p class="teacher-name">{{item.userName}}</p>
                                            <p :class="[item.userClubRole=='LEADER'?'job-leader':item.userClubRole=='TUTOR'?'job-tutor':'']">
                                                {{item.userClubRole=='LEADER'?'组长':item.userClubRole=='TUTOR'?'导师':''}}
                                            </p>
                                            <!-- @click="replyChildComment(item,citem)" -->
                                            <p class="teacher-write flex_1">：{{item.content}}</p>
                                            <img src="@/assets/pc/del.png" alt="" v-show="item.isDeleteCapable" class="del-comment" @click="goDel(item,'','parent')"/>
                                             <img src="@/assets/pc/edit.png" alt="" v-show="item.isUpdateCapable" class="del-comment" @click="goEditComment(item)"/>
                                        </div>
                                        <p class="from">{{item.formattedCommentTime}}</p>
                                    </div>
                                  </div>
                                  
                                
                               </div>
                             
                          
                         </div>
                     </div>
                     <el-empty description="暂无评论" v-show="comments.length==0" style="height:calc(100vh - 400px)"/>
               </div>
            </div>
          
    </div>
      <el-dialog
        v-model="commentDialogVisible"
        title="编辑评论"
        width="500"
        :before-close="cancelEditComment"
    >
        <el-input v-model="editCommentForm.content" placeholder="请输入"></el-input>
        <template #footer>
        <div class="dialog-footer" style="margin-top:24px">
            <el-button  @click="cancelEditComment">
              取消
            </el-button>
            <el-button type="primary" @click="confirmEditComment">确认</el-button>
            
        </div>
        </template>
    </el-dialog>
      <el-image-viewer
        v-if="showPreview"
        :url-list="previewurlList"
        show-progress
        :initial-index="0"
        @close="showPreview = false"
      />
</template>
<script setup>
import {onMounted, reactive, ref} from 'vue'
import {useRoute,useRouter} from 'vue-router'
import {themes,themeDetail,themeComment,themeCommentAdd,themeCommentDel,themeCommentEdit} from '@/api/pc/index.js'
import {ElMessage,ElMessageBox} from 'element-plus'
import ThemeLeft from '@/views/pc/component/act-research-association/themeLeft.vue'
import DefaultAvatar from "@/assets/layouts/default-avatar.png"
let route=useRoute()
let router=useRouter()
let sortType=ref(1)
let starnum=ref(2)
let showPreview=ref(false)
let previewurlList=ref([])
let clubTopicId=ref('')
let clubPostId=ref('')
let diaryList=ref([])
let detailRow=ref({})
let comments=ref([])
let searchForm=reactive({
    // lastCommentTime:'',
    pageSize:15,
    pageNum:1
    // lastId:'',
   
})
let commentForm=reactive({
    parentId:'',
    replyToId:'',
    replyName:'',
    content:'',
    postId:''
})
let tempParentIds=[] 
let hasNextPage=false
let maxPage=0
let goClose=()=>{
     Object.assign(commentForm,{
         parentId:'',
        replyToId:'',
        replyName:'',
        content:'',
        postId:''
     })
}

let editCommentForm=reactive({
    commentId:'',
    content:''
})

let confirmEditComment=async()=>{
   if(editCommentForm.content==''){
     cancelEditComment()
   }else{
      let res=await themeCommentEdit(editCommentForm)
      if(res){
           ElMessage.success('编辑成功')
           comments.value.forEach(item=>{
             if(item.id==editCommentForm.commentId){
                item.content=editCommentForm.content
             }
           })
           cancelEditComment()
      }
   }
}
let cancelEditComment=()=>{
  commentDialogVisible.value=false
  Object.assign(editCommentForm,{
    commentId:'',
    content:''
  })
}
let goEditComment=(row)=>{
   commentDialogVisible.value=true
   Object.assign(editCommentForm,{
    commentId:row.id,
    content:row.content
  })
}
let goDel=(row1,row2,type)=>{
 ElMessageBox.confirm(
    '确认删除该条评论吗?',
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async() => {
        let res=await themeCommentDel({
            commentId:row1.id
        })
        if(res){
          if(type=='parent'){
            comments.value=comments.value.filter(item=>{
                return item.id!=row1.id
            })
           }else{
            row2.children=row2.children.filter(item=>{
                return item.id!=row1.id
            })
          }
            ElMessage({
                type: 'success',
                message: '删除成功',
            })
        }
    
    })
    .catch(() => {
      
    })
}
let goPreview=(row)=>{
    if(row.userAvatarUrl){
         previewurlList.value=[row.userAvatarUrl]
        showPreview.value=true
    }
}
let load=()=>{
  if(hasNextPage){
       Object.assign(searchForm,{
        lastCommentTime:comments.value[comments.value.length-1].commentTime,
        lastId:comments.value[comments.value.length-1].id,
       })
       getcommentList()
  }
}
// let replyParentComment=(row)=>{
//    Object.assign(commentForm,{
//     replyName:row.userName,
//     parentId:row.id
//    })
// } 
// let replyChildComment=(parentRow,row)=>{
//    Object.assign(commentForm,{
//         replyName:row.userName,
//         parentId:parentRow.id,
//         replyToId:row.id
//    })
// } 
let goSendComment=async()=>{
    Object.assign(commentForm,{
        postId:clubPostId.value
    })
    if(commentForm.content){
        let res=await themeCommentAdd(commentForm) 
        if(res){
            tempParentIds.push(res.data.id)
                res.data.children=[]
                comments.value.unshift({
                    ...res.data
                })
            // if(commentForm.parentId){
                
            //     if(commentForm.replyToId){
            //         //回复二级评论
            //          comments.value.forEach(item=>{
            //         if(item.id==commentForm.parentId){
            //              item.children.push(res.data)
            //         }
            //      })
            //     }else{
            //         //回复一级评论
               
            //      comments.value.forEach(item=>{
            //         console.log(item)
            //         if(item.id==commentForm.parentId){
            //              item.children.unshift({
            //                 ...res.data
            //              })
            //         }
            //      })
            //     }
               
            // }else{
                
            // }
            ElMessage.success('评论成功')
            Object.assign(commentForm,{
                content:'',

            })
        }
    }else{
        return
    }
    
}
let getcommentList=async()=>{
    let res=await themeComment({
         postId:clubPostId.value,
        lastCommentTime:searchForm.lastCommentTime,
        pageSize:searchForm.pageSize,
        pageNum:searchForm.pageNum,
        lastId:searchForm.lastId
    })
    if(res){
       res.data.list.forEach(item=>{
            item.children=item.children?item.children:[]
        })
        let temp=[]
        res.data.list.forEach(item=>{
            if(tempParentIds.indexOf(item.id)<0){
                temp.push(item)
            }
        })
        comments.value=comments.value.concat([...temp])
        //   total.value=res.data.total
         
         hasNextPage=res.data.hasNextPage
    }
}
let getThemeList=async()=>{
    let res=await themes()
    if(res){
        diaryList.value=res.data
    }
}
let changeTab=(item)=>{
    clubTopicId.value=item.id
    router.push('/actResearchAssociation/theme?clubTopicId='+clubTopicId.value)
    //   getDetail()
}
let getDetail=async()=>{
  let res=await themeDetail({
    clubPostId:clubPostId.value
  })
  if(res){
    detailRow.value=res.data
    clubTopicId.value=res.data.clubTopicId
  }
}
onMounted(()=>{
    
    clubPostId.value=route.query.clubPostId
   getThemeList()
   getDetail()
   getcommentList()
})
</script>
<style lang="scss" scoped>
.left{
    width: 180px;
    margin-top: 16px;
    .person-column{
        border-radius: 8px;
        background: #fff;
        padding-bottom: 16px;
        .title{
             padding: 18px 0  24px 18px;
             font-weight: bold;
             font-size: 20px;
             color: #2B2C33;
        }
        .list{
            li{
                cursor: pointer;
                padding: 10px 8px  8px 18px;
                font-size: 16px;
                color: #2B2C33;
                &.active{
                    background: #DFEAFF;color: #508CFF;
                }
                .desc{
                    img{
                        width: 12px;
                        height: auto;
                        margin-right: 4px;
                    }
                }
                p{
                    width:80px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
    }
    .friend-link{
        margin-top: 10px;
    }
    .icon{
        width: 18px;
    }
}
.right{
    padding: 16px 19px;
 
    margin-right: 10px;
    border-radius:4px;
    .diary{
   
     background: #fff;  
    padding: 16px;
    border-radius: 4px;
    .head{
        width: 52px;
        height: 52px;
        margin-right: 10px;
    }
    .info{   background: #fff; 
        .teacher{
            font-size: 16px;
            color: #2B2C33;
            font-weight: bold;
            margin-right: 24px;
        }
        .stars{
            .star-box{
                margin-right: 4px;
            }
            img{
                width: 24px;
                height: auto;
            }
        }
    }
    .date{
        font-size: 14px;
        color: #94959C;
        margin-top: 4px;
    }
   
    .rich{
        position: relative;
        .rich-html{
        margin-top:8px;
       
        font-size: 16px;
        color: #2B2C33;
     
      line-height: 24px;
    }
    
  
    }
    .bottom{
        .tag-list{
            li{
                padding: 4px 10px;
                border-radius: 100px;
                font-size: 16px;
                border-radius: 19px;
                border: 1px solid #E2E3E6;
                color: #2B2C33;
                margin: 10px 10px 0 0;cursor: pointer;
                img{
                    width: 16px;
                    height: auto;
                    margin-right: 8px;
                    
                }
            }
        }
    }
     }
  
    .comment{
        padding:16px 16px 16px 62px;
        background: #fff;
        margin-top: 10px;
        border-radius: 4px;
        .write-own-comment{
             margin-bottom: 24px;
            .ipt{
                 img{
                    width: 34px;
                    height: 34px;
                 }
                 :deep(.el-input){
                    height: 38px;
                 
                    border-radius: 4px;
                   
                    border:none;
                    outline: none;
                  
                    .el-input__wrapper{
                        width: 100%;  background: #F0F1F4;
                        height: 38px;  padding-right: 30px;
                        
                    }
                 }
                 button{
                    width: 88px;
                    height: 38px;
                    background: #508CFF;
                    border-radius: 4px;
                    font-size: 16px;
                  color: #FFFFFF;
                  border: none;
                  cursor: pointer;
                 }
                   .icon-box{
                      margin: 0 16px 0 10px;
                       position: relative;
                       background: #F0F1F4;
                       button{
                        width: 64px;
                        height: 30px;
                        position: absolute;
                        right: 10px;
                        bottom: 10px;
                       }
                       :deep(.el-textarea){
                        padding-bottom: 36px;
                        border:1px solid #dcdfe6 ;
                        box-shadow: 0 0 0 1px #dcdfe6 #dcdfe6 inset;
                        .el-textarea__inner{
                            border: none;
                            box-shadow:none;
                             background: #F0F1F4;
                        }
                       }
                     .icon{
                        position: absolute;
                        right: 6px;
                        top:50%;
                        cursor: pointer;
                        transform: translateY(-50%);
                        
                     }
                      :deep(.el-textarea .el-input__count){
                        right: 80px;
                        background: none;
                        bottom:16px;
                     }
                   }
            }
            .star{
                 margin: 16px 0 24px 0;
                 .label{
                    font-size: 16px;
                     color: #2B2C33;
                     font-weight: bold;
                     margin-right: 16px;
                 }
                 img{
                    width: 35px;
                    height: auto;
                    margin-right: 12px;
                    cursor: pointer;
                 }
            }
        }
         
        .sort{
            font-size: 14px;
            color: #2B2C33;
            p{
                margin-right: 30px;cursor: pointer;
                &.active{
                    color: #4260FD;
                }
            }
        }
        .comment-list{
           
             max-height: calc(100vh - 370px);
             overflow: auto;
            .comment-item{
                margin-top: 24px;
                &:first-child{
                    margin-top: 0;
                }
                 .level_1_comment{
                    .teacher-logo{
                        width: 34px;
                        height: 34px;
                        border-radius: 200px;
                        margin-right: 10px;
                    }
                    .teacher-name{
                        font-size: 15px;
                        color: #5C76D4;
                        font-weight: bold;
                    }
                    .job-leader{
                        width: 34px;
                        height: 14px;
                       background: #C197FF;
                        border-radius: 2px;
                        color: #fff;
                        font-size: 11px;
                        line-height: 14px;
                        text-align: center;
                        margin: 0 8px;
                    }
                     .job-tutor{
                        width: 34px;
                        height: 14px;
                        background: #83BCFE;
                        border-radius: 2px;
                        color: #fff;
                        font-size: 11px;
                        line-height: 14px;
                        text-align: center;
                        margin: 0 8px;
                    }
                    .from{
                        font-size: 14px;
                        margin-top: 4px;
                        color: #94959C;
                        line-height: 20px;
                    }
                 }
                 .child-comment{
                    margin-top: 16px;
                 }
                 .level_2_comment{
                         margin-left: 44px;
                         padding-left: 10px;
                         border-left: 1px solid #e2e3e6;
                         padding-bottom: 10px;
                         &:last-child{
                            padding-bottom: 0;
                         }
                 }
                 .del-comment{
                    width: 14px;
                    height: 14px;cursor: pointer;margin-left: 16px;
                 }
                 .teacher-write{
                    cursor: pointer;
                    white-space: pre-wrap;
                    word-break: break-all;
                 }
            }
        }
        .thumb-list{
            margin-left: 24px;
            align-items: flex-start;
            .item{
                font-size: 14px;
               color: #94959C;
               margin-right: 24px;
               cursor: pointer;
               &:last-child{
                margin-right: 0;
               }
                img{
                    width: 14px;
                    height: auto;
                    margin-right: 6px;
                }
            }
        }
    }
}
</style>
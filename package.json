{"name": "web-dxs", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build --mode production", "build:dev": "vite build --mode development", "build:staging": "vite build --mode staging", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@ffmpeg/core": "0.12.10", "@ffmpeg/ffmpeg": "0.12.15", "@rollup/plugin-commonjs": "28.0.3", "@tencentcloud/chat": "3.5.4", "@vue/eslint-config-prettier": "10.2.0", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "5.1.12", "axios": "1.8.1", "cos-js-sdk-v5": "1.8.7", "dayjs": "1.11.13", "echarts": "5.6.0", "element-plus": "2.9.5", "eslint": "9.21.0", "eslint-plugin-prettier": "5.2.3", "eslint-plugin-vue": "9.32.0", "file-saver": "2.0.5", "html-doc-js": "1.0.9", "html2pdf.js": "0.10.3", "js-cookie": "3.0.5", "jsdom": "26.0.0", "katex": "0.16.8", "lamejs": "1.2.0", "lodash-es": "4.17.21", "mammoth": "1.9.0", "markdown-it": "14.1.0", "markdown-it-katex": "2.0.3", "marked": "15.0.11", "mitt": "3.0.1", "normalize.css": "8.0.1", "nprogress": "0.2.0", "path-browserify": "1.0.1", "path-to-regexp": "8.2.0", "pinia": "3.0.1", "pinia-plugin-persistedstate": "4.2.0", "postcss-px-to-viewport": "github:evrone/postcss-px-to-viewport", "prettier": "3.5.3", "recordrtc": "5.6.2", "sass": "1.85.1", "swiper": "11.2.6", "tcplayer": "0.0.30", "tim-upload-plugin": "1.4.2", "vant": "4.9.17", "vod-js-sdk-v6": "1.7.1-beta.1", "vue": "^3.2.13", "vue-eslint-parser": "9.4.3", "vue-router": "4.5.0", "vue3-audio-player": "1.0.7", "vue3-seamless-scroll": "3.0.2", "xgplayer": "3.0.21", "xh-htmlword": "1.3.2"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "vite": "^6.2.0"}}
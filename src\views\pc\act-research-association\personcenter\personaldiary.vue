<template>
    <div>
        <PersonalCount :countForm='countForm' />
        <div class="flex_between mt10">
            <div class="flex_1">
                <Sort @emitForm='emitForm'  />
                
            </div>
            <button class="upload flex_center" @click="goUpload" v-show="activeIndex==1" >
                <img src="@/assets/pc/write-diary.png" alt="">
                上传日记
            </button>
        </div>
        <div class="empty" v-show="total==0">
              <el-empty description="暂无数据" />
        </div>
         <div  v-show="total!==0" >
            <div class="infinite-list" style="overflow: auto" ref="infiniteListRef">
                <DiaryItem v-for="item in tableData" @emitGoDetail='emitGoDetail' @emitDel="emitDel" :key="item.id" :canDel='activeIndex==1' :canEdit='activeIndex==1' :url='url' :row='item' @emitIsLike='emitIsLike'/>
            </div>
              <el-pagination background style="margin-bottom:10px" layout="prev, pager, next" :page-size='pageSize' :total="total" @current-change='handlelCurrentChange' />
        </div>
    </div>
</template>
<script setup>
import PersonalCount from '@/views/pc/component/act-research-association/personalcount.vue'
import Sort from '@/views/pc/component/act-research-association/sort.vue'
import DiaryItem from '@/views/pc/component/act-research-association/diaryItem.vue'
import {useRouter} from 'vue-router'
import {defineProps,ref} from 'vue'
let props=defineProps(['countForm','tableData','url','activeIndex','total','pageSize','role'])
let emits=defineEmits(['emitsParentForm','emitParentIsLike','needLoad','emitParentDel','emitParentDetail','emitCurrentChange'])
let router=useRouter()
let infiniteListRef=ref()
let handlelCurrentChange=(val)=>{
    infiniteListRef.value.scrollTop=0
    emits('emitCurrentChange',val)
}
// let load=()=>{
//      emits('needLoad')
// }
let emitForm=(val)=>{
  emits('emitsParentForm',val)
}
let goUpload=()=>{
    router.push('/actResearchAssociation/personcenter/upload')
}
let emitIsLike=(islike,id,type)=>{
    emits('emitParentIsLike',islike,id,type)
}
let emitDel=(id)=>{
    emits('emitParentDel',id)
}
let emitGoDetail=(recordId)=>{
   emits('emitParentDetail',recordId)
}
</script>
<style scoped>
.infinite-list{
    height: calc(100vh - 390px);
}
.upload{
    width: 120px;
    height: 44px;
    background: #508CFF;
    border-radius: 4px;
    font-size: 16px;
    color: #FFFFFF;
    font-weight: bold;
    cursor: pointer;
    border:none;
    margin-bottom: 10px;
}
.upload img{
    width: 12px;
    height: auto;
    margin-right: 8px;
}
.empty{
    background: #fff;
    margin-top: 10px;
}
</style>


.green{color: #23D486;}
.orange{color: #FF8932;}
.orange2{color:#FF9032;}
.orange3{color:#E99D42;}
.orange4{color:#FFA032;}
.grey{color: #B4B6BE;}
.grey2{color: #94959C;}
.grey3{color: #6D6F75;}
.grey4{color: #2B2C33;}
.blue{color: #508CFF;}
.blue2{color: #1F6CFF;}
.red{color: #FF4D4F;}
.red2{color: #E72C4A;}

.search_form{
    .el-form-item{display: block!important;width: 100%;}
    .hidden-label{
        .el-form-item__label{opacity: 0;}
    }
    .el-form-item__label{font-weight: 600; font-size: 16px; height: 24px;line-height: 24px;color: #2B2C33;padding-right: 16px;margin-right: 0;margin-bottom: 8px; font-family: PingFangSC, PingFang SC;}
}

.mr_2{margin-right: 2px;}
.mr_3{margin-right: 3px;}
.mlr_5{margin-left: 5px;margin-right: 5px;}
.mt_8{margin-top: 8px;}
.mt_10{margin-top: 10px;}
.mr_10{margin-right: 10px;}
.ml_10{margin-left: 10px;}
.mlr_10{margin-left: 10px;margin-right: 10px;}
.mt_12{margin-top: 12px;}
.mr_12{margin-right: 12px;} 
.mb_12{margin-bottom: 12px;}
.mb_13{margin-bottom: 13px;}
.mt_16{margin-top: 16px;}
.ml_16{margin-left: 16px;}
.mb_16{margin-bottom: 16px;}
.mt_18{margin-top: 18px;}
.ml_20{margin-left: 20px;}
.mt_20{margin-top: 20px;}
.mb_20{margin-bottom: 20px;}
.mt_22{margin-top: 22px;}
.mt_24{margin-top: 24px;}
.mr_24{margin-right: 24px;}
.ml_24{margin-left: 24px;}
.mb_24{margin-bottom: 24px;}
.mt_26{margin-top: 26px;}
.mt_30{margin-top: 30px;}
.mr_32{margin-right: 32px;}
.mr_40{margin-right: 40px;}

.text_12{font-weight: 400; font-size: 12px; line-height: 18px; text-align: left;}
.text_14{font-weight: 400; font-size: 14px; line-height: 20px; text-align: left;}
.text_15{font-weight: 400; font-size: 15px; line-height: 24px; text-align: left;}
.text_16{font-weight: 400; font-size: 16px; line-height: 24px; text-align: left;}
.text_16_bold{font-weight: 600; font-size: 16px; color: #2B2C33; line-height: 24px; text-align: left;}
.text_17_bold{font-weight: 600; font-size: 17px; color: #2B2C33; line-height: 24px; text-align: left;}
.text_18_bold{font-weight: 600; font-size: 18px; line-height: 28px; text-align: left;}
.text_20_bold{font-weight: 600; font-size: 20px; color: #2B2C33; line-height: 28px; text-align: left;}


.btn_64_32{width: 64px;height: 32px;border-radius: 8px;font-weight: bold; font-size: 16px; line-height: 24px; text-align: left;}
.btn_80_32{width: 80px;height: 32px;border-radius: 8px;font-weight: bold;font-size: 16px; line-height: 24px;}
.btn_96_48{width: 96px;height: 48px;border-radius: 8px;font-weight: bold; font-size: 18px; line-height: 26px; text-align: left;}
.btn_112_40{width: 112px;height: 40px;border-radius: 8px;font-weight: 600; font-size: 16px; line-height: 24px; text-align: left;
    span{margin-left: 4px;}
} 
.btn_132_40{width: 132px;height: 40px;border-radius: 8px;font-weight: 600; font-size: 16px; line-height: 24px; text-align: left;
    span{margin-left: 4px;}
} 
.btn_120_48{width: 120px;height: 48px;border-radius: 8px;font-weight: bold; font-size: 18px; line-height: 26px; text-align: left;}
.btn_150_40{width: 150px; height: 40px;border-radius: 8px;font-weight: 400; font-size: 16px; color: #FFFFFF; line-height: 24px; text-align: left;}
.btn_184_48{width: 184px;height: 48px;border-radius: 8px;font-weight: bold; font-size: 18px; line-height: 26px; text-align: left;}
.primary-btn{background: #508CFF;color: #FFFFFF; }
.primary-btn2{background: #508CFF; box-shadow: 0px 4px 6px 0px rgba(66,96,253,0.24); border-radius: 12px;}
.plain-btn{background: #FFFFFF; color: #94959C;}
.plain-btn2{background: #FFFFFF; color: #508CFF;}
.plain-blue-btn{border: 1px dashed #508CFF;font-family: HarmonyOS_Sans_SC; font-size: 16px; color: #508CFF; line-height: 20px; text-align: left; font-style: normal;font-weight: 400;}
.plain-blue-btn_solid{border: 1px solid #508CFF;font-family: HarmonyOS_Sans_SC; font-size: 16px; color: #508CFF; line-height: 20px; text-align: left; font-style: normal;font-weight: 400;}
.upload-plain{border: 1px solid #508CFF;border-radius: 8px;width: 96px;height: 48px;font-family: PingFangSC, PingFang SC; font-weight: 600; font-size: 16px; color: #508CFF; line-height: 24px; text-align: left;}
.green_btn{background: #23D486;border: 1px solid #23D486; color: #FFFFFF;}
.red_btn{border: 1px solid #E72C4A;border-radius: 8px;color: #E72C4A;}


.input_40{  width: 100%; height: 40px;
    ::v-deep .el-input__wrapper{ width: 100%; height: 40px; border-radius: 4px; line-height: 40px; font-size: 14px; font-weight: 400; color: #B4B6BE;border-radius: 8px;}
}
.input_48{  width: 100%; height: 48px;
    ::v-deep .el-input__wrapper{ width: 100%; height: 48px; border-radius: 8px; line-height: 48px; font-size: 16px; font-weight: 400; color: #B4B6BE;border-radius: 8px;}
}
.search_input{
    ::v-deep .el-input__wrapper{border: none;box-shadow:none;border-radius: 8px;}
}
.my-textarea{
    .el-textarea__inner{border-radius: 8px;background: #F4F5F7;font-weight: 400; font-size: 14px; color: #B4B6BE; line-height: 24px; text-align: left;padding: 14px 10px;}
}
.my-textarea2{
    .el-textarea__inner{border-radius: 8px;background: #fff;font-weight: 400; font-size: 14px; line-height: 24px; text-align: left;padding: 14px 10px;}
}

.select_40{ min-width: 184px; height: 40px; width: 100%;
    .el-select__wrapper{height: 40px!important;}
    ::v-deep .el-select__wrapper{ min-width: 184px; width: 100%; height: 40px; border-radius: 8px; line-height: 40px; font-size: 14px; font-weight: 400; color: #B4B6BE;}
}
.select_40_2{ height: 40px; width: 100%;
    .el-select__wrapper{height: 40px!important;}
    ::v-deep .el-select__wrapper{ width: 100%; height: 40px; border-radius: 8px; line-height: 40px; font-size: 14px; font-weight: 400; color: #B4B6BE;}
}
.select_48{ min-width: 184px; height: 48px; width: 100%;
    .el-select__wrapper{height: 48px!important;}
    ::v-deep .el-select__wrapper{ min-width: 184px; width: 100%; height: 48px; border-radius: 8px; line-height: 48px; font-size: 14px; font-weight: 400; color: #B4B6BE;}
}

.datepicker_40.el-date-editor.el-input{ width: 184px; height: 40px;
    ::v-deep .el-input__wrapper{ width: 100%; height: 40px; border-radius: 4px; line-height: 40px; font-size: 14px; font-weight: 400; color: #B4B6BE;}
}
::v-deep .daterange_40{height: 40px;border-radius: 8px;
    .el-input__wrapper{width: 100%; height: 40px; border-radius: 8px; line-height: 40px; font-size: 14px; font-weight: 400; color: #B4B6BE;}
}
::v-deep .my-date-picker{width: 100%; height: 48px;
    .el-input__wrapper{width: 100%; height: 48px; border-radius: 8px; line-height: 48px; font-size: 14px; font-weight: 400; color: #B4B6BE;}
}
::v-deep .my-date-picker_40{width: 100%; height: 40px;
    .el-input__wrapper{width: 100%; height: 40px; border-radius: 8px; line-height: 40px; font-size: 14px; font-weight: 400; color: #B4B6BE;}
}

.table-line{ width: 1px; height: 18px; background: #B4B6BE; margin: 0 12px; }

.my-dialog{border-radius: 12px;padding:24px;margin-top:0;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);
    .el-dialog__header.show-close{text-align: left;}
}

.my-table{ 
    .el-table__cell{padding: 0;}
    .cell{font-size: 14px;}
    .el-select__wrapper{border: none;box-shadow:none;}
    .el-select__wrapper.is-hovering:not(.is-focused){box-shadow: none;}
}

.pointer{cursor: pointer;}
/* 省略号*/
.ellipsis1{ white-space: nowrap;text-overflow: ellipsis;overflow: hidden;word-break: break-all;}
.ellipsis2{text-overflow: -o-ellipsis-lastline;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;line-clamp: 2;-webkit-box-orient: vertical;}
.content-box{max-width: 1128px;margin:0 auto;font-family: PingFangSC, PingFang SC;}


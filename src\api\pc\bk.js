import { request } from "@/utils/service"
///fs/file
export function memberList(data) {
  return request({
    url: "/bk/group/member/search",
    method: "get",
    params:data
  })
}

export function gradeList(data) {
    return request({
      url: "/bk/group/member/"+data.tenantId+"/grade",
      method: "get",
    
    })
  }
  //b
  export function bkgroupList(data) {
    return request({
      url: "/bk/group/list",
      method: "get",
      params:data
    })
  }
  //
  export function bkgroupDetail(data) {
    return request({
      url: "/bk/group/"+data.groupId,
      method: "get",
    })
  }
  export function bkgroupDel(data) {
    return request({
      url: "/bk/group/"+data.groupId,
      method: "delete",
    })
  }
  export function bkgroupEdit(data) {
    return request({
      url: "/bk/group/"+data.groupId,
      method: "put",
      data
    })
  }
  export function bkgroupAdd(data) {
    return request({
      url: "/bk/group",
      method: "post",
      data
    })
  }
  export function bkResource(data) {
    return request({
      url: "/bk/portal/resource",
      method: "get",
      params:data
    })
  }
  
  export function bkRecommendResource(data) {
    return request({
      url: "/bk/portal/resource/recommend/"+data.resourceId,
      method: "get",
      params:data
    })
  }
  export function bkResourceDetail(data) {
    return request({
      url: "/bk/portal/resource/"+data.resourceId,
      method: "get",
    })
  }
  export function bkResourceLike(data) {
    return request({
      url: "/bk/portal/resource/like/"+data.resourceId,
      method: "put",
    })
  }
  export function bkResourceFavourite(data) {
    return request({
      url: "/bk/portal/resource/favourite/"+data.resourceId,
      method: "put",
    })
  }
  //{resourceId}
  export function bkResourceEdit(data) {
    return request({
      url: "/rcs/admin/resource/"+data.resourceId,
      method: "put",
      data
    })
  }
  export function bkResourceDel(data) {
    return request({
      url: "/rcs/admin/resource/"+data.resourceId,
      method: "delete",
     
    })
  }

  export function bkResourceShare(data) {
    return request({
      url: "/bk/portal/resource/share/"+data.resourceId,
      method: "put",
     
    })
  }

  export function basketAdd(data) {
    return request({
      url: "/bk/set/join/"+data.resourceId,
      method: "post",
     
    })
  }
  export function basketDel(data) {
    return request({
      url: "/bk/set/quit/"+data.resourceId,
      method: "post",
     
    })
  }
  export function basketList(data) {
    return request({
      url: "/bk/set",
      method: "get",
     
    })
  }
  export function basketDelAll(data) {
    return request({
      url: "/bk/set",
      method: "delete",
     
    })
  }
  //
  export function packageAdd(data) {
    return request({
      url: "/bk/package",
      method: "post",
     data
    })
  }
  export function packageEdit(data) {
    return request({
      url: "/bk/package/"+data.packageId,
      method: "put",
     data
    })
  }
  export function packageList(data) {
    return request({
      url: "/bk/package",
      method: "get",
     params:data
    })
  }
  export function packageDetail(data) {
    return request({
        url: "/bk/package/"+data.packageId,
      method: "get",
    })
  }
  
  export function packageBatchDel(data) {
    return request({
      url: "/bk/package/batch",
      method: "delete",
     data:data
    })
  }
  //
  export function packageBatchShare(data) {
    return request({
      url: "/bk/package/share",
      method: "post",
     data
    })
  }
  export function packageMember(data) {
    return request({
      url: "/bk/package/member/search",
      method: "get",
     params:data
    })
  }

  export function makeWhiteBoard(data) {
    return request({
      url: "/bk/group/"+data.groupId+"/whiteboard",
      method: "get",
     
    })
  }
  //
  export function bkGropuMy(data) {
    return request({
      url: "/bk/group/my",
      method: "get",
     params:data
    })
  }
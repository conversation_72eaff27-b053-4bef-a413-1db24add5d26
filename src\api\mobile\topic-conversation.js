import { request } from "@/utils/service"

// 主题交流列表
export function getTopicConversationListApi(params) {
  return request({
    url: "xzs/club-post/me",
    method: "get",
    params
  })
}

// 创建主题交流贴
export function createTopicConversationApi(data) {
  return request({
    url: "xzs/club-post/me",
    method: "post",
    data
  })
}

// 发表评论
export function createTopicConversationCommentApi(postId, data) {
  return request({
    url: `xzs/club-post/${postId}/comment`,
    method: "post",
    data
  })
}

// 评论列表
export function getTopicConversationCommentListApi(postId, params) {
  return request({
    url: `xzs/club-post/${postId}/pageable-comment`,
    method: "get",
    params
  })
}

// 获取主题交流详情
export function getTopicConversationDetailApi(clubPostId) {
  return request({
    url: `xzs/club-post/me/${clubPostId}`,
    method: "get"
  })
}


// 组列表
export function getGroupListApi() {
  return request({
    url: "xzs/club-post/groups",
    method: "get"
  })
}

// 删除主题交流贴
export function deleteTopicConversationApi(clubPostId) {
  return request({
    url: `xzs/club-post/me/${clubPostId}`,
    method: "delete"
  })
}

// 编辑主题交流贴
export function editTopicConversationApi(clubPostId, data) {
  return request({
    url: `xzs/club-post/me/${clubPostId}`,
    method: "put",
    data
  })
}

// 删除评论
export function deleteTopicConversationCommentApi(commentId) {
  return request({
    url: `xzs/club-post-comment/${commentId}`,
    method: "delete"
  })
}

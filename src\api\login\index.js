import { request } from "@/utils/service"

/** 登录并返回 Token */
export function loginApi(data) {
  return request({
    url: "login",
    method: "post",
    data
  })
}

/** 获取用户详情 */
export function getUserInfoApi() {
  return request({
    url: "user-details",
    method: "get"
  })
}
export function logoutapi(){
  return request({
     url:'/logout',
      method: "get",
    
    })
}

// 获取图形验证码
export function getCaptchaApi() {
  return request({
    url: "captcha-image",
    method: "get"
  })
}

// 重置密码
export function resetPasswordApi(data) {
  return request({
    url: "reset-password",
    method: 'patch',
    data
  })
}
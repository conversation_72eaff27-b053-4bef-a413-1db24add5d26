
import { request } from "@/utils/service"

export function jyfxList(params) {
  return request({
    url: "/rcs/jyfx/resource",
    method: "get",
    params
  })
}

export function jyfxEdit(params) {
    return request({
      url: "/rcs/jyfx/resource",
      method: "put",
      data:params
    })
  }
export function jyfxDetail(params) {
    return request({
      url: "/rcs/jyfx/resource/"+params.resourceId,
      method: "get",
    })
  }
  export function jyfxDetailRecommend(params) {
    return request({
      url: "/rcs/jyfx/resource/recommend/"+params.resourceId,
      method: "get",
    })
  }

  export function jyfxFavourite(params) {
    return request({
      url: "/rcs/jyfx/resource/favourite/"+params.resourceId,
      method: "put",
    })
  }
  export function jyfxLike(params) {
    return request({
      url: "/rcs/jyfx/resource/like/"+params.resourceId,
      method: "put",
    })
  }
  export function jyfxShare(params) {
    return request({
      url: "/rcs/jyfx/resource/share/"+params.resourceId,
      method: "put",
    })
  }
  ///rcs/jyfx//{resourceId}
  export function jyfxAnalysis(params) {
    return request({
      url: "/rcs/jyfx/analysis/"+params.resourceId,
      method: "post",
    })
  }
  //
  // {resourceId}
  export function jyfxEvaluate(params) {
    return request({
      url: "/rcs/jyfx/resource/evaluate",
      method: "post",
      data:params
    })
  }
  export function jyfxEvaluates(params) {
    
    return request({
      url: "/rcs/jyfx/resource/evaluate/"+params.resourceId,
      method: "get",
      params
    })
  }
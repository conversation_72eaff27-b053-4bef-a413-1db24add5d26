import{aC as E,aE as A,X as H,r as g,h as P,Y as $,ag as r,z as p,A as d,B as o,Q as i,u as l,J as C,I as w,P as v,a6 as _,H as F,E as j,R as k}from"./vue-CelzWiMA.js";import{_ as J}from"./sortby-BLbqg64u.js";import{s as M}from"./element-BwwoSxMX.js";import{R as Q}from"./resource-Dzm2z5cO.js";import{o as X,bg as Y,bh as G,bi as K,bj as W,bk as Z}from"./index-DCRFd5B4.js";import{j as ee}from"./wisdom-B9T_kFnx.js";import{_ as te}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./emptyvideo-23xdlzjC.js";import"./r-comment-B0JHErW1.js";import"./vant-C4eZMtet.js";const ae={class:"content-box question mt10 flex_start_0"},le={class:"left"},se={class:"title flex_between"},ne={class:"right flex_1 flex_column flex_start_0"},oe={class:"select_form flex_between"},ie={class:"flex_start"},re={class:"flex_start change-tab"},de={class:"question-list flex_start flex_wrap"},ue={__name:"index",setup(pe){let N=X(),{questionCategory:ce,questionDegree:ge,stages:me,ResourceCategory:fe,currentInfo:ve}=E(N),z=A(),t=H({name:"",belongToTenantId:"",stage:"",category:"",subjectId:"",editionId:"",volumeId:"",chapter1Id:"",chapter2Id:"",orderBy:"1",orderRule:"DESC",pageSize:9,pageNum:1,analysis:""});const T=[{id:"0",title:"未提交"},{id:"1",title:"分析中"},{id:"2",title:"已分析"}];let c=g(0),I=g([]),O={children:"children"},b=g([]),B=g([{value:"10",label:"幼儿园",type:"stage"},{value:"20",label:"小学",type:"stage"},{value:"30",label:"初中",type:"stage"},{value:"40",label:"高中",type:"stage"}]),V=async(a,e)=>{if(a.level==0)e([...B.value]);else if(a.level==1){let s=await G({stage:a.data.value});s&&e([...s.data])}else if(a.level==2){let s=await K({stage:a.parent.data.value,subjectId:a.data.value});s&&e([...s.data])}else if(a.level==3){let s=await W({editionId:a.data.value});s&&e([...s.data])}else if(a.level==4){let s=await Z({volumeId:a.data.value});s&&e([...s.data])}else a.level==5?e(a.data.children?a.data.children:[]):e([])},S=async()=>{let a=await Y();a&&(a.data.unshift({isAdmin:!1,tenantId:"0",tenantName:"个人"}),a.data.unshift({isAdmin:!1,tenantId:"",tenantName:"全部"}),b.value=a.data)},R=a=>{Object.assign(t,{pageNum:a}),u()},h=a=>{Object.assign(t,{pageNum:1,orderBy:a}),u()},m=a=>{Object.assign(t,{pageNum:1}),u()};const L=(a,e)=>{Object.assign(t,{stage:"",subjectId:"",editionId:"",volumeId:"",chapter1Id:"",chapter2Id:"",pageSize:9,pageNum:1}),e.level==1?Object.assign(t,{stage:a.value}):e.level==2?Object.assign(t,{subjectId:a.value}):e.level==3?Object.assign(t,{editionId:a.value}):e.level==4?Object.assign(t,{volumeId:a.value}):e.level==5?Object.assign(t,{chapter1Id:a.value}):e.level==6&&Object.assign(t,{chapter2Id:a.value}),u()};let y=async()=>{z.push("/resource/wisdom/check")},u=async()=>{let a={pageNum:t.pageNum,pageSize:t.pageSize,stageId:t.stage,subjectId:t.subjectId,editionId:t.editionId,bookId:t.volumeId,chapterId:t.chapter2Id?t.chapter2Id:t.chapter1Id?t.chapter1Id:"",title:t.name,order:t.orderBy,belongToTenantId:t.belongToTenantId,analysis:t.analysis},e=await ee(a);e&&(e.data.list.forEach(s=>{s.url=s.sealImg,s.name=s.title,s.favorites=s.favoriteCount,s.views=s.viewCount}),I.value=e.data.list,c.value=e.data.total)};return P(()=>{S()}),$(()=>{u()}),(a,e)=>{const s=r("el-tree"),U=r("el-input"),f=r("el-option"),x=r("el-select"),q=r("el-empty"),D=r("el-pagination");return d(),p("div",ae,[o("div",le,[o("div",se,[e[7]||(e[7]=o("p",null,"智慧课堂",-1)),o("p",{class:"gocheck",onClick:e[0]||(e[0]=(...n)=>l(y)&&l(y)(...n))}," 前往审核列表")]),i(s,{style:{"max-width":"600px"},load:l(V),lazy:"",props:l(O),onNodeClick:L},null,8,["load","props"])]),o("div",ne,[o("div",oe,[o("div",ie,[i(U,{modelValue:l(t).name,"onUpdate:modelValue":e[1]||(e[1]=n=>l(t).name=n),style:{width:"240px","margin-right":"16px"},placeholder:"搜索课程","suffix-icon":l(M),onChange:l(m)},null,8,["modelValue","suffix-icon","onChange"]),i(x,{modelValue:l(t).belongToTenantId,"onUpdate:modelValue":e[2]||(e[2]=n=>l(t).belongToTenantId=n),placeholder:"全部",size:"large",style:{width:"160px","margin-right":"16px"},onChange:l(m)},{default:w(()=>[(d(!0),p(v,null,_(l(b),n=>(d(),F(f,{key:n.tenantId,label:n.tenantName,value:n.tenantId},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"]),i(x,{modelValue:l(t).analysis,"onUpdate:modelValue":e[3]||(e[3]=n=>l(t).analysis=n),placeholder:"全部",size:"large",clearable:"",onChange:l(m),style:{width:"160px"}},{default:w(()=>[i(f,{label:"全部",value:""}),(d(),p(v,null,_(T,n=>i(f,{key:n.id,label:n.title,value:n.id},null,8,["label","value"])),64))]),_:1},8,["modelValue","onChange"])])]),o("div",re,[e[8]||(e[8]=o("img",{src:J,alt:""},null,-1)),o("p",{class:j(l(t).orderBy=="1"?"active":""),onClick:e[4]||(e[4]=n=>l(h)("1"))},"按播放量",2),o("p",{class:j(l(t).orderBy=="2"?"active":""),onClick:e[5]||(e[5]=n=>l(h)("2"))},"按上传时间",2)]),o("div",de,[(d(!0),p(v,null,_(l(I),n=>(d(),p("div",{class:"question-item",key:n.id},[i(Q,{row:n},null,8,["row"])]))),128))]),C(i(q,{description:"暂无数据",style:{"margin-top":"10px"},class:"flex_1"},null,512),[[k,l(c)==0]]),C(i(D,{"current-page":l(t).pageNum,background:"",layout:"total,prev, pager, next",total:l(c),"page-size":l(t).pageSize,"onUpdate:pageSize":e[6]||(e[6]=n=>l(t).pageSize=n),class:"mt-4",onCurrentChange:l(R)},null,8,["current-page","total","page-size","onCurrentChange"]),[[k,l(c)!=0]])])])}}},Ne=te(ue,[["__scopeId","data-v-f0d025e7"]]);export{Ne as default};

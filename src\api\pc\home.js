import { request } from "@/utils/service"

export function getProfileApi(data) {
  return request({
    url: "xzs/homepage/profile",
    method: "get",
    data
  })
}

export function getLatestActivityApi(data) {
  return request({
    url: "xzs/homepage/top-activity",
    method: "get",
    data
  })
}

export function getSixStarRecordApi(data) {
  return request({
    url: "xzs/homepage/top-six-star-record",
    method: "get",
    data
  })
}
//需要去除
export function getActiveUserApi(data) {
  return request({
    url: "xzs/homepage/top-active-user",
    method: "get",
    data
  })
}

// 获取社团
export function getClubApi(data) {
  return request({
    url: "xzs/club",
    method: "get",
    data
  })
}

// 获取好书推荐
export function getRecommendBooksApi(data) {
  return request({
    url: "xzs/homepage/top-book",
    method: "get",
    data
  })
}


export function getHomePageProfileApi(data) {
  return request({
    url: "/homepage/profile",
    method: "get",
    data
  })
}
export function getHomePageHotResource(data) {
  return request({
    url: "/homepage/hot-resource",
    method: "get",
    params:data
  })
}
export function getHomePageNewResource(data) {
  return request({
    url: "/homepage/new-resource",
    method: "get",
    params:data
  })
}
export function getHomePageRecommendResource(data) {
  return request({
    url: "/homepage/recommended-resource",
    method: "get",
    params:data
  })
}
///
export function getHomeTimeAble(data) {
  return request({
    url: "/homepage/timetable",
    method: "get",
    params:data
  })
}
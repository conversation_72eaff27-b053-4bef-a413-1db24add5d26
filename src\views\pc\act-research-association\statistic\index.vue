<template>
    <div class="content-box flex_start_0 mt10">
       
       <div class="left">
        
          <StaticLeft  />
       </div>
       <div class="flex_1 right">
           <router-view></router-view>
           
       </div>
       
    </div>
</template>
<script setup>
import {onMounted, reactive, ref,onActivated, } from 'vue'
import {themeList} from '@/api/pc/index.js'
import StaticLeft from '@/views/pc/component/act-research-association/statisticLeft.vue'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'
import {useRouter} from 'vue-router'
let userInfo=useUserInfoStoreHook()
let {themeScrollTop,}=storeToRefs(userInfo)
let router=useRouter()


onMounted(()=>{
//  themes.value=[]
})


</script>
<style lang="scss" scoped>

.mt10{
    margin-top: 10px;
}
.left{
    width: 180px;
}
.right{
    margin-left: 10px;
    .ipt{
        width: 400px;
        height: 40px;
        background: #FFFFFF;
        border-radius: 20px;
        padding: 2px 20px;
        input{
            border:none;
            outline: none;
            width: 100%;
        }
        
    }
   button{
    width: 120px;
height: 40px;
background: #508CFF;
border-radius: 20px;
border:none;
color: #fff;
margin-left: 10px;cursor: pointer;
line-height:40px;
img{
    width: 12px;
    height: auto;
    margin-right: 8px;
}
   }
   .themes{
      max-height: calc(100vh - 200px);
      margin-bottom: 10px;
   }
}
</style>
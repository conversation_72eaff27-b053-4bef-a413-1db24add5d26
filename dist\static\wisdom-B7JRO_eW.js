import{P as O,a as j,b as q,S as M}from"./index.min-B4KxBPKy.js";import{az as H,r as g,X as z,h as K,ag as v,z as V,A as _,B as l,K as R,H as X,O as d,u as t,Q as n,I as u,P as B,a6 as Q,E as Y,m as G}from"./vue-CelzWiMA.js";import{e as J}from"./emptyvideo-23xdlzjC.js";import{m as W,a as Z,g as $}from"./wisdom-D2h0XiM3.js";import{d as ee}from"./default-avatar-DU3ymOCx.js";import{k as te}from"./index-B8SKqXT3.js";import{_ as le}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{d as oe,b as se}from"./vant-C4eZMtet.js";import"./element-BwwoSxMX.js";const ae={class:"detail"},re={class:"title"},ne={class:"time"},ie={class:"flex_start time"},de={style:{width:"50%"}},ue={style:{width:"50%"}},me={class:"flex_start time"},ce={style:{width:"50%"}},fe={style:{width:"50%"}},ge={key:0,class:"form"},pe={class:"flex_start item"},ve={class:"flex_start item"},_e={class:"flex_start item"},he={class:"flex_start item"},xe={class:"flex_start item"},Ve={class:"flex_start item"},ye={class:"flex_start item"},we={class:"flex_start item"},ke={class:"flex_start item"},Ae={class:"flex_start item"},Ee={class:"flex_center"},be=["src"],Ce={class:"flex_1"},ze={class:"top-line flex_between"},Re={class:"flex_start person-info"},Ue={class:"comment-content"},Se={__name:"wisdom",setup(De){const y=te();let m="",U=H(),i=g({}),h="",c=g(!1),A=g(!1),w={id:"video",width:"100%",height:"100%",url:"",poster:"",autoplay:!1,autoplayMuted:!1,videoInit:!0,playsinline:!0,defaultPlaybackRate:1,volume:.72,loop:!1,startTime:0,videoAttributes:{},lang:"zh-cn",fluid:!0,fitVideoSize:"fixed",videoFillMode:"auto",seekedStatus:"play",thumbnail:null,marginControls:!1,domEventType:"default",icons:{},i18n:[],controls:!0,miniprogress:!0,screenShot:!1,rotate:!0,download:!1,pip:!1,mini:!1,cssFullscreen:!0,playbackRate:[.5,1,1.5,2,3],playbackRate:!0,keyShortcut:!1};const S=()=>{m=new O({...w}),m&&(m.on(j,a=>{}),m.on(q,a=>{}),m.on("loadedmetadata",a=>{}),m.on(M,a=>{}))};let D=async()=>{let a=await W({resourceId:h});a&&(i.value=a.data,w.url=i.value.fileUrl,w.poster=i.value.sealImg?i.value.sealImg:J,S())},E=g(),s=z({targetAccurate:0,targetAchievement:0,contentAccurate:0,contentLogic:0,contentInteresting:0,methodDiversity:0,methodEffective:0,studentEngagement:0,homeworkCompletion:0,examResults:0,evaluate:""}),f=g([]),b=g(0),x=z({pageSize:10,pageNum:1}),I=()=>{var a;((a=f.value)==null?void 0:a.length)>=b.value?A.value=!0:(Object.assign(x,{pageNum:x.pageNum+1}),k())},k=async()=>{c.value=!0;let a=await Z({resourceId:h,...x});a&&(b.value=a.data.total,f.value=f.value.concat([...a.data.list])),c.value=!1},T={targetAccurate:[{required:!0,message:"请选择",trigger:"change"},{validator:(a,e,r)=>{s.targetAchievement==0||s.targetAccurate==0?r(new Error("填写完整教学目标诊断")):r()},trigger:"change"}],contentAccurate:[{required:!0,message:"请选择",trigger:"change"},{validator:(a,e,r)=>{s.contentAccurate==0||s.contentLogic==0||s.contentInteresting==0?r(new Error("填写完整教学内容诊断")):r()},trigger:"change"}],methodDiversity:[{required:!0,message:"请选择",trigger:"change"},{validator:(a,e,r)=>{s.methodDiversity==0||s.methodEffective==0||s.studentEngagement==0?r(new Error("填写完整教学方法诊断")):r()},trigger:"change"}],homeworkCompletion:[{required:!0,message:"请选择",trigger:"change"},{validator:(a,e,r)=>{s.homeworkCompletion==0||s.examResults==0?r(new Error("填写完整教学效果诊断")):r()},trigger:"change"}]},C=()=>{E.value.validate(async a=>{a&&oe({title:"提示",message:"确认提交评价吗"}).then(async()=>{await $({resourceId:h,...s})&&(se("评价成功"),i.value.isEvaluate=!0,Object.assign(x,{pageNum:1}),f.value=[],k())}).catch(()=>{})})};return K(()=>{h=U.query.id,D(),k()}),(a,e)=>{const r=v("el-rate"),p=v("el-form-item"),L=v("el-input"),N=v("el-form"),P=v("van-list");return _(),V("div",null,[e[23]||(e[23]=l("div",{class:"video",id:"video",style:{width:"100%"}},null,-1)),l("div",ae,[l("h3",re,d(t(i).title),1),l("p",ne,d(t(i).createTime?t(i).createTime.slice(0,10):""),1),l("div",ie,[l("p",de,"播放量："+d(t(i).viewCount),1),l("p",ue,"分享次数："+d(t(i).shareCount),1)]),l("div",me,[l("p",ce,"资源作者："+d(t(i).createTitle),1),l("p",fe,"作者单位："+d(t(i).schoolTitle),1)])]),!t(i).isEvaluate&&t(y).userInfo.globalRoles&&(t(y).userInfo.globalRoles.indexOf("EXPERT")>-1||t(y).userInfo.globalRoles.indexOf("DIRECTOR")>-1)?(_(),V("div",ge,[n(N,{model:t(s),"label-position":"top",ref_key:"evaluateFormRef",ref:E,rules:t(T)},{default:u(()=>[n(p,{label:"教学目标诊断",prop:"targetAccurate"},{default:u(()=>[l("div",pe,[e[13]||(e[13]=l("p",{class:"flex_start"},"目标明确性",-1)),n(r,{modelValue:t(s).targetAccurate,"onUpdate:modelValue":e[0]||(e[0]=o=>t(s).targetAccurate=o),size:"large"},null,8,["modelValue"])]),l("div",ve,[e[14]||(e[14]=l("p",{class:"flex_start"},"目标达成度",-1)),n(r,{modelValue:t(s).targetAchievement,"onUpdate:modelValue":e[1]||(e[1]=o=>t(s).targetAchievement=o),size:"large"},null,8,["modelValue"])])]),_:1}),n(p,{label:"教学内容诊断",prop:"contentAccurate"},{default:u(()=>[l("div",_e,[e[15]||(e[15]=l("p",{class:"flex_start"},"内容准确性",-1)),n(r,{modelValue:t(s).contentAccurate,"onUpdate:modelValue":e[2]||(e[2]=o=>t(s).contentAccurate=o),size:"large"},null,8,["modelValue"])]),l("div",he,[e[16]||(e[16]=l("p",{class:"flex_start"},"内容逻辑性",-1)),n(r,{modelValue:t(s).contentLogic,"onUpdate:modelValue":e[3]||(e[3]=o=>t(s).contentLogic=o),size:"large"},null,8,["modelValue"])]),l("div",xe,[e[17]||(e[17]=l("p",{class:"flex_start"},"内容趣味性",-1)),n(r,{modelValue:t(s).contentInteresting,"onUpdate:modelValue":e[4]||(e[4]=o=>t(s).contentInteresting=o),size:"large"},null,8,["modelValue"])])]),_:1}),n(p,{label:"教学方法诊断",prop:"methodDiversity"},{default:u(()=>[l("div",Ve,[e[18]||(e[18]=l("p",{class:"flex_start"},"方法多样性",-1)),n(r,{modelValue:t(s).methodDiversity,"onUpdate:modelValue":e[5]||(e[5]=o=>t(s).methodDiversity=o),size:"large"},null,8,["modelValue"])]),l("div",ye,[e[19]||(e[19]=l("p",{class:"flex_start"},"方法有效性",-1)),n(r,{modelValue:t(s).methodEffective,"onUpdate:modelValue":e[6]||(e[6]=o=>t(s).methodEffective=o),size:"large"},null,8,["modelValue"])]),l("div",we,[e[20]||(e[20]=l("p",{class:"flex_start"},"学生参与度",-1)),n(r,{modelValue:t(s).studentEngagement,"onUpdate:modelValue":e[7]||(e[7]=o=>t(s).studentEngagement=o),size:"large"},null,8,["modelValue"])])]),_:1}),n(p,{label:"教学效果诊断",prop:"homeworkCompletion"},{default:u(()=>[l("div",ke,[e[21]||(e[21]=l("p",{class:"flex_start"},"作业完成情况",-1)),n(r,{modelValue:t(s).homeworkCompletion,"onUpdate:modelValue":e[8]||(e[8]=o=>t(s).homeworkCompletion=o),size:"large"},null,8,["modelValue"])]),l("div",Ae,[e[22]||(e[22]=l("p",{class:"flex_start"},"考试成绩",-1)),n(r,{modelValue:t(s).examResults,"onUpdate:modelValue":e[9]||(e[9]=o=>t(s).examResults=o),size:"large"},null,8,["modelValue"])])]),_:1}),n(p,{label:"总体评价"},{default:u(()=>[n(L,{placeholder:"请输入",modelValue:t(s).evaluate,"onUpdate:modelValue":e[10]||(e[10]=o=>t(s).evaluate=o),type:"textarea",resize:"none",rows:"6"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),l("div",Ee,[l("button",{class:"submit",onClick:e[11]||(e[11]=(...o)=>t(C)&&t(C)(...o))},"提交评价")])])):R("",!0),t(f).length>0?(_(),X(P,{key:1,class:"comment-list",loading:t(c),"onUpdate:loading":e[12]||(e[12]=o=>G(c)?c.value=o:c=o),finished:t(A),"finished-text":"没有更多了",onLoad:t(I)},{default:u(()=>[(_(!0),V(B,null,Q(t(f),o=>(_(),V("div",{class:Y(["comment-item",o.evaluate?"flex_start_0":"flex_start"]),key:o.id},[l("img",{src:o.userAvatarUrl||t(ee),alt:"",class:"teacher-logo"},null,8,be),l("div",Ce,[l("div",ze,[l("div",Re,[l("h3",null,d(o.userName),1),l("p",null,d(o.createTime),1)]),n(r,{modelValue:o.finalScore,"onUpdate:modelValue":F=>o.finalScore=F,size:"large",disabled:""},null,8,["modelValue","onUpdate:modelValue"])]),l("p",Ue,d(o.evaluate),1)])],2))),128))]),_:1},8,["loading","finished","onLoad"])):R("",!0)])}}},Be=le(Se,[["__scopeId","data-v-f465e216"]]);export{Be as default};

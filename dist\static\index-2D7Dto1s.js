import{aE as i,az as n,r as l,h as _,z as d,A as m,B as t,u,O as o}from"./vue-CelzWiMA.js";import{d as p}from"./default-avatar-DU3ymOCx.js";import{v as h,b as v}from"./message-B_sWJld4.js";import{_ as f}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DCRFd5B4.js";import"./element-BwwoSxMX.js";import"./vant-C4eZMtet.js";const g={class:"message-detail"},x={class:"header"},y={class:"flex-start-center"},w=["src"],N={class:"flex-column-center-start"},A={class:"name grey1 m-text-14-20-600"},B={class:"time grey5 m-text-12-18-400"},D={class:"content m-pt14"},M=["innerHTML"],T={__name:"index",setup(k){i();const a=n(),c=async()=>{try{await h(a.query.id)}catch(e){}},s=l({}),r=async()=>{try{const e=await v(a.query.id);s.value=e.data}catch(e){}};return _(()=>{c(),r()}),(e,q)=>(m(),d("div",g,[t("div",x,[t("div",y,[t("img",{src:u(p),alt:"头像",class:"avatar m-mr8",width:"32",height:"32"},null,8,w),t("div",N,[t("span",A,o(s.value.userName),1),t("span",B,o(s.value.postTime),1)])])]),t("div",D,[t("span",{class:"title m-text-14-20-400",innerHTML:s.value.content},null,8,M)])]))}},O=f(T,[["__scopeId","data-v-437232ee"]]);export{O as default};

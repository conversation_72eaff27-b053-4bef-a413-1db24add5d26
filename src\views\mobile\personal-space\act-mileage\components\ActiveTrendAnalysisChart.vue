<script setup>
import { ref, watch, onMounted } from "vue"
import { EchartsUI, useEcharts } from "@/components/Echart"

const chartRef = ref()
const { renderEcharts } = useEcharts(chartRef)

const props = defineProps({
  chartData: {
    type: Object,
    default: () => ({
      data: []  // 活跃趋势数据，包含日期和值
    })
  }
})

watch(
  () => props.chartData,
  () => {
    paintChart()
  },
  { deep: true }
)

onMounted(() => {
  paintChart()
})

const paintChart = () => {
  const chartData = props.chartData.data || []
  
  // 提取日期和数值
  const dates = chartData.map(item => item.date) // 只显示月-日部分
  const values = chartData.map(item => item.value)
  
  // 计算Y轴最大值，向上取整到50的倍数
  const maxValue = Math.max(...values, 0)
  const yAxisMax = Math.ceil(maxValue / 50) * 50

  // 判断是否需要滑动
  const needScroll = dates.length > 7

  renderEcharts({
    backgroundColor: "transparent",
    grid: {
      left: '5%',
      right: '6%',
      bottom: needScroll ? 40 : '3%', // 给dataZoom留空间
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      boundaryGap: false,
      axisLine: {
        lineStyle: {
          color: '#EAEDF2'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#94959C',
        fontSize: 12,
        align: 'center',
        interval: 0, // 全部显示
        formatter: function (value) {
          return value
        }
      }
    },
    yAxis: {
      type: 'value',
      max: yAxisMax,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#EAEDF2',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#94959C',
        fontSize: 12
      }
    },
    series: [
      {
        data: values,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#508CFF'
        },
        lineStyle: {
          color: '#508CFF',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(80, 140, 255, 0.6)' },
              { offset: 1, color: 'rgba(80, 140, 255, 0.05)' }
            ]
          }
        },
        markLine: {
          silent: true,
          lineStyle: {
            color: '#FF4D4F',
            type: 'solid',
            width: 1
          },
          data: [
            {
              xAxis: '5.21',
              label: {
                show: false
              }
            }
          ]
        }
      }
    ],
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}步',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      borderWidth: 0,
      textStyle: {
        color: '#fff'
      }
    },
    dataZoom: needScroll
      ? [
          {
            type: 'inside',
            xAxisIndex: 0,
            start: 0,
            end: 100 * 7 / dates.length,
            minValueSpan: 7,
            maxValueSpan: 7
          }
        ]
      : []
  })
}
</script>

<template>
  <EchartsUI ref="chartRef" :height="'201px'" />
</template>

<style scoped></style>

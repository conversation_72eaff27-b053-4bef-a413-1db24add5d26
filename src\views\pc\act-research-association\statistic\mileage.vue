<template>
    <div class="flex_between_0 flex_wrap">
        <div class="echart-box flex_start_0 flex_column">
            <p class="title flex_start">社员里程排名</p>
            <ul class="flex_between split">
                <li :class="mileageActive==1?'active':''" @click="choiceMileage(1)">本周</li>
                <li :class="mileageActive==2?'active':''" @click="choiceMileage(2)">每月</li>
                <li :class="mileageActive==3?'active':''" @click="choiceMileage(3)">历史</li>
            </ul>
            <div class="rank-list">
                <div class="flex_start rank" v-for="(item,index) in mileageList" :key="item.userId">
                    <img src="@/assets/pc/score/first.png" alt="" v-if="index==0">
                    <img src="@/assets/pc/score/first.png" alt="" v-if="index==1">
                    <img src="@/assets/pc/score/first.png" alt="" v-if="index==2">
                    <div class="rank-number" v-if="index>2">
                        <h3>{{index+1}}</h3>
                    </div>
                    <div class="flex_1">
                        <div class="flex_between">
                            <div class="flex_start">
                                <p class="name">{{item.userName}}</p>
                                <img src="@/assets/pc/score/good.png" alt="" v-show="item.isAwesomeAuthor" />
                                 <img src="@/assets/pc/score/star.png" alt="" v-show="item.isActiveStar" />
                            </div>
                            <p class="steps">{{item.points}}</p>
                        </div>
                        <el-progress :percentage="item.percent" class="progress"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="echart-box flex_start_0 flex_column">
            <p class="title flex_start">小组里程排名</p>
            <ul class="flex_between split">
                <li :class="groupActive==1?'active':''" @click="choiceGroup(1)">本周</li>
                <li :class="groupActive==2?'active':''" @click="choiceGroup(2)">每月</li>
                <li :class="groupActive==3?'active':''" @click="choiceGroup(3)">历史</li>
            </ul>
            <div class="flex_1" id="member-group" style="width:100%">
                
            </div>
        </div>
         <div class="echart-box flex_start_0 flex_column">
            <p class="title flex_start">荣誉统计</p>
            <ul class="flex_between split">
                <li :class="honnerActive==1?'active':''" @click="choiceHonner(1)">本周</li>
                <li  :class="honnerActive==2?'active':''" @click="choiceHonner(2)">每月</li>
                <li  :class="honnerActive==3?'active':''" @click="choiceHonner(3)">历史</li>
            </ul>
            <div class="flex_1" id="honer-statistic" style="width:100%">
                
            </div>
        </div>
        <div class="echart-box flex_start_0 flex_column">
            <p class="title flex_start">里程分布</p>
            <ul class="flex_between split">
                <li :class="distributionActive==1?'active':''" @click="choiceDistribution(1)">本周</li>
                <li :class="distributionActive==2?'active':''" @click="choiceDistribution(2)">每月</li>
                <li :class="distributionActive==3?'active':''" @click="choiceDistribution(3)">历史</li>
            </ul>
            <div class="flex_1" id="mileage-distribution" style="width:100%">
                
            </div>
        </div>
        <div class="echart-box flex_start_0 flex_column" style="width:100%">
            <p class="title flex_start">近三十天总里程趋势</p>
            <div class="flex_1" id="thirdty-day" style="width:100%">
                
            </div>
        </div>
    </div>
</template>
<script setup>
import * as echarts from 'echarts';
import {
DirctorMileageWeekRank,DirctorMileageMonthRank,DirctorMileageHistoryRank,
DirctorHonnerWeek,DirctorHonnerMonth,DirctorHonnerHistory,
DirctorGroupWeekRank,DirctorGroupMonthRank,DirctorGroupHistoryRank,
DirctorMileageDistributionWeek,DirctorMileageDistributionMonth,DirctorMileageDistributionHistory,
DirctorMileageThirdtyPoints
} from '@/api/pc/mileage.js'
import {onMounted, ref} from 'vue'
let mileageList=ref([])
let mileageActive=ref('1')
let choiceMileage=async(arg)=>{
    let res=''
    mileageActive.value=arg
    if(mileageActive.value==1){
      res=await DirctorMileageWeekRank()
    }
    if(mileageActive.value==2){
      res=await DirctorMileageMonthRank()
    }
    if(mileageActive.value==3){
      res=await DirctorMileageHistoryRank()
    }
    if(res){
        let arrNum=res.data.map(item=>item.points)
        let max=Math.max(...arrNum)
        res.data.forEach(item=>{
            if(max==0){
                item.percent=0
            }else{
                item.percent=(item.points/max)*100
            }
        })
        mileageList.value=res.data
    }
}

let echartGroup=''
let groupActive=ref('1')
let choiceGroup=async(arg)=>{
      let res=''
      if(arg==1){
        res=await DirctorGroupWeekRank()
      }
      if(arg==2){
        res=await DirctorGroupMonthRank()
      }
      if(arg==3){
        res=await DirctorGroupHistroyRank()
      }
      if(res){
         initEchartGroup(res.data)
      }
}
let initEchartGroup=(row)=>{
    echartGroup = echarts.init(document.getElementById('member-group'));

   let x=row.map(item=>item.clubGroupName)
    let y=row.map(item=>item.points)
    var salvProMax =[];//背景按最大值
    for (let i = 0; i < y.length; i++) {
        salvProMax.push(y[0])
    }
    const color=['#FF8940','#FFAC38','#FFD738','#C4E326','#FF8940','#FFAC38','#FFD738','#C4E326'];
    let option = {
        backgroundColor:"#fff",
    
    grid: {
        left: '2%',
        right: '5%',
        bottom: '0%',
        top: '5%',
        containLabel: true
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'none'
        },
        formatter: function(params) {
            return params[0].name  + ' : ' + params[0].value
        }
    },
    xAxis: {
        show: true,
        type: 'value',
     
         axisLine: {
            show: true,
            
        },
        axisLabel: {   // X轴线 标签修改 
            textStyle: {
                color: '#8DA2B5', //坐标值得具体的颜色
            }
        },
        splitLine:{  
            show: false, // X轴线 颜色类型的修改
            lineStyle: {
                type: 'dashed',
                color: ''
                }  
        },
        axisLine: { 
            show: true, // X轴 网格线 颜色类型的修改
            lineStyle: {
                type: 'solid',
                color: '#E8E9F0'
            }  
        },  
    },
      
    yAxis: [{
        type: 'category',
        inverse: true,
        itemStyle: {
            normal: {
                color: function(params) {
                    return 'red';
                }
            }
        },
        axisLabel: {
            show: true,
            textStyle: {
                color: '#8DA2B5'
            },
        },
        splitLine: {
            show: false
        },
        axisTick: {
            show: false
        },
         axisLine: { 
            show: true, // X轴 网格线 颜色类型的修改
            lineStyle: {
                 type: 'solid',
                 color: '#E8E9F0'
                 }  
            },
        data: x
      }],


         series: [{
            name: '值',
            type: 'bar',
           
            zlevel: 2,
          
            itemStyle: {
                normal: {
                    barBorderRadius: [0,30,30,0],
                    color: (params)=>{
                        
                        return color[params.dataIndex]
                    },
                    
                },
                
            },
            label: {
                show: true,
                position: 'right',
                // 使用与itemStyle相同的颜色逻辑
                formatter: function(params) {
                    return [
                    `{value|${params.value}}`
                    ].join('\n');
                },
                  rich: {
                    name: {
                        color: '#333',
                        fontSize: 14,
                        fontWeight: 'bold'
                    },
                    value: {
                    color: (row)=>{
                        alert(11)
                    },
                    fontSize: 12
                    }
                }
            },
            barWidth: 16,
            data: y
        }
    ]
};
  echartGroup.setOption(option)
}

let honnerActive=ref('1')
let echartHoner=''
let choiceHonner=async(arg)=>{
    let res=''
    honnerActive.value=arg
    if(honnerActive.value==1){
        res=await DirctorHonnerWeek()
    }
    if(honnerActive.value==2){
        res=await DirctorHonnerMonth()
    }
    if(honnerActive.value==3){
        res=await DirctorHonnerHistory()
    }
    if(res){
        initEchartHonner(res.data)
    }
}
let initEchartHonner=(row)=>{
    let x=row.map(item=>item.name)
     let x1=row.map(item=>item.awesomeAuthorNumber)
    let x3=row.map(item=>item.activeStarNumber)
   
  echartHoner = echarts.init(document.getElementById('honer-statistic'));
  let option = {
    backgroundColor: '#fff',
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow'
        },
        formatter:(params)=>{
            return '<div>'+'<p>'+'优质创作者：'+params[0].value+'</p><p>'+'活跃之星：'+params[1].value+'</p>'+'</div>'
        }
    },
    grid: {
        top: '12%',
        right: '0%',
        left: '8%',
        bottom: '20%'
    },
   legend: {
        show: true,
        data: ['优质创作者', '活跃之星'],
        bottom:0,
        left:"center",
        orient: 'horizontal',
        textStyle: { color: '#2B2C33', fontSize: 15 },
        icon: 'circle' 
    },
    xAxis: [{
        type: 'category',
        data:x,
        axisLine: {
            lineStyle: {
                color: 'rgba(255,255,255,0.12)',
                 
            }
        },
        axisLabel: {
            margin: 10,
            color: '#8DA2B5',
           
            textStyle: {
                fontSize: 12,
             
            },
             formatter: function (value, index) {
                        const maxLength = 6; // 每行最大字符数
                        const maxLines = 3; // 最大行数
                        let result = '';
                        for (let i = 0; i < value.length; i += maxLength) {
                            if (Math.ceil(i / maxLength) >= maxLines) {
                                result += '';
                                break;
                            }
                            result += value.slice(i, i + maxLength);
                            if (i + maxLength < value.length) {
                                result += '\n';
                            }
                        }
                        return result
                    }
         },
       }],
        yAxis: [{
            axisLabel: {
                formatter: '{value}',
                color: '#8DA2B5',
                textStyle: {
                    fontSize: 12
                },
            },
            axisLine: {
                show: false
            },
            splitLine: {
                lineStyle: {
                    color: ' #F1F2F5',
                    type:'dashed'
                }
            }
        }],
      
    series: [{
        name:'优质创作者',
        type: 'bar',
        data:x1,
        barWidth: '12px',
        itemStyle: {
            normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: '#FF9C48' // 0% 处的颜色
                }, {
                    offset: 1,
                    color: '#FFDD75' // 100% 处的颜色
                }], false),
                barBorderRadius: [30, 30, 0,0],
               
             
            }
        },
      
    },{
        type: 'bar',
        name:'活跃之星',
        data: x3,
        barWidth: '12px',
        itemStyle: {
            normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: '#88C0FF' // 0% 处的颜色
                }, {
                    offset: 1,
                    color: '#508CFF' // 100% 处的颜色
                }], false),
                barBorderRadius: [30, 30, 0,0],
               
             
            }
        },
      
    }]
  };
  echartHoner.setOption(option)
}
let echartDistribution=''
let distributionActive=ref('1')
let choiceDistribution=async(arg)=>{
    let res=''
    distributionActive.value=arg
    if(arg==1){
      res=await DirctorMileageDistributionWeek()
    }
   if(arg==2){
      res=await DirctorMileageDistributionMonth()
    }
     if(arg==3){
      res=await DirctorMileageDistributionHistory()
    }
    if(res){
       initEchartMileageDistribution(res.data)
    }

}
let initEchartMileageDistribution=(row)=>{
   let total=0
   var scaleData=[]
    row.forEach(item=>{
        total=total+item.points
        
    })
       row.forEach(item=>{
       
         if(item.points>0){
            scaleData.push({
                 name:item.label,
                value:item.points,
                percent:(Number(item.points/total).toFixed(2)*100).toFixed(1).toString()+'%'
            })
         }
    })
    console.log(scaleData,'rowss')
//    var scaleData =[
//       {name:'自我学习进步',value:'13'},
//       {name:'互动交流进步',value:'3'},
//       {name:'阶段成长进步',value:'3'},
//       {name:'行知成果进步',value:'3'},
//       {name:'退步',value:'3'}]

var data = [];
var color=['#EB58B8','#FD9623','#35C0D8','#78C613','#6C86FB','#EB58B8','#FD9623','#35C0D8','#78C613','#6C86FB']
for (var i = 0; i < scaleData.length; i++) {
    data.push({
        value: scaleData[i].value,
        name: scaleData[i].name,
       percent:scaleData[i].percent,
        itemStyle: {
            normal: {
                borderWidth: 0,
                shadowBlur: 1200,
                borderColor:color[i%color.length],
                shadowColor: "#fff",
                color:color[i],
                
            }
        }
    }, {
        value:scaleData.length==1?0:(total/120)*scaleData.length,
        name: '',
        show:false,
        itemStyle: {
            normal: {
                
                label: {
                    show: false
                },
                labelLine: {
                    show: false
                },
                color:'rgba(0, 0, 0, 0)',
                borderColor: 'rgba(0, 0, 0, 0)',
                borderWidth: 0
            }
        }
    });
}

   echartDistribution = echarts.init(document.getElementById('mileage-distribution'));
    let option={
           backgroundColor: '#ffffff',
            tooltip: {
                show: false
            },
         
            legend: {
                show: false
            },
            toolbox: {
                show: false
            },
          
            series: [{
                    name: '',
                    type: 'pie',
                    clockWise: false,
                    radius: [40, 100],
                    hoverAnimation: false,
                    itemStyle: {
                        normal: {
                            color: function(params) {
                                return color[params.dataIndex]
                            }
                        }
            },
            label: {
                
                // formatter: function(params) {
                //          return params.data.name+'\n'+params.data.value+' | '+'20%'
                //     },
                  formatter:(params)=>{
                  
                    if(params.name){
                        if(params.name=='自我学习进步'){
                            return [
                                `{zwxx|${params.name}}`,
                                `{zwxx|${params.data.value} | ${params.data.percent}}`,
                            ].join('\n');
                        }else if(params.name=='互动交流进步'){
                            return [
                                `{hdjl|${params.name}}`,
                                `{hdjl|${params.data.value} | ${params.data.percent}}`,
                            ].join('\n');
                        }else if(params.name=='阶段成长进步'){
                            return [
                                `{jdcz|${params.name}}`,
                                `{hdjl|${params.data.value} | ${params.data.percent}}`,
                            ].join('\n');
                        }else if(params.name=='行知成果进步'){
                            return [
                                `{xzcg|${params.name}}`,
                                `{xzcg|${params.data.value} | ${params.data.percent}}`,
                            ].join('\n');
                        }else if(params.name=='退步'){
                            return [
                                `{tb|${params.name}}`,
                                `{tb|${params.data.value} | ${params.data.percent}}`,
                            ].join('\n');
                        }
                      
                    }
                   
                  },
                  rich:{
                    zwxx:{
                        padding:[0,0,4],
                        color:'#EB58B8'
                    },
                    hdjl:{
                         padding:[0,0,4],
                        color:'#FFA952'
                    },
                    jdcz:{
                        padding:[0,0,4],
                        color:'#FFA952'
                    },
                    xzcg:{
                        padding:[0,0,4],
                        color:'#35C0D8'
                    },
                    tb:{
                            padding:[0,0,4],
                        color:'#7F62FF'
                    }
                    // number:{
                    //     padding:[0,0,4],
                    //     color:(params)=>{
                    //         alert(111)
                    //         console.log(params)
                    //     }
                    // }
                  }
        
            },

            data: data
                }]
            }
   echartDistribution.setOption(option)
}
 let echartThirty=''
 let choiceThirty=async(row)=>{
        let res=await DirctorMileageThirdtyPoints()
        if(res){
           initEchartThirty(res.data) 
        }
 }
 let initEchartThirty=(row)=>{
    let x=row.map(item=>item.date)
    let y=row.map(item=>item.points)
    echartThirty = echarts.init(document.getElementById('thirdty-day'));
    let option = {
    title: {
        text: '',
        textStyle: {
            fontWeight: 'normal',
            fontSize: 16,
            color: '#F1F1F3'
        },
        left: '6%'
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            lineStyle: {
                color: '#57617B'
            }
        }
    },
  
    grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
    },
    xAxis: [{
        type: 'category',
        boundaryGap: false,
        axisLine: {
            lineStyle: {
                color: '#8DA2B5'
            }
        },
        data: x
    }],
    yAxis: [{
        type: 'value',
        name: '',
        axisTick: {
            show: false
        },
        axisLine: {
            lineStyle: {
                color: '#8DA2B5'
            }
        },
        axisLabel: {
            margin: 10,
            textStyle: {
                fontSize: 14
            }
        },
        splitLine: {
            lineStyle: {
                color: '#8DA2B5',
                type:'dashed'

            }
        }
    }],
    series: [{
        name: '里程',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 5,
        showSymbol: false,
        lineStyle: {
            normal: {
                width: 1
            }
        },
        areaStyle: {
            normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: 'rgb(178,238,255)'
                }, {
                    offset: 0.8,
                    color: 'rgb(201,221,255)'
                }], false),
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 10
            }
        },
        itemStyle: {
            normal: {
                color: 'rgb(178,238,255)',
                borderColor: 'rgb(178,238,255)',
                borderWidth: 12

            }
        },
        data: y
    }]
};
    echartThirty.setOption(option)
 }
onMounted(()=>{
  choiceMileage(mileageActive.value)
  choiceGroup(groupActive.value)
//   initEchartGroup(res.data)
//   initEchartHonner(arr2)
  choiceHonner(honnerActive.value)
  choiceDistribution(distributionActive.value)
//   initEchartMileageDistribution()
choiceThirty()
// initEchartThirty()
})
</script>
<style lang='scss' scoped>
.echart-box{
  width: 49.5%;
  min-height: 398px;
  background: #fff;
  margin-bottom: 10px;
  padding: 16px;
  border-radius: 4px;
  .title{
    font-weight: bold;
    font-size: 16px;
    color: #2B2C33;
    line-height: 24px;
    &::before{
       content: '';
       display: block;
       width: 2px;
        height: 14px;
        background: #508CFF;
        border-radius: 1px;
        margin-right: 6px;
    }
  }
  .split{
    height: 32px;
    width: 204px;
    line-height: 32px;
    background: #F0F5FF;
    border-radius: 4px;
    border: 1px solid #508CFF;
    margin-top: 12px;
    li{
        flex:1;
        text-align: center;
        font-size: 14px;
        color: #508CFF;
        cursor: pointer;
    }
    .active{
       background: #508CFF;
        color: #fff;
    }
  }
  .rank-list{
     height:300px;
     overflow:auto;
     margin-top: 20px;
     padding-right: 10px;
      .rank{
        margin-bottom: 20px;
        .rank-number{
            width: 28px;
            font-weight: bold;
             margin-right: 10px;
            font-size: 16px;
            color: #2B2C33;
            line-height: 19px;
            font-weight: bold;
            h3{
                text-align: center;
            }
        }
        &:last-child{
            margin-bottom: 0;
        }
      img{
        width: 28px;
        height: auto;
        margin-right: 10px;
      }
      :deep(.el-progress__text){
        display: none;
      }
      :deep(.el-progress-bar__inner){
       background: linear-gradient( 270deg, #508CFF 0%, #55C2F5 100%);
      }
      .name{
        font-size: 16px;
        color: #2B2C33;
        line-height: 22px;
        font-weight: bold;
        margin-right: 8px;
      }
      .steps{
        font-weight: bold;
        font-size: 16px;
        color: #2B2C33;
      }
      .progress{
        margin-top: 6px;
      }
  }
  }
 
}
</style>
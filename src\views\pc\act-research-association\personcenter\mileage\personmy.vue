<template>
    <div class="top flex_start_0">
        <div class="current-week">
            <div class="title flex_start">
                <img src="@/assets/pc/score/current-week.png" alt="">
                <p>本周里程</p>
            </div>
            <ul class="flex_between flex_wrap">
                <li class="flex_center flex_column">
                      <h3>{{currenWeekDetail.totalPoints}}</h3>
                      <p>本周步数</p>
                </li>
                <li class="line"></li>
                <li class="flex_center flex_column">
                      <h3>{{currenWeekDetail.eventGroups[0]?currenWeekDetail.eventGroups[0].points:0}}</h3>
                      <p>自我学习进步</p>
                </li>
                <li class="line"></li>
                <li class="flex_center flex_column">
                     <h3>{{currenWeekDetail.eventGroups[1]?currenWeekDetail.eventGroups[1].points:0}}</h3>
                      <p>互动交流进步</p>
                </li>
                <li class="flex_center flex_column">
                      <h3>{{currenWeekDetail.eventGroups[2]?currenWeekDetail.eventGroups[2].points:0}}</h3>
                      <p>阶段成长进步</p>
                </li>
                <li class="line"></li>
                <li class="flex_center flex_column">
                       <h3>{{currenWeekDetail.eventGroups[3]?currenWeekDetail.eventGroups[3].points:0}}</h3>
                      <p>行知成果进步</p>
                </li>
                 <li class="line"></li>
                <li class="flex_center flex_column">
                      <h3>{{currenWeekDetail.eventGroups[4]?currenWeekDetail.eventGroups[4].points:0}}</h3>
                      <p>退步</p>
                </li>
            </ul>
        </div>
        <div class="rank flex_1 flex_between flex_column">
            <div class="group current-week">
                  <div class="title flex_start">
                        <img src="@/assets/pc/score/group.png" alt="">
                        <p>社团排名</p>
                  </div>
                  <div class="rank-number flex_center">
                       <p>第</p>
                       <h4>{{rankRow.clubRanking}}</h4>
                       <p>名</p>
                  </div>
            </div>
            <div class="group member current-week">
                  <div class="title flex_start">
                        <img src="@/assets/pc/score/member.png" alt="">
                        <p>小组排名</p>
                  </div>
                  <div class="rank-number flex_center">
                       <p>第</p>
                       <h4>{{rankRow.clubGroupRanking}}</h4>
                       <p>名</p>
                  </div>
            </div>
        </div>
        <div class="flex_1 task current-week">
              <div class="title flex_start">
                    <img src="@/assets/pc/score/task.png" alt="">
                    <p>今日任务</p>
              </div>
                 <div class="task-title flex_start" v-for="item in dailyList" :key="item.dailyTaskValue" @click="goCompleteTask(item)">
                    <p>{{item.label}}</p>
                    <el-icon style="margin-left:10px" v-if="!item.isCompleted"><ArrowRight /></el-icon>
                    <img src="@/assets/pc/score/complete.png" alt="" v-else/>
                 </div>
                 
        </div>
    </div>
    <div class="detail-list">
          <p class="title">里程明细</p>
          <div class="flex_start form">
              <el-select placeholder="里程类型" clearable v-model="searchForm.eventGroup" style="width:160px;margin-right:16px"  @change="choiceEventLog">
                  <el-option :label='item.label' :value='item.value' v-for="item in eventGroupList" :key="item.label"></el-option>
              </el-select>
              <div style="width:248px">
                <el-date-picker
                    @change="choiceEventLog"
                   
                    v-model="searchForm.range"
                    type="daterange"
                    value-format="YYYY-MM-DD"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                />
              </div>
               
          </div>
          <div class="action-list">
              <el-empty description="暂无数据" style="height:320px" v-if="total==0" />
              <div class="action-item flex_between" v-for="(item,index) in tableData" :key="index">
                 <div class="left flex_start">
                       <img src="@/assets/pc/score/type7.png" alt="" v-if="item.points<0" />
                       <img src="@/assets/pc/score/type3.png" alt="" v-else-if="item.eventGroupLabel=='互动交流进步'" />
                       <img src="@/assets/pc/score/type4.png" alt="" v-else-if="item.eventGroupLabel=='阶段成长进步'" />
                       <img src="@/assets/pc/score/type6.png" alt="" v-else-if="item.eventGroupLabel=='行知成果进步'" />
                       <img src="@/assets/pc/score/type1.png" alt="" v-else />
                     <div>
                         <div class="flex_start desc">
                             <h3>{{item.eventTypeLabel}}</h3>
                             <p :class="item.points<0?'p-tb':item.eventGroupLabel=='互动交流进步'?'p-hdjl':item.eventGroupLabel=='阶段成长进步'?'p-jdcz':item.eventGroupLabel=='行知成果进步'?'p-xzcg':''">{{item.eventGroupLabel}}</p>
                         </div>
                         <p class="date">{{item.eventTime}}</p>
                     </div>
                 </div>
                 <div class="right flex_start">
                      <img src="@/assets/pc/score/stepup.png" alt="" v-show="item.points>0">
                      <img src="@/assets/pc/score/stepdown.png" alt="" v-show="item.points<0">
                      <p>{{item.points>0?'+':item.points<0?'-':""}}{{item.points}}步</p>
                 </div>
              </div>
          </div>
         <el-pagination v-if="total!=0" background style="margin-bottom:10px" layout="prev, pager, next" :current-page="searchForm.pageNum" :page-size='searchForm.pageSize' :total="total" @current-change='handlelCurrentChange' />
    </div>
</template>
<script setup>
import {onMounted, reactive,ref} from 'vue'
import {currentWeekProfile,currentDailyProfile,currentWeekProfileRank,dailyProfileLog,eventGroups} from '@/api/pc/mileage.js'
import {useRouter} from 'vue-router'
let router=useRouter()
let rankRow=ref({})
let searchForm=reactive({
    eventGroup:'',
    range:[],
    pageSize:10,
    pageNum:1
})

let eventGroupList=ref([])
let geteventGroups=async()=>{
    let res=await eventGroups()
    if(res){
        console.log(res.data,'eventGroups')
        eventGroupList.value=res.data
    }
}
// 搜索模块
let total=ref(10)
let tableData=ref([])
let choiceEventLog=()=>{
    Object.assign(searchForm,{
        pageNum:1,
    })
    getdailyProfileLog()
}
const handlelCurrentChange=(val)=>{
 Object.assign(searchForm,{
        pageNum:val,
    })
    getdailyProfileLog()
}
let getdailyProfileLog=async()=>{
    let params={
        eventGroup:searchForm.eventGroup,
        beginTime:(searchForm.range&&searchForm.range[0])?(searchForm.range[0]+' 00:00:00').replace(' ','T'):'',
        endTime:(searchForm.range&&searchForm.range[1])?(searchForm.range[1]+' 23:59:59').replace(' ','T'):'',
        pageSize:searchForm.pageSize,
        pageNum:searchForm.pageNum
    }
      let res=await dailyProfileLog(params)
      if(res){
        console.log(res)
        tableData.value=res.data.list
        total.value=res.data.total
      }
}
let getRank=async()=>{
    let res=await currentWeekProfileRank()
    if(res){
        rankRow.value=res.data
    }
}
let goCompleteTask=(row)=>{
    if(row.isCompleted){
        return
    }else{
        if(row.value==1){
            router.push('/actResearchAssociation/personcenter/upload')
        }else{
            router.push('/actResearchAssociation/square/index')
        }
    }
}
//今日任务
let dailyList=ref([])
let getcurrentDailyProfile=async()=>{
    let res=await currentDailyProfile()
    if(res){
        dailyList.value=res.data.dailyTasks
    }
}
//本周里程
let currenWeekDetail=reactive({
    eventGroups:[],
    totalPoints:0
})
let getcurrentWeekProfile=async()=>{
    let res=await currentWeekProfile()
    if(res){
        Object.assign(currenWeekDetail,{
            ...res.data
        })
    }
}
onMounted(()=>{
    getcurrentWeekProfile()
    getcurrentDailyProfile()
    getRank()
    getdailyProfileLog()
    geteventGroups()
})
</script>
<style lang='scss' scoped>
.top{
    .current-week{
        background: url(@/assets/pc/score/current-week-bg.png) no-repeat;
        background-size: 100% 100%;
        width: 60%;
        padding: 20px;
        border-radius: 4px;
         .title{
            font-size: 16px;
            color: #FFFFFF;
            line-height: 24px;
            img{
               width: 24px;
               height: auto;
               margin-right: 8px;
            }
         }
         ul{
            li{
                width:33%;
                margin-top: 20px;
                &:first-child{
                    h3{
                        font-size: 40px;
                    }
                }
                h3{
                    font-weight: bold;
                    font-size: 24px;
                    color: #FFFFFF;
                    line-height: 30px;
                }
                p{
                    font-size: 14px;
                    color: #FFFFFF;
                    line-height: 20px;
                    margin-top: 4px;
                }
            }
            .line{
                  width: 1px;
                  height: 32px;
                  background: #fff;
            }
         }
    }
    .rank{
        margin-left: 10px;
         .group{
            padding: 16px 16px 20px  16px;
            width: 100%;
             background: url(@/assets/pc/score/group-bg.png) no-repeat;
             .rank-number{
                margin-top: 13px;
                p{
                    font-size: 16px;
                    color: #FFFFFF;
                    line-height: 22px;
                }
                h4{
                    font-weight: bold;
                    font-size: 32px;
                    color: #FFFFFF;
                    line-height: 46px;
                    margin: 0 12px;
                }
             }
        }
         .member{
            padding: 16px 16px 20px  16px;
             width: 100%;
             background: url(@/assets/pc/score/member-bg.png) no-repeat;
        }
        .member{
            margin-top: 10px;
        }
    }
    .task{
         background: url(@/assets/pc/score/task-bg.png) no-repeat;
         background-size: 100% 100%;
         margin-left: 10px;
         .title{
            margin-bottom: 6px;
         }
         .task-title{
            width: 100%;
            font-size: 16px;
            color: #FFFFFF;
            line-height: 22px;
            color: #fff;
            margin-top: 24px;
            cursor: pointer;
            &:hover{
                text-decoration: underline;
            }
            img{
                width: 14px;
                height: auto;
                margin-left: 10px;
            }
         }
    }
}
.detail-list{
    padding: 16px;
    margin-top: 10px;
    background: #fff;
    .title{
        font-size: 16px;
        color: #2B2C33;
        line-height: 24px;
        font-weight: bold;
    }
    .form{
        margin-top: 10px;
        :deep(.el-select__wrapper){
            height: 40px;
        }
        :deep(.el-date-editor .el-range-input){
            height: 40px;
            line-height: 40px;
        }
        :deep(.el-range-editor.el-input__wrapper){
            height: 40px;
         
        }
    }
    .action-list{
        margin-top: 20px;
        .action-item{
            padding: 16px 0;
            border-bottom: 1px solid #f5f6fa;
            &:last-child{
                border-bottom: none;
            }
            .left{
                img{
                    width: 45px;
                    height: auto;
                    margin-right: 16px;
                }
                .desc{
                    h3{
                        font-weight: bold;
                        font-size: 16px;
                        color: #2B2C33;
                        line-height: 22px;
                    }
                    p{
                        font-size: 12px;
                        color: #00BDED;
                        line-height: 17px;
                        padding: 2px 6px;
                        background: #D9F5FD;
                       border-radius: 4px;
                       margin-left: 12px;
                    }
                    .p-hdjl{
                        background: #FFECE2;
                        color: #FF7C2F;
                    }
                    .p-jdcz{
                        background: #FFF1DC;
                        color: #FF8F00
                    }
                    .p-xzcg{
                         background: #FDEBFD;
                        color: #E371F4
                    }
                    .p-tb{
                        background: #F1F2F5;
                        color: #94959C
                    }

                }
                .date{
                    font-size: 14px;
                    color: #B4B6BE;
                    line-height: 20px;
                    margin-top: 3px;
                }
            }
            .right{
                img{
                    width: 34px;
                    margin-right: 12px;
                    height: auto;
                }
                p{
                    font-size: 20px;
                    color: #FF9832;
                    line-height: 28px;
                    // font-weight: bold;
                }
            }
        }
    }
}
</style>
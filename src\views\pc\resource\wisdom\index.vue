<template>
    <div class="content-box  question mt10 flex_start_0" >
        <div class="left">
            
            <div class="title flex_between">
                <p>智慧课堂</p>
                <p class="gocheck" @click="gocheck"> 前往审核列表</p>
            </div>
             <el-tree
                style="max-width: 600px"
                :load="loadNode"
                lazy
                :props="defaultProps"
                @node-click="handleNodeClick"
            />
        </div>
      
        <div class="right flex_1 flex_column flex_start_0">
            
            <div class="select_form flex_between">
                <div class="flex_start">
                   <el-input
                        v-model="form.name"
                        style="width: 240px;margin-right:16px"
                        placeholder="搜索课程"
                        :suffix-icon="Search"
                        @change="changeResCategory"
                    />
                    <el-select
                        v-model="form.belongToTenantId"
                        placeholder="全部"
                        size="large"
                        style="width: 160px;margin-right:16px"
                         @change="changeResCategory"
                    >
                        <el-option
                            v-for="item in tenantOptions"
                            :key="item.tenantId"
                            :label="item.tenantName"
                            :value="item.tenantId"
                        />
                    </el-select>
                    <el-select
                        v-model="form.analysis"
                        placeholder="全部"
                        size="large"
                        clearable
                        @change="changeResCategory"
                        style="width: 160px"
                    >
                         <el-option
                            label="全部"
                            value=""
                        />
                        <el-option
                            v-for="item in analysisList"
                            :key="item.id"
                            :label="item.title"
                            :value="item.id"
                        />
                    </el-select>
                </div>
                

            </div>
            <div class="flex_start change-tab">
                  <img src="@/assets/pc/sortby.png" alt="">
                  <p :class="form.orderBy=='1'?'active':''" @click="searchOrderBy('1')">按播放量</p>
                  <p :class="form.orderBy=='2'?'active':''"  @click="searchOrderBy('2')">按上传时间</p>
            </div>
            <!-- v-for='item in tableData' :key="item.id" -->
            <div class="question-list flex_start flex_wrap">
              <div class="question-item" v-for="item in tableData" :key="item.id" >
                 <Resource :row='item' />
               </div>
            </div>
           
              <el-empty description="暂无数据" v-show="total==0"  style="margin-top:10px" class="flex_1"/>
              <el-pagination
              :current-page="form.pageNum"
                background
               v-show="total!=0"
                layout="total,prev, pager, next"
                :total="total"
                v-model:page-size='form.pageSize'
                class="mt-4"
                @current-change='currentChange'
            />
        </div>
       
    </div>
</template>
<script setup>
import {onActivated, onMounted, reactive, ref} from 'vue'
import {Search } from '@element-plus/icons-vue'
import Resource from '@/views/pc/component/resource/resource.vue'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'
import {questionList,subjectList,editionList,volumeList,chapterTree,resources,userTenant} from '@/api/pc/index.js'
import {jyfxList} from '@/api/pc/wisdom.js'
import {useRouter} from 'vue-router'
import { ElMessage,ElMessageBox } from 'element-plus'
let userInfo=useUserInfoStoreHook()
let {questionCategory,questionDegree,stages,ResourceCategory,currentInfo}=storeToRefs(userInfo)
let router=useRouter()
//VIEWS UPLOAD_TIME 
let form=reactive({
    name:'',
    belongToTenantId:'',
    stage:"",
    category:'',
    subjectId:'',
    editionId:'',
    volumeId:'',
    chapter1Id:'',
    chapter2Id:'',
    orderBy:'1',
    orderRule:'DESC',
    pageSize:9,
    pageNum:1,
    analysis:'',

})
const analysisList=[{id:'0',title:'未提交'},{id:'1',title:'分析中'},{id:'2',title:'已分析'},]
let total=ref(0)
let tableData=ref([])
let defaultProps={
      children: 'children',
}

let tenantOptions=ref([])

let treeData=ref([
       {
            value:'10',
            label:'幼儿园',
            type:'stage',
        },
        {
            value:'20',
            label:'小学',
             type:'stage',
        },
        {
            value:'30',
            label:'初中',
             type:'stage',
        },{
            value:'40',
            label:'高中',
             type:'stage',
        }
])
let loadNode=async(node,resolve)=>{
   
    if(node.level==0){
        resolve([...treeData.value])
    } else if(node.level==1){
       let res1=await subjectList({
        stage:node.data.value
       })
       if(res1){
         resolve([...res1.data])
       }
    }else if(node.level==2){
       
        let res1=await editionList({
             stage:node.parent.data.value,
            subjectId:node.data.value
        })
        if(res1){
         resolve([...res1.data])
        }
    }else if(node.level==3){
        let res1=await volumeList({
            editionId:node.data.value
        })
        if(res1){
          resolve([...res1.data])
        }
    }else if(node.level==4){
        let res1=await chapterTree({
            volumeId:node.data.value
        })
        if(res1){
          resolve([...res1.data])
        }
    }else if(node.level==5){
         resolve(node.data.children?node.data.children:[])
    }else{
        resolve([])
    }

}
let getUserTenant=async()=>{
    let res=await userTenant()
    if(res){
      
         res.data.unshift({
            isAdmin:false,
            tenantId:'0',
            tenantName:"个人"
        })
        res.data.unshift({
            isAdmin:false,
            tenantId:'',
            tenantName:"全部"
        })
        
        tenantOptions.value=res.data
    }
}
let currentChange=(val)=>{
    Object.assign(form,{
        pageNum:val,
    })
    getList()
}
let searchOrderBy=(arg)=>{
    Object.assign(form,{
        pageNum:1,
        orderBy:arg
    })
      getList()
}
let changeResCategory=(val)=>{
    Object.assign(form,{
       
        pageNum:1
    })
    getList()
}
const handleNodeClick=(val1,val2)=>{
   
    Object.assign(form,{
        stage:"",
        subjectId:'',
        editionId:'',
        volumeId:'',
        chapter1Id:'',
        chapter2Id:'',
        pageSize:9,
        pageNum:1
    })
    if(val2.level==1){
         Object.assign(form,{
          stage:val1.value
         })
    }else if(val2.level==2){
         Object.assign(form,{
            subjectId:val1.value
         })
    }else if(val2.level==3){
         Object.assign(form,{
            editionId:val1.value
         })
    }else if(val2.level==4){
         Object.assign(form,{
            volumeId:val1.value
         })
    }else if(val2.level==5){
         Object.assign(form,{
            chapter1Id:val1.value
         })
    }else if(val2.level==6){
         Object.assign(form,{
            chapter2Id:val1.value
         })
    }
     getList()
}
let gocheck=async()=>{
    router.push('/resource/wisdom/check')
}
let getList=async()=>{
    let params={
        pageNum:form.pageNum,
        pageSize:form.pageSize,
        stageId:form.stage,
        subjectId:form.subjectId,
        editionId:form.editionId,
        bookId:form.volumeId,
        chapterId:form.chapter2Id?form.chapter2Id:form.chapter1Id?form.chapter1Id:'',
        title:form.name,
        order:form.orderBy,
        belongToTenantId:form.belongToTenantId,
        analysis:form.analysis
    }
    let res=await jyfxList(params)
    if(res){
    res.data.list.forEach(item=>{
        item.url=item.sealImg
        item.name=item.title
        item.favorites=item.favoriteCount
        item.views=item.viewCount
    })
    tableData.value=res.data.list
    total.value=res.data.total
    }
}
let goUpload=()=>{
  router.push('/resource/teach/upload')
}
onMounted(()=>{
    getUserTenant()
})
onActivated(()=>{
      getList()
})
</script>
<style lang="scss" scoped>
.content-box{
    margin-bottom: 10px;
}
.left{
    width: 324px;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 24px 24px 16px 24px;
   .title{
        padding-bottom: 16px;
        border-bottom: 1px solid  #E2E3E6;
        p{
            font-size: 20px;
            color: #2B2C33;
            font-weight: bold;
        }
        .gocheck{
            font-size: 16px;
            cursor: pointer;
            &:hover{
                color: #409eff;
            }
        }
        .my-tj{
            cursor: pointer;
            img{
                width: 16px;
                height: auto;
                margin-right: 4px;
            }
            p{
                font-size: 16px;
                color: #508CFF;
            }
        }
  }
 :deep(.el-tree){
    height: calc(100vh - 240px);
    overflow: auto;
 }
  :deep(.el-tree>.el-tree-node>.el-tree-node__content){
     font-weight: 600;
    font-size: 16px;
    color: #2B2C33;
    padding: 8px 0;

     
  }
  :deep(.el-tree>.el-tree-node .el-tree-node__children){
    font-size: 16px;color: #2B2C33;
  }
  :deep(.el-tree-node__content){
    height: 40px;
  }
  :deep(.el-tree-node__label){
    width: 165px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  :deep(.el-tree>.el-tree-node .is-current>.el-tree-node__content){
     background: #DFEAFF;
     font-weight: 600;
    font-size: 16px;
    color: #508CFF;
  }
  :deep(.el-tree-node__content:hover){
    background: #DFEAFF;
  }
  :deep(.el-tree .el-icon){
    margin-right: 8px;
  }
}
.right{
    margin-left: 10px;
  .select_form{
    :deep(.el-input){
        height: 40px;
       
    }
    :deep(.el-input__wrapper){
         border-radius: 8px;
    }
    :deep(.el-select__wrapper){
         border-radius: 8px;
    }
     .up-question{
        width: 116px;
        height: 40px;
        background: #508CFF;
        border-radius: 8px;
        border: 1px solid #508CFF;
        font-size: 16px;
        color: #FFFFFF;
        img{
            width: 16px;
            height: auto;
            margin-right: 4px;
        }
     }
  }
  .change-tab{
    padding: 16px 0;
    font-size: 12px;
    color: #94959C;
    img{
        width: 14px;
        margin-right: 10px;
        height: auto;
    }
    p{
        margin-right: 10px;
        cursor: pointer;
        line-height: 100%;
    }
    .active{
        color: #508CFF;
    }
  }
  .question-list{
        .question-item{
            margin-bottom: 24px;
            width: 32%;
            margin-right: 2%;
            &:nth-child(3n+3){
                margin-right: 0;
            }
        }
  }
  
}


</style>
<template>
    <div class="content-box flex_start_0 mt10">
       <div class="left">
           <div class="person-column">
             
              <p class="title">{{$route.name=='ActResearchAssociation-square-userRecord'?($route.query.userName+'的日记'):isRecommend?'推荐日记':'日记广场'}}</p>
              <ul class="list" v-show="!isRecommend&&!clubId&&$route.name!='ActResearchAssociation-square-userRecord'">
                <li :class="['flex_between',item.value==searchForm.clubGroupId?'active':'']" v-for="item in diaryList" :key="item.value" @click="changeTab(item)">
                    <div class="flex_start">
                  
                        <p>{{item.label}}</p>
                    </div>
                    <el-icon><ArrowRight /></el-icon>
                </li>
              </ul>
           </div>
           <!-- <div class="friend-link" v-if="userInfo.roles.indexOf('TUTOR')>-1">
              <FriendLink :recommendList='recommendList' />
           </div> -->
       </div>
       <div class="flex_1 right">
        <div>
            <!--  @emitIsShowOwn='emitIsShowOwn'推荐日记   @needLoad='needLoad'  -->
              <Personaldiary 
                :url='url' 
                :clubGroupId='searchForm.clubGroupId'
              
                @emitParentIsLike='emitParentIsLike'
                @emitsParentForm='emitsParentForm' 
                @emitParentDel='emitParentDel'
                @emitParentHide='emitParentHide'
                @emitParentRecommend='emitParentRecommend'
                @emitIsShowNoComment='emitIsShowNoComment'
                @emitParentDetail='emitParentDetail'
                @exportData='exportData'
                :tableData='tableData' 
                @emitCurrentChange='emitCurrentChange'
                :pageSize='searchForm.pageSize'
                :total='total'
            />
             <el-empty description="暂无数据" v-show="total==0" style="margin-top:0"/>
        </div>
       <!-- <div>
           <DiaryItemPower v-for="item in 5" :key="item"/>
       </div> -->
       </div>
       
    </div>
</template>
<script setup>
import {onActivated, onMounted, reactive, ref} from 'vue'
import Personaldiary from './personaldiary.vue'
import DiaryItemPower from '@/views/pc/component/act-research-association/diaryItemPower.vue'
import FriendLink from '@/views/pc/component/act-research-association/friendLink.vue'
import {exportSixStarRecord,squareAll,myGroups,recommendRecord,directorDiary,tutorDiary,directorDiaryRecommend,tutorDiaryRecommend,wxRecommend,userRecordList} from '@/api/pc/index.js'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import download from '@/utils/download.js'
import {useRoute} from 'vue-router'
import {storeToRefs} from 'pinia'
let route=useRoute()
let userInfoStore=useUserInfoStoreHook()
let {squareCurrentInfo,userInfo,roles}=storeToRefs(userInfoStore)
let diaryList=ref([{id:'1',title:'全部'},{id:'2',title:'已读'},{id:'3',title:'未读'}])
let recommendList=ref([])
let clubId=ref('')
let score=ref('')
let searchForm=reactive({
    pageNum:1,
    pageSize:8,
    // isMyGroupViewOnly:false,
    orderBy:'',
    beginRecordDate:'',
    endRecordDate:'',
    clubGroupId:'',
    orderRule:'DESC',
    score:'',
    isViewed:'',
  isLike:'',
  isCommented:''
})
let clubGroupName=ref('')
let isRecommend=ref(false)
let recommendForm=reactive({
     pageNum:1,
    pageSize:8,
})
let url=ref('/actResearchAssociation/square/detail')
let tableData=ref([])
let total=ref(0)
let maxPage=ref(0)

onActivated(()=>{
    getList()
    
})
let exportData=async(arg,score)=>{
    
    if(arg.indexOf('DIRECTOR')>-1&&!searchForm.clubGroupId){
      download.zip('/xzs/club-record/public/club/'+userInfo.value.xingZhiShe?.clubId+'/record/file?beginRecordDate='+searchForm.beginRecordDate+'&endRecordDate='+searchForm.endRecordDate+'&score='+score,score==6?'六星日记.zip':'全部日记.zip')
    }else if(arg.indexOf('DIRECTOR')>-1&&parseInt(searchForm.clubGroupId)>0){
          download.zip('/xzs/club-record/public/group/'+searchForm.clubGroupId+'/record/file?beginRecordDate='+searchForm.beginRecordDate+'&endRecordDate='+searchForm.endRecordDate+'&score='+score,score==6?(clubGroupName.value+'-六星日记.zip'):(clubGroupName.value+'-全部日记.zip'))
    }else if(arg.indexOf('DIRECTOR')>-1&&parseInt(searchForm.clubGroupId)==-1){
          download.zip('/xzs/club-record/public/director/record/file?beginRecordDate='+searchForm.beginRecordDate+'&endRecordDate='+searchForm.endRecordDate+'&score='+score,score==6?'社长日记-六星日记.zip':'社长日记.zip')
    }else if(arg.indexOf('DIRECTOR')>-1&&parseInt(searchForm.clubGroupId)==-2){
          download.zip('/xzs/club-record/public/director/recommended-record/file?beginRecordDate='+searchForm.beginRecordDate+'&endRecordDate='+searchForm.endRecordDate+'&score='+score,score==6?'社长推荐日记-六星日记.zip':'社长推荐日记.zip')
    }else if(arg.indexOf('DIRECTOR')>-1&&parseInt(searchForm.clubGroupId)==-3){
          download.zip('/xzs/club-record/public/tutor/record/file?beginRecordDate='+searchForm.beginRecordDate+'&endRecordDate='+searchForm.endRecordDate+'&score='+score,score==6?'导师日记-六星日记.zip':'导师日记.zip')
    }else if(arg.indexOf('DIRECTOR')>-1&&parseInt(searchForm.clubGroupId)==-4){
          download.zip('/xzs/club-record/public/tutor/recommended-record/file?beginRecordDate='+searchForm.beginRecordDate+'&endRecordDate='+searchForm.endRecordDate+'&score='+score,score==6?'导师推荐日记-六星日记.zip':'导师推荐日记.zip')
    }else if(arg.indexOf('DIRECTOR')>-1&&parseInt(searchForm.clubGroupId)==-5){
          download.zip('/xzs/club-record/public/wx-mp/recommended-record/file?beginRecordDate='+searchForm.beginRecordDate+'&endRecordDate='+searchForm.endRecordDate+'&score='+score,score==6?'公众号推荐日记-六星日记.zip':'公众号推荐日记.zip')
    }else if((arg.indexOf('LEADER')>-1||arg.indexOf('TUTOR')>-1)&&(userInfo.value.xingZhiShe?.clubGroupId==searchForm.clubGroupId)){
        //导师或者组长导出自己的组
       download.zip('/xzs/club-record/public/group/'+searchForm.clubGroupId+'/record/file?beginRecordDate='+searchForm.beginRecordDate+'&endRecordDate='+searchForm.endRecordDate+'&score='+score,score==6?(clubGroupName.value+'-六星日记.zip'):(clubGroupName.value+'-全部日记.zip'))
       
    }
}
let emitParentDetail=(recordId)=>{
  
    squareCurrentInfo.value={
        clubId:clubId.value,
        orderBy:searchForm.orderBy,
        beginRecordDate:searchForm.beginRecordDate,
        endRecordDate:searchForm.endRecordDate,
        clubGroupId:searchForm.clubGroupId,
        orderRule:searchForm.orderRule,
        score:searchForm.score
        // tutorIsRead:searchForm.tutorIsRead,
    }
}
let emitParentDel=(val)=>{
    getList()
   
}
let emitIsShowNoComment=(val)=>{

    if(val){
        score.value=-1
        Object.assign(searchForm,{
            score:val?'-1':'',
            pageNum:1
        })
    }else{
       score.value=''
       Object.assign(searchForm,{
             score:val?'-1':'',
             pageNum:1
        }) 
    }
    tableData.value=[]
    getList()
}
let emitParentHide=(val)=>{
   val.isHidden=!val.isHidden
}
let emitParentRecommend=(val,recommendType)=>{
   if(recommendType==1){
        val.isRecommended=!val.isRecommended
        if(val.isRecommended){
            val.isCancelRecommendCapable=true
        }else{
            val.isCancelRecommendCapable=false
        }
   }else if(recommendType==2){  
        val.isWxMpRecommended=!val.isWxMpRecommended
       
   }
   
}
let getMyGroup=async()=>{
   let res=await myGroups()
   if(res){
    // console.log(res,'my')
    diaryList.value=res.data
    if(roles.value.indexOf('DIRECTOR')>-1){
      diaryList.value.unshift({
        label:'公众号推荐',
        value:'-5'
     })
    }
    diaryList.value.unshift({
        label:'导师推荐',
        value:'-4'
    })
      diaryList.value.unshift({
        label:'导师日记',
        value:'-3'
    })
     diaryList.value.unshift({
        label:'社长推荐',
        value:'-2'
    })
    diaryList.value.unshift({
        label:'社长日记',
        value:'-1'
    })
    diaryList.value.unshift({
        label:'全部',
        value:''
    })
   }
}
// let getRecommendRecord=async()=>{
//     let params={
//        pageNum:searchForm.pageNum,
//        pageSize:searchForm.pageSize,
//        orderBy:searchForm.orderBy,
//        orderRule:searchForm.orderRule,
//        tutorIsRead:searchForm.tutorIsRead,
//     }
//     let res=await recommendRecord(params)
//    if(res){
//     // console.log(res.data)
//      res.data.list.forEach(item=>{
//             item.week=getWeekday(item.recordDate)
//         })
//          tableData.value=res.data.list
//          total.value=res.data.total
//          maxPage=res.data.pages
//    }
// }
// let needLoad=()=>{
//       if(searchForm.pageNum<maxPage){
//         Object.assign(searchForm,{
//            pageNum:searchForm.pageNum+1
//         })
//          getList()
//     }
// }
// let emitIsShowOwn=(val)=>{

//    tableData.value=[]
//    isRecommend.value=val //推荐
//    Object.assign(searchForm,{
//     pageNum:1
//    })
//    if(val){
//       getRecommendRecord()
//    }else{
//     getList()
//    }
// }
let changeTab=(row)=>{
   clubGroupName.value=row.label
    Object.assign(searchForm,{
        pageNum:1,
        clubGroupId:row.value,
        score:(userInfo.xingZhiShe?.clubGroupId==row.value||row.value==0)?score.value:''
    })
     tableData.value=[]
 
    getList()
}
let emitsParentForm=(val)=>{
    tableData.value=[]
    Object.assign(searchForm,{
        pageNum:1,
        orderBy:val.orderBy?val.orderBy:'',
        orderRule:val.orderRule?val.orderRule:'',
        beginRecordDate:(val.daterange&&val.daterange[0])?val.daterange[0]:'' ,
        endRecordDate:(val.daterange&&val.daterange[1])?val.daterange[1]:'' ,
        isViewed:val.isViewed,
        isLike:val.isLike,
        isCommented:val.isCommented
    })
    getList()
}
let emitParentIsLike=(islike,id,type)=>{
    // console.log(islike,id)
    if(type=='thumb'){
       tableData.value.forEach(item=>{
        if(item.id==id){
            item.likes=islike?(item.likes-1):(item.likes+1)
            item.isLike=!islike
        }
      })
    }else if(type=='favorite'){
       tableData.value.forEach(item=>{
        if(item.id==id){
            item.favorites=islike?(item.favorites-1):(item.favorites+1)
            item.isFavorite=!islike
        }
      })  
    }
   
    // getCount()
}
function getWeekday(date) {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const dayIndex = new Date(date).getDay();
    return weekdays[dayIndex];
}
let getDirectorList=async()=>{
        let res=await directorDiary(searchForm)
 
     if(res){
        res.data.list.forEach(item=>{
            item.week=getWeekday(item.recordDate)
        })
        
         tableData.value=res.data.list
         total.value=res.data.total
         maxPage=res.data.pages
     }
}
let getDirectorRecommendList=async()=>{
        let res=await directorDiaryRecommend(searchForm)
 
     if(res){
        res.data.list.forEach(item=>{
            item.week=getWeekday(item.recordDate)
        })
        
         tableData.value=res.data.list
         total.value=res.data.total
         maxPage=res.data.pages
     }
}
let getWxRecommendList=async()=>{
    let res=await wxRecommend(searchForm)
     if(res){
        res.data.list.forEach(item=>{
            item.week=getWeekday(item.recordDate)
        })
        
         tableData.value=res.data.list
         total.value=res.data.total
         maxPage=res.data.pages
     }
}
let getTutorList=async()=>{
        let res=await tutorDiary(searchForm)
 
     if(res){
        res.data.list.forEach(item=>{
            item.week=getWeekday(item.recordDate)
        })
        
         tableData.value=res.data.list
         total.value=res.data.total
         maxPage=res.data.pages
     }
}
let getTutorRecommendList=async()=>{
        let res=await tutorDiaryRecommend(searchForm)
 
     if(res){
        res.data.list.forEach(item=>{
            item.week=getWeekday(item.recordDate)
        })
        
         tableData.value=res.data.list
         total.value=res.data.total
         maxPage=res.data.pages
     }
}
let getRecord=async()=>{
    let res=await userRecordList({
        userId:route.query.userId,
        ...searchForm
    })
    if(res){
        res.data.list.forEach(item=>{
            item.week=getWeekday(item.recordDate)
        })
        
         tableData.value=res.data.list
         total.value=res.data.total
         maxPage=res.data.pages
     }
}
let emitCurrentChange=(val)=>{
      Object.assign(searchForm,{
        pageNum:val
    })
    getList()
}
let getList=async()=>{
    Object.assign(searchForm,{
        clubId:clubId.value
    })
    if(route.name=='ActResearchAssociation-square-userRecord'){
           getRecord()
    }else if(searchForm.clubGroupId==-1){
          getDirectorList()
    }else  if(searchForm.clubGroupId==-2){
          getDirectorRecommendList()
    }else  if(searchForm.clubGroupId==-3){
          getTutorList()
    }else  if(searchForm.clubGroupId==-4){
          getTutorRecommendList()
    }else if(searchForm.clubGroupId==-5){
        getWxRecommendList()
    }else {
        let res=await squareAll(searchForm)
        if(res){
            res.data.list.forEach(item=>{
                item.week=getWeekday(item.recordDate)
            })
            tableData.value=res.data.list
            total.value=res.data.total
            maxPage=res.data.pages
        }
    }
  
     
}
onMounted(()=>{
  clubId.value=route.query.clubId
  getList()
  getMyGroup()
  
})
</script>
<style lang="scss" scoped>
.mt10{
    margin-top: 10px;
}
.left{
    width: 180px;
    .person-column{
        border-radius: 8px;
        background: #fff;
        padding-bottom: 16px;
        .title{
             padding: 18px 0  24px 18px;
             font-weight: bold;
             font-size: 20px;
             color: #2B2C33;
        }
       
        .list{
            li{
                cursor: pointer;
                padding: 10px 8px  8px 18px;
                font-size: 16px;
                color: #2B2C33;
                img{
                    width: 16px;
                    height: auto;
                    margin-right: 6px;
                }
                &.active{
                    background: #DFEAFF;color: #508CFF;
                }
            }
        }
    }
    .friend-link{
        margin-top: 10px;
    }
}
.right{
    margin-left: 10px;
}
</style>
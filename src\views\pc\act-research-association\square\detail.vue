<template>
    <div class="diary-detail mt10 flex_start_0 content-box">
         
           <div class="left flex_1">
            <div class="flex_start title" @click="goBack">
            <el-icon style="margin-right: 4px;"><ArrowLeft /></el-icon>
              返回列表
          </div>
                 
                <div class="flex_start_0 diary">
                    <el-avatar :src="detailRow.userAvatarUrl || DefaultAvatar" class="avatar head" :size="32" @click="goPreview(detailRow)" />
                    <!-- <img src="@/assets/pc/teacher.png" alt="" class="head" /> -->
                    <div class="flex_1">
                        <div class="flex_between">
                          <div class="info">
                            <div class="flex_start ">
                                <p class="teacher">{{detailRow.userName}}</p>
                                
                                <div class="flex_start stars" v-show="starnum!==null&&(detailRow.userClubRole=='MEMBER'||detailRow.userClubRole=='LEADER')">
                                    <div v-for="item in 6" :key="item" class="star-box">
                                        <img src="@/assets/pc/star.png" alt="" v-if="item>starnum">
                                        <img src="@/assets/pc/stared.png" alt="" v-else>
                                    </div>
                                </div>
                            </div>
                            <p class="date">{{detailRow.recordDate}}</p>
                         </div>
                         <div class="changeArticle flex_start">
                         
                               <button :class="['flex_center',exchangeForm.prevId?'exist':'']" style="margin-right:16px" @click="exchangeForm.prevId&&goOther('prev')">
                                   <img src="@/assets/pc/prev.png" alt="" style=" margin-right: 4px;" v-show="!exchangeForm.prevId">
                                   <img src="@/assets/pc/preved.png" alt="" style=" margin-right: 4px;" v-show="exchangeForm.prevId">
                                  上一篇
                               </button>
                                <button :class="['flex_center',exchangeForm.nextId?'exist':'']" @click="exchangeForm.nextId&&goOther('next')">
                                 
                                  下一篇
                                   <img src="@/assets/pc/next.png" alt="" style=" margin-left: 4px;" v-show="!exchangeForm.nextId">
                                    <img src="@/assets/pc/nexted.png" alt="" style=" margin-left: 4px;" v-show="exchangeForm.nextId">
                               </button>
                           </div>
                        </div>
                        
                        <div class="flex_start diary-title" v-show="detailRow.title">
                            <h3>标题：</h3>
                            <p>{{detailRow.title}}</p>
                        </div>
                    <div class="rich">
                        <div class="rich-html" ref="richRef" v-html="detailRow.content">
                      
                        </div>
                        
                    </div>
                    <div class="flex_end btn-list">
                         <div class="recommend flex_center" @click="goRecommend(detailRow,'1')" v-show="detailRow.isRecommended!==null&&!detailRow.isRecommended">
                            栏目推荐
                        </div>
                         <div class="hasrecommend flex_center" v-show="detailRow.isRecommended!==null&&detailRow.isRecommended"  @click="cancelCancelRecommend(detailRow,'1')">
                            取消栏目推荐
                            <!-- 已设为推荐<span v-show="row.isCancelRecommendCapable">（取消推荐）</span> -->
                        </div>
                         <div class="wxrecommend flex_center" style="width:126px;margin-left:10px" @click="goRecommend(detailRow,'2')" v-show="detailRow.isWxMpRecommended!==null&&!detailRow.isWxMpRecommended">
                            公众号推荐
                        </div>
                        <div class="haswxrecommend flex_center" style="margin-left:10px" v-show="detailRow.isWxMpRecommended!==null&&detailRow.isWxMpRecommended" @click="cancelCancelRecommend(detailRow,'2')">
                            取消公众号推荐
                        </div>
                    </div>
                
                    <div class="bottom">
                        <ul class="flex_start tag-list flex_wrap">
                            <li class="flex_start" v-for="item in detailRow.topics" :key="item.id">
                                <img src="@/assets/pc/tag.png" alt="">
                                <p>{{item.name}}</p>
                            </li>
                        </ul>
                    </div>
                </div>
               </div>
               <ul class="flex_center action-list">
                        <li class="flex_start">
                            <img src="@/assets/pc/eyed.png" alt="">
                            <p>浏览{{detailRow.views}}</p>
                        </li>
                        <li class="flex_start" @click="gofavorite(detailRow)">
                                <img src="@/assets/pc/comment-star.png" alt="" v-show="!detailRow.isFavorite">
                               <img src="@/assets/pc/comment-stared.png" alt="" v-show="detailRow.isFavorite">
                            <p>收藏{{detailRow.favorites}}</p>
                        </li>
                        <li class="flex_start">
                            <img src="@/assets/pc/comment.png" alt=""  v-show="!detailRow.isCommented" />
                             <img src="@/assets/pc/commented.png" alt="" v-show="detailRow.isCommented" />
                            <p>评论{{detailRow.comments}}</p>
                        </li>
                        <li class="flex_start" @click="detailRow.isLikeCapable&&goThumb(detailRow)">
                            <img src="@/assets/pc/thumb.png" alt="" v-show="!detailRow.isLike">
                            <img src="@/assets/pc/thumbed.png" alt="" v-show="detailRow.isLike">
                            <p>点赞{{detailRow.likes}}</p>
                        </li>
               </ul>
               <div class="comment">
                   <div class="write-own-comment">
                        <div class="ipt flex_start_0" v-if="detailRow.isCommentCapable">
                            <img src="@/assets/pc/teacher.png" alt="">
                            <!-- <el-input type="text" clearable v-model="commentForm.content" placeholder="发表你的评论" class="flex_1" >
                                <template #prepend v-if="commentForm.replyName">回复{{commentForm.replyName}}:</template>
                            </el-input> -->
                             <div class="flex_1 icon-box">
                                  <el-input type="textarea" rows="5" resize="none"  v-model="commentForm.content" show-word-limit placeholder="发表你的评论"   maxlength="350"  v-pcpermission='["LEADER","MEMBER","DIRECTOR","VICE_DIRECTOR"]' ></el-input>
                                   <el-input type="textarea" rows="5" resize="none" v-model="commentForm.content" show-word-limit placeholder="发表导师点评" maxlength="350"  v-pcpermission='["TUTOR"]' ></el-input>
                                   <!-- <el-icon class="icon" @click="goClose"><CircleCloseFilled /></el-icon> -->
                                    <el-button @click="goSendComment"  v-pcpermission='["LEADER","MEMBER","DIRECTOR","VICE_DIRECTOR"]'>评论</el-button>
                                    <el-button @click="goSendComment"  v-pcpermission='["TUTOR"]'>点评</el-button>
                            </div>
                           
                        </div>
                        <div class="star flex_start"  v-if="detailRow.isRateCapable&&detailRow.selfScore==-1">
                               <p class="label">评星</p>
                               <div class="flex_start stars">
                                    <div v-for="item in 6" :key="item" class="star-box" @click="choiceStar(item)">
                                        <img src="@/assets/pc/star.png" alt="" v-if="item>selfstarnum">
                                        <img src="@/assets/pc/stared.png" alt="" v-else>
                                    </div>
                                    <el-button type="primary" style="background:#508CFF;margin-left:20px" @click="gosubmitStar">提交</el-button>
                                </div>
                        </div>
                   </div>
                   
                      <el-empty description="暂无评论" v-show="comments.length==0"/>
                     <div class="comment-list" v-infinite-scroll="load" style="overflow:auto">
                         <div class="comment-item flex_between_0" v-for="item in comments" :key="item.id">
                                <div>
                                <div class="flex_start_0 level_1_comment">
                                     <img :src="item.userAvatarUrl || DefaultAvatar" alt="" class="teacher-logo" @click="goPreview(item)">
                                     <!-- <el-avatar :src="item.userAvatarUrl || DefaultAvatar" class="avatar teacher-logo" :size="32" @click="goPreview(item)" /> -->
                                    <div>
                                        <div class="flex_start_0">
                                            <p class="teacher-name">{{item.userName}}</p>
                                            <p :class="item.userClubRole=='LEADER'?'job-leader':item.userClubRole=='TUTOR'?'job-tutor':item.userClubRole=='DIRECTOR'?'job-director':''">
                                                {{item.userClubRole=='LEADER'?'组长':item.userClubRole=='TUTOR'?'导师':item.userClubRole=='DIRECTOR'?'社长':''}}
                                            </p>
                                            <p class="teacher-write flex_1">：{{item.content}}</p>
                                          
                                        </div>
                                        <p class="from">{{item.formattedCommentTime}}&nbsp;&nbsp; 来自{{item.userSourceName}}</p>
                                    </div>
                                  </div>
                                 
                                
                               </div>
                             
                              <div class="thumb-list flex_end">
                                    <!-- <div class="flex_start item">
                                        <img src="@/assets/pc/comment.png" alt="">
                                        <p>{{item.comments}}</p>
                                    </div> -->
                                    <div class="flex_start item" @click="goRecordComment(item)">
                                        <img src="@/assets/pc/thumb.png" alt="" v-show="!item.isLike">
                                        <img src="@/assets/pc/thumbed.png" alt="" v-show="item.isLike">
                                        <p>{{item.likes}}</p>
                                    </div>
                                    <img src="@/assets/pc/del.png" alt="" v-show="item.isDeleteCapable" class="deleteCapable" @click="goDelComment(item)" style="margin-right:16px" />
                                    <img src="@/assets/pc/edit.png" alt="" v-show="item.isUpdateCapable" class="deleteCapable" @click="goEditComment(item)" />
                              </div>
                         </div>
                     </div>
               </div>
            </div>
            <div class="right">
                <div class="title">点赞列表</div>
                    <ul>
                        <li class="thumb-item" v-for="item in likes" :key="item.id">
                            <div class="flex_start">
                                 <img src="@/assets/pc/teacher.png" alt="" class="header">
                                 <p class="name">{{item.name}}</p>
                                  <img src="@/assets/pc/thumbed.png" alt="" class="thumb">
                            </div>
                            <p class="date">{{item.actionTime}}</p>
                        </li>
                    </ul>
            </div>
    </div>
     <el-dialog
        v-model="commentDialogVisible"
        title="编辑评论"
        width="500"
        :before-close="cancelEditComment"
    >
        <el-input v-model="editCommentForm.content" placeholder="请输入"  maxlength="350" show-word-limit type="textarea" rows="6" resize="none"></el-input>
        <template #footer>
        <div class="dialog-footer" style="margin-top:24px">
            <el-button  @click="cancelEditComment">
              取消
            </el-button>
            <el-button type="primary" @click="confirmEditComment">确认</el-button>
            
        </div>
        </template>
    </el-dialog>
     <el-image-viewer
        v-if="showPreview"
        :url-list="previewurlList"
        show-progress
        :initial-index="0"
        @close="showPreview = false"
      />
</template>
<script setup>
import {ref,onMounted, reactive,nextTick} from 'vue'
import {ElMessageBox,ElMessage} from 'element-plus'
import {editComment,diaryRecommend,cancelDiaryRecommend,delComment,squareLocation,wxMpRecommendLocation,squareDirectorLocation,squareDirectorRecommendLocation,squareTutorLocation,squareTutorRecommendLocation,recordComment,commentStar,recordDetail,recordView,commentList,likeList,squareDetail,thumbSubmit,favoriteSubmit,sendComment,userRecordLocation} from '@/api/pc/index.js'
import {useRoute,useRouter} from 'vue-router'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import DefaultAvatar from "@/assets/layouts/default-avatar.png"
let userInfo=useUserInfoStoreHook()
import {storeToRefs} from 'pinia'
let commentDialogVisible=ref(false)
let showPreview=ref(false)
let previewurlList=ref([])
let {squareCurrentInfo}=storeToRefs(userInfo)
let router=useRouter()
let route=useRoute()
let sortType=ref(1)
let selfstarnum=ref(0)
let starnum=ref(0)
let recordId=''
let commentForm=reactive({
    // parentId:'',
    // replyToId:'',
    // replyName:'',
    content:'',
    recordId:''

})
let editCommentForm=reactive({
    commentId:'',
    content:''
})
let goPreview=(row)=>{
    if(row.userAvatarUrl){
         previewurlList.value=[row.userAvatarUrl]
        nextTick(()=>{
             showPreview.value=true
        })
    }
}
let cancelCancelRecommend=async(row,recommendType)=>{

        ElMessageBox.confirm(
             recommendType==1?'确认将该日记取消推荐吗?':'确认将该日记取消公众号推荐吗',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
           let res=await cancelDiaryRecommend({
            recordId:row.id,
            recommendType
            })
            if(res){
                ElMessage.success('设置成功')
                if(recommendType==1){
                    row.isRecommended=!row.isRecommended
                }else if(recommendType==2){  
                    row.isWxMpRecommended=!row.isWxMpRecommended
                    
                }
           }
           
          
        })
        .catch(() => {
      
        })
   
}
let goRecommend=(row,recommendType)=>{
      ElMessageBox.confirm(
           recommendType==1?'确认将该日记设置为推荐吗?':'确认将该日记设置为公众号推荐吗',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
            let res=await diaryRecommend({
                recordId:row.id,
                recommendType
            })
           if(res){
             ElMessage.success('设置成功')
                if(recommendType==1){
                    row.isRecommended=!row.isRecommended
                }else if(recommendType==2){  
                    row.isWxMpRecommended=!row.isWxMpRecommended
                    
                }
          }  
        })
        .catch(() => {
      
        })
}
let confirmEditComment=async()=>{
   if(editCommentForm.content==''){
     cancelEditComment()
   }else{
      let res=await editComment(editCommentForm)
      if(res){
           ElMessage.success('编辑成功')
           comments.value.forEach(item=>{
             if(item.id==editCommentForm.commentId){
                item.content=editCommentForm.content
             }
           })
           cancelEditComment()
      }
   }
}
let cancelEditComment=()=>{
  commentDialogVisible.value=false
  Object.assign(editCommentForm,{
    commentId:'',
    content:''
  })
}
let goEditComment=(row)=>{
   commentDialogVisible.value=true
   Object.assign(editCommentForm,{
    commentId:row.id,
    content:row.content
  })
}
// 
let tempParentIds=[]  //用于储存新增的id
let maxPage=0
let searchForm=reactive({
    pageSize:10,
    pageNum:1,
    orderBy:'HEAT' //TIME
})
let likes=ref([])
let comments=ref([])
let detailRow=ref({})
let exchangeForm=ref({
    nextId:'',
    prevId:''
})
let goClose=()=>{
    Object.assign(commentForm,{
        //  parentId:'',
        //     replyToId:'',
            // replyName:'',
            content:'',
            recordId:''
    })
}
let goDelComment=(row)=>{
    ElMessageBox.confirm(
    '确认删除该条评论吗?',
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async() => {
        let res=await delComment({
            commentId:row.id
        })
        if(res){
          comments.value=comments.value.filter(item=>{
                return item.id!=row.id
            })
            detailRow.value.comments=detailRow.value.comments-1
            ElMessage({
                type: 'success',
                message: '删除成功',
            })
        }
    
    })
    .catch(() => {
      
    })
}
let getCurrentLocation=async()=>{
     let res=''
     if(route.query.userId){
        res=await userRecordLocation({
            userId:route.query.userId,
             recordId:recordId,
          ...squareCurrentInfo.value
        })
     }else if(squareCurrentInfo.value.clubGroupId==-1){
       res=await squareDirectorLocation({
        recordId:recordId,
        ...squareCurrentInfo.value
       })
    }else if(squareCurrentInfo.value.clubGroupId==-2){
      res=await squareDirectorRecommendLocation({
        recordId:recordId,
        ...squareCurrentInfo.value
       })
    }else if(squareCurrentInfo.value.clubGroupId==-3){
          res=await squareTutorLocation({
        recordId:recordId,
        ...squareCurrentInfo.value
       })
    }else if(squareCurrentInfo.value.clubGroupId==-4){
        res=await squareTutorRecommendLocation({
          recordId:recordId,
         ...squareCurrentInfo.value
       })
    }else if(squareCurrentInfo.value.clubGroupId==-5){
        res=await wxMpRecommendLocation({
          recordId:recordId,
         ...squareCurrentInfo.value
       })
    }else{
        res=await squareLocation({
         recordId:recordId,
         ...squareCurrentInfo.value
       })
    }
    if(res){
        console.log(res.data)
        exchangeForm.value=res.data?res.data:{
            nextId:'',
            prevId:''
        }
    }
}
let goOther=(type)=>{
    comments.value=[]
   if(type=='next'){
      recordId=exchangeForm.value.nextId
   }else{
      recordId=exchangeForm.value.prevId
   }
   if(route.query.userId){
        router.replace('/actResearchAssociation/square/detail?recordId='+recordId+'&userId='+route.query.userId)
   }else{
        router.replace('/actResearchAssociation/square/detail?recordId='+recordId)
   }
   
   init()
}
let getDetail=async()=>{
    let res=await squareDetail({
        recordId:recordId
    })
    if(res){
          starnum.value=res.data.score
          if(starnum.value==-1){
            starnum.value=0
          }
          selfstarnum.value=res.data.selfScore?res.data.selfScore:0
         detailRow.value=res.data
        //   sessionStorage.setItem('detailAction',JSON.stringify({
        //     isFavorite:detailRow.value.isFavorite,
        //     isLike:detailRow.value.isLike,
        //     isCommented:detailRow.value.isCommented,
        //     isViewed:true,
        //     id:detailRow.value.id
        // }))
    }
}
let choiceStar=async(num)=>{
    selfstarnum.value=num
  
}
let gosubmitStar=()=>{
     ElMessageBox.confirm(
    '提交后不可修改打星，请确认后提交',
    '确认提交打星吗',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async() => {
          let res=await commentStar({
            recordId:recordId,
            score:selfstarnum.value
        })
        if(res){
            starnum.value=selfstarnum.value
            detailRow.value.selfScore=selfstarnum.value
            ElMessage.success('评星成功')
        }
      
    })
    .catch(() => {
     
    })
}
let searchComment=(arg)=>{
    comments.value=[]
   Object.assign(searchForm,{
      orderBy:arg,
      pageNum:1
   }) 
   getcommentList()
}
// let replyParentComment=(row)=>{
//    Object.assign(commentForm,{
//     replyName:row.userName,
//     parentId:row.id
//    })
// } 
// let replyChildComment=(parentRow,row)=>{
//    Object.assign(commentForm,{
//     replyName:row.userName,
//     parentId:parentRow.id,
//     replyToId:row.id
//    })
// } 
let goRecordComment=async(item)=>{
    let res=await recordComment({
        commentId:item.id,
        like:!item.isLike
    })
    if(res){
         if(item.isLike){
             item.isLike=!item.isLike
             item.likes=item.likes-1
         }else{
              item.isLike=!item.isLike
             item.likes=item.likes+1
         }
    }
}
let load=()=>{
  if(searchForm.pageNum<maxPage){
        Object.assign(searchForm,{
           pageNum:searchForm.pageNum+1
        })
        // alert(11)
         getcommentList()
    }
}
let goBack=()=>{
    router.go(-1)
}
let goSendComment=async()=>{
    Object.assign(commentForm,{
        recordId:recordId
    })
    if(commentForm.content){
        let res=await sendComment(commentForm) 
        if(res){
            //记录在详情里面的操作
            // sessionStorage.setItem('detailAction',JSON.stringify({
            //     isFavorite:detailRow.value.isFavorite,
            //     isLike:detailRow.value.isLike,
            //     isCommented:true,
            //     isViewed:true,
            //     id:detailRow.value.id
            // }))
             res.data.children=[]
                tempParentIds.push(res.data.id)
                comments.value.unshift({
                    ...res.data
                })
                //手动设置为true
                detailRow.value.isCommented=true
                detailRow.value.comments=detailRow.value.comments+1
            // if(commentForm.parentId){
                
            //     if(commentForm.replyToId){
            //         //回复二级评论
            //          comments.value.forEach(item=>{
            //         if(item.id==commentForm.parentId){
            //              item.children.push(res.data)
            //         }
            //      })
            //     }else{
            //         //回复一级评论
               
            //      comments.value.forEach(item=>{
            //         if(item.id==commentForm.parentId){
            //              item.children.unshift({
            //                 ...res.data
            //              })
            //         }
            //      })
            //     }
               
            // }else{
                 
            // }
            ElMessage.success('评论成功')
            Object.assign(commentForm,{
                content:'',

            })
        }
    }else{
        return
    }
    
}
let sendView=async()=>{
   await recordView({
        recordId:recordId
    })
    
}
let getcommentList=async()=>{
    let res=await commentList({
        recordId:recordId,
        pageNum:searchForm.pageNum,
        pageSize:searchForm.pageSize,
        // orderBy:searchForm.orderBy
    })
    if(res){
       res.data.list.forEach(item=>{
            item.children=item.children?item.children:[]
        })
        let temp=[]
        res.data.list.forEach(item=>{
            if(tempParentIds.indexOf(item.id)<0){
                temp.push(item)
            }
        })
        comments.value=comments.value.concat([...temp])
        //   total.value=res.data.total
         maxPage=res.data.pages
    }
}
let goThumb=async(row)=>{
     let res=await thumbSubmit({
        recordId:row.id,
     })
     if(res){
        //   sessionStorage.setItem('detailAction',JSON.stringify({
        //     isFavorite:detailRow.value.isFavorite,
        //      isLike:!detailRow.value.isLike,
        //      isCommented:detailRow.isCommented,
        //      isViewed:true,
        //     id:row.id
        // }))
        if(detailRow.value.isLike){
           detailRow.value.isLike=false
           detailRow.value.likes=detailRow.value.likes-1
        }else{
             detailRow.value.isLike=true
           detailRow.value.likes=detailRow.value.likes+1
        }
     
     }
}
let gofavorite=async(row)=>{
   let res=await favoriteSubmit({
    recordId:row.id,
    favorite:!detailRow.value.isFavorite
   })
    if(res){
        //   sessionStorage.setItem('detailAction',JSON.stringify({
        //     isFavorite:!detailRow.value.isFavorite,
        //      isLike:detailRow.value.isLike,
        //     id:row.id
        // }))
       if(detailRow.value.isFavorite){
           detailRow.value.isFavorite=false
           detailRow.value.favorites=detailRow.value.favorites-1
        }else{
             detailRow.value.isFavorite=true
           detailRow.value.favorites=detailRow.value.favorites+1
        }
     }
}
let getLike=async()=>{
    let res=await likeList({
        recordId:recordId
    })
    if(res){
         likes.value=res.data
    }
}
let init=()=>{
     getDetail()
    sendView()
    getcommentList()
    getLike()
    getCurrentLocation()
}
onMounted(()=>{
    recordId=route.query.recordId
    
    getDetail()
    sendView()
    getcommentList()
    getLike()
    getCurrentLocation()
})
</script>
<style lang="scss" scoped>
.diary-detail{
    margin-bottom: 24px;
}
  .title{
        font-weight: bold;
        font-size: 20px;
         color: #2B2C33;cursor: pointer;
         margin-bottom: 16px;
    }
    .diary-title{
        margin-top: 16px;
        h3{
            font-size: 18px;
        }
        p{
             font-size: 16px;
            font-weight: bold;
        }
    }
.right{
  padding: 16px 6px 16px 16px;
  border-radius: 4px;
  overflow: auto;
  background: #fff;
  width: 180px;
  .title{
    font-size: 16px;
    color: #2B2C33;
    font-weight: bold;
  }
  ul{
    max-height: calc(100vh - 200px);
    overflow: auto;
  }
  .thumb-item{
    margin-top: 16px;
    div{
        .header{
            width: 24px;
            height: auto;
        }
        .name{
            font-size: 14px;
            color: #2B2C33;
            margin: 0 8px;
            font-weight: bold;
            line-height: 22px;
        }
        .thumb{
            width: 14px;
            height: auto;
        }
    }
    .date{
        font-size: 12px;
        color: #94959C;
        margin-top:6px
    }
  }
}
.left{
    padding: 16px 19px;
    background: #fff;  margin-right: 10px;border-radius:4px;
    .diary{
    margin-top: 10px;
  
   
    .head{
        width: 52px;
        height: 52px;
        margin-right: 10px;
    }
    .info{
        .teacher{
            font-size: 16px;
            color: #2B2C33;
            font-weight: bold;
            margin-right: 24px;
        }
        .stars{
            .star-box{
                margin-right: 4px;
            }
            img{
                width: 24px;
                height: auto;
            }
        }
    }
    .date{
        font-size: 14px;
        color: #94959C;
        margin-top: 4px;
    }
   
    .rich{
        position: relative;
        .rich-html{
        margin-top:8px;
       
        font-size: 16px;
        color: #2B2C33;
     
      line-height: 24px;
      :deep(img){
        width: 100%;
      }
    }
    
  
    }
     .btn-list{
            margin-top: 10px;
            button{
                cursor: pointer;
             width: 96px;
             height: 30px;
             background: #FFFFFF;
             border-radius: 4px;
             border: 1px solid #E2E3E6;
            }
            .recommend{
                width: 112px;
                height: 30px;
                cursor: pointer;
                color: #FF9B00;
                background: #fff;
                border: 1px solid #E2E3E6;
                // background: linear-gradient( 270deg, #FF9F50 0%, #FFCF55 100%), #508CFF;
                border-radius: 4px;  
                font-size: 16px;
               line-height: 16px;
            }
            .hasrecommend{
                   width: 112px;
                height: 30px;
                // background: #FFF3E5;
               background: linear-gradient( #FFA919 0%, #FFC33B 100%), #FFFFFF;
                border-radius: 4px;  
                font-size: 16px;
                line-height: 16px;color: #fff;cursor: pointer;
            
            }
               .wxrecommend{
              width: 128px;
                height: 30px;
                cursor: pointer;
                color: #60AE24;
                background: #fff;
                border: 1px solid #E2E3E6;
                // background: linear-gradient( 270deg, #FF9F50 0%, #FFCF55 100%), #508CFF;
                border-radius: 4px;  
                font-size: 16px;
               line-height: 16px;
            }
            .haswxrecommend{
                height: 30px;
                 width: 128px;
                // background: #FFF3E5;
                background: linear-gradient( 180deg, #8CE049 0%, #6DBB31 100%), #FFFFFF;
                border-radius: 4px;  
                font-size: 16px;
                line-height: 16px;color: #fff;cursor: pointer;
            
            }
        }
    .bottom{
        .tag-list{
            li{
                padding: 4px 10px;
                border-radius: 100px;
                font-size: 16px;
                border-radius: 19px;
                border: 1px solid #E2E3E6;
                color: #2B2C33;
                margin: 10px 10px 0 0;cursor: pointer;
                img{
                    width: 16px;
                    height: auto;
                    margin-right: 8px;
                    
                }
            }
        }
    }
     }
     .action-list{
        margin: 24px 0;
        li{
            font-size: 16px;
             color: #94959C;
             padding: 8px 52px;
             border-right: 1px solid #E2E3E4;;
          
            cursor: pointer;
            &:last-child{
                margin-right: 0;
                padding: 8px 0px 8px 52px;
                border-right: none;
            }
            &:first-child{
                padding: 8px 52px 8px 0px;
            }
            img{
                width: 18px;
                height: auto;
                margin-right: 4px;
            }
        }
    }
    .comment{
        margin-left: 62px;
        .write-own-comment{
            .ipt{
                 img{
                    width: 34px;
                    height: 34px;
                 }
                 :deep(.el-input){
                    height: 38px;
                 
                    border-radius: 4px;
                   
                    border:none;
                    outline: none;
                  
                    .el-input__wrapper{
                        width: 100%;  background: #F0F1F4;
                        height: 38px;  padding-right: 30px;
                    }
                 }
                 button{
                    width: 88px;
                    height: 38px;
                    background: #508CFF;
                    border-radius: 4px;
                    font-size: 16px;
                  color: #FFFFFF;
                  border: none;
                  cursor: pointer;
                 }
                   .icon-box{
                      margin: 0 16px 0 10px;
                       position: relative;
                       background: #F0F1F4;
                       button{
                        width: 64px;
                        height: 30px;
                        position: absolute;
                        right: 10px;
                        bottom: 10px;
                       }
                       :deep(.el-textarea){
                        padding-bottom: 36px;
                        border:1px solid #dcdfe6 ;
                        box-shadow: 0 0 0 1px #dcdfe6 #dcdfe6 inset;
                        .el-textarea__inner{
                            border: none;
                            box-shadow:none;
                             background: #F0F1F4;
                        }
                       }
                     .icon{
                        position: absolute;
                        right: 6px;
                        top:50%;
                        cursor: pointer;
                        transform: translateY(-50%);
                     }
                     :deep(.el-textarea .el-input__count){
                        right: 80px;
                        background: none;
                        bottom:16px;
                     }
                   }
            }
            .star{
                 margin: 16px 0 0 0;
                 .label{
                    font-size: 16px;
                     color: #2B2C33;
                     font-weight: bold;
                     margin-right: 16px;
                 }
                 img{
                    width: 35px;
                    height: auto;
                    margin-right: 12px;
                    cursor: pointer;
                 }
            }
        }
         
        .sort{
            font-size: 14px;
            color: #2B2C33;
            margin-top: 24px;
            p{
                margin-right: 30px;cursor: pointer;
                &.active{
                    color: #4260FD;
                }
            }
        }
        .comment-list{
            max-height: 600px;
            overflow: auto;
            // &::-webkit-scrollbar {
            //   display: none;
            //  }
            margin-top: 24px;
            .comment-item{
                 margin-top: 24px;
                 &:first-child{
                    margin-top: 0;
                 }
                 .level_1_comment{
                    .teacher-logo{
                        width: 34px;
                        height: 34px;
                        border-radius: 200px;
                        margin-right: 10px;
                    }
                    .teacher-name{
                        font-size: 15px;
                        color: #5C76D4;
                        font-weight: bold;
                    }
                    .job-leader{
                        width: 34px;
                        height: 14px;
                       background: #C197FF;
                        border-radius: 2px;
                        color: #fff;
                        font-size: 11px;
                        line-height: 14px;
                        text-align: center;
                        margin: 0 8px;
                    }
                     .job-tutor{
                        width: 34px;
                        height: 14px;
                        background: #83BCFE;
                        border-radius: 2px;
                        color: #fff;
                        font-size: 11px;
                        line-height: 14px;
                        text-align: center;
                        margin: 0 8px;
                    }
                    .job-director{
                        width: 34px;
                        height: 14px;
                        border-radius: 2px;
                        color: #fff;
                        font-size: 11px;
                        line-height: 14px;
                        text-align: center;
                        margin: 0 8px;
                        background:  #FFA132;
                    }
                    .from{
                        font-size: 14px;
                        margin-top: 4px;
                        color: #94959C;
                        line-height: 20px;
                    }
                 }
                 .child-comment{
                    margin-top: 16px;
                 }
                 .level_2_comment{
                         margin-left: 44px;
                         padding-left: 10px;
                         border-left: 1px solid #e2e3e6;
                         padding-bottom: 10px;
                         &:last-child{
                            padding-bottom: 0;
                         }
                 }
                 .teacher-write{
                    cursor: pointer;white-space: pre-wrap;word-break: break-all;
                 }
                 
            }
        }
        .thumb-list{
            margin-left: 24px;
            align-items: flex-start;
            min-width: 120px;
            padding-right: 12px;
            .item{
                font-size: 14px;
               color: #94959C;
               margin-right: 16px;
               cursor: pointer;
               &:last-child{
                margin-right: 0;
               }
                img{
                    width: 14px;
                    height: auto;
                    margin-right: 6px;
                }
            }
            .deleteCapable{
                    cursor: pointer;
                    width: 13px;
                    height: auto;
                   
                 }
        }
    }
}
</style>
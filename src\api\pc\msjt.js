import { request } from "@/utils/service"

export function applyList(params) {
  return request({
    url: "/msjt/apply/list",
    method: "get",
    params
  })
}

export function applyAdd(data) {
    return request({
      url: "/msjt/apply",
      method: "post",
      data
    })
}

export function applyDetail(liveId) {
    return request({
      url: `/msjt/apply/${liveId}`,
      method: "get",
    })
}

export function approve(liveId,data) {
    return request({
      url: `/msjt/apply/approve/${liveId}`,
      method: "post",
      data:data
    })
}

export function msjtDel(liveId) {
    return request({
      url: `/msjt/apply/del/${liveId}`,
      method: "delete",
    })
}

export function msjtClose(liveId) {
    return request({
      url: `/msjt/apply/close/${liveId}`,
      method: "get",
    })
}

export function msjtOpen(liveId) {
  return request({
    url: `/msjt/apply/open/${liveId}`,
    method: "get",
  })
}

export function msjtList(params) {
  return request({
    url: `/msjt/list`,
    method: "get",
    params
  })
}

export function msjtDetail(liveId) {
  return request({
    url: `/msjt/${liveId}`,
    method: "get",
  })
}

export function msjtView(liveId) {
  return request({
    url: `/msjt/view/${liveId}`,
    method: "get",
  })
}

export function userSign(userId) {
  return request({
    url: `/msjt/im/user-sig/${userId}`,
    method: "get",
  })
}

export function chatList(groupId,params) {
  return request({
    url: `/msjt/im/message/${groupId}`,
    method: "get",
    params
  })
}

export function imConfig() {
  return request({
    url: `/msjt/im/config`,
    method: "get",
  })
}

export function pushAddress(liveId) {
  return request({
    url: `/msjt/apply/live-view-push/${liveId}`,
    method: "get",
  })
}

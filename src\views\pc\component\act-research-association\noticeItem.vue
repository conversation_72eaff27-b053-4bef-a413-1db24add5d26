<template>
  <div class="flex_start_0 diary">
        <el-avatar :src="row.userAvatarUrl || DefaultAvatar" class="avatar head" :size="32"  style="margin-right:8px" />
          <!-- <img src="@/assets/pc/teacher.png" alt="" class="head" /> -->
          <div class="flex_1">
                 <div class="flex_between">
                    <div class="info">
                        <div class="flex_start ">
                            <p class="teacher">{{row.userName}}</p>
                            
                        </div>
                        <p class="date">{{row.postTime}}</p>
                    </div>
                     <ul class="flex_start action-list">
                        <li class="flex_start">
                            <img src="@/assets/pc/s2.png" alt="" v-show="row.isView">
                             <img src="@/assets/pc/s3-3.png" alt="" v-show="!row.isView">
                            <p :style="row.isView?'color: #94959C':'color: #496EEE'">{{row.isView?'已读':'未读'}}</p>
                        </li>
                     
                     </ul>
                 </div>
                 <div class="rich" >
                    <div class="html flex_start_0">
                        <img src="@/assets/pc/loud.png" alt="">
                        <div :class="!isopen?'rich-html':'rich-open-html'" ref="richRef"  @click="goDetail(row)">
                          {{row.content}}
                        </div>
                    </div>
                  
                     <p class="has-more" v-show="hasmore" @click="isopen=!isopen">
                         {{ isopen?'收起':'全部' }}
                     </p>
                 </div>
               
                 <div class="bottom">
                    <ul class="flex_start tag-list">
                        <li class="flex_start" v-for="item in row.topics" :key="item.id">
                            <img src="@/assets/pc/tag.png" alt="">
                            <p>{{item.name}}</p>
                        </li>
                    </ul>
                 </div>
          </div>
  </div>
</template>

<script setup>
import {onMounted, ref,defineProps, nextTick} from 'vue'
import {thumbSubmit,favoriteSubmit} from '@/api/pc/index.js'
import {useRouter,useRoute} from 'vue-router'
import DefaultAvatar from "@/assets/layouts/default-avatar.png"
let props=defineProps(['row','url'])
let emits=defineEmits(['emitIsLike','emitScrollTop'])
let starnum=ref(3)
let richRef=ref()
let hasmore=ref(false)
let isopen=ref(false)
let router=useRouter()
let route=useRoute()
function checkLines() {
    // 获取文字容器元素
    const textElement = richRef.value;
    // 比较元素的 scrollHeight 和 clientHeight
    if (textElement.scrollHeight > textElement.clientHeight) {
        // console.log('文字超过了四行');
        hasmore.value=true
    } 
}

let goDetail=(row)=>{
    emits('emitScrollTop')
    router.push(props.url+'?id='+row.id)
   
}
onMounted(()=>{
   nextTick(()=>{
     checkLines()
   })
})
</script>

<style lang="scss" scoped>
.diary{
    margin-bottom: 10px;
    background: #fff;
    padding: 16px 19px;
    .head{
        width: 52px;
        height: 52px;
        margin-right: 10px;
    }
    .info{
        .teacher{
            font-size: 16px;
            color: #2B2C33;
            font-weight: bold;
            margin-right: 24px;
        }
        .stars{
            .star-box{
                margin-right: 4px;
            }
            img{
                width: 24px;
                height: auto;
            }
        }
    }
    .date{
        font-size: 14px;
        color: #94959C;
        margin-top: 4px;
    }
    .action-list{
        li{
            font-size: 14px;
            color: #94959C;
            margin-right: 24px;
            cursor: pointer;
            &:last-child{
                margin-right: 0;
            }
            img{
                width: 14px;
                height: auto;
                margin-right: 4px;
            }
        }
    }
    .rich{
        position: relative;
        cursor: pointer;
        .html{
              margin-top:8px;
              position: relative;
           img{
            width: 24px;
            height: 27px;
            position:absolute;
            left:0;
            top:0
           }
        }
        .rich-html{
        // margin-top:8px;
        text-indent: 28px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        font-size: 16px;
        color: #2B2C33;
      padding-right: 4ch;
      line-height: 24px;
    }
    .rich-open-html{
       text-indent: 2em;
        line-height: 24px;
         font-size: 16px;
        color: #2B2C33;
    }
    .has-more{
        font-size: 16px;
        color: #496EEE;
        position: absolute;
        right: 0;
        bottom:0;
        cursor: pointer;
    }
    }
    .bottom{
        .tag-list{
            li{
                padding: 4px 10px;
                border-radius: 100px;
                font-size: 16px;
                border-radius: 19px;
                border: 1px solid #E2E3E6;
                color: #2B2C33;
                margin: 10px 10px 0 0;cursor: pointer;
                img{
                    width: 16px;
                    height: auto;
                    margin-right: 8px;
                    
                }
            }
        }
    }
}
</style>
<template>
     <div class="theme-left">
           <div class="person-column">
              <p class="title">主题交流</p>
              <ul class="list">
                <li :class="['flex_between',item.id==activeIndex?'active':'']" v-for="item in diaryList" :key="item.id" @click="changeActive(item)">
                    <div class="flex_start desc flex_1">
                        <img src="@/assets/pc/tag-1.png" alt="" v-show="item.id!=activeIndex" >
                         <img src="@/assets/pc/tag.png" alt="" v-show="item.id==activeIndex">
                        <p>{{item.title}}</p>
                       
                    </div>
                    
                   
                </li>
              </ul>
              <!-- <ul class="list">
                <li :class="['flex_between',item.id==activeIndex?'active':'']" v-for="item in diaryList" :key="item.id" @click="changeActive(item)">
                    <div class="flex_start desc flex_1">
                        <img src="@/assets/pc/tag-1.png" alt="" v-show="item.id!=activeIndex" >
                         <img src="@/assets/pc/tag.png" alt="" v-show="item.id==activeIndex">
                        <p>{{item.name}}</p>
                         <div class="flex_end flex_1" v-pcpermission='["LEADER"]'>
                            <img src="@/assets/pc/del.png" alt="" @click="goDel(item)" />
                          <img src="@/assets/pc/edit.png" alt="" @click="goEdit(item)"/>
                         </div>
                    </div>
                    
                    <div class="icon flex_center">
                        <el-icon v-show="item.id==activeIndex"><ArrowRight /></el-icon>
                    </div>
                </li>
              </ul>
              <img src="@/assets/pc/add.png" alt="" class="addTheme" @click="goAdd" v-pcpermission='["LEADER"]'/> -->
           </div>
             <!-- <el-dialog
                v-model="dialogVisible"
                title="新增主题"
                width="456px"
                :close-on-click-modal='false'
                :before-close="cancel"
            >
                <el-form :model="addForm" :rules="addRules" ref="addRef" label-width="auto" style="max-width: 600px" label-position="top">
                    <el-form-item label="主题名称" prop='name'>
                        <el-input v-model="addForm.name" placeholder="请输入主题名称" />
                    </el-form-item>
                </el-form>
                <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancel" class="cancel">取消</el-button>
                    <el-button type="primary" @click="confirm" class="confirm">
                    确认
                    </el-button>
                </div>
                </template>
            </el-dialog> -->
       </div>
</template>
<script setup>
import {onMounted, ref,defineEmits, onActivated} from 'vue'
// import {themeAdd,themeEdit,themes,themeDel} from '@/api/pc/index.js'
// import {ElMessage,ElMessageBox } from 'element-plus'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {useRoute,useRouter} from 'vue-router'
// let props=defineProps(['clubPostId'])
let route=useRoute()
let router=useRouter()
let diaryList=ref([{id:'1',title:'组内讨论'},{id:'2',title:'全社讨论'}])
let userStore=useUserInfoStoreHook()
let activeIndex=ref(1)
// let dialogVisible=ref(false)
// let handleClose=()=>{}
// let addForm=ref({name:'',id:''})
// let addRules=ref({
//     name:[{ required: true, message: '请输入主题名称', trigger: 'blur' },]
// })

// let addRef=ref()
let changeActive=(item)=>{
    if(route.path=='/actResearchAssociation/theme/detail'){
          router.replace('/actResearchAssociation/theme?clubPostType='+item.id)
    }else{
         activeIndex.value=item.id
         emits('emitThemes',activeIndex.value,item.name)
    }
   
    
}
let emits=defineEmits(['emitThemes'])
// let cancel=()=>{
//  if (!addRef.value) return
//     addForm.value={name:''}
//     addRef.value.resetFields()
//     dialogVisible.value=false
// }
// let getList=async()=>{
//     let res=await themes()
//     if(res){
//         diaryList.value=res.data
//     }
// }
// let goAdd=()=>{
//     addForm.value.id=''
//     dialogVisible.value=true

// }
// let goDel=(item)=>{
//  ElMessageBox.confirm(
//     ('确认删除主题 '+item.name+'？'),
//     '提示',
//     {
//       confirmButtonText: '确认',
//       cancelButtonText: '取消',
//       type: 'warning',
//     }
//   )
//     .then(async() => {
//         let res=await themeDel({id:item.id})
//         if(res){
//             activeIndex.value=diaryList.value[0].id
//              emits('emitThemes',activeIndex.value,diaryList.value[0].name)
//             ElMessage({
//                type: 'success',
//                message: '删除成功',
//             })
//             getList()
//         }
     
//     })
//     .catch(() => {
    
//     })
// }
// let goEdit=(item)=>{
//  addForm.value={
//     name:item.name,
//     id:item.id
//  }
//  dialogVisible.value=true
// }
// let confirm=async()=>{
//      if (!addRef.value) return
//        await addRef.value.validate(async(valid, fields) => {
//         if (valid) {
//             if(addForm.value.id){
//              let res=await themeEdit({
//                 name:addForm.value.name,
//                 id:addForm.value.id
//              })
//             if(res){
//                 ElMessage.success("编辑成功")
//                 getList()
//                 cancel()
//             }
//             }else{
//               let res=await themeAdd({
//                 name:addForm.value.name
//               })
//             if(res){
//                 ElMessage.success("新增成功")
//                 getList()
//                 cancel()
//             }
//             }
           
//         } else {
//            console.log('error submit!', fields)
//         }
//   })
// }
onMounted(async()=>{
    // await getList()
    if(route.query.clubPostType){
       activeIndex.value= route.query.clubPostType
    }else{
       activeIndex.value=diaryList.value[0].id
    }
     emits('emitThemes',activeIndex.value)
})
onActivated(async()=>{
    // console.log(userStore.roles)
    // if(userStore.roles.indexOf("MEMBER")>-1){
    //      diaryList.value=[{id:'1',title:'组内讨论'}]
    // }
    if(route.query.clubPostType){
       activeIndex.value= route.query.clubPostType
    }else{
       activeIndex.value=diaryList.value[0].id
    }
   
     emits('emitThemes',activeIndex.value)
})
</script>
<style scoped lang='scss'>
.theme-left{
    // width: 180px;
   height: calc(100vh - 180px);
    overflow: auto;
    .person-column{
        border-radius: 8px;
        background: #fff;
        padding-bottom: 16px;
        .title{
             padding: 18px 0  24px 18px;
             font-weight: bold;
             font-size: 20px;
             color: #2B2C33;
        }
        .list{
            li{
                cursor: pointer;
                padding: 10px 8px  8px 18px;
                font-size: 16px;
                color: #2B2C33;
                &.active{
                    background: #DFEAFF;color: #508CFF;
                }
                .desc{
                    img{
                        width: 12px;
                        height: auto;
                        margin-right: 4px;
                    }
                }
                p{
                    width:80px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
    }
    .friend-link{
        margin-top: 10px;
    }
    .icon{
        width: 18px;
    }
    .addTheme{
        width: 19px;
        height: auto;
        margin-top: 20px;
       margin-left: 14px;
       cursor: pointer;
    }
}
</style>
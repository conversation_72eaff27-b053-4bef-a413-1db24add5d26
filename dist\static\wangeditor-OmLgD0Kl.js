import"./index.esm-Rd4GtLAD.js";import{a as g}from"./pc-upload-DDnPogYD.js";import{T as h,E as v}from"./index.esm-BPDRZKp6.js";/* empty css                                                                   */import{_ as C}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{s as x,r as y,h as H,j as b,W as E,z as N,A as V,B,Q as i,u as o}from"./vue-CelzWiMA.js";const O={class:"editor-container"},U={style:{border:"1px solid #ccc"}},w={__name:"wangeditor",props:["html"],emits:["emitHtml"],setup(m,{emit:c}){let n="default";const l=x();let u=m,s=c;const a=y("");H(()=>{}),b(()=>u.html,e=>{a.value=e},{immediate:!0});const p={excludeKeys:["insertVideo","group-video","fullScreen"]};let f=e=>{e.getHtml()=="<p><br></p>"?s("emitHtml",""):s("emitHtml",e.getHtml())};const d={placeholder:"请输入内容...",MENU_CONF:{}};d.MENU_CONF.uploadImage={async customUpload(e,t){g(e,r=>{t(r)})}},E(()=>{const e=l.value;e!=null&&e.destroy()});const _=e=>{l.value=e};return(e,t)=>(V(),N("div",O,[B("div",U,[i(o(h),{style:{"border-bottom":"1px solid #ccc"},editor:l.value,defaultConfig:p,mode:o(n)},null,8,["editor","mode"]),i(o(v),{style:{height:"310px","overflow-y":"hidden"},modelValue:a.value,"onUpdate:modelValue":t[0]||(t[0]=r=>a.value=r),defaultConfig:d,mode:o(n),onOnCreated:_,onOnChange:o(f)},null,8,["modelValue","mode","onOnChange"])])]))}},T=C(w,[["__scopeId","data-v-7518858e"]]);export{T as W};

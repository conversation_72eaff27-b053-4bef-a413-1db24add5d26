<template>
   
       <div class="content-box  preview mt10 flex_start_0" style="width:100%">
         <div class="left flex_column flex_start_0">
                <div class="flex_between">
                    <div class="flex_start title" @click="goCancel">
                        <el-icon style="margin-right: 4px;"><ArrowLeft /></el-icon>
                       {{row.title}}
                    </div>
                    <div class="flex_start" v-if="currentInfo.type=='teacher'">
                        <div class="flex_start liked" @click="route.query.type!='preview'&&changeFavorite()">
                             <img src="@/assets/pc/r-comment.png" alt="" v-show="!row.isFavorite" />
                              <img src="@/assets/pc/r-liked.png" alt="" v-show="row.isFavorite" />
                            <p>{{row.favoriteCount?row.favoriteCount:0}}</p>
                        </div>
                        <div class="flex_start liked star" @click="route.query.type!='preview'&&changeLike">
                             <img src="@/assets/pc/star.png" alt="" v-show="!row.isLike" />
                            <img src="@/assets/pc/r-star.png" alt="" v-show="row.isLike" />
                            <p>{{row.likeCount?row.likeCount:0}}</p>
                        </div>
                    </div>
                </div>
                <div class="flex_between" style="margin-top:8px">
                    <div class="flex_start detail">
                        <p>上传时间：{{row.createTime?row.createTime.slice(0,10):''}}</p>
                        <p>播放量：{{row.viewCount}}</p>
                        <p>分享次数：{{row.shareCount}}</p>
                    </div>
                    <div class="flex_start action" v-show="route.query.type!='preview'">
                              <div class="flex_start edit" @click="goEdit" v-show="row.canUp">
                                    <img src="@/assets/pc/action-edit.png" alt="">
                                    <p>编辑</p>
                              </div>
                              <div class="flex_start copy" @click="goCopy">
                                    <img src="@/assets/pc/action-copy.png" alt=""  />
                                    <p>复制链接</p>
                              </div>
                              
                    </div>
                     
                </div>
                
                <div class="file flex_1">
                    <Preview :row='row' />
                </div>
                <div class="flex_between bottom-detail">
                   <div class="flex_start autor">
                      <div class="flex_start autor1">
                          <div class="flex_start">
                            <img src="@/assets/pc/autor1.png" alt="">
                            <p class="label">资源作者</p>
                          </div>
                           <p class="value">{{row.createTitle}}</p>
                      </div>
                       <div class="flex_start autor1">
                          <div class="flex_start">
                            <img src="@/assets/pc/autor2.png" alt="">
                            <p class="label">所属单位</p>
                          </div>
                          <p class="value">{{row.schoolTitle}}</p>
                      </div>
                  </div>
                  <div class="flex_start" v-if="route.query.type!='preview'">
                    <!--  -->
                      <button class="uplevel"  v-show="(row.upgrade==2||row.upgrade==3)&&currentInfo.type=='teacher'" @click="row.upgrade==3&&goUpLevel()">{{row.upgrade==2?'审核中':'我要升级'}}</button>
                      <button class="uplevel" v-show="canEdit" @click="goJYFX" style="margin-left:12px">{{row.analysis==1?'分析中':'教研分析'}}</button>
                  </div>
                
                </div>
               
        </div>
        <div class="right flex_1">
            <div class="title">相关推荐</div>
            <el-empty description="暂无相关推荐" v-show="recommendList.length==0"></el-empty>
            <ul  v-show="recommendList.length!=0">
                <li class="item" v-for="item in recommendList" :key="item.id" @click="goRecommend(item)">
                    <p>{{item.title}}</p>
                   
                    <div class="recommend-cover">
                          <img :src="item.sealImg" alt="" v-if="item.sealImg" />
                          <img src="@/assets/pc/emptyvideo.png" alt="" v-else />
                    </div>
                </li>
            </ul>
        </div>
       </div>
       <div class="content-box  preview mt10 " style="margin-bottom:10px;width:100%" v-if="route.query.type!='preview'&&!row.isEvaluate&&userInfo.globalRoles&&(userInfo.globalRoles.indexOf('EXPERT')>-1||userInfo.globalRoles.indexOf('DIRECTOR')>-1)">
         <div class="comment-box  flex_start_0" style="width:100%;">
            <div class="left-star">
                <el-form :model="commentForm"  label-position="top" ref="evaluateFormRef" :rules="commentRules">
                    <el-form-item label='教学目标诊断' prop='targetAccurate'>
                        <div class="flex_start item">
                            <p class="flex_start">目标明确性</p>
                            <el-rate v-model="commentForm.targetAccurate" size="large" />
                        </div>
                      <div class="flex_start item">
                            <p class="flex_start">目标达成度</p>
                            <el-rate v-model="commentForm.targetAchievement" size="large" />
                        </div>
                    </el-form-item>
                    <el-form-item label='教学内容诊断' prop='contentAccurate'>
                        <div class="flex_start item">
                            <p class="flex_start">内容准确性</p>
                            <el-rate v-model="commentForm.contentAccurate" size="large" />
                        </div>
                      <div class="flex_start item">
                            <p class="flex_start">内容逻辑性</p>
                            <el-rate v-model="commentForm.contentLogic" size="large" />
                        </div>
                        <div class="flex_start item">
                            <p class="flex_start">内容趣味性</p>
                            <el-rate v-model="commentForm.contentInteresting" size="large" />
                        </div>
                    </el-form-item>
                    <el-form-item label='教学方法诊断' prop='methodDiversity'>
                        <div class="flex_start item">
                            <p class="flex_start">方法多样性</p>
                            <el-rate v-model="commentForm.methodDiversity" size="large" />
                        </div>
                      <div class="flex_start item">
                            <p class="flex_start">方法有效性</p>
                            <el-rate v-model="commentForm.methodEffective" size="large" />
                        </div>
                        <div class="flex_start item">
                            <p class="flex_start">学生参与度</p>
                            <el-rate v-model="commentForm.studentEngagement" size="large" />
                        </div>
                    </el-form-item>
                    <el-form-item label='教学效果诊断' prop='homeworkCompletion'>
                        <div class="flex_start item">
                            <p class="flex_start">作业完成情况</p>
                            <el-rate v-model="commentForm.homeworkCompletion" size="large" />
                        </div>
                      <div class="flex_start item">
                            <p class="flex_start">考试成绩</p>
                            <el-rate v-model="commentForm.examResults" size="large" />
                        </div>
                    </el-form-item>
                </el-form>
            </div>
            <div class="right-text flex_start_0 flex_column flex_1">
                <p>总体评价</p>
                <el-input type="textarea" v-model="commentForm.evaluate" style="height:100%;width:100%" resize="none" placeholder="请输入"></el-input>
            </div>
         </div>

         <div class="flex_center" style="margin-top:10px" >
            <el-button class="submit" @click="goSumit" style="width: 184px;height: 40px;background: #508CFF;border-radius: 12px;color:#fff;">提交评价</el-button>
         </div>
        
       </div>
        <div class="content-box  preview mt10 " style="margin-bottom:10px;width:100%">
           <div class="comment-list"  style="overflow:auto">
                         <div class="comment-item flex_between_0" v-for="item in comments" :key="item.id">
                                
                                <div class="flex_start_0 level_1_comment">
                                     <img :src="item.userAvatarUrl || DefaultAvatar" alt="" class="teacher-logo">
                                     <!-- <el-avatar :src="item.userAvatarUrl || DefaultAvatar" class="avatar teacher-logo" :size="32" @click="goPreview(item)" /> -->
                                    <div>
                                       <div class="flex_start_0">
                                        <div class="flex_start_0 flex_column" style="margin-right:24px">
                                            <p class="teacher-name">{{item.userName}}</p>
                                             <p class="from">{{item.createTime}}</p>
                                        </div>
                                        <el-rate v-model="item.finalScore" size="large" disabled />
                                        <!--   -->
                                      </div>
                                       <p class="teacher-write flex_1">{{item.evaluate}}</p>
                                    </div>
                                    
                                </div>
                               
                         </div>
                     </div>
                     <el-empty description="暂无评论" style="height:220px"  v-show="commentTotal==0"></el-empty>
                       <el-pagination
                            :current-page="commentSearch.pageNum"
                            background
                             v-show="commentTotal!=0"
                            layout="total,prev, pager, next"
                            :total="commentTotal"
                            v-model:page-size='commentSearch.pageSize'
                            class="mt-4"
                            @current-change='currentCommentChange'
                        />
        </div>
        <!-- 升级资源 -->
        <el-dialog
            v-model="dialogVisible"
            title="升级资源"
            width="456"
            :before-close="cancelUpLevel"
        >
            <el-form label-position="top"  ref="levelRef"
            style="max-width: 600px"
            :model="levelForm"
            :rules="levelRules">
           
                <el-form-item label='升级范围'  prop='level'>
                        <el-select v-model="levelForm.level" placeholder='请选择升级范围' @change="changeLevel">
                            <el-option v-for="item in tenantTypes" :key="item.id" :value='item.id' :label='item.title'></el-option>
                        </el-select>
                </el-form-item>
                    </el-form>
                <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelUpLevel">取消</el-button>
                    <el-button type="primary" @click="confirmUpLevel" :loading='loading'>
                    确认
                    </el-button>
                </div>
                </template>
        </el-dialog>  
        <el-dialog
            v-model="teacherFileDialogVisible"
            title="教案上传"
            width="912"
        >
         <template #header="{ close, titleId, titleClass }">
            <div class="my-header flex_start">
                <h4 :id="titleId" :class="titleClass" style="margin-right:12px">教案上传</h4>
                <el-icon style="margin-right:6px"><QuestionFilled /></el-icon>
                <p style="margin-right:12px">若不上传教案则分析结果中不包含教案分析内容</p>
                <el-button type="primary" @click="goJYFXAction({})">跳过继续分析</el-button>
            </div>
            </template>
            <el-form 
                label-position="top"  
                ref="teacherFileRef"
                style="max-width: 100%"
                :model="teacherFileForm"
                :rules="teacherFileRules">
                <el-form-item label='教学目标'  prop='teachTarget'>
                        <el-input placeholder="请输入教学目标" v-model="teacherFileForm.teachTarget"></el-input>
                </el-form-item>
                {{teacherFileForm}}
                 <el-form-item label='教学内容（不同内容用换行隔开）'  prop='teachContentList'>
                        <el-input type="textarea" v-model="teacherFileForm.teachContentList" placeholder="请输入教学内容" resize="none" rows="6"></el-input>
                </el-form-item>
             </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelTeacherFile">取消</el-button>
                    <el-button type="primary" @click="confirmTeachFile"> 确认</el-button>
                </div>
                </template>
        </el-dialog>  
</template>
<script setup>
import {onMounted, reactive, ref} from 'vue'
import DefaultAvatar from "@/assets/layouts/default-avatar.png"
import {resourceRescommend,resourceFavourite,resourceLike,resourceDel,resourceShare,userTenant,userTenantToParent,} from '@/api/pc/index.js'
import Preview from '@/views/pc/component/resource/preview.vue'
import {jyfxDetail,jyfxDetailRecommend,jyfxFavourite,jyfxLike,jyfxShare,jyfxAnalysis,jyfxEvaluate,jyfxEvaluates,jyfxDetailNew,wisdomUpLevel} from '@/api/pc/wisdom.js'
import {useRoute,useRouter} from 'vue-router'
import {ElMessageBox,ElMessage,ElLoading} from 'element-plus'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'
let userInfoStore=useUserInfoStoreHook()
let {currentInfo,userInfo}=storeToRefs(userInfoStore)
let route=useRoute()
let router=useRouter()
let resourceId=''
let row=ref({})
let recommendList=ref([])
let goCancel=()=>{
    router.go(-1)
}
let canEdit=ref(false)
let levelRef=ref()
let tenantOptions=ref([])
let tenantTypes=ref([])
let tenantMap={
      30:'区(县、县级市)',
      40:'街道/镇',  
      50:'学校',  
      60:'集团校'

}
// 教案上传
let teacherFileRules={
    teachContentList:[{required: true, message: '请输入教学内容',trigger: 'change',},],
    teachTarget:[{required: true, message: '请输入教学目标',trigger: 'change',},]
}
let teacherFileRef=ref()
let teacherFileForm=reactive({
  teachContentList:'',
  teachTarget:''
})
let teacherFileDialogVisible=ref(false)
const confirmTeachFile=()=>{
    //   let temp=teacherFileForm.teachContentList.slice('\n')
    //   console.log(teacherFileForm.teachContentList)
     const linesArray = teacherFileForm.teachContentList.split(/\r?\n/);
    // 3. 可选：过滤空行
    const filteredArray = linesArray.filter(line => line.trim()!== "");
   if (!teacherFileRef.value) return
    teacherFileRef.value.validate(async(valid, fields) => {
        if (valid) {
        let params={
            teachContentList:filteredArray,
            teachTarget:teacherFileForm.teachTarget
        }
        goJYFXAction(params)
        //   console.log('submit!')
        } else {
        console.log('error submit!', fields)
        }
    })
    }
    const cancelTeacherFile=()=>{
      teacherFileRef.value.resetFields()
    }
//升级资源模块
let dialogVisible=ref(false)
let loading=ref(false)
let levelForm=reactive({
    toTenantId:'',
    level:''
})
let levelRules={
    level:[{required: true, message: '请选择升级范围',trigger: 'change',},],
    toTenantId:[{required: true, message: '请选择所属单位',trigger: 'change',},]
}
const goUpLevel=()=>{
  dialogVisible.value=true
}
const changeLevel=(val)=>{
    if(val==50){
       Object.assign(levelForm,{
           toTenantId:row.value.tenantId
        })
    }else{
        getUserTenant()
    }
}
const cancelUpLevel=()=>{
  dialogVisible.value=false
  Object.assign(levelForm,{
     toTenantId:'',
      level:''
  })
  levelRef.value.resetFields()
}
const confirmUpLevel=async()=>{
   if (!levelRef.value) return
  await levelRef.value.validate(async(valid, fields) => {
    if (valid) {
        loading.value=true
        let res=await wisdomUpLevel({
            toTenantId:levelForm.toTenantId,
            level:levelForm.level,
            resourceId:resourceId
        })
        if(res){
            loading.value=false
            ElMessage.success('申请已提交审核')
            row.value.upgrade=2
            cancelUpLevel()
        }else{
            loading.value=false
        }
    //   console.log('submit!')
    } else {
      console.log('error submit!', fields)
    }
  })
}
// 
let goJYFXAction=(row)=>{
       let loadingInstance = ElLoading.service({ fullscreen: true,text:"分析中,请稍候" })
        jyfxAnalysis({
                resourceId,
                ...row
        }).then(res=>{
            loadingInstance.close()
            row.value.analysis=1
            teacherFileDialogVisible.value=false
            //  Object.assign(teacherFileForm,{
            //     teachContentList:'',
            //     teachTarget:''
            //  })
            teacherFileRef.value.resetFields()
            ElMessage.success('提交成功')
        }).catch(err=>{
             teacherFileRef.value.resetFields()
            //  Object.assign(teacherFileForm,{
            //     teachContentList:'',
            //     teachTarget:''
            //  })
            loadingInstance.close()
        })
}
let goJYFX=async()=>{
    // console.log(row.value)
    if(row.value.analysis==1){
        ElMessage.warning('分析中，请稍候')
    }else if(row.value.analysis==2){
      window.open(import.meta.env.VITE_REDIRECT_URL+'/jyfx/#/login?resourceId='+resourceId)
    }else{
        teacherFileDialogVisible.value=true
        //     ElMessageBox.confirm(
        //     '确认将该资源提交分析吗?',
        //     '提示',
        //     {
        //     confirmButtonText: '确认',
        //     cancelButtonText: '取消',
        //     type: 'warning',
        //     }
        // )
        //     .then(async() => {
        //          let loadingInstance = ElLoading.service({ fullscreen: true,text:"分析中,请稍候" })
        //          jyfxAnalysis({
        //             resourceId
        //         }).then(res=>{
        //             loadingInstance.close()
        //             row.value.analysis=1
        //             ElMessage.success('提交成功')
        //             // if(res.data==200){
                     
        //             // }else {
        //             //     row.value.analysis=0
        //             //     ElMessage.error(res.data.msg)
        //             // }   
        //         }).catch(err=>{
        //              loadingInstance.close()
        //         })
                
            
        //     })
        //     .catch(() => {
            
        //     })
        
    }
    // 
}
let goCopy=async()=>{
    let url=import.meta.env.VITE_REDIRECT_URL+'/dxs/#/publicwisdom?id='+resourceId
       
            try {
                // 创建一个 ClipboardItem 对象，包含 HTML 和纯文本内容
                await navigator.clipboard.writeText(url).then(
                    async() => {
                         let res=await jyfxShare({
                            resourceId
                         })
                       
                    
                         if(res){
                            row.value.shareCount=row.value.shareCount+1
                         }
                        ElMessage.success("链接已复制到剪贴板");
                    },
                    (err) => {
                        console.log("~ copyText ~ err:", err);
                    })
            } catch (err) {
                console.error('复制失败:', err);
            }
}
let goEdit=()=>{
    router.push('/resource/wisdom/edit?id='+resourceId)
}


let goRecommend=(item)=>{
     router.push('/resource/wisdom/detail?id='+item.resourceId+'&type='+(route.query.type?route.query.type:'')).then(()=>{
         resourceId=route.query.id
    
         getDetail()
     })
}
let goDel=()=>{
     ElMessageBox.confirm(
    '确认删除该资源吗?',
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async() => {
        let res=await resourceDel({
            resourceId
        })
        if(res){
           router.go(-1)
            ElMessage({
                type: 'success',
                message: '删除成功',
            })

        }
    
    })
    .catch(() => {
      
    })
}
let getDetail=async()=>{
    let res=await jyfxDetailNew({
        resourceId
    })
    if(res){
        if(res.data.tenantTypes){
            res.data.tenantTypes.forEach(item=>{
                tenantTypes.value.push({
                    id:item,
                    title:tenantMap[item]
                })
            })
        }
        res.data.fileType=2
        res.data.from='wisdom'  //用于标记哪个模块的  后端数据不统一数据得重新转换成我需要的

        row.value=res.data
    }
}

let changeFavorite=async()=>{
    let res=await jyfxFavourite({resourceId:resourceId})
     if(res){
        row.value.isFavorite=!row.value.isFavorite
        if(row.value.isFavorite){
              row.value.favoriteCount= row.value.favoriteCount+1
        }else{
            row.value.favoriteCount= row.value.favoriteCount-1
        }
     }
}
let changeLike=async()=>{
 let res=await jyfxLike({resourceId:resourceId})
   if(res){
        row.value.isLike=!row.value.isLike
        if(row.value.isLike){
              row.value.likeCount= row.value.likeCount+1
        }else{
            row.value.likeCount= row.value.likeCount-1
        }
     }
}
let getRecommend=async()=>{
    let res=await jyfxDetailRecommend({
        resourceId
    })  
    if(res){
        res.data.forEach(item=>{
            item.coverFileUrl=import.meta.env.VITE_BASE_URL+'/fs/file?fileId='+item.coverFileId
        })
       recommendList.value=res.data
    }
}
let evaluateFormRef=ref()
let comments=ref([
    
])
let commentSearch=reactive({
    pageSize:10,
    pageNum:1
})
let commentForm=reactive({
    targetAccurate:0,
    targetAchievement:0,

    contentAccurate:0,
    contentLogic:0,
    contentInteresting:0,

    methodDiversity:0,
    methodEffective:0,
    studentEngagement:0,

    homeworkCompletion:0,
    examResults:0,
    evaluate:'',
})
let checkedTargetAccurate=(rule,value,callBack)=>{
    if(commentForm.targetAchievement==0||commentForm.targetAccurate==0){
       callBack(new Error('填写完整教学目标诊断'))
    }else{
        callBack()
    }
}
let checkedContentAccurate=(rule,value,callBack)=>{
 if(commentForm.contentAccurate==0||commentForm.contentLogic==0||commentForm.contentInteresting==0){
       callBack(new Error('填写完整教学内容诊断'))
    }else{
        callBack()
    }
}
let checkedMethodDiversity=(rule,value,callBack)=>{
if(commentForm.methodDiversity==0||commentForm.methodEffective==0||commentForm.studentEngagement==0){
       callBack(new Error('填写完整教学方法诊断'))
    }else{
        callBack()
    }
}
let checkedHomeworkCompletion=(rule,value,callBack)=>{
if(commentForm.homeworkCompletion==0||commentForm.examResults==0){
       callBack(new Error('填写完整教学效果诊断'))
    }else{
        callBack()
    }
}
let commentRules={
  targetAccurate:[{ required: true, message: '请选择',trigger: 'change'},{ validator: checkedTargetAccurate, trigger: 'change' }],
  contentAccurate:[{ required: true, message: '请选择',trigger: 'change'},{ validator: checkedContentAccurate, trigger: 'change' }],
  methodDiversity:[{ required: true, message: '请选择',trigger: 'change'},{ validator: checkedMethodDiversity, trigger: 'change' }],
  homeworkCompletion:[{ required: true, message: '请选择',trigger: 'change'},{ validator: checkedHomeworkCompletion, trigger: 'change' }],
}
let goSumit=()=>{
   evaluateFormRef.value.validate(async(valid) => {
    if (valid) {
      ElMessageBox.confirm(
           '确认提交吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
               let res=await jyfxEvaluate({
                    resourceId,
                    ...commentForm
                })
                if(res){
                    ElMessage.success('评价成功')
                    row.value.isEvaluate=true
                    getComments()
                }
            
        })
        .catch(() => {
      
        })
    
    } else {
      console.log('error submit!')
    }
  })
}
let commentTotal=ref(0)
let currentCommentChange=(val)=>{
    Object.assign(commentSearch,{
        pageNum:val
    })
    getComments()
}
let getComments=async()=>{
    let res=await jyfxEvaluates({
        resourceId,
        ...commentSearch
    })
    if(res){
        commentTotal.value=res.data.total
           comments.value=res.data.list
    }
}
onMounted(()=>{
    resourceId=route.query.id
    // console.log(userInfoStore.userInfoStore.userTenants,'11')
    getComments()
    if(userInfoStore.userInfo.userTenants){
         canEdit.value=userInfoStore.userInfo.userTenants.some(item=>item.tenantType==30&&item.isAdmin)
       
    }
    getDetail()
    getRecommend()
})
</script>
    <style scoped lang='scss'>
    .preview{
       background: #fff;
       padding: 24px;
       .left{
        width: 70%;
        padding-right: 24px;
        border-right: 1px solid #E2E3E6;
        .title{
            font-size: 20px;
            color: #2B2C33;
            font-weight: bold;
            cursor: pointer;
        }
        .liked{
            img{
            width: 24px;
            height: auto;
            margin-right: 4px;
              cursor: pointer;
            }
            p{
                font-size: 14px;
                color: #94959C;
            }
        }
        .star{
            margin-left: 32px;
            cursor: pointer;
        }
        .detail{
          
            p{
                font-size: 14px;
                color: #6D6F75;
                margin-right: 32px;
            }
        }
        .action{
            font-size: 14px;
            img{
                width: 14px;
                height: auto;
                margin-right: 4px;
            }
            .edit{
                color: #508CFF;
                margin-right: 24px;
                cursor: pointer;
            }
            .del{
             color: #E72C4A;
             margin-right: 24px; cursor: pointer;
            }
            .copy{
              color: #508CFF;   cursor: pointer;
            }
        }
        .file{
            // height: 452px;
            margin-top: 16px;
            // border:1px solid
        }
        .bottom-detail{
            margin-top: 24px;
            .autor{
           
            .autor1{
                margin-right: 80px;
                &:last-child{
                    margin-right: 0;
                }
                img{
                    height: 16px;
                    width: auto;
                    margin-right: 4px;
                }
                .label{
                    font-size: 14px;
                   color: #6D6F75;
                   margin-right: 24px;
                }
                .value{
                    font-size: 14px;
                   color: #2B2C33;
                }
            }
        }
        .uplevel{
             width: 128px;
                height: 40px;
                background: #508CFF;
                border-radius: 12px;
                color: #fff;
                border:none;
                cursor: pointer;
           }
        }
      
       }
       .right{
         padding-left: 24px;
        .title{
            font-size: 18px;
           color: #2B2C33;
           font-weight: bold;
           margin-bottom: 16px;
        }
        ul{
            height: calc(100vh - 300px);
            overflow: auto;
        }
        .item{
            margin-bottom: 16px;
            &:last-child{
                margin-bottom: 0;
            }
             p{
                font-size: 16px;
               color: #2B2C33;
             
               margin-bottom: 10px;
             }
             .recommend-cover{
                width: 100%;
                height: 125px;
                img{
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
             }
        }
       }
    }
    .comment-box{
        padding: 14px;
        :deep(.el-form-item__label){
            font-size: 18px;
           color: #2B2C33;
           font-weight: bold;
        }
        .left-star{
            width: 50%;
            // :deep(.el-rate .el-rate__icon){
            //     font-size: 24px;
            // }
            //  :deep(.el-rate .is-active), :deep(.el-rate .hover){
            //     font-size: 24px;
            // }
            :deep(.el-form-item:last-child){
                margin-bottom: 0;
            }
          .item{
            padding-left: 64px;
            // margin-bottom:8px;
            p{
                font-size: 16px;
                color: #2B2C33;
                margin-right: 12px;
                width: 120px;
                &::before{
                    content: '';
                    display: block;
                    width: 6px;
                    height: 6px;
                   background: #508CFF;
                   border-radius: 200px;
                   margin-right: 8px;
                }
            }
          }
        }
        .right-text{
            p{
             font-size: 18px;
              color: #2B2C33;
              font-weight: bold;
                margin-bottom: 16px;
            }
            :deep(.el-textarea__inner){
                height: 100%;
              
            }
        }
      
    }
     .comment-list{
            // max-height: 600px;
            overflow: auto;
            // &::-webkit-scrollbar {
            //   display: none;
            //  }
            margin-top: 24px;
            .comment-item{
                 margin-top: 24px;
                 &:first-child{
                    margin-top: 0;
                 }
                 :deep(.el-rate--large){
                    height: 20px;
                 }
                 .level_1_comment{
                    .teacher-logo{
                        width: 34px;
                        height: 34px;
                        border-radius: 200px;
                        margin-right: 10px;
                    }
                    .teacher-name{
                      font-size: 16px;
                       color: #2B2C33;
                        font-weight: bold;
                    }
                 
                    .from{
                        font-size: 14px;
                        margin-top: 4px;
                        color: #94959C;
                        line-height: 20px;
                    }
                 }
                
                 .teacher-write{
                    cursor: pointer;
                    margin-top: 4px;
                    line-height: 24px;
                 }
                 
            }
        }
:deep(.el-rate .el-rate__icon){
    font-size: 24px;
}
    </style>
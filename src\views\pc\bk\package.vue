<template>
    <div class="content-box  question mt10" >
          <div class="top-header flex_between">
                <ul class="tab flex_start">
                    <li :class="['flex_center',form.isMy?'active':'']" @click="changeTab(true)">我的课例包</li>
                    <li :class="['flex_center',!form.isMy?'rightactive':'']" @click="changeTab(false)">分享课例包</li>
                </ul>
                <div class="ipt flex_1 flex_start">
                    <input type="text" placeholder="请输入" class="flex_1" v-model="form.name" @keyup.enter="search">
                   <el-icon style="font-size:20px;cursor:pointer" @click="search"><Search /></el-icon>
                </div>
                <div class="btnlist">
                      <el-button @click="goBatchShare">
                          <img src="@/assets/pc/bk-share.png" alt="">
                          分享
                      </el-button>
                      <el-button @click="goBatchDel">
                          <img src="@/assets/pc/bk-del.png" alt="">
                          删除
                      </el-button>
                </div>
          </div>
         
          <ul class="flex_start flex_wrap kl-list" v-show="tableData!=0">
              <li class="kl-item" v-for="item in tableData" :key="item.id" >
                  <div class="flex_between">
                       <div class="flex_start logo" @click="goDetail(item)">
                           <img src="@/assets/pc/kl-package.png" alt="">
                           <div>
                              <h3>{{item.name}}</h3>
                              <p style="margin-top:4px">{{item.createTime}}</p>
                           </div>
                       </div>
                       <img src="@/assets/pc/check.png" alt="" class="check" v-show="idList.indexOf(item.id)<0" @click.self="check(item,true)"/>
                       <img src="@/assets/pc/checked.png" alt="" class="check" v-show="idList.indexOf(item.id)>-1" @click.self="check(item,false)"/>
                  </div>
                  <div class="class-info" @click="goDetail(item)">
                     <div class="flex_between item">
                        <p class="label">学科</p>
                        <p class="value">{{item.subjectName?item.subjectName:'-'}}</p>
                     </div>
                     <div class="flex_between item">
                        <p class="label">教材</p>
                        <p class="value">{{item.editionName?item.editionName:'-'}}</p>
                     </div>
                     <div class="flex_between item">
                        <p class="label">章（课）节</p>
                        <p class="value">{{item.volumeName?item.volumeName:'-'}}</p>
                     </div>
                  </div>
                  <div class="btn-list flex_center">
                         <button class="flex_start"  @click="goStartClass(item)">
                            <img src="@/assets/pc/kl-user.png" alt="">
                             开始上课
                         </button>
                         <button class="flex_start" @click="goEdit(item)" style="margin-left:16px" v-show="form.isMy">
                            <img src="@/assets/pc/kl-user.png" alt="">
                             编辑
                         </button>
                  </div>
              </li>
          </ul>
           <el-empty description="暂无数据" v-show="total==0"  style="height:calc(100vh - 200px)"/>
          <el-pagination
                background
                layout="total,prev, pager, next"
                :total="total"
                v-show="total!=0"
                v-model:page-size='form.pageSize'
                class="mt-4"
                @current-change='currentChange'
                style="margin-bottom:32px"
            />
    </div>
    <choicePersonDialog :choicePersonDialogShow='choicePersonDialogShow' :idList='idList' @closeDialog='closeDialog'/>
</template>
<script setup>
import {onActivated, onMounted, reactive, ref} from 'vue'
import {packageList,packageBatchDel} from '@/api/pc/bk.js'
import {useRouter} from 'vue-router'
import {ElMessage,ElMessageBox} from 'element-plus'
import choicePersonDialog from '@/views/pc/component/bk/choicePersonDialog.vue'
let router=useRouter()
let idList=ref([])
let form =reactive({
    pageSize:9,
    pageNum:1,
    name:'',
    isMy:true
})
let choicePersonDialogShow=ref(false)
let goEdit=(row)=>{
  router.push('/bk/res/editres?id='+row.id)
}
let goStartClass=(row)=>{
     router.push('/fullscreen?id='+row.id)
}
let check=(row,arg)=>{
    if(arg){
       idList.value.push(row.id)
    }else{
       idList.value=idList.value.filter(item=>{
          return item!=row.id
       })
    }
}
let closeDialog=()=>{
    choicePersonDialogShow.value=false
}
let goBatchShare=()=>{
    if(idList.value.length==0){
        ElMessage.warning('请选择文件')
    }else{
        choicePersonDialogShow.value=true
    }
}
let goBatchDel=()=>{
     if(idList.value.length==0){
        ElMessage.warning('请选择文件')
        return
    }
      ElMessageBox.confirm(
            '确认删除吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
            let res=await packageBatchDel({
                idList:idList.value
            })
            if(res){
                ElMessage.success('删除成功')
               search()
                // router.go(-1)
            }
        })
        .catch(() => {
        }) 
}
let currentChange=(val)=>{
   Object.assign(form,{
        pageNum:val
    })
    getList()
}
let changeTab=(arg)=>{
    Object.assign(form,{
        isMy:arg,
        pageNum:1,
    })
    getList()
}
let search=()=>{
    Object.assign(form,{
      
        pageNum:1,
    })
    getList()
}
let total=ref(0)
let tableData=ref([])
let getList=async()=>{
  let res=await packageList(form)
  if(res){
 
    tableData.value=res.data.list
    total.value=res.data.total
  }
}
let goDetail=(row)=>{
    router.push('/bk/package/detail?packageId='+row.id)
}
onMounted(()=>{
  
})
onActivated(()=>{
 getList() 
})
</script>
<style scoped lang='scss'>
.top-header{
    .tab{
        border-radius: 8px;
        background: #fff;
      li{
        width: 144px;
        height: 40px;
        font-size: 16px;
         color: #6D6F75;
         cursor: pointer;
      }
      .active{
        background: #508CFF;
        color: #fff;
        border-radius: 8px 0px 0px 8px
      }
      .rightactive{
         background: #508CFF;
        color: #fff;
        border-radius: 0 8px 8px 0
      }
    }
    .ipt{
        margin: 0 10px;
        height: 40px;
        background: #fff;
        border-radius: 100px;
         padding: 4px 16px;
        input{
           border: none;
        }
    }
}
.kl-list{
    .kl-item{
        cursor: pointer;
        padding: 24px 24px 16px 24px;
        background: #F1F3F7;
        width: 32%;
        margin-right: 2%;
        border-radius: 12px;
        margin-top: 10px;
        background: url('@/assets/pc/kl-bg.png') no-repeat;
        background-size:100% 100%;
        &:nth-child(3n+3){
            margin-right: 0;
        }
        .logo{
             img{
                width: 48px;
                height: auto;
                margin-right: 16px;
             }
             h3{
                color: #2B2C33;  font-size: 18px;width: 200px;
                white-space: nowrap;overflow: hidden;text-overflow: ellipsis;
             }
             p{
                font-size: 14px;
                color: #94959C;
             }
        }
        .check{
            width: 24px;
            height: auto;
        }
        .class-info{
            padding: 16px 0 24px 0;
            border-bottom: 1px solid #E2E3E6;
            .item{
                margin-bottom: 8px;
                &:last-child{
                    margin-bottom: 0;
                }
            }
            .label{
                font-size: 16px;
               color: #6D6F75;
            }
            .value{
                font-size: 16px;
               color: #2B2C33;
            }
        }
        .btn-list{
            margin-top: 16px;
            button{
                padding: 6px 12px;
                border-radius: 16px;
                border: 1px solid #E2E3E6;
                line-height: 20px;
                font-size: 14px;
                color: #6D6F75;
                background: #fff;
                cursor: pointer;
                &:hover{
                    background:  #DFEAFF;
                }
                img{
                    width: 12px;
                    height: auto;
                    margin-right: 4px;
                }
            }
        }
    }
}
:deep(.el-button){
    width: 125px;
    font-size: 16px;
    color: #508CFF;
    border-radius: 8px;
    border: 1px solid #508CFF;
    height: 40px;
    img{
        width:16px;
        height: auto;
        margin-right: 4px;
    }
}
</style>
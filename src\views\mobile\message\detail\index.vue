<script setup>
import { ref, onMounted } from "vue"
import { useRouter, useRoute } from "vue-router"
import defaultAvatar from "@/assets/layouts/default-avatar.png"
import { getNoticeDetailApi, viewNoticeApi } from "@/api/mobile/message"

const router = useRouter()
const route = useRoute()

const viewNotice = async () => {
  try {
    await viewNoticeApi(route.query.id)
  } catch (error) {
    console.log(error)
  }
}

const detail = ref({})
const getDetail = async () => {
  try {
    const res = await getNoticeDetailApi(route.query.id)
    detail.value = res.data
  } catch (error) {
    console.log(error)
  }
}

onMounted(() => {
  viewNotice()
  getDetail()
})

</script>

<template>
  <div class="message-detail">
    <div class="header">
      <div class="flex-start-center">
        <img :src="defaultAvatar" alt="头像" class="avatar m-mr8" width="32" height="32" />
        <div class="flex-column-center-start">
          <span class="name grey1 m-text-14-20-600">{{ detail.userName }}</span>
          <span class="time grey5 m-text-12-18-400">{{ detail.postTime }}</span>
        </div>
      </div>
    </div>
    <div class="content m-pt14">
      <span class="title m-text-14-20-400" v-html="detail.content"></span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "@/views/mobile/styles/custom-global-mobile-style.scss";
.message-detail {
  width: 100%;
  height: 100%;
  padding: 15px;
  background-color: #fff;

  .header {
    padding-bottom: 5px;
    border-bottom: 1px solid #e2e3e4;
  }
}
</style>

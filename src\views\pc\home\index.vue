<script setup>
import { ref, onMounted, computed, nextTick, watch, reactive, onBeforeUnmount, onActivated } from "vue"
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay } from 'swiper/modules'
import 'swiper/css'
import { getProfileApi, getLatestActivityApi, getSixStarRecordApi, getActiveUserApi, getClubApi, getRecommendBooksApi } from "@/api/pc/home"
import { getDirectorRecommendDiaryListApi, getTutorRecommendDiaryListApi } from "@/api/mobile/personalSpace"
import { likeDiaryApi, favoriteDiaryApi } from "@/api/mobile/memberOperation"
import { ElMessage } from "element-plus"
import { useRouter } from "vue-router"

const router = useRouter()

const profile = ref({})

const getProfile = async () => {
  try {
    const res = await getProfileApi()
    if (res) {
      profile.value = res.data
    }
  } catch (err) {
    console.log(err)
  }
}

const newDiaryList = ref([])
const activityNotifyList = ref([])

// 计算是否需要滚动
const needScrollDiary = computed(() => newDiaryList.value?.length > 5)
const needScrollActivity = computed(() => activityNotifyList.value?.length > 5)

const getLatestActivityList = async () => {
  try {
    const res = await getLatestActivityApi()
    if (res) {
      newDiaryList.value = res.data
    }
  } catch (err) {
    console.log(err)
  }
}

const getSixStarRecordList = async () => {
  try {
    const res = await getSixStarRecordApi({
      topK: 10
    })
    if (res) {
      activityNotifyList.value = res.data
    }
  } catch (err) {
    console.log(err)
  }
}

// 添加跳转到详情页的方法
const goToDetail = (row) => {
  window.open(row.url)
}

const recommendBooks = ref([])
// 添加控制书籍列表展示的状态
const isBookExpanded = ref(false)
// 计算是否需要折叠显示
const needFoldBooks = computed(() => recommendBooks.value.length > 3)
// 计算当前应该显示的书籍列表
const displayBooks = computed(() => {
  if (!needFoldBooks.value || isBookExpanded.value) {
    return recommendBooks.value
  }
  return recommendBooks.value.slice(0, 3)
})

const getRecommendBooks = async () => {
  try {
    const res = await getRecommendBooksApi()
    if (res) {
      recommendBooks.value = res.data
      // 默认折叠状态
      isBookExpanded.value = false
    }
  } catch (err) {
    console.log(err)
  }
}

// 切换展开/折叠状态
const toggleBookExpand = () => {
  isBookExpanded.value = !isBookExpanded.value
}

onActivated(() => {

})
onMounted(() => {
  getProfile()
  getLatestActivityList()
  getSixStarRecordList()
  getRecommendBooks()

})

// 修改 swiper 配置
const swiperOptions = {
  modules: [Autoplay],
  direction: 'vertical',
  loop: true,
  autoplay: {
    delay: 0,
    disableOnInteraction: false,
    pauseOnMouseEnter: true
  },
  speed: 2500,
  slidesPerView: 5,
  spaceBetween: 16,
  allowTouchMove: false,
  simulateTouch: false,
  cssMode: false
}
</script>

<template>
  <div class="home">
    <header class="home-header">
      <img src="@/assets/pc/home/<USER>" alt="" />
    </header>
    <section class="home-statistics">
      <div class="statistics">
        <div class="statistics-item flex-center-center">
          <div class="statistics-item-title">
            <img src="@/assets/mobile/home/<USER>" alt="" width="48" height="48" />
          </div>
          <div class="statistics-item-value">
            <span class="score statistics-item-value-number text-22-30-400">{{ profile.numberOfAllianceUnit || 0
              }}</span>
            <span class="statistics-item-value-text text-14-20-400 grey3">联盟单位</span>
          </div>
        </div>
        <div class="statistics-item flex-center-center">
          <div class="statistics-item-title">
            <img src="@/assets/mobile/home/<USER>" alt="" width="48" height="48" />
          </div>
          <div class="statistics-item-value">
            <span class="club statistics-item-value-number text-22-30-400">{{ profile.numberOfClub || 0
              }}</span>
            <span class="statistics-item-value-text text-14-20-400 grey3">分社数量</span>
          </div>
        </div>
        <div class="statistics-item flex-center-center">
          <div class="statistics-item-title">
            <img src="@/assets/mobile/home/<USER>" alt="" width="48" height="48" />
          </div>
          <div class="statistics-item-value">
            <span class="comment statistics-item-value-number text-22-30-400">{{ profile.numberOfMemberOrLeader || 0
              }}</span>
            <span class="statistics-item-value-text text-14-20-400 grey3">加入成员</span>
          </div>
        </div>
      </div>
    </section>
    <section class="home-news pt16">
      <div class="new-diary flex-column-start-start">
        <div class="flex-start-center">
          <span class="text-18-26-600 pl4 pr8">最新活动</span>
        </div>
        <div class="new-diary-list mt10">
          <div class="new-diary-content">
            <swiper v-if="needScrollDiary" 
              v-bind="swiperOptions"
              class="scroll-container">
              <swiper-slide v-for="item in newDiaryList" :key="item.id">
                <div class="new-diary-item flex-between-center">
                  <div class="flex-start-center left">
                    <span class="text-14-20-400 pl16 pr20 grey1 new-diary-item-title" @click="goToDetail(item)">
                      {{ item.title }}
                    </span>
                  </div>
                </div>
              </swiper-slide>
            </swiper>
            <div v-else class="scroll-container">
              <div class="new-diary-item flex-between-center" v-for="item in newDiaryList" :key="item.id">
                <div class="flex-start-center left">
                  <span class="text-14-20-400 pl16 pr20 grey1 new-diary-item-title" @click="goToDetail(item)">
                    {{ item.title }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="activity-notify">
        <div class="flex-start-center">
          <img src="@/assets/mobile/home/<USER>" alt="" width="17" height="17" />
          <span class="text-18-26-600 pl4 pr8">六星日记</span>
          <img src="@/assets/pc/home/<USER>" alt="" width="30" height="16" />
        </div>
        <div class="activity-notify-list mt10">
          <div class="activity-notify-content">
            <swiper v-if="needScrollActivity" 
              v-bind="swiperOptions"
              class="scroll-container">
              <swiper-slide v-for="item in activityNotifyList" :key="item.id">
                <div class="activity-notify-item flex-between-center">
                  <div class="flex-start-center left">
                    <img src="@/assets/mobile/home/<USER>" alt="" width="14" height="13" />
                    <span class="text-14-20-400 pl16 pr20 grey1 activity-notify-item-title pointer" @click="goToDetail(item)">
                      {{ item.title }}
                    </span>
                  </div>
                </div>
              </swiper-slide>
            </swiper>
            <div v-else class="scroll-container">
              <div class="activity-notify-item flex-between-center" v-for="item in activityNotifyList" :key="item.id">
                <div class="flex-start-center left">
                  <img src="@/assets/mobile/home/<USER>" alt="" width="14" height="13" />
                  <span class="text-14-20-400 pl16 pr20 grey1 activity-notify-item-title pointer" @click="goToDetail(item)">
                    {{ item.title }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="home-bottom pt16">
      <div class="left">
        <div class="flex-start-center">
          <img src="@/assets/mobile/home/<USER>" alt="" width="15" height="15" />
          <span class="text-18-26-600 pl4">好书推荐</span>
        </div>
        <div class="book-content">
          <div class="book-item" v-for="(item, index) in displayBooks" :key="index">
            <div class="book-item-content flex-start-start">
              <img :src="item.cover" alt="" width="58" height="82">
              <div class="book-item-title flex-column-start-start">
                <span class="text-16-24-600 m-pb8">{{ item.title }}</span>
                <span class="text-14-20-400 grey3">{{ item.introduction }}</span>
              </div>
            </div>
          </div>
          <!-- 展开收起按钮 -->
          <div class="book-toggle flex-end-center" v-if="needFoldBooks">
            <div class="toggle-btn pointer" @click="toggleBookExpand">
              <span class="text-14-20-400 primary">{{ isBookExpanded ? '收起全部' : '显示全部' }}</span>
              <i :class="['el-icon', isBookExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"></i>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
.home {
  width: 100%;
  height: 100%;
  padding: 16px 13.9%;

  &-header {
    img {
      width: 100%;
      height: 180px;
    }
  }

  &-statistics {
    .statistics {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;
      margin-top: 20px;

      &-item {
        width: 100%;
        height: 100%;
        background-color: #fff;
        border-radius: 4px;
        padding: 18px 16px;

        &-value {
          display: flex;
          align-items: center;
          justify-content: center;

          &-number {
            background-image: linear-gradient(to right, #ff8a00, #ff5630);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            padding: 0 16px;

            &.diary {
              // 可以为特定类型设置不同的渐变色
              background-image: linear-gradient(90deg, #b496fc 0%, #6e46fd 100%);
            }

            &.score {
              background-image: linear-gradient(90deg, #ffc736 0%, #fb950f 100%);
            }

            &.club {
              background-image: linear-gradient(90deg, #7de0c8 0%, #27cb96 100%);
            }

            &.comment {
              background-image: linear-gradient(90deg, #83beff 0%, #4b8afe 100%);
            }

            &.evaluations {
              background-image: linear-gradient(90deg, #7de0c8 0%, #27cb96 100%);
            }

            &.like {
              background-image: linear-gradient(90deg, #ff9bb7 0%, #ff6262 100%);
            }

            &.view {
              background-image: linear-gradient(90deg, #83beff 0%, #4b8afe 100%);
            }
          }
        }
      }
    }
  }

  &-news {
    display: flex;
    justify-content: space-between;
    align-items: stretch;

    .new-diary {
      width: calc(50% - 5px);

      &-list {
        width: 100%;
        height: 240px;
        overflow: hidden;
        background-image: url("@/assets/pc/home/<USER>");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 35px 25px;
        position: relative;
      }

      &-content {
        width: 100%;
        height: 180px;
        overflow: hidden;
      }

      &-item {
        flex: 1;
        width: 100%;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .left {
          flex: 1;
          width: calc(100% - 120px);
          overflow: hidden;
        }

        &-title {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          cursor: pointer;

          &:hover {
            text-decoration: underline;
            color: #508cff;
          }
        }

        &-label {
          min-width: 120px;
          text-align: left;
          flex-shrink: 0;
        }
      }
    }

    .activity-notify {
      width: calc(50% - 5px);

      &-list {
        width: 100%;
        height: 240px;
        overflow: hidden;
        background-image: url("@/assets/pc/home/<USER>");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 35px 25px;
        position: relative;
      }

      &-content {
        width: 100%;
        height: 180px;
        overflow: hidden;
      }

      &-item {
        width: 100%;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .left {
          flex: 1;
          width: calc(100% - 120px);
          overflow: hidden;
        }

        &-title {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          &:hover {
            text-decoration: underline;
            color: #508cff;
          }
        }

        &-label {
          text-align: left;
          min-width: 120px;
          flex-shrink: 0;
        }
      }
    }

    .scroll-container {
      width: 100%;
      height: 100%;

      :deep(.swiper-slide) {
        height: auto !important;
        transition-timing-function: linear !important; // 使用线性过渡效果
      }

      :deep(.swiper-wrapper) {
        transition-timing-function: linear !important; // 使用线性过渡效果
      }
    }

    .new-diary-content,
    .activity-notify-content {
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
  }

  &-bottom {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;

    .left {
      flex: 1;

      .book-content {
        padding: 32px 24px;
        background-color: #fff;
        border-radius: 4px;
        margin-top: 10px;

        .book-item {
          padding-bottom: 24px;
          padding-top: 24px;
          border-bottom: 1px dashed #E2E3E6;

          &:last-child {
            border-bottom: none;
            padding-bottom: 0;
          }

          &:first-child {
            padding-top: 0;
          }

          &-title {
            padding-left: 24px;
          }
        }
        
        .book-toggle {
          margin-top: 24px;
          
          .toggle-btn {
            display: flex;
            align-items: center;
            
            span {
              margin-right: 4px;
            }
            
            i {
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}

.primary {
  color: #508cff;
}

.pointer {
  cursor: pointer;
}

.flex-end-center {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>

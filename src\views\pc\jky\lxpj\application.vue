<!-- 申请表 -->
<template>
    <div class="application-main content-box">
        <div class="radius-box">
            <!-- <img v-if="route.name == 'jky-lxcs-application'" src="@/assets/pc/jky/pass.png" class="result-icon" alt=""> -->
            <!-- <img src="@/assets/pc/jky/unpass.png" class="result-icon" alt=""> -->
            <div class="flex_between">
                <p class="text_20_bold flex_start mr_40 pointer" @click="router.back()"><el-icon><ArrowLeftBold /></el-icon>申请表</p> 
                <div class="flex_end" v-if="route.name == 'jky-lxcs-application'">
                    <el-button class="plain-btn2 btn_132_40" type="primary" @click="reject_visible = true">驳回</el-button>
                    <el-button class="primary-btn btn_132_40" type="primary" @click="toReport">上报 </el-button>
                </div>
                <div class="flex_end" v-if="route.name == 'jky-lxcp-application'">
                    <!-- 只有单位管理员要，单位专家不要专家指定 -->
                    <el-button class="plain-btn2 btn_132_40" type="primary" @click="allocate_visible = true">专家指定</el-button>
                    <el-button class="primary-btn btn_132_40" type="primary" @click="mark_visible = true">评分</el-button>
                    <!-- <p>已指定专家张三评审 </p> -->
                </div>
            </div>
            <BaseInfo></BaseInfo>
            <!-- <el-form :model="add_form" inline ref="addRef" :rules="rules" class="search_form add-form mt_24">
                <el-form-item label="课题名称" prop="taskTitle" style="margin-right: 40px;">
                    <el-input v-model="add_form.taskTitle" class="input_48" placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="是否青年专项" style="margin-right: 0;">
                    <el-radio-group v-model="add_form.canReplay" class="ml-4" :disabled="route.params.id != 0">
                        <el-radio label="是" :value="true" size="large"></el-radio>
                        <el-radio label="否" :value="false" size="large"></el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="姓名" prop="taskTitle" style="margin-right: 40px;">
                    <el-input v-model="add_form.taskTitle" class="input_48" placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="性别" style="margin-right: 0;">
                    <el-radio-group v-model="add_form.canReplay" class="ml-4" :disabled="route.params.id != 0">
                        <el-radio label="男" :value="true" size="large"></el-radio>
                        <el-radio label="女" :value="false" size="large"></el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="出生年月" style="margin-right: 40px;">
                    <el-date-picker v-model="add_form.recordDate" class="my-date-picker" type="date" value-format="YYYY-MM" placeholder="请选择"> </el-date-picker>
                </el-form-item>
                <el-form-item label="学段" style="margin-right: 0;">
                    <el-select class="select_48" v-model="add_form.sch" placeholder="请选择" @change="changeExtend">
                        <el-option v-for="item in stage_list" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="学科" style="margin-right: 40px;">
                    <el-select class="select_48" v-model="add_form.sch" placeholder="请选择" @change="changeExtend">
                        <el-option v-for="item in subject_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="联系电话" style="margin-right: 0;">
                    <el-input v-model="add_form.taskContent" class="input_48" placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="学历" style="margin-right: 40px;">
                    <el-select class="select_48" v-model="add_form.sch" placeholder="请选择" @change="changeExtend">
                        <el-option v-for="item in subject_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="职称" style="margin-right: 0;">
                    <el-select class="select_48" v-model="add_form.sch" placeholder="请选择" @change="changeExtend">
                        <el-option v-for="item in subject_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="专业称号" style="margin-right: 0;">
                    <el-input v-model="add_form.taskContent" class="input_48" placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="工作单位" style="margin-right: 0;">
                    <el-input v-model="add_form.taskContent" class="input_48" placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="电子邮件" style="margin-right: 40px;">
                    <el-input v-model="add_form.taskContent" class="input_48" placeholder="请输入"></el-input>
                </el-form-item>
            </el-form> -->
        </div>
        
        <div class="report-box radius-box">
            <p class="text_20_bold flex_center">课题论证报告</p>
            <ul class="report-ul">
                <li>
                    <p class="report-title text_16_bold">问题的提出与研究的意义、价值</p>
                    <WangEditor :html='add_form.questionContent' @emitHtml='emitContentHtml'  />
                    <!-- <div class="report-content">现实需求层面
                        需基于具体社会现象或行业发展困境，揭示研究问题的紧迫性23。如民间委托理财纠纷中保底条款引发的市场秩序问题，或教育领域资源分配不均等现实矛盾25。
                        理论缺口层面
                        应指出现有研究的局限性，如汉语哲学研究揭示的西方哲学范式与中国传统思想的不适配性1，或英语阅读教学理论对课外阅读机制研究的不足5。</div> -->
                </li>
                <li>
                    <p class="report-title text_16_bold">课题界定</p>
                    <WangEditor :html='add_form.questionContent' @emitHtml='emitContentHtml'  />
                    <!-- <div class="report-content">现实需求层面
                        需基于具体社会现象或行业发展困境，揭示研究问题的紧迫性23。如民间委托理财纠纷中保底条款引发的市场秩序问题，或教育领域资源分配不均等现实矛盾25。
                        理论缺口层面
                        应指出现有研究的局限性，如汉语哲学研究揭示的西方哲学范式与中国传统思想的不适配性1，或英语阅读教学理论对课外阅读机制研究的不足5。</div> -->
                </li>
                <li>
                    <p class="report-title text_16_bold">研究内容</p>
                    <WangEditor :html='add_form.questionContent' @emitHtml='emitContentHtml'  />
                    <!-- <div class="report-content">现实需求层面
                        需基于具体社会现象或行业发展困境，揭示研究问题的紧迫性23。如民间委托理财纠纷中保底条款引发的市场秩序问题，或教育领域资源分配不均等现实矛盾25。
                        理论缺口层面
                        应指出现有研究的局限性，如汉语哲学研究揭示的西方哲学范式与中国传统思想的不适配性1，或英语阅读教学理论对课外阅读机制研究的不足5。</div> -->
                </li>
                <li>
                    <p class="report-title text_16_bold">研究方法与步骤</p>
                    <WangEditor :html='add_form.questionContent' @emitHtml='emitContentHtml'  />
                    <!-- <div class="report-content">现实需求层面
                        需基于具体社会现象或行业发展困境，揭示研究问题的紧迫性23。如民间委托理财纠纷中保底条款引发的市场秩序问题，或教育领域资源分配不均等现实矛盾25。
                        理论缺口层面
                        应指出现有研究的局限性，如汉语哲学研究揭示的西方哲学范式与中国传统思想的不适配性1，或英语阅读教学理论对课外阅读机制研究的不足5。</div> -->
                </li>
                <li>
                    <p class="report-title text_16_bold">已有研究基础</p>
                    <WangEditor :html='add_form.questionContent' @emitHtml='emitContentHtml'  />
                    <!-- <div class="report-content">现实需求层面
                        需基于具体社会现象或行业发展困境，揭示研究问题的紧迫性23。如民间委托理财纠纷中保底条款引发的市场秩序问题，或教育领域资源分配不均等现实矛盾25。
                        理论缺口层面
                        应指出现有研究的局限性，如汉语哲学研究揭示的西方哲学范式与中国传统思想的不适配性1，或英语阅读教学理论对课外阅读机制研究的不足5。</div> -->
                </li>
                <li>
                    <p class="report-title text_16_bold">预计研究成果</p>
                    <WangEditor :html='add_form.questionContent' @emitHtml='emitContentHtml'  />
                    <!-- <div class="report-content">现实需求层面
                        需基于具体社会现象或行业发展困境，揭示研究问题的紧迫性23。如民间委托理财纠纷中保底条款引发的市场秩序问题，或教育领域资源分配不均等现实矛盾25。
                        理论缺口层面
                        应指出现有研究的局限性，如汉语哲学研究揭示的西方哲学范式与中国传统思想的不适配性1，或英语阅读教学理论对课外阅读机制研究的不足5。</div> -->
                </li>
                <li>
                    <p class="report-title text_16_bold">所需研究支持</p>
                    <WangEditor :html='add_form.questionContent' @emitHtml='emitContentHtml'  />
                    <!-- <div class="report-content">现实需求层面
                        需基于具体社会现象或行业发展困境，揭示研究问题的紧迫性23。如民间委托理财纠纷中保底条款引发的市场秩序问题，或教育领域资源分配不均等现实矛盾25。
                        理论缺口层面
                        应指出现有研究的局限性，如汉语哲学研究揭示的西方哲学范式与中国传统思想的不适配性1，或英语阅读教学理论对课外阅读机制研究的不足5。</div> -->
                </li>
            </ul>
        </div>
        <div class="radius-box"> 
            <p class="text_16_bold ">初审意见</p>
            <div class="suggest">
                这是一段评审意见，这是一段评审意见，这是一段评审意见，这是一段评审意见，这是一段评审意见，这是一段评审意见，这是一段评审意见，这是一段评审意见
                这是一段评审意见
                这是一段评审意见
            </div>
            <p class="text_16_bold mt_24">评鉴评分</p>
            <ul class="flex_start_0 score-ul">
                <li>
                    <p class="score-project grey-bg"></p>
                    <p class="score-project flex_center">分值</p>
                    <p class="score-project flex_center grey-bg">评分人</p>
                    <p class="score-project flex_center multiple-cell">备注</p>
                </li>
                <li v-for="(item,index) in tableData" :key="index">
                    <p class="score-project grey-bg flex_center">{{item.project}}</p>
                    <p class="flex_center">{{item.score}}</p>
                    <div class="flex_start grey-bg score-cell">
                        <p class="flex_center" style="width:100%;" v-for="(i,iindex) in item.name" :key="iindex">{{i}}</p>
                    </div>
                    <div class="flex_start score-cell">
                        <el-text class="flex_center multiple-cell text_16" style="width:100%;text-align:center;" v-for="(i,iindex) in item.remark" :key="iindex" line-clamp="2">{{i}}</el-text>
                        <!-- <p class="flex_center multiple-cell" style="width:100%;" v-for="(i,iindex) in item.remark" :key="iindex">{{i}}</p> -->
                    </div>
                    <!-- <p class="flex_center">{{item.remark}}</p> -->
                </li>
            </ul> 
            <!-- <el-table :data="tableData" :span-method="objectSpanMethod" stripe style="width: 100%">
                <el-table-column prop="project" label="" width="180" />
                <el-table-column prop="score" label="分值" width="100" />
                <el-table-column prop="name" label="评分人" width="280">
                    <template #default='scope'>
                        <div v-for="item in scope.row.name">{{item}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="remark" label="备注"></el-table-column>
            </el-table> -->
        </div>

        <el-dialog v-model="reject_visible" v-if="reject_visible" title="驳回" width="456px" center class="my-dialog">
            <el-form :model="reject_form" ref="addRef" :rules="rules">
                <el-form-item label="驳回原因" prop="reason" style="margin-right: 16px;">
                    <el-input v-model="reject_form.reason" :rows="4" type="textarea" class="my-textarea" placeholder="请输入"></el-input>
                </el-form-item>
            </el-form>
            <div class="flex_center">
                <el-button class="plain-btn btn_96_48" type="plain" @click="emit('close')">取消</el-button>
                <el-button class="primary-btn btn_96_48" type="primary" @click="submitClick">确认</el-button>
            </div>
        </el-dialog>

        <el-dialog v-model="mark_visible" v-if="mark_visible" title="评分 " width="456px" center class="my-dialog">
            <Mark></Mark>
        </el-dialog>

        <el-dialog v-model="allocate_visible" v-if="allocate_visible" title="专家指定 " width="456px" center class="my-dialog">
            <Allocate></Allocate>
        </el-dialog>
    </div>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import {ElMessageBox,ElMessage} from 'element-plus'
    import WangEditor from '@/views/pc/component/wangeditor.vue'
    import BaseInfo from '@/views/pc/component/jky/BaseInfo.vue'
    import { toRaw } from "@vue/reactivity"
    import Mark from '../lxcp/Mark.vue'
    import Allocate from '../lxcp/Allocate.vue'
    import { useRouter, useRoute } from 'vue-router'
    const router = useRouter()
    const route = useRoute()

    const reject_visible = ref(false)
    const mark_visible = ref(false)
    const allocate_visible = ref(false)

    const rejectObj = function(obj = {}){
        this.reason = obj.reason || ''
    }

    const reject_form = ref(new rejectObj({}))

    const formObj = function(obj = {}){
        this.taskTitle = obj.taskTitle || ''
        this.taskContent = obj.taskContent || ''
        this.reviewSections = obj.reviewSections || []
    }
    const add_form = reactive(new formObj({}))
    const rules = ref({ 
        taskTitle:[{required: true,message: '请输入课题名称',trigger: 'blur'}],
        reviewSections:[{required: true,message: '请上传材料清单',trigger: 'blur'}]
    })
    let emitContentHtml=(val)=>{
        Object.assign(add_form,{
            taskContent:val
        })
    }
    const tableData = ref([
        {
            project: '选题价值',
            name: ['Tom', 'Jerry'],
            score: '234',
            remark: ['吾人最大之知识，系反躬自省。','sss'],
        },
        {
            project: '设计与论证',
            name: ['Tom2'],
            score: '165',
            remark: ['4.43'],
        },
        {
            project: '可行性分析',
            name: ['Tom3'],
            score: '324',
            remark: ['1.9'],
        },
        {
            project: '总得分',
            score: '621',
            name:[]
        }
        ])

    const toReport = () => { // 初审管理员 立项审批  上报
        ElMessageBox.confirm( '确认上报该立项申请吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        ).then(() => {
            
        })
    }
    onMounted(() => {
        console.log(toRaw(route),'12dfd')
    })
</script>
<style lang="scss" scoped>
.application-main{
    .radius-box{position: relative;background: #FFFFFF;border-radius: 12px;padding: 24px;margin: 24px auto;}
    .add-form{
        :deep(.el-form-item){width: calc(50% - 40px);display: inline-block!important;}
    }
    .report-box{
        .report-ul{
            li{font-weight: 400; font-size: 14px; color: #6D6F75; line-height: 20px; text-align: left;margin-bottom:24px;
                .report-title{margin-bottom: 14px;position: relative;padding-left: 8px;}
                .report-title:before{content: "";display: inline-block;width: 2px;height: 16px;border-radius: 1px;margin-right: 10px;background: #508CFF;position: absolute;top: 56%;left:0;transform: translateY(-50%);}
                .report-content{background: #F2F5F9;border-radius: 8px;padding: 16PX;}
            }
        }
    }
    .suggest{padding: 14px 16px;border: 1px solid #E2E3E6;border-radius: 8px;margin-top: 8px;}
    .score-ul{border: 1px solid #E2E3E6;border-radius: 12px;background: #FFFFFF;overflow:hidden;margin-top:9px;
        li{width: 100%;height:100%;color: #2B2C33;font-weight: 400;
            p, .score-cell{min-width:124px;min-height:64px;padding: 0 4px;}
            .grey-bg{background: #F9F9F9;}
            .score-project{font-weight: 600; font-size: 16px; color: #94959C; line-height: 24px; text-align: left;}
            .multiple-cell{height: 107px;}
        }
        li:not(:last-child){border-right: 1px solid #E2E3E6;}
    }
    .result-icon{position: absolute;top: 24px;right: 32px;width: 103px;height: 83px;}
}
</style>
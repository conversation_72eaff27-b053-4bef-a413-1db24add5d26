import COS from "cos-js-sdk-v5"
import TcVod from "vod-js-sdk-v6"
import { getdate, randomString, getCookieCos, setCookieCos } from "@/utils/index"
import { getCos, getVodSignApi } from "@/api/common"
import { toRaw } from "@vue/reactivity"

let cos = ""
const img_types = [".jpg", ".png", ".jpeg"]
const video_types = [".mp4", ".avi", ".rmvb", ".wmv", ".flv", ".mkv", ".mp3", ".wav", ".wma", ".aac", ".flac"]
const pdf_types = [".pdf"]
const excel_types = [".xls", ".xlsx"]
const ppt_types = [".ppt", ".pptx"]
const word_types = [".doc", ".docx"]
const zip_types = [".zip"]

const getCosRes = async () => {
  let res = await getCos()
  if (res) {
    cos = res.data
    setCookieCos(res.data, 10)
  }
}
export const uploadFile = async (File, callback, processBack) => {
  if (getCookieCos("bcxxsfzlcos")) {
    cos = JSON.parse(getCookieCos("bcxxsfzlcos"))
  } else {
    await getCosRes()
  }
  console.log("上传js中的file name", toRaw(File))
  var index = File.name.lastIndexOf(".")
  var file_type = File.name.substr(index)
  var key =
    "bcxxsfzl/" +
    getdate() +
    randomString(32, "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ") +
    file_type
  var cosobj = new COS({
    SecretId: cos.token.credentials.tmpSecretId,
    SecretKey: cos.token.credentials.tmpSecretKey,
    XCosSecurityToken: cos.token.credentials.sessionToken,
    StartTime: cos.token.startTime,
    ExpiredTime: cos.token.expiredTime
  })
  let filebody
  if (File.raw) {
    filebody = File.raw
  } else {
    filebody = File.url
  }
  console.log(cosobj, "cosobj")
  cosobj.putObject(
    {
      Bucket: cos.bucket /* 必须 */,
      Region: cos.region /* 存储桶所在地域，必须字段 */,
      Key: key /* 必须 */,
      StorageClass: "STANDARD",
      Body: File, // 上传文件对象
      onProgress: function (progressData) {
        // that.percent=parseInt(progressData.percent*100);
        processBack(parseInt(progressData.percent * 100))
      }
    },
    function (err, data) {
      if (err == null && data.statusCode == 200) {
        let lastIndexOf = File.name.lastIndexOf(".")
        let suffix = File.name.substring(lastIndexOf)
        let type = img_types.includes(suffix)
          ? "image"
          : video_types.includes(suffix)
            ? "video"
            : pdf_types.includes(suffix)
              ? "pdf"
              : excel_types.includes(suffix)
                ? "excel"
                : ppt_types.includes(suffix)
                  ? "ppt"
                  : word_types.includes(suffix)
                    ? "word"
                    : zip_types.includes(suffix)
                      ? "zip"
                      : "other"
        let file_list = []
        file_list.push({
          name: File.name,
          url: "https://" + data.Location,
          suffix: type
        })
        console.log("callback", file_list)
        callback(file_list)
        // showSuccessToast('上传成功!');
      }
    }
  )
}
export const uploadFileWang = async (File, callback, processBack) => {
  if (getCookieCos("bcxxsfzlcos")) {
    cos = JSON.parse(getCookieCos("bcxxsfzlcos"))
  } else {
    await getCosRes()
  }
  console.log(toRaw(File), "上传js中的file name")
  var index = File.name.lastIndexOf(".")
  var file_type = File.name.substr(index)
  var key =
    "bcxxsfzl/" +
    getdate() +
    randomString(32, "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ") +
    file_type
  var cosobj = new COS({
    SecretId: cos.token.credentials.tmpSecretId,
    SecretKey: cos.token.credentials.tmpSecretKey,
    XCosSecurityToken: cos.token.credentials.sessionToken,
    StartTime: cos.token.startTime,
    ExpiredTime: cos.token.expiredTime
  })
  let filebody
  if (File.raw) {
    filebody = File.raw
  } else {
    filebody = File.url
  }
  var file = File
  cosobj.putObject(
    {
      Bucket: cos.bucket /* 必须 */,
      Region: cos.region /* 存储桶所在地域，必须字段 */,
      Key: key /* 必须 */,
      StorageClass: "STANDARD",
      Body: File, // 上传文件对象
      onProgress: function (progressData) {
        // that.percent=parseInt(progressData.percent*100);
        processBack(parseInt(progressData.percent * 100))
      }
    },
    function (err, data) {
      if (err == null && data.statusCode == 200) {
        let lastIndexOf = File.name.lastIndexOf(".")
        let suffix = File.name.substring(lastIndexOf)
        let type = img_types.includes(suffix)
          ? "image"
          : video_types.includes(suffix)
            ? "video"
            : pdf_types.includes(suffix)
              ? "pdf"
              : excel_types.includes(suffix)
                ? "excel"
                : ppt_types.includes(suffix)
                  ? "ppt"
                  : word_types.includes(suffix)
                    ? "word"
                    : zip_types.includes(suffix)
                      ? "zip"
                      : "other"
        let files = {
          name: File.name,
          url: "https://" + data.Location,
          suffix: type
        }
        callback(files)
        // showSuccessToast('上传成功!');
      }
    }
  )
}

const getSignature = async function () {
  let res = await getVodSignApi()
  return res.data.signature
}

export const uploadVideo = (file, callback, callbackProgress) => {
  let vod = new TcVod({
    getSignature: getSignature
  })
  console.log(vod, "vod")
  var uploader = vod.upload({
    mediaFile: file
  })
  uploader.on("media_progress", (info) => {
    let percent = parseInt(info.percent * 100)
    console.log("当前上传进度：", percent)
    callbackProgress(percent)
  })
  uploader
    .done()
    .then(function (doneResult) {
      let params = {
        name: file.name,
        fileUrl: doneResult.video.url,
        size: file.size,
        fileId: doneResult.fileId
      }
      let file_list = []
      file_list.push(params)
      callback(file_list)
    })
    .catch(function (err) {
      console.log(err, "err")
    })
}

<template>
<div class="message">
       <div class="success flex_column flex_center" v-if="bindResult=='OK'">
             <img src="@/assets/mobile/land/qr-success.png" alt="">
             <p>扫码成功</p>
       </div>
       <div class="success flex_column flex_center"  v-if="bindResult=='ERROR'">
             <img src="@/assets/mobile/land/qr-error.png" alt="">
             <p>{{msg}}</p>
       </div>
    </div>
</template>
<script setup>
import {onMounted, ref} from 'vue'
import {useRoute} from 'vue-router'
let route=useRoute()
let msg=ref('')
let bindResult=ref('')
onMounted(()=>{
   bindResult.value=route.query.result
    msg.value=route.query.msg
}) 
</script>
<style scoped lang='scss'>
@use "@/views/mobile/styles/custom-global-mobile-style.scss";

.success{
     padding-top: 156px;
    img{
        width: 80%;
        height: auto;
    }
    p{
        color: #2B2C33;
        margin-top: 16px;
        font-weight: bold;
        text-align: center;
    }
}
</style>
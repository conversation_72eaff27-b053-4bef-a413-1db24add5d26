<template>
     <div class="theme-left">
           <div class="person-column">
              <p class="title">数据统计</p>
            
              <ul class="list">
                <li :class="['flex_between',item.id==activeIndex?'active':'']" v-for="item in diaryList" :key="item.id" @click="changeActive(item)">
                    <div class="flex_start desc flex_1">
                         <img src="@/assets/pc/p1.png" alt="" v-show="activeIndex!=item.id&&item.id==1" />
                         <img src="@/assets/pc/p1-1.png" alt="" v-show="activeIndex==item.id&&item.id==1" />
                          <img src="@/assets/pc/p6.png" alt="" v-show="activeIndex!=item.id&&item.id==2" />
                         <img src="@/assets/pc/p6-6.png" alt="" v-show="activeIndex==item.id&&item.id==2" />
                        <p>{{item.title}}</p>
                    </div>
                </li>
              </ul>

           </div>
            
       </div>
</template>
<script setup>
import {onMounted, ref,defineEmits,onActivated} from 'vue'
// import {themeAdd,themeEdit,themes,themeDel} from '@/api/pc/index.js'
// import {ElMessage,ElMessageBox } from 'element-plus'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {useRoute,useRouter} from 'vue-router'
let userInfo=useUserInfoStoreHook()
// let props=defineProps(['clubPostId'])
let route=useRoute()
let router=useRouter()
let diaryList=ref([{id:'1',title:'社员情况统计'},{id:'2',title:'日记情况统计'}])
let userStore=useUserInfoStoreHook()
let activeIndex=ref(1)

let changeActive=(item)=>{
    if(item.id==1){
       router.push('/actResearchAssociation/statistic/joiner')
    }else{
       router.push('/actResearchAssociation/statistic/diary')
    }
}
let emits=defineEmits(['emitThemes'])

onMounted(async()=>{
  
    if(userInfo.roles.indexOf('TUTOR')>-1||userInfo.roles.indexOf('LEADER')>-1){
        diaryList.value=[{id:'2',title:'日记情况统计'}]
    }
    if(route.path=='/actResearchAssociation/statistic/diary'){
        activeIndex.value=2
    }else{
         activeIndex.value=1
    }
})
onActivated(async()=>{
    // console.log(userStore.roles)
    // if(userStore.roles.indexOf("MEMBER")>-1){
    //      diaryList.value=[{id:'1',title:'组内讨论'}]
    // }
    // if(route.query.clubPostType){
    //    activeIndex.value= route.query.clubPostType
    // }else{
    //    activeIndex.value=diaryList.value[0].id
    // }
   
    //  emits('emitThemes',activeIndex.value)
})
</script>
<style scoped lang='scss'>
.theme-left{
    // width: 180px;
   height: calc(100vh - 180px);
    overflow: auto;
    .person-column{
        border-radius: 8px;
        background: #fff;
        padding-bottom: 16px;
        .title{
             padding: 18px 0  24px 18px;
             font-weight: bold;
             font-size: 20px;
             color: #2B2C33;
        }
        .list{
            li{
                cursor: pointer;
                padding: 10px 8px  8px 18px;
                font-size: 16px;
                color: #2B2C33;
                &.active{
                    background: #DFEAFF;color: #508CFF;
                }
                .desc{
                    img{
                        width: 12px;
                        height: auto;
                        margin-right: 4px;
                    }
                }
                p{
                    width:80px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
    }
    .friend-link{
        margin-top: 10px;
    }
    .icon{
        width: 18px;
    }
    .addTheme{
        width: 19px;
        height: auto;
        margin-top: 20px;
       margin-left: 14px;
       cursor: pointer;
    }
}
</style>
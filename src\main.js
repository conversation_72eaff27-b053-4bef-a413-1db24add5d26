import { createApp } from 'vue'
import App from './App.vue'
import { loadPlugins } from "@/plugins"
import { pinia } from "@/store"
import { loadDirectives } from "@/directives"
import { router } from "@/router"
import Permission from "@/components/Permission/index.vue"
import "@/router/permission"
import "normalize.css"
import "element-plus/dist/index.css"
import "element-plus/theme-chalk/dark/css-vars.css"
import "vant/lib/index.css"
import "@/styles/index.scss"
import '@/css/pc-public.css'
const app = createApp(App)
loadPlugins(app)
loadDirectives(app)
app.use(pinia).use(router).component('Permission', Permission)
router.isReady().then(() => {
  app.mount('#app')
})

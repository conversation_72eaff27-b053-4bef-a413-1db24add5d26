<template>
    <div class='mileage-box'>
      
        <ul class="flex_start tab">
            <li :class="$route.name=='ActResearchAssociation-personcenter-mileage-personmy'?'active':''" @click="goModel('ActResearchAssociation-personcenter-mileage-personmy')">我的里程</li>
            <li :class="$route.name=='ActResearchAssociation-personcenter-mileage-week'?'active':''"  @click="goModel('ActResearchAssociation-personcenter-mileage-week')">里程周报</li>
            <li :class="$route.name=='ActResearchAssociation-personcenter-mileage-month'?'active':''"  @click="goModel('ActResearchAssociation-personcenter-mileage-month')">里程月报</li>
        </ul>
        <div class="router-view">
          
            <router-view></router-view>
        </div>
         <div class="score-num flex_column flex_center">
            <p class="all-step-num">345678</p>
            <p class="all-step">总步数</p>
        </div>
    </div>
       
</template>
<script setup>
import { onMounted } from "vue";
import {useRouter} from 'vue-router'
let router=useRouter()
let goModel=(pathName)=>{
    router.push({
        name:pathName
    })
}
onMounted(()=>{
    const box = document.querySelector('.score-num');
   document.querySelector(".app-scrollbar").addEventListener('scroll', () => {
        const scrollY = document.querySelector(".app-scrollbar").scrollTop; // 获取垂直滚动值
        box.style.top = `${170 + scrollY}px`; // 更新 top 值

    });
})

</script>
<style lang='scss' scoped>
.mileage-box{
    position: relative;
}
.tab{
    border-radius: 4px;
    background: #fff;
    padding: 8px 40px 0 40px;
    li{
        margin-right: 80px;
        font-size: 16px;
        color: #2B2C33;
        line-height: 24px;
        padding: 0 2px 8px 2px;
        border-bottom:2px solid #fff;
        cursor: pointer;
        &:last-child{
            margin-right: 0;
        }
        &.active{
            border-bottom:2px solid #508CFF;
            color: #508CFF;
            font-weight: bold;
        }
    }
}
.router-view{
    margin-top: 10px;
    margin-bottom: 20px;
}
 .score-num{
    position: absolute;
   
    top:170px;
    right: 0;
    transform: translateX(100%);
    // transform: translateY(calc(150px + var(--scroll)));
    width: 162px;
    height: 162px;
    background: url(@/assets/pc/score/step.png) no-repeat;
    background-size: contain;
    cursor: pointer;
    p{
        transform: translate(-10px,-10px);
        font-size: 26px;
        color: #FFFFFF;
        line-height: 31px;
        text-shadow: 5px 5px 4px rgba(56,120,224,0.7), inset -1px -1px 2px rgba(64,95,255,0.3);
    } 
    .all-step{
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        line-height: 25px;
        text-shadow: 2px 2px 2px rgba(56,102,224,0.7), inset 0px 0px 0px rgba(64,80,255,0.3);
    }
   }
</style>
<template>
    <div class="cgpj-detail content-box">
        <p class="text_20_bold flex_start mr_40 pointer" style="display:inline-flex;" @click="router.back()"><el-icon><ArrowLeftBold /></el-icon>返回</p>  
        <div class="mt_16 mb_24" :class="detail.state != 4 ? 'flex_between' : ''">
            <div class="flex_between">
                <div class="flex_start">
                    <p class="text_18_bold mr_24">XXX课题研究XXX课题研究</p>
                    <div class="flex_start text_14">
                        <img v-if="detail.state == 1" src="@/assets/pc/jky/state-blue.png" class="state-icon" alt="">
                        <img v-else-if="detail.state == 2" src="@/assets/pc/jky/state-orange.png" class="state-icon" alt="">
                        <img v-else-if="detail.state == 3" src="@/assets/pc/jky/state-green.png" class="state-icon" alt="">
                        <img v-else src="@/assets/pc/jky/state-grey.png" class="state-icon"  alt="">
                        <span :class="detail.state == 1 ? 'blue' : detail.state == 2 ? 'orange' : detail.state == 3 ? 'green' : 'grey'">{{detail.state == 1 ? '报送中' : detail.state == 2 ? '待分配' : detail.state == 3 ? '评鉴中' : '已结束'}}</span>
                    </div>
                </div>  
            </div>
            
            <ul class="flex_start tab-ul">
                <li v-for="item in tab_list" :key="item.id" :class="current_tab == item.id ? 'active-tab' : ''" @click="changeTab(item)">{{item.title}}</li>
            </ul>
        </div>
        <el-table v-if="current_tab == 1" :data="task_list" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F9F9F9"}'>
            <el-table-column prop="taskTitle" label="立项编号" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="taskTitle" label="主持人" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="taskTitle" label="课题名称" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="taskTitle" label="学段" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="taskTitle" label="学科" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="createTime" label="状态" />
            <el-table-column prop="taskTitle" label="审核结果" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="taskTitle" label="审核人" show-overflow-tooltip></el-table-column> 
            <el-table-column label="报送材料" width="120"> 
                <template #default='scope'> 
                    <el-button type="primary" link style="font-size:16px;font-weight:400;" @click="goDetail(scope.row)">审核</el-button>
                    <el-button type="primary" link style="font-size:16px;font-weight:400;" @click="goDetail(scope.row)">查看</el-button>
                </template> 
            </el-table-column>
        </el-table>
        <el-table v-else :data="task_list" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F9F9F9"}'>
            <el-table-column type="index" label="序号" width="80"  />
            <el-table-column prop="taskTitle" label="主持人" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="taskTitle" label="课题名称" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="taskTitle" label="学段" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="taskTitle" label="学科" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="createTime" label="状态" />
            <el-table-column prop="taskTitle" label="评分" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="taskTitle" label="评分人" show-overflow-tooltip></el-table-column> 
            <el-table-column label="报送材料" width="120"> 
                <template #default='scope'> 
                    <el-button type="primary" link style="font-size:16px;font-weight:400;" @click="goDetail(scope.row)">查看</el-button>
                </template> 
            </el-table-column>
        </el-table>
        <el-pagination background layout="total,prev, pager, next" :total="total" class="mt-4" :current-page='search_form.pageNum' @current-change='currentChange' />
        <el-dialog v-model="allocation_visible" v-if="allocation_visible" title="评委分配" width="920px" center class="my-dialog">
            <!-- <Allocation :id="taskMasterId" @close="allocation_visible = false, init()"></Allocation> -->
        </el-dialog>
    </div>
</template>
<script setup>  
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    // import Allocation from './Allocation.vue'
    import { useRouter, useRoute } from 'vue-router'
    const router = useRouter()
    const route = useRoute()
    const formObj = function(obj = {}){
        this.name = obj.name || ''
        this.pageNum = obj.pageNum || 1
        this.pageSize = obj.pageSize || 10
    }
    const search_form = ref(new formObj({}))
    const tab_list = ref([{id:1,title:'立项'},{id:2,title:'自选'}])
    const current_tab = ref(1)
    const detail = ref({state:4}) // 1:申报中 2:待分配 3: 评鉴中 4:已结束
    const task_list = ref([{}]) 
    const total = ref(0) 
    const has_pwmd = ref(true)
    const score_visible = ref(false) // 总成绩单
    const allocation_visible = ref(false)

    const goDetail = (row) => {
        router.push(`/jky/cgcp/resultadd/${row.taskMasterId}`)
    }
    const changeTab = (item) => {
        current_tab.value = item.id 
    }
    const goJudgesList = () => {
        router.push(`/jky/cgpj/judgeslist/0`)
    }
    onMounted(() => {
    })
</script>
<style lang="scss" scoped>
.cgpj-detail{margin: 16px auto;background: #FFFFFF;padding: 24px;border-radius: 12px;
    .state-icon{width: 14px;margin-right: 5px;}
    .detail-form{
        :deep(.el-form-item__label){height:40px!important;line-height:40px!important;font-weight: 600; font-size: 16px; color: #2B2C33; line-height: 24px; text-align: left;}
    }
    .hidden-label{
        :deep(.el-form-item__content){justify-content:space-between;}
    }
    .tab-ul{border: 1px solid #508CFF;background: #F0F5FF;display: inline-flex;border-radius:8px;margin-top:20px;overflow:hidden;
        li{padding: 8px 56px;font-weight: 400; font-size: 16px; color: #508CFF; line-height: 24px; text-align: left;cursor: pointer;}
        .active-tab{background: #508CFF;color: #fff;font-weight: 600;}
    } 
}
</style> 
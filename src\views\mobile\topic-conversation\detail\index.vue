<script setup>
import { ref, onMounted, reactive, computed } from "vue"
import { useRouter, useRoute } from "vue-router"
import { showSuccessToast, showToast, showConfirmDialog } from "vant"
import {
  createTopicConversationCommentApi,
  getTopicConversationCommentListApi,
  getTopicConversationDetailApi,
  deleteTopicConversationCommentApi
} from "@/api/mobile/topic-conversation"
import { likeCommentApi } from "@/api/mobile/common"
import DefaultAvatar from "@/assets/layouts/default-avatar.png"
import { useUserStore } from "@/store/modules/user"

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const detail = ref({})
const comments = ref([])
const showReplyPopup = ref(false)
const currentComment = ref(null)
const replyContent = ref("")

const getDetail = async () => {
  try {
    const res = await getTopicConversationDetailApi(route.query.id)
    if (res) {
      detail.value = res.data
    }
  } catch (err) {
    console.log(err)
  }
}

const isDirector = computed(() => userStore.roles?.includes("DIRECTOR"))
const isMember = computed(() => userStore.roles?.includes("MEMBER"))
const isTutor = computed(() => userStore.roles?.includes("TUTOR"))
const isViceDirector = computed(() => userStore.roles?.includes("VICE_DIRECTOR"))

const page = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

const loading = ref(false)
const finished = ref(false)
const error = ref(false)
const getComments = async () => {
  try {
    loading.value = true
    const res = await getTopicConversationCommentListApi(route.query.id, {
      pageNum: page.pageNum,
      pageSize: page.pageSize
    })
    if (res) {
      if (page.pageNum === 1) {
        comments.value = res.data.list || []
      } else {
        comments.value = [...comments.value, ...res.data.list]
      }
      page.total = res.data.total
      loading.value = false

      if (comments.value.length >= page.total) {
        finished.value = true
      } else {
        page.pageNum++
      }
    }
  } catch (err) {
    console.log(err)
  }
}

// 回复 主体 的评论
const replyMainComment = ref("")
const submitReplyMainComment = async () => {
  try {
    const res = await createTopicConversationCommentApi(route.query.id, {
      content: replyMainComment.value
    })
    if (res) {
      replyMainComment.value = ""
      comments.value?.unshift({
        ...res.data,
        type: "main"
      })
      showSuccessToast("评论成功")
    }
  } catch (err) {
    console.log(err)
  }
}

// 打开回复弹窗
const replyToComment = ref(null)

const openReplyPopup = (comment, reply) => {
  currentComment.value = comment
  replyToComment.value = reply
  showReplyPopup.value = true
}

// 提交回复
const submitReply = async () => {
  if (!replyContent.value.trim()) {
    showToast("请输入回复内容")
    return
  }

  try {
    const res = await createTopicConversationCommentApi(route.query.id, {
      content: replyContent.value,
      parentId: currentComment.value.id,
      replyToId: replyToComment.value ? replyToComment.value.id : ""
    })
    if (res) {
      const comment = comments.value?.find((item) => item.id === currentComment.value.id)
      if (comment) {
        if (comment.children) {
          comment.children.push(res.data)
        } else {
          comment.children = [res.data]
        }
      }

      // 重置并关闭弹窗
      replyContent.value = ""
      showReplyPopup.value = false
      showSuccessToast("回复成功")
    }
  } catch (err) {
    console.log(err)
  }
}

const deleteComment = (id) => {
  showConfirmDialog({
    title: "提示",
    message: "确定删除该评论吗？",
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(async () => {
    try {
      const res = await deleteTopicConversationCommentApi(id)
      if (res) {
        removeCommentById(comments.value, id)
        showSuccessToast("删除成功")
      }
    } catch (err) {
      console.log(err)
    }
  })
}

// 根据ID从评论列表中移除评论
const removeCommentById = (commentsList, id) => {
  // 检查一级评论
  const index = commentsList.findIndex(item => item.id === id)
  if (index !== -1) {
    commentsList.splice(index, 1)
    return true
  }

  // 检查子评论
  for (let i = 0; i < commentsList.length; i++) {
    if (commentsList[i].children && commentsList[i].children.length) {
      const childIndex = commentsList[i].children.findIndex(child => child.id === id)
      if (childIndex !== -1) {
        commentsList[i].children.splice(childIndex, 1)
        return true
      }
    }
  }

  return false
}

const clickLikeComment = async (id, isLike) => {
  try {
    const res = await likeCommentApi(id, {
      like: isLike
    })
    if (res) {
      mapComment(comments.value, id)
      showSuccessToast(isLike ? "点赞成功" : "取消点赞成功")
    }
  } catch (err) {
    console.log(err)
  }
}

onMounted(() => {
  getDetail()
  getComments()
})
</script>

<template>
  <div class="topic-conversation-detail">
    <div class="list-item flex-start-start">
      <div class="left-box">
        <img :src="detail.userAvatarUrl || DefaultAvatar" alt="" width="32" height="32" style="border-radius: 50%;" />
      </div>
      <div class="right-box m-pl5">
        <div class="right-top flex-between-center">
          <span class="flex-start-center">
            <span class="m-text-14-20-600 grey1 m-pr8">{{ detail.userName }}</span>
            <span class="m-text-12-18-400 grey3">{{ detail.recordDate }}</span>
          </span>
        </div>
        <div class="right-middle">
          <div class="content-wrapper">
            <div class="m-text-14-20-400 grey1 content-text">
              <div v-html="detail.content"></div>
            </div>
          </div>
        </div>
        <div class="right-bottom flex-start-center flex-wrap">
          <div class="label m-text-12-18-400 grey1 m-mr8 flex-start-center">
            <img src="@/assets/mobile/personal-space/label.png" alt="" width="12" height="12" />
            <span class="m-pl4">{{ detail.clubTopicName }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 评论列表 -->
    <div class="comments-section">
      <div class="flex-between-start reply-main-container" v-if="(!isDirector && !isViceDirector && detail.postType == '1') || (detail.postType == '2')">
        <img :src="detail.userAvatarUrl || DefaultAvatar" alt="" width="32" height="32" style="border-radius: 50%;" />
        <van-field v-model="replyMainComment" rows="4" type="textarea" :placeholder="isTutor ? '发表导师点评' : '发表你的评论'" maxlength="350">
          <template #button>
            <van-button @click="submitReplyMainComment">{{ isTutor ? '发表' : '发送' }}</van-button>
          </template>
        </van-field>
      </div>

      <div class="comment-list">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="getComments"
          :immediate-check="false" :error="error" error-text="加载失败">
          <div v-for="comment in comments" :key="comment.id" class="comment-item">
            <div class="comment-main flex-start-start">
              <div class="avatar-box">
                <img :src="comment.userAvatarUrl || DefaultAvatar" alt="" width="32" height="32" class="avatar" style="border-radius: 50%;" />
              </div>
              <div class="comment-content flex-1 m-pl10">
                <div class="comment-header-row">
                  <span class="name m-text-14-20-600">{{ comment.userName }}</span>
                  <span v-if="comment.userClubRole" class="role m-ml5">
                    <span class="role-text">{{ comment.userClubRoleName }}</span>
                  </span>
                  <span class="comment-text m-text-14-20-400 grey1 m-pl5">
                    <span>：</span>
                    {{ comment.content }}
                    <img v-if="comment.isDeleteCapable" class="m-ml10"
                      src="@/assets/mobile/topic-conversation/delete.png" alt="" width="12" height="12"
                      @click="deleteComment(comment.id)" />
                  </span>
                </div>
                <div class="comment-footer flex-between-center m-mt5">
                  <div class="flex-start-center">
                    <span class="time m-text-12-18-400 grey3">{{ comment.formattedCommentTime }}</span>
                  </div>
                </div>

                <!-- 回复列表 -->
                <div v-if="comment.children && comment.children.length" class="replies-list m-mt10">
                  <div v-for="reply in comment.children" :key="reply.id" class="reply-item m-mb10">
                    <div class="reply-content flex-start-start">
                      <div class="avatar-box">
                        <img :src="reply.userAvatarUrl || DefaultAvatar" alt="" width="24" height="24" class="avatar" style="border-radius: 50%;" />
                      </div>
                      <div class="reply-right flex-1 m-pl8">
                        <div class="reply-header-row">
                          <span class="name m-text-14-20-600">{{ reply.userName }}</span>
                          <span v-if="reply.userClubRole" class="role m-ml5">
                            <span class="role-text">{{ reply.userClubRoleName }}</span>
                          </span>
                          <span v-if="reply.replyToUserName" class="name m-text-14-20-400 grey1 m-pl5">
                            <span class="m-text-12-18-400 grey3">回复</span>
                            {{ reply.replyToUserName }}
                            <span v-if="reply.replyToUserClubRole" class="role m-ml5">
                              <span class="role-text">{{ reply.replyToUserClubRoleName }}</span>
                            </span>
                          </span>
                          <span class="reply-text m-text-14-20-400 grey1 m-pl5">
                            <span>：</span>
                            {{ reply.content }}
                            <img v-if="reply.isDeleteCapable" class="m-ml10"
                              src="@/assets/mobile/topic-conversation/delete.png" alt="" width="12" height="12"
                              @click="deleteComment(reply.id)" />
                          </span>
                        </div>
                        <div class="reply-footer flex-between-center m-mt5">
                          <div class="flex-start-center">
                            <span class="time m-text-12-18-400 grey3">{{ reply.formattedCommentTime }}</span>
                          </div>
                          <!-- <div class="flex-end-center">
                            <div class="count-item comment-count flex-start-center m-mr16">
                              <img
                                class="m-mr6"
                                src="@/assets/mobile/personal-space/comment.png"
                                alt=""
                                width="12"
                                height="12"
                              />
                              <span class="m-text-12-18-400 grey3">{{ reply.comments }}</span>
                            </div>
                            <div
                              class="count-item good-count flex-start-center pointer"
                              @click="clickLike(detail.id, !detail.isLike)"
                            >
                              <img
                                v-if="!detail.isLike"
                                class="m-mr6"
                                src="@/assets/mobile/personal-space/good.png"
                                alt=""
                                width="12"
                                height="12"
                              />
                              <img
                                v-else
                                class="m-mr6"
                                src="@/assets/mobile/personal-space/good-active.png"
                                alt=""
                                width="14"
                                height="14"
                              />
                              <span class="m-text-12-18-400 grey3">{{ detail.likes }}</span>
                            </div>
                          </div> -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </van-list>
      </div>
    </div>
    <!-- 回复弹窗 -->
    <van-popup v-model:show="showReplyPopup" position="bottom" round :style="{ height: '45%' }">
      <div class="reply-popup">
        <div class="reply-header flex-between-center">
          <span class="m-text-16-24-600 grey1">
            {{ currentComment ? "回复 " + currentComment.userName : "发表评论" }}
          </span>
          <van-icon name="cross" size="20" @click="showReplyPopup = false" />
        </div>
        <div class="reply-body">
          <van-field v-model="replyContent" rows="4" autosize type="textarea" placeholder="请输入回复内容" show-word-limit
            maxlength="350" />
        </div>
        <div class="reply-footer m-mt15">
          <van-button type="primary" block round @click="submitReply">发送</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<style lang="scss" scoped>
@use "@/views/mobile/styles/custom-global-mobile-style.scss";

.topic-conversation-detail {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .list-item {
    width: 100%;
    background-color: #fff;
    padding: 15px;

    .right-box {
      width: calc(100% - 32px);
    }

    .right-middle {
      position: relative;

      .content-wrapper {
        display: inline;
      }
    }

    .right-bottom {
      padding-top: 8px;

      .label {
        padding: 0 7px;
        border-radius: 19px;
        border: 1px solid #e2e3e6;
        height: 24px;
        line-height: 24px;
      }

      .clocking-time {
        padding: 0 11px;
        border-radius: 19px;
        background-color: #fff5e7;
        height: 24px;
        line-height: 24px;
      }
    }
  }

  :deep(.van-tabs) {
    width: 45%;

    .van-tabs__nav {
      background: transparent;
    }

    .van-tabs__wrap,
    .van-tab {
      height: 40px;

      &--line {
        font-weight: 400;
      }
    }

    .van-tabs__line {
      background: linear-gradient(270deg, #496eee 0%, #0ac1fa 100%);
    }
  }

  .sort {
    .active {
      color: #4260fd;
    }
  }

  .comments-section {
    display: flex;
    flex-direction: column;
    flex: 1;
    background-color: #fff;
    padding: 15px;

    .reply-main-container {
      gap: 12px;

      :deep(.van-field) {
        flex: 1;
        background-color: #f0f1f4;
        border-radius: 4px;
        border: none;
        font-size: 14px;
        --van-cell-vertical-padding: 4px;
        padding-right: 4px;

        .van-field__body {
          align-items: flex-end;
        }
      }

      :deep(.van-button) {
        width: 52px;
        height: 32px;
        background-color: #508cff;
        color: #fff;
        border-radius: 4px;
        padding: 0;
        font-size: 14px;
        border: none;
      }
    }

    .comment-title {
      border-left: 3px solid #508cff;
      padding-left: 8px;
    }

    .comment-item {
      padding: 12px 0;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .avatar {
        border-radius: 50%;
      }

      .comment-header-row,
      .reply-header-row {
        .name {
          flex-shrink: 0;
          color: #5c76d4;
        }

        .role {
          display: inline-block;
          padding: 3px 0;
          height: 20px;

          .role-text {
            background-color: #83bcfe;
            color: #fff;
            border-radius: 4px;
            height: 14px;
            line-height: 14px;
            padding: 0 6px;
            font-size: 12px;
          }
        }

        .comment-text,
        .reply-text {
          word-break: break-all;
          cursor: pointer;
        }
      }

      .comment-footer,
      .reply-footer {
        .reply-action {
          cursor: pointer;
        }
      }

      .replies-list {
        padding: 0 10px;
        border-left: 1px solid #e2e3e6;

        .reply-item {
          .avatar {
            border-radius: 50%;
          }
        }
      }
    }

    .comment-list {
      flex: 1;
      overflow-y: auto;
    }
  }

  .likes-section {
    display: flex;
    flex-direction: column;
    flex: 1;
    background-color: #fff;
    padding: 15px;

    .likes-list {
      flex: 1;
      overflow-y: auto;
    }

    .likes-item {
      padding-bottom: 12px;

      &:last-child {
        padding-bottom: 0;
      }
    }
  }

  .comment-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    padding: 10px 15px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);

    .comment-btn {
      text-align: left;
      padding-left: 15px;
      height: 40px;
      color: #999;
    }
  }

  .reply-popup {
    display: flex;
    flex-direction: column;
    padding: 20px;
    height: 100%;

    .reply-header {
      padding-bottom: 15px;
      border-bottom: 1px solid #f5f5f5;
    }

    .reply-body {
      flex: 1;

      :deep(.van-field) {
        height: 100%;
        background-color: #f0f1f4;
        border-radius: 8px;

        .van-field__body {
          height: calc(100% - 20px);
          overflow-y: auto;

          .van-field__control {
            height: 100% !important;
          }
        }
      }
    }

    :deep(.van-button) {
      background-color: #508cff;
      color: #fff;
    }
  }

  ::-webkit-scrollbar {
    display: none;
  }
}
</style>

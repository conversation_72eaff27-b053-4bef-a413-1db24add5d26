<script setup>
import { ref, onMounted, computed, reactive, onActivated } from 'vue';
import { CaretBottom } from '@element-plus/icons-vue'
import { getMileageEventTypeApi, getMileageEventGroupApi, getCurrentWeekMileageApi, getCurrentWeekMileageRankApi, getDailyTaskApi, getMileageDetailApi, getHistoryTotalMileageApi, getWeekOverviewApi, getWeekAbilityApi, getWeekActiveTrendApi, getWeekDataStatisticsApi, getWeekRankApi, getWeekListApi, getMonthListApi, getMonthOverviewApi, getMonthAbilityApi, getMonthActiveTrendApi, getMonthDataStatisticsApi, getMonthRankApi } from '@/api/mobile/mileage';
import { useRouter } from 'vue-router';
import AbilityAnalysisChart from './components/AbilityAnalysisChart.vue'
import ActiveTrendAnalysisChart from './components/ActiveTrendAnalysisChart.vue'
import { statisticIcon } from './statisticIcon'
import dayjs from 'dayjs';

import FirstLoginIcon from '@/assets/mobile/act-mileage/my-mileage/first-login-icon.png'
import WriteDiaryIcon from '@/assets/mobile/act-mileage/my-mileage/finish-diary-icon.png'
import ReadDiaryIcon from '@/assets/mobile/act-mileage/my-mileage/read-diary-icon.png'
import WeekActiveStarIcon from '@/assets/mobile/act-mileage/my-mileage/week-active-star-icon.png'
import GetRecommendIcon from '@/assets/mobile/act-mileage/my-mileage/get-recommend-icon.png'
import CancelIcon from '@/assets/mobile/act-mileage/my-mileage/cancel-icon.png'
import DeliverGoodCommentIcon from '@/assets/mobile/act-mileage/my-mileage/deliver-good-comment-icon.png'
import GetEfficientCommentIcon from '@/assets/mobile/act-mileage/my-mileage/get-efficient-comment-icon.png'
import GoodCreatorIcon from '@/assets/mobile/act-mileage/my-mileage/good-creator-icon.png'
import BeStarIcon from '@/assets/mobile/act-mileage/my-mileage/be-star-icon.png'
import PlusStepIcon from '@/assets/mobile/act-mileage/my-mileage/plus-step.png'
import MinusStepIcon from '@/assets/mobile/act-mileage/my-mileage/minus-step.png'
import RiseIcon from '@/assets/mobile/act-mileage/week-mileage/rise.png'
import DownIcon from '@/assets/mobile/act-mileage/week-mileage/down.png'
import AvgIcon from '@/assets/mobile/act-mileage/week-mileage/avg.png'

const router = useRouter()

const activeTab = ref()
const tabs = ref([
  {
    name: 1,
    title: "我的里程"
  },
  {
    name: 2,
    title: "里程周报"
  },
  {
    name: 3,
    title: "里程月报"
  }
])
const getTabList = () => {
  activeTab.value = tabs.value[0].name
}
const handleClickTab = async (tab) => {
  activeTab.value = tab.name

  if (activeTab.value === 1) {
    getCurrentWeekMileage()
    getCurrentWeekMileageRank()
    getDailyTask()
    getHistoryTotalMileage()
  } else if (activeTab.value === 2) {
    await getWeekList()
    getWeekRankList()
    getWeekOrMonthMileageData()
    getAbilityAnalysisChartData()
    getActiveTrendAnalysisData()
    getDataStatisticData()
  } else if (activeTab.value === 3) {
    await getMonthList()
    getMonthRankList()
    getWeekOrMonthMileageData()
    getAbilityAnalysisChartData()
    getActiveTrendAnalysisData()
    getDataStatisticData()
  }
}

// 获取事件类型
// const eventTypeList = ref([])
// const getEventTypeList = async () => {
//   try {
//     const res = await getMileageEventTypeApi({
//       isAuto: true,
//       eventGroup: ''
//     })
//     if (res) {
//       eventTypeList.value = res.data
//     }
//   } catch (err) {
//     console.log(err)
//   }
// }

// 获取事件组
const eventGroupList = ref([])
const getEventGroupList = async () => {
  try {
    const res = await getMileageEventGroupApi({
      isAuto: true
    })
    if (res) {
      eventGroupList.value = res.data
    }
  } catch (err) {
    console.log(err)
  }
}

// 我的里程
const currentWeekMileage = ref({})
const getCurrentWeekMileage = async () => {
  try {
    const res = await getCurrentWeekMileageApi()
    if (res) {
      currentWeekMileage.value = res.data
    }
  } catch (error) {
    console.log(error)
  }
}

// 本周里程排名
const currentWeekMileageRank = ref({})
const getCurrentWeekMileageRank = async () => {
  try {
    const res = await getCurrentWeekMileageRankApi()
    if (res) {
      currentWeekMileageRank.value = res.data
    }
  } catch (error) {
    console.log(error)
  }
}

// 获取每日任务
const dailyTask = ref({})
const getDailyTask = async () => {
  try {
    const res = await getDailyTaskApi()
    if (res) {
      dailyTask.value = res.data.dailyTasks
    }
  } catch (error) {
    console.log(error)
  }
}
// 类型
const mileageTypeList = computed(() => {
  const list = eventGroupList.value.map(item => ({
    ...item,
    text: item.label
  }))
  list.unshift({
    value: '-1',
    label: '全部',
    text: '全部'
  })
  return list
})
const selectedMyMileageType = ref([])
const selectedMyMileageTypeLabel = computed(() => {
  return mileageTypeList.value.find(item => item.value === selectedMyMileageType.value?.[0])?.text
})

// 类型选择器弹出层
const showTypePicker = ref(false)
const handleTypePickerConfirm = ({ selectedOptions }) => {
  selectedMyMileageType.value = [selectedOptions?.[0]?.value]
  showTypePicker.value = false
  page.pageNum = 1
  finished.value = false
  getMileageDetailList()
}

// 日期
const selectedTimeRange = ref([])
const showCalendar = ref(false)
const calendarType = ref('range')
const minDate = ref(new Date(new Date().getFullYear() - 1, 0, 1))
const maxDate = ref(new Date())
const calendarRef = ref(null)

// 日期范围显示文本
const timeRangeText = computed(() => {
  if (!selectedTimeRange.value || selectedTimeRange.value.length < 2) {
    return { start: '开始时间', end: '结束时间' }
  }
  return {
    start: dayjs(selectedTimeRange.value?.[0]).format('YYYY-MM-DD'),
    end: dayjs(selectedTimeRange.value?.[1]).format('YYYY-MM-DD')
  }
})

// 清除日期范围
const onCalendarClear = () => {
  selectedTimeRange.value = []
  showCalendar.value = false
  page.pageNum = 1
  finished.value = false
  getMileageDetailList()
}

// 日历确认
const onCalendarConfirm = () => {
  let dates = []
  if (calendarRef.value) {
    dates = calendarRef.value.getSelectedDate()
    console.log('dates', dates)
    selectedTimeRange.value = [dayjs(dates[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss'), dayjs(dates[dates.length - 1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')]
  }
  showCalendar.value = false
  page.pageNum = 1
  finished.value = false
  getMileageDetailList()
}

// 获取里程明细
const page = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})
const loading = ref(false)
const finished = ref(false)
const error = ref(false)

const mileageDetailList = ref([])
const getMileageDetailList = async () => {
  try {
    loading.value = true

    const res = await getMileageDetailApi({
      pageNum: page.pageNum,
      pageSize: page.pageSize,
      eventGroup: selectedMyMileageType.value?.[0] === '-1' ? '' : selectedMyMileageType.value?.[0],
      beginTime: selectedTimeRange.value?.[0] ? selectedTimeRange.value?.[0] : '',
      endTime: selectedTimeRange.value?.[1] ? selectedTimeRange.value?.[1] : ''
    })
    if (res) {
      if (page.pageNum === 1) {
        mileageDetailList.value = res.data.list
      } else {
        mileageDetailList.value = [...mileageDetailList.value, ...res.data.list]
      }

      mileageDetailList.value.forEach(item => {
        if (item.points >= 0) {
          item.plusType = 1
        } else {
          item.plusType = 0
        }

        switch (item.eventTypeValue) {
          case '10001':
            item.icon = FirstLoginIcon
            break
          case '10002':
            item.icon = WriteDiaryIcon
            break
          case '20001':
            item.icon = ReadDiaryIcon
            break
          case '20002':
            item.icon = DeliverGoodCommentIcon
            break
          case '20003':
            item.icon = ReadDiaryIcon
            break
          case '20004':
            item.icon = GetEfficientCommentIcon
            break
          case '30001':
            item.icon = WeekActiveStarIcon
            break
          case '30002':
            item.icon = GoodCreatorIcon
            break
          case '40001':
            item.icon = BeStarIcon
            break
          case '40002':
            item.icon = BeStarIcon
            break
          case '40003':
            item.icon = BeStarIcon
            break
          case '40004':
            item.icon = GetRecommendIcon
            break
          case '40005':
            item.icon = GetRecommendIcon
            break
          case '50001':
            item.icon = CancelIcon
            break
          default:
            item.icon = ''
        }
      })
      loading.value = false
      page.total = res.data.total

      if (mileageDetailList.value.length >= page.total) {
        finished.value = true
      } else {
        page.pageNum++
      }
    }
  } catch (err) {
    console.log('err', err)
    error.value = true
  }
}

const getGroupLabelStyle = (item) => {
  let padding = '2px 6px'
  let color = ''
  let backgroundColor = ''
  let borderRadius = '4px'
  if (item.eventGroupValue === 1) {
    color = '#29B0FE'
    backgroundColor = '#D9EFFD'
  } else if (item.eventGroupValue === 2) {
    color = '#FF7C2F'
    backgroundColor = '#FFECE2'
  } else if (item.eventGroupValue === 3) {
    color = '#FF8F00'
    backgroundColor = '#FFF1DC'
  } else if (item.eventGroupValue === 4) {
    color = '#E371F4'
    backgroundColor = '#FDEBFD'
  } else if (item.eventGroupValue === 5) {
    color = '#94959C'
    backgroundColor = '#F1F2F5'
  }
  return {
    color,
    backgroundColor,
    padding,
    borderRadius
  }
}

const goToTask = (item) => {
  if (item.value === 1) {
    router.push('/personalSpace/add')
  } else if (item.value === 2 || item.value === 3) {
    router.push('/diarySquare')
  }
}

// 历史总分
const historyTotalMileage = ref()
const getHistoryTotalMileage = async () => {
  try {
    const res = await getHistoryTotalMileageApi()
    if (res) {
      historyTotalMileage.value = res.data || 0
    }
  } catch (err) {
    console.log('err', err)
  }
}




// 里程周报
const selectedWeek = ref([])
const selectedWeekLabel = ref('')
const weekList = ref([])
const weekPickerVisible = ref(false)

const getWeekList = async () => {
  try {
    const res = await getWeekListApi()
    if (res) {
      weekList.value = res.data.weeks || []
    }

    weekList.value.forEach(item => {
      item.text = item.label
      item.value = item.year + '-' + item.weekOfYear
    })
    selectedWeek.value = [res.data?.current.year + '-' + res.data?.current.weekOfYear]
    selectedWeekLabel.value = res.data?.current.label
  } catch (err) {
    console.log('err', err)
  }
}

// 获取周排名
const weekRankInfo = ref({})
const getWeekRankList = async () => {
  try {
    const res = await getWeekRankApi({
      year: selectedWeek.value?.[0]?.split('-')[0],
      weekOfYear: selectedWeek.value?.[0]?.split('-')[1]
    })
    if (res) {
      weekRankInfo.value = res.data
    }
  } catch (err) {
    console.log('err', err)
  }
}

const showWeekPickerPopup = () => {
  weekPickerVisible.value = true
}

const onWeekPickerConfirm = ({ selectedOptions }) => {
  selectedWeek.value = [selectedOptions?.[0]?.value]
  selectedWeekLabel.value = selectedOptions?.[0]?.text
  weekPickerVisible.value = false
  getWeekRankList()
  getWeekOrMonthMileageData()
  getAbilityAnalysisChartData()
  getActiveTrendAnalysisData()
  getDataStatisticData()
}

// 里程月报
const selectedMonth = ref([])
const selectedMonthLabel = ref('')
const monthList = ref([])
const monthPickerVisible = ref(false)

const getMonthList = async () => {
  try {
    const res = await getMonthListApi()
    if (res) {
      monthList.value = res.data.months || []
    }
    monthList.value.forEach(item => {
      item.text = item.label
      item.value = item.year + '-' + item.month
    })
    selectedMonth.value = [monthList.value[0]?.value]
    selectedMonthLabel.value = monthList.value[0]?.label
  } catch (err) {
    console.log('err', err)
  }
}

const showMonthPickerPopup = () => {
  monthPickerVisible.value = true
}

const onMonthPickerConfirm = ({ selectedOptions }) => {
  selectedMonth.value = [selectedOptions?.[0]?.value]
  selectedMonthLabel.value = selectedOptions?.[0]?.text
  monthPickerVisible.value = false
  getMonthRankList()
  getWeekOrMonthMileageData()
  getAbilityAnalysisChartData()
  getActiveTrendAnalysisData()
  getDataStatisticData()
}

// 获取月排名
const monthRankInfo = ref({})
const getMonthRankList = async () => {
  try {
    const res = await getMonthRankApi({
      year: selectedMonth.value?.[0]?.split('-')[0],
      month: selectedMonth.value?.[0]?.split('-')[1]
    })
    if (res) {
      monthRankInfo.value = res.data
    }
  } catch (err) {
    console.log('err', err)
  }
}
// 周/月里程数据
const weekOrMonthMileageData = ref({})
const getWeekOrMonthMileageData = async () => {
  try {
    let res
    if (activeTab.value === 2) {
      res = await getWeekOverviewApi({
        year: selectedWeek.value?.[0]?.split('-')[0],
        weekOfYear: selectedWeek.value?.[0]?.split('-')[1]
      })
    } else if (activeTab.value === 3) {
      res = await getMonthOverviewApi({
        year: selectedMonth.value?.[0]?.split('-')[0],
        month: selectedMonth.value?.[0]?.split('-')[1]
      })
    }

    if (res) {
      weekOrMonthMileageData.value = res.data
      if (weekOrMonthMileageData.value.totalPointsWeekOnWeek < 0) {
        weekOrMonthMileageData.value.showTotalPointsWeekOnWeek = -weekOrMonthMileageData.value.totalPointsWeekOnWeek
        weekOrMonthMileageData.value.plusType = 0
        weekOrMonthMileageData.value.icon = DownIcon
      } else if (weekOrMonthMileageData.value.totalPointsWeekOnWeek > 0) {
        weekOrMonthMileageData.value.showTotalPointsWeekOnWeek = weekOrMonthMileageData.value.totalPointsWeekOnWeek
        weekOrMonthMileageData.value.plusType = 1
        weekOrMonthMileageData.value.icon = RiseIcon
      } else {
        weekOrMonthMileageData.value.showTotalPointsWeekOnWeek = 0
        weekOrMonthMileageData.value.plusType = null
        weekOrMonthMileageData.value.icon = AvgIcon
      }
      weekOrMonthMileageData.value.eventGroups.forEach(item => {
        if (item.weekOnWeek < 0) {
          item.showWeekOnWeek = -item.weekOnWeek
          item.plusType = 0
          item.icon = DownIcon
        }
        if (item.weekOnWeek > 0) {
          item.showWeekOnWeek = item.weekOnWeek
          item.plusType = 1
          item.icon = RiseIcon
        }
        if (item.weekOnWeek === 0 || item.weekOnWeek === null || item.weekOnWeek === undefined) {
          item.plusType = null
          item.showWeekOnWeek = 0
          item.icon = AvgIcon
        }
      })
    }

  } catch (err) {
    console.log('err', err)
  }
}

// 能力分析图表数据
const abilityAnalysisChartData = ref({})
const getAbilityAnalysisChartData = async () => {
  try {
    let res
    if (activeTab.value === 2) {
      res = await getWeekAbilityApi({
        year: selectedWeek.value?.[0]?.split('-')[0],
        weekOfYear: selectedWeek.value?.[0]?.split('-')[1]
      })
    } else if (activeTab.value === 3) {
      res = await getMonthAbilityApi({
        year: selectedMonth.value?.[0]?.split('-')[0],
        month: selectedMonth.value?.[0]?.split('-')[1]
      })
    }
    if (res) {
      const data = res.data.eventGroups

      const userData = data.map(item => item.myPoints)
      const avgData = data.map(item => item.avgPoints)
      const indicator = data.map(item => ({
        name: item.label,
        max: Math.max(...userData) > 100 ? Math.max(...userData) : Math.max(...avgData) > 100 ? Math.max(...avgData) : 100
      }))

      abilityAnalysisChartData.value = {
        userData,
        avgData,
        indicator,
        userColor: '#508CFF',
        avgColor: '#FEAC12',
        userLegendColor: '#2189FD',
        avgLegendColor: '#FDC535'
      }
    }

  } catch (err) {
    console.log('err', err)
  }
}

// 活跃趋势分析
const activeTrendAnalysisData = ref({})
const getActiveTrendAnalysisData = async () => {
  try {
    let res
    if (activeTab.value === 2) {
      res = await getWeekActiveTrendApi({
        year: selectedWeek.value?.[0]?.split('-')[0],
        weekOfYear: selectedWeek.value?.[0]?.split('-')[1]
      })
    } else if (activeTab.value === 3) {
      res = await getMonthActiveTrendApi({
        year: selectedMonth.value?.[0]?.split('-')[0],
        month: selectedMonth.value?.[0]?.split('-')[1]
      })
    }

    if (res) {
      const data = res.data.dailyPointsList.map(item => ({
        date: item.date,
        value: item.points
      }))

      activeTrendAnalysisData.value = {
        data
      }
    }

  } catch (err) {
    console.log('err', err)
  }
}

// 数据统计
const dataStatisticData = ref({})
const currentTabData = ref({})

const getDataStatisticData = async () => {
  try {
    let res
    if (activeTab.value === 2) {
      res = await getWeekDataStatisticsApi({
        year: selectedWeek.value?.[0]?.split('-')[0],
        weekOfYear: selectedWeek.value?.[0]?.split('-')[1]
      })
    } else if (activeTab.value === 3) {
      res = await getMonthDataStatisticsApi({
        year: selectedMonth.value?.[0]?.split('-')[0],
        month: selectedMonth.value?.[0]?.split('-')[1]
      })
    }

    if (res) {
      dataStatisticData.value = res.data?.eventGroups || []
      dataStatisticTabs.value = dataStatisticData.value.map(item => ({
        label: item.label,
        value: item.value
      }))
      activeDataStatisticTab.value = dataStatisticTabs.value[0].value
      currentTabData.value = dataStatisticData.value[0].eventTypes
    }

  } catch (err) {
    console.log('err', err)
  }
}

const dataStatisticTabs = ref([])
const activeDataStatisticTab = ref('')
const changeDataStatisticTab = (tab) => {
  activeDataStatisticTab.value = tab
  currentTabData.value = dataStatisticData.value.find(item => item.value === activeDataStatisticTab.value)?.eventTypes
}

const getEventTypeIcon = (item) => {
  switch (item.value) {
    case '10001':
      return statisticIcon.LoginIcon
    case '10002':
      return statisticIcon.WriteCommentIcon
    case '20001':
      return statisticIcon.ReadDiaryIcon
    case '20002':
      return statisticIcon.DeliverCommentIcon
    case '20003':
      return statisticIcon.DiaryBeReadIcon
    case '20004':
      return statisticIcon.GetCommentIcon
    case '20005':
      return statisticIcon.JoinActivityIcon
    case '20006':
      return statisticIcon.JoinCommunicationIcon
    case '30001':
      return statisticIcon.ActiveStarIcon
    case '30002':
      return statisticIcon.GoodCreatorIcon
    case '40001':
      return statisticIcon.StarDiaryIcon
    case '40002':
      return statisticIcon.StarDiaryIcon
    case '40003':
      return statisticIcon.StarDiaryIcon
    case '40004':
      return statisticIcon.GetRecommendIcon
    case '40005':
      return statisticIcon.WxRecommendIcon
    case '40006':
      return statisticIcon.GetAwardIcon
    case '50001':
      return statisticIcon.DownStepIcon
    default:
      return statisticIcon.DownStepIcon
  }
}


onMounted(() => {
  getTabList()
  // getEventTypeList()
  getEventGroupList()
  getCurrentWeekMileage()
  getCurrentWeekMileageRank()
  getMileageDetailList()
  getDailyTask()
  getHistoryTotalMileage()
})

onActivated(async () => {
  getTabList()
  // getEventTypeList()
  getEventGroupList()

  if (activeTab.value === 1) {
    getCurrentWeekMileage()
    getCurrentWeekMileageRank()
    getMileageDetailList()
    getDailyTask()
    getHistoryTotalMileage()
  } else if (activeTab.value === 2) {
    await getWeekList()
    getWeekRankList()
    getWeekOrMonthMileageData()
    getAbilityAnalysisChartData()
    getActiveTrendAnalysisData()
    getDataStatisticData()
  } else if (activeTab.value === 3) {
    await getMonthList()
    getMonthRankList()
    getWeekOrMonthMileageData()
    getAbilityAnalysisChartData()
    getActiveTrendAnalysisData()
    getDataStatisticData()
  }
})
</script>

<template>
  <div class="act-mileage">
    <header class="header-area">
      <van-tabs v-model:active="activeTab" @click-tab="handleClickTab" swipe-threshold="3">
        <van-tab v-for="tab in tabs" :key="tab.name" :name="tab.name" :title="tab.title"></van-tab>
      </van-tabs>
    </header>
    <section class="my-mileage" v-if="activeTab == 1">
      <section class="my-mileage-statistic">
        <div class="week-mileage m-p16">
          <div class="week-milage-header flex-start-center">
            <img src="@/assets/mobile/act-mileage/my-mileage/week-mileage-icon.png" alt="" width="20px" height="20px">
            <span class="white m-text-14-20-400 m-pl8">本周里程</span>
          </div>
          <div class="week-mileage-content">
            <div class="mileage-item flex-column-center-center">
              <div class="value big-value">{{ currentWeekMileage?.totalPoints || 0 }}</div>
              <div class="label big-label">本周步数</div>
            </div>
            <div class="mileage-item flex-column-center-center" v-for="(item, index) in currentWeekMileage.eventGroups"
              :key="item.value" :class="index === 0 || index === 3 ? 'has-border' : ''">
              <div class="value">{{ item.points }}</div>
              <div class="label">{{ item.label }}</div>
            </div>
          </div>
        </div>
        <div class="other-container m-mt12">
          <div class="left">
            <div class="club-rank flex-start-center">
              <div class="club-rank-header flex-start-center">
                <img src="@/assets/mobile/act-mileage/my-mileage/club-rank-icon.png" alt="" width="20px" height="20px">
                <span class="white m-text-14-20-400 m-pl8">总社排名</span>
              </div>
              <div class="club-rank-text">
                第 <span class="text">{{ currentWeekMileageRank?.clubRanking || 0 }}</span> 名
              </div>
            </div>
            <div class="group-rank">
              <div class="group-rank-header flex-start-center">
                <img src="@/assets/mobile/act-mileage/my-mileage/group-rank-icon.png" alt="" width="20px" height="20px">
                <span class="white m-text-14-20-400 m-pl8">小组排名</span>
              </div>
              <div class="group-rank-text">
                第 <span class="text">{{ currentWeekMileageRank?.clubGroupRanking || 0 }}</span> 名
              </div>
            </div>
          </div>
          <div class="right today-task">
            <div class="today-task-header flex-start-center">
              <img src="@/assets/mobile/act-mileage/my-mileage/today-task-icon.png" alt="" width="20px" height="20px">
              <span class="white m-text-14-20-400 m-pl8">今日任务</span>
            </div>
            <div class="today-task-content flex-column-start-start">
              <div class="task-item" v-for="item in dailyTask" :key="item.value" @click="goToTask(item)">
                <span class="white m-text-16-24-400 m-mr10 pointer" :class="true ? 'underline' : ''">{{ item.label
                  }}</span>
                <img v-if="!item.isCompleted" src="@/assets/mobile/act-mileage/my-mileage/right-arrow.png" alt=""
                  width="7" height="14">
                <img v-if="item.isCompleted" src="@/assets/mobile/act-mileage/my-mileage/finish.png" alt="" width="14"
                  height="14">
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="my-mileage-detail">
        <div class="m-text-16-24-400 gray1 m-mb10">里程明细</div>
        <div class="my-mileage-search-box">
          <div class="type-selector pointer flex-between-center" @click="showTypePicker = true">
            <div class="type-selector-label m-text-14-20-400">{{ selectedMyMileageTypeLabel ? selectedMyMileageTypeLabel
              : '全部类型' }}</div>
            <el-icon color="#b4b6be" style="width: 8px; height: 5px;">
              <CaretBottom />
            </el-icon>
          </div>
          <div class="time-selector pointer flex-between-center" @click="showCalendar = true">
            <div class="time-selector-text flex-between-center m-text-14-20-400">
              <span>{{ timeRangeText.start }}</span>
              <span class="separator">-</span>
              <span>{{ timeRangeText.end }}</span>
            </div>
          </div>
        </div>
        <div class="my-mileage-detail-list">
          <van-list v-model:loading="loading" v-model:error="error" error-text="请求失败，点击重新加载"
            @load="getMileageDetailList" :finished="finished" finished-text="没有更多了" :immediate-check="false">
            <div class="my-mileage-detail-list-item flex-start-center" v-for="(item, index) in mileageDetailList"
              :key="index">
              <img class="type-icon" :src="item.icon" alt="" width="40px" height="40px">
              <div class="info flex-between-center">
                <div class="left flex-column-start-start">
                  <div class="flex-start-center">
                    <div class="title m-text-14-20-600 m-mr12">{{ item.eventTypeLabel }}</div>
                    <div class="group-label m-text-12-18-400" :style="getGroupLabelStyle(item)">{{ `${item.plusType === 0 ? '取消' : ''}${item.eventGroupLabel}`
                      }}
                    </div>
                  </div>
                  <div class="time m-text-12-18-400 m-mt3">{{ item.eventTime }}</div>
                </div>
                <div class="right flex-start-center">
                  <img class="m-mr8" :src="item.plusType === 1 ? PlusStepIcon : MinusStepIcon" alt="" width="24"
                    height="24">
                  <div v-if="item.plusType !== null && item.plusType !== undefined" class="m-text-16-24-400"
                    :style="{ color: item.plusType === 1 ? '#ff9832' : '#94959C' }">{{ `${item.points}步` }}</div>
                </div>
              </div>
            </div>
          </van-list>
        </div>

        <!-- 类型选择器弹出层 -->
        <van-popup v-model:show="showTypePicker" position="bottom" round>
          <van-picker v-model="selectedMyMileageType" title="选择类型" :columns="mileageTypeList"
            @confirm="handleTypePickerConfirm" @cancel="showTypePicker = false" show-toolbar />
        </van-popup>

        <!-- 日期选择器弹出层 -->
        <van-calendar ref="calendarRef" v-model:show="showCalendar" title="选择日期范围" :min-date="minDate"
          :max-date="maxDate" :show-confirm="true" :type="calendarType" color="#508CFF">

          <template #footer>
            <div class="calendar-footer">
              <van-button plain class="calendar-clear-btn" @click="onCalendarClear">清除</van-button>
              <van-button type="primary" class="calendar-confirm-btn" @click="onCalendarConfirm">确认</van-button>
            </div>
          </template>
        </van-calendar>
      </section>
      <div class="total-step">
        <div class="total-step-content">
          <div class="total-step-value">{{ historyTotalMileage }}</div>
          <div class="total-step-label">总步数</div>
        </div>
      </div>
    </section>
    <section class="week-month-mileage" v-if="activeTab == 2 || activeTab == 3">
      <div class="mileage-header-bg"></div>
      <div class="mileage-header">
        <div class="mileage-header-selector">
          <div class="mileage-header-selector-inner" :class="{ 'week': activeTab == 2, 'month': activeTab == 3 }">
            <div class="week-selector" v-if="activeTab === 2" @click="showWeekPickerPopup">
              <div class="selected-week">{{ selectedWeekLabel || '请选择周' }}</div>
              <el-icon color="#508CFF" style="margin-left: 5px;" size="12">
                <CaretBottom />
              </el-icon>
            </div>
            <div class="month-selector" v-if="activeTab === 3" @click="showMonthPickerPopup">
              <div class="selected-month">{{ selectedMonthLabel || '请选择月' }}</div>
              <el-icon color="#508CFF" style="margin-left: 5px;" size="12">
                <CaretBottom />
              </el-icon>
            </div>
          </div>
        </div>
        <div class="mileage-overview">
          <div class="overview-content">
            <div class="overview-header">
              <div class="header-btn">里程概览</div>
            </div>
            <div class="mileage-grid">
              <div class="week-mileage-item">
                <div class="value big-value">{{ weekOrMonthMileageData.totalPoints }}</div>
                <div class="item-bottom flex-column-center-center">
                  <div class="percentage up">
                    <img :src="weekOrMonthMileageData.icon" alt="" width="20" height="20">
                    <span>{{ weekOrMonthMileageData.showTotalPointsWeekOnWeek }}%</span>
                  </div>
                  <div class="label big-label">月总步数</div>
                </div>
              </div>
              <div class="week-mileage-item" v-for="item in weekOrMonthMileageData.eventGroups" :key="item.value">
                <div class="value">{{ item.points }}</div>
                <div class="item-bottom flex-column-center-center">
                  <div class="percentage" :class="{ 'up': item.plusType === 1, 'down': item.plusType === 0 }">
                    <img :src="item.icon" alt="" width="20" height="20">
                    <span>{{ item.showWeekOnWeek }}%</span>
                  </div>
                  <div class="label">{{ item.label }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="rank-container">
            <div class="rank-box club-rank">
              <div class="rank-header">
                <div class="rank-btn">总社排名</div>
              </div>
              <div class="rank-content">
                <div class="rank-label">第</div>
                <div class="rank-value">{{ activeTab === 2 ? weekRankInfo.clubRank || '-' : activeTab === 3 ?
                  monthRankInfo.clubRank || '-' : '-' }}</div>
                <div class="rank-label">名</div>
              </div>
            </div>
            <div class="rank-box group-rank">
              <div class="rank-header">
                <div class="rank-btn">小组排名</div>
              </div>
              <div class="rank-content">
                <div class="rank-label">第</div>
                <div class="rank-value">{{ activeTab === 2 ? weekRankInfo.clubGroupRank || '-' : activeTab === 3 ?
                  monthRankInfo.clubGroupRank || '-' : '-' }}</div>
                <div class="rank-label">名</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="ability-analysis">
        <div class="ability-analysis-header m-mb10">
          能力分析
        </div>
        <div class="ability-analysis-chart">
          <ability-analysis-chart :chart-data="abilityAnalysisChartData" />
        </div>
      </div>
      <div class="active-trend-analysis">
        <div class="active-trend-analysis-header m-mb10">
          活跃趋势分析
        </div>
        <div class="active-trend-analysis-chart">
          <active-trend-analysis-chart :chart-data="activeTrendAnalysisData" />
        </div>
      </div>
      <div class="data-statistic">
        <div class="data-statistic-header m-mb10">
          数据统计
        </div>
        <div class="data-statistic-content">
          <van-tabs v-model:active="activeDataStatisticTab" @change="changeDataStatisticTab">
            <van-tab v-for="tab in dataStatisticTabs" :key="tab.value" :name="tab.value">
              <template #title>
                <span v-html="tab.label && tab.label.replace('进步', '<br>进步')"></span>
              </template>
            </van-tab>
          </van-tabs>
          <div class="data-statistic-content-list">
            <div class="data-statistic-content-item flex-column-start-start" v-for="item in currentTabData"
              :key="item.eventTypeValue">
              <div class="data-statistic-content-item-label">{{ item.label }}</div>
              <div class="data-statistic-content-item-value flex-between-center">
                <div class="data-statistic-content-item-value-number flex-start-center">{{ item.count }}
                </div>

                <img :src="getEventTypeIcon(item)" alt="" width="20" height="20">
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 周选择器弹出层 -->
      <van-popup v-model:show="weekPickerVisible" position="bottom" round>
        <van-picker v-model="selectedWeek" title="选择周" :columns="weekList" @confirm="onWeekPickerConfirm"
          @cancel="weekPickerVisible = false" show-toolbar />
      </van-popup>

      <!-- 月选择器弹出层 -->
      <van-popup v-model:show="monthPickerVisible" position="bottom" round>
        <van-picker v-model="selectedMonth" title="选择月" :columns="monthList" @confirm="onMonthPickerConfirm"
          @cancel="monthPickerVisible = false" show-toolbar />
      </van-popup>
    </section>
  </div>
</template>

<style lang="scss" scoped>
@use "@/views/mobile/styles/custom-global-mobile-style.scss";

.act-mileage {
  display: flex;
  flex-direction: column;
  height: calc(100% - 56px);

  .header-area {
    :deep(.van-tabs) {
      .van-tabs__nav {
        background: transparent;
      }

      .van-tab {
        font-size: 14px;

        &--active {
          color: #508CFF !important;
          font-weight: 600 !important;
        }
      }

      .van-tabs__line {
        background: #508CFF;
        border-radius: 3px;
      }
    }
  }

  .my-mileage {
    flex: 1;
    overflow-y: auto;

    &-statistic {
      padding: 12px;
      background-color: #fff;
      margin-bottom: 12px;

      .week-mileage {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        background-image: url('@/assets/mobile/act-mileage/my-mileage/week-mileage-bg.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;

        &-content {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          grid-template-rows: repeat(2, 1fr);
          row-gap: 14px;
          width: 100%;
          padding-top: 6px;

          .mileage-item {
            width: 100%;
            color: #fff;

            .value {
              font-size: 20px;
              line-height: 28px;
              margin-bottom: 4px;

              &.big-value {
                font-size: 28px;
                /* line-height: 40px; */
                /* margin-bottom: 0; */
              }
            }

            .label {
              font-size: 12px;
              line-height: 18px;

              &.big-label {
                font-size: 14px;
                line-height: 20px;
              }
            }

            &.has-border {
              border-left: 1px solid #fff;
              border-right: 1px solid #fff;
            }
          }
        }

      }

      .other-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        column-gap: 13px;

        .left,
        .right {
          width: 100%;
        }

        .club-rank,
        .group-rank {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: flex-start;
          padding: 16px;
        }

        .club-rank {
          background-image: url('@/assets/mobile/act-mileage/my-mileage/club-rank-bg.png');

          .club-rank-header {
            margin-bottom: 8px;
          }

          .club-rank-text {
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: 16px;
            width: 100%;

            .text {
              font-size: 30px;
              line-height: 44px;
              margin-left: 15px;
              margin-right: 15px;
            }
          }
        }

        .group-rank {
          margin-top: 12px;
          background-image: url('@/assets/mobile/act-mileage/my-mileage/group-rank-bg.png');

          .group-rank-header {
            margin-bottom: 8px;
          }

          .group-rank-text {
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: 16px;
            width: 100%;

            .text {
              font-size: 30px;
              line-height: 44px;
              margin-left: 15px;
              margin-right: 15px;
            }
          }
        }
      }

      .today-task {
        padding: 16px;
        background-image: url('@/assets/mobile/act-mileage/my-mileage/today-task-bg.png');

        .today-task-header {
          margin-bottom: 24px;
        }

        .today-task-content {
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          color: #fff;
          font-size: 16px;
          width: 100%;

          .task-item {
            margin-bottom: 24px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }

    &-detail {
      padding: 16px 12px;
      background-color: #fff;

      .my-mileage-search-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .type-selector {
          width: 32.3%;
          padding: 8px 12px;
          border-radius: 8px;
          border: 1px solid #E2E3E6;
          margin-right: 12px;
          flex-shrink: 0;

          &-label {
            flex: 1;
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .el-icon {
            flex-shrink: 0;
          }
        }

        .time-selector {
          flex: 1;
          padding: 8px 12px;
          border-radius: 8px;
          border: 1px solid #E2E3E6;

          &-text {
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #606266;

            .separator {
              margin: 0 4px;
            }
          }
        }

      }

      :deep(.van-calendar) {
        .calendar-footer {
          display: flex;
          justify-content: space-between;
          padding: 10px 16px;
          border-top: 1px solid #f5f6fa;

          .calendar-clear-btn {
            flex: 1;
            margin-right: 10px;
            border-color: #508CFF;
            color: #508CFF;
            font-size: 16px;
          }

          .calendar-confirm-btn {
            flex: 1;
            background-color: #508CFF;
            border-color: #508CFF;
            font-size: 16px;
          }
        }
      }

      .my-mileage-detail-list {
        &-item {

          .type-icon {
            margin-right: 12px;
          }

          .info {
            flex: 1;
            padding: 16px 0;
            border-bottom: 1px solid #F5F6FA;

            .left {
              flex: 1;
              padding-right: 10px;
            }

            .right {
              width: 90px;
            }
          }
        }
      }

    }

    .total-step {
      position: fixed;
      right: 12px;
      bottom: calc(200px + env(safe-area-inset-bottom));
      width: 76px;
      height: 76px;
      background-image: url('@/assets/mobile/act-mileage/my-mileage/total-step-bg-no-shadow.png');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      box-shadow: 6px 6px 12px -3px rgba(85, 150, 255, 0.6), 14px 14px 22px 0px rgba(85, 161, 255, 0.2), 8px 8px 14px -8px rgba(255, 255, 255, 0.9);
      border-radius: 50%;

      &-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
      }

      &-value {
        font-family: ResourceHanRoundedCN-Bold;
        font-weight: 800;
        font-size: 20px;
        color: #FFFFFF;
        line-height: 24px;
        text-shadow: 0 0 6px rgba(56,
            120,
            224,
            0.7), 0 0 3px rgba(64,
            95,
            255,
            0.3);
        text-align: center;
        font-style: normal;
      }

      &-label {
        font-family: ResourceHanRoundedCN-Bold;
        font-weight: bold;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 20px;
        text-shadow: 0 0 4px rgba(56,
            120,
            224,
            0.7), 0 0 2px rgba(64,
            95,
            255,
            0.3);
        text-align: center;
        font-style: normal;
      }


    }
  }

  .week-month-mileage {
    position: relative;
    flex: 1;
    overflow-y: auto;
    padding: 30px 12px;
    background-color: #fff;

    .mileage-header-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 500px;
      background-image: url('@/assets/mobile/act-mileage/week-mileage/header-bg.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .mileage-header {
      margin-bottom: 20px;

      &-selector {
        width: 100%;
        height: 49px;
        display: flex;
        justify-content: center;
        align-items: center;

        &-inner {
          height: 100%;
          display: flex;
          position: relative;

          &.week {
            justify-content: flex-end;
            align-items: center;
            width: 100%;
            background: url('@/assets/mobile/act-mileage/week-mileage/week-bg.png') no-repeat;
            background-size: 100% 100%;
          }

          &.month {
            justify-content: center;
            align-items: center;
            width: 248px;
            height: 49px;
            background: url('@/assets/mobile/act-mileage/week-mileage/month-bg.png') no-repeat;
            background-size: 100% 100%;
          }

          .week-selector,
          .month-selector {
            position: absolute;
            top: 5px;
            right: 22px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #FFFFFF;
            border-radius: 4px;
            padding: 3px 15px;
            height: 26px;
            cursor: pointer;

            .selected-week,
            .selected-month {
              font-size: 14px;
              color: #333;
              white-space: nowrap;
              font-weight: 500;
            }
          }

          .week-selector {
            width: 210px;
          }

          .month-selector {
            width: 107px;
          }
        }
      }

      .mileage-overview {
        margin-top: 27px;
        padding: 0 2px;

        .overview-content {
          position: relative;
          background: #508CFF;
          border-radius: 10px;
          padding: 16px 10px;
          box-shadow: 0 4px 12px rgba(80, 140, 255, 0.2);

          .mileage-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px 0;

            .week-mileage-item {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: flex-end;
              position: relative;

              .item-bottom {
                width: 100%;
              }

              &:not(:nth-child(3n)) {
                &::after {
                  content: '';
                  position: absolute;
                  right: 0;
                  top: 55%;
                  height: 40%;
                  width: 1px;
                  background-color: #fff;
                }
              }

              .value {
                font-size: 20px;
                font-weight: bold;
                color: #FFFFFF;
                line-height: 30px;
                font-family: SourceHanSansCN, SourceHanSansCN;
                width: 100%;
                text-align: center;

                &.big-value {
                  font-size: 28px;
                  line-height: 40px
                }
              }

              .percentage {
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 2px 0;
                font-size: 12px;
                width: 100%;
                color: #fff;

                img {
                  margin-right: 3px;
                }

                &.up {
                  color: #FFCF49;
                }

                &.down {
                  color: #72E9B4;
                }
              }

              .label {
                font-size: 12px;
                line-height: 18px;
                color: #FFFFFF;
                white-space: nowrap;
                width: 100%;
                text-align: center;

                &.big-label {
                  font-size: 14px;
                }
              }
            }
          }
        }

        .rank-container {
          display: flex;
          justify-content: space-between;
          margin-top: 31px;
          gap: 13px;

          .rank-box {
            position: relative;
            background: #508CFF;
            border-radius: 10px;
            padding: 27px 10px 17px;
            flex: 1;

            .rank-header {
              margin-bottom: 10px;

              .rank-btn {
                padding: 3px 14px;
                font-size: 14px;
                white-space: nowrap;
              }
            }

            .rank-content {
              display: flex;
              align-items: center;
              justify-content: center;

              .rank-label {
                font-size: 16px;
                color: #FFFFFF;
              }

              .rank-value {
                font-size: 30px;
                font-weight: 700;
                color: #FFFFFF;
                margin: 0 5px;
                line-height: 1;
              }
            }
          }
        }

        .overview-header,
        .rank-header {
          display: flex;
          justify-content: space-between;
          position: absolute;
          left: 50%;
          top: -15px;
          transform: translateX(-50%);
        }

        .header-btn,
        .rank-btn {
          height: 30px;
          background: #FFFFFF;
          border-radius: 4px;
          padding: 4px 20px;
          font-size: 14px;
          font-weight: 500;
          color: #508CFF;
          display: flex;
          align-items: center;
          justify-content: center;

          &::before {
            display: inline-block;
            content: ' ';
            width: 2px;
            height: 13px;
            background-color: #508CFF;
            margin-right: 8px;
          }
        }
      }
    }

    .ability-analysis {
      margin-top: 50px;
      z-index: 2;

      &-header {
        position: relative;
        width: 137px;
        height: 30px;
        background: url('@/assets/mobile/act-mileage/week-mileage/title-bg-1.png') no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-size: 16px;
        color: #fff;
        padding-right: 10px;
        z-index: 3;
      }
    }

    .active-trend-analysis {
      margin-top: 50px;
      z-index: 2;

      &-header {
        position: relative;
        width: 175px;
        height: 30px;
        background: url('@/assets/mobile/act-mileage/week-mileage/title-bg-2.png') no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-size: 16px;
        color: #fff;
        padding-right: 10px;
        z-index: 3;
      }
    }

    .data-statistic {
      margin-top: 50px;
      z-index: 2;

      &-header {
        position: relative;
        width: 137px;
        height: 30px;
        background: url('@/assets/mobile/act-mileage/week-mileage/title-bg-1.png') no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-size: 16px;
        color: #fff;
        padding-right: 10px;
        z-index: 3;
      }

      &-content {
        :deep(.van-tabs) {

          .van-tabs__wrap {
            height: 70px;
            border-bottom: 1px solid #e2e3e6;
          }

          .van-tab {
            padding: 0;

            .van-tab__text--ellipsis {
              overflow: visible;
              text-align: center;
            }

            .van-tab__text {
              display: flex;
              align-items: center;
              justify-content: center;
              height: 63px;
              width: 100%;
              padding-bottom: 8px;
            }

            &--active {
              .van-tab__text {
                background: url('@/assets/mobile/act-mileage/week-mileage/data-statistic/tab-bg.png') no-repeat;
                background-size: 100% 100%;
                color: #fff;
              }
            }

          }
        }

        &-list {
          padding-top: 14px;
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          column-gap: 9px;
          row-gap: 12px;

          .data-statistic-content-item {
            width: 100%;
            /* height: 72px; */
            background-color: #F3F7FF;
            padding: 12px 8px 12px 12px;

            &-label {
              font-size: 13px;
              color: #94959C;
              line-height: 18px;
              text-align: left;
              margin-bottom: 3px;
            }

            &-value {
              width: 100%;
              padding-right: 4px;

              &-number {
                font-weight: bold;
                font-size: 20px;
                color: #2B2C33;
                line-height: 29px;
                text-align: left;
              }

              &-unit {
                font-size: 14px;
                color: #2B2C33;
                line-height: 21px;
                text-align: center;
                padding-left: 2px;
              }

              img {
                width: 20px;
                height: 20px;
                margin-left: 14px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
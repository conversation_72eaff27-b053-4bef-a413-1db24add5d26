<!-- 评分详情 -->
<template>
    <div class="score-detail">
        <p class="text_16_bold">评分概况</p>
        <el-table :data="score_overview_list" class="my-table" stripe style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F8F9FF"}'>
            <el-table-column prop="title" label="评分方向" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="sch" label="材料总数" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="sch" label="已评" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="sch" label="打分进度" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="sch" label="已评材料平均分" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="sch" label=">=43" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="sch" label="35-42" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="sch" label="<35" show-overflow-tooltip></el-table-column> 
        </el-table>
        <p class="text_16_bold mt_24">评分详情</p>
        <el-table :data="score_list" class="my-table" stripe style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F8F9FF"}'>
            <el-table-column type="index" label="序号" width="80"  />
            <el-table-column prop="title" label="学段" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="sch" label="学科" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="sch" label="课题名称" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="sch" label="所在单位" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="sch" label="支持人" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="sch" label="是否青年专项" show-overflow-tooltip></el-table-column> 
            <el-table-column prop="sch" label="选题价值" show-overflow-tooltip></el-table-column> 
        </el-table>
        <el-pagination background layout="total,prev, pager, next" :total="total" class="mt-4" :current-page='pageNum' :page-size="pageSize" @current-change='currentChange' />
    </div>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    const score_overview_list = ref([{title:'选题价值'}]) // 评分概况
    const score_list = ref([{title:'选题价值'}]) // 评分详情
    const pageNum = ref(1) // 当前页数
    const pageSize = ref(10) // 每页显示条数
    const total = ref(1) // 总页数
    const currentChange = (currentPage) => {
        pageNum.value = currentPage
    }
    onMounted(() => {
    })
</script>
<style lang="scss" scoped>
.score-detail{
     
}
</style>
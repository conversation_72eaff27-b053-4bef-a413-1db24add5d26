<template>
    <div class="allocation-main">
        <ul class="flex_start tab-ul">
            <li v-for="item in tab_list" :key="item.id" :class="current_tab === item.id? 'active-tab' : ''" @click="current_tab = item.id">{{item.title}}</li>
        </ul>
        <Group v-if="current_tab == 1"></Group>
        <Judges v-else></Judges>
    </div>
</template>

<script setup>
import { reactive, ref, computed, onBeforeMount, onMounted, nextTick, watch, toRefs } from 'vue'
import { toRaw } from "@vue/reactivity"
import Group from '@/views/pc/component/jky/Group.vue'
import Judges from './Judges.vue'
import { taskList, taskDel } from '@/api/pc/jky'
import { applyList, msjtDel } from '@/api/pc/msjt'

const total = ref(0)
const tab_list = ref([{id:1,title:'评委分组'},{id:2,title:'评委分配'}])
const current_tab = ref(1)

// 生命周期钩子
onMounted(() => {  
}) 

</script>


<style lang="scss" scoped>
.allocation-main{
    .tab-ul{ border-bottom: 1px solid #E2E3E6; margin-bottom:20px; display:inline-flex;
        li{ margin-right: 32px; color: #94959C; padding: 6px 0; cursor: pointer; }
        .active-tab{ font-weight: bold; color: #508CFF; border-bottom: 2px solid #508CFF; }
    }
    
}
</style>    
<template>
    <div class="jky-main">
        <div class="radius-box">
            <el-form inline :model="search_form" class="search_form flex_start flex_wrap">
                <el-form-item label="任务名称" style="margin-right: 16px;width: calc(33% - 16px);">
                    <el-input v-model="search_form.name" @input="changeUserName" class="input_40" placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="评审专家" style="margin-right: 16px;width: calc(33% - 16px);">
                    <el-input v-model="search_form.name" @input="changeUserName" class="input_40" placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="任务下发时间" style="margin-right: 16px;width: calc(33% - 16px);">
                    <el-date-picker class="daterange_40" v-model="search_form.date" type="daterange" range-separator="-" start-placeholder="选择开始时间" end-placeholder="选择结束时间" :size="size" />
                </el-form-item>
                <el-form-item label="评价是否完成" style="margin-right: 16px;width: calc(33% - 16px);">
                    <el-select v-model="search_form.enable" class="select_40" clearable placeholder="请选择" @change="changeExtend">
                        <el-option v-for="item in SMS_APP_STATE" :key="item.id" :value="item.id" :label="item.name"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="评价完成时间" style="margin-right: 16px;width: calc(33% - 16px);">
                    <el-date-picker class="daterange_40" v-model="search_form.finishTime" type="daterange" range-separator="-" start-placeholder="选择开始时间" end-placeholder="选择结束时间" :size="size" />
                </el-form-item>
                <el-form-item label="操作" class="hidden-label" style="width: 132px;margin-right: 0;">
                    <div class="flex_end" style="width: 100%"> 
                        <el-button class="primary-btn btn_132_40" type="primary" @click="searchClick">搜索</el-button>
                    </div>
                </el-form-item>
            </el-form>
        </div>
        <div class="radius-box">
            <div class="flex_between">
                <p class="text_20_bold">自评任务列表</p>
            </div>
            <el-table :data="sch_task_list" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F9F9F9"}'>
                <el-table-column type="index" label="序号" width="80"  />
                <el-table-column prop="taskTitle" label="任务名称" show-overflow-tooltip></el-table-column> 
                <el-table-column prop="reviewUserNames" label="考核专家" />
                <el-table-column prop="createTime" label="创建时间" />
                <el-table-column prop="isReviewCompleted" label="是否完成专家评审">
                    <template #default='scope'>
                        <p><img v-if="scope.row.isReviewCompleted" src="@/assets/pc/jky/success.png" alt="" class="state-icon"><img v-else src="@/assets/pc/jky/fail.png" alt="" class="state-icon">{{scope.row.isReviewCompleted ? '是' : '否'}}</p>
                    </template>
                </el-table-column>
                <el-table-column prop="reviewCompleteTime" label="专家评审完成时间" />
                <el-table-column prop="address" label="操作" width="200">
                    <template #default='scope'>
                        <div class="flex_start" style="height:100%">
                            <el-button type="primary" link style="font-size:16px;font-weight:400;" @click="allocation_visible = true, taskSchoolId = scope.row.taskSchoolId">名额分配</el-button>
                            <p class="table-line"></p>
                            <el-button type="primary" link style="font-size:16px;font-weight:400;" @click="detail_visible = true, taskSchoolId = scope.row.taskSchoolId">查看任务</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    background
                    layout="total,prev, pager, next"
                    :total="total"
                    class="mt-4"
                    :current-page='search_form.page'
                    @current-change='currentChange'
                /> 
        </div>
        <el-dialog v-model="detail_visible" v-if="detail_visible" title="查看任务" width="456px" center class="my-dialog">
            <Detail :id="taskSchoolId" @close="detail_visible = false"></Detail>
        </el-dialog>
        <el-dialog v-model="allocation_visible" v-if="allocation_visible" title="分配专家" width="920px" center class="my-dialog">
            <Allocation :id="taskSchoolId" @close="allocation_visible = false, init()"></Allocation>
        </el-dialog>
    </div>
</template> 
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import { CirclePlusFilled } from '@element-plus/icons-vue'
    import Detail from './detail.vue'
    // import Result from './result.vue'
    import Allocation from './allocation.vue'
    import { schTask } from '@/api/pc/jky'
    const formObj = function(obj = {}){
        this.name = obj.name || ''
        this.pageNum = obj.pageNum || 1
        this.pageSize = obj.pageSize || 10
    }
    const search_form = ref(new formObj({}))
    const total = ref(0)
    const sch_task_list = ref([{}])
    const detail_visible = ref(false)
    const examine_visible = ref(false)
    const allocation_visible = ref(false)
    const taskSchoolId = ref('')

    const getSchTask = async () => {
        let res = await schTask(search_form.value)
        if(res){
            sch_task_list.value = res.data.list
            total.value = res.data.total
        }
    }

    const init = () => {
        search_form.value.pageNum = 1
        getSchTask()
    }

    const searchClick = () => {

    }
    const currentChange = (page) => {}
    onMounted(() => {
        getSchTask()
    })
</script>
<style lang="scss" scoped>
.jky-main{max-width: 1128px;margin:0 auto;font-family: PingFangSC, PingFang SC;
    .radius-box{border-radius: 12px;background: #fff;padding: 24px 24px 6px;margin: 10px 0;}
    .state-icon{width: 16px;height: 16px;margin-right: 8px;}
}
</style> 
<style lang="scss">
    .el-table th.el-table__cell.is-leaf{border: none!important;}
</style>
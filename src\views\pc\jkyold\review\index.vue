<template>
    <div class="jky-main">
        <div class="radius-box">
            <el-form inline :model="search_form" class="search_form flex_start">
                <el-form-item label="材料名称" style="margin-right: 16px;width: calc(33% - 16px);">
                    <el-input v-model="search_form.name" @input="changeUserName" class="input_40" placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="状态" style="margin-right: 16px;width: calc(33% - 16px);">
                    <el-select v-model="search_form.enable" class="select_40" clearable placeholder="请选择" @change="changeExtend">
                        <el-option v-for="item in SMS_APP_STATE" :key="item.id" :value="item.id" :label="item.name"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="操作" class="hidden-label" style="width: 132px;margin-right: 0;" >
                    <div class="flex_end" style="width: 100%"> 
                        <el-button class="primary-btn btn_132_40" type="primary" @click="searchClick">搜索</el-button>
                    </div>
                </el-form-item>
            </el-form> 
        </div>
        <div class="radius-box">
            <p class="text_20_bold">专家评审列表</p>
            <el-table :data="review_list" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F9F9F9"}'>
                <el-table-column type="index" label="序号" width="80"  />
                <el-table-column prop="taskTitle" label="任务名称" show-overflow-tooltip></el-table-column> 
                 <!-- v-if="route.query.type == '2'" -->
                <el-table-column prop="schoolName" label="学校名称" />
                <!-- <el-table-column v-if="route.query.type == '1'" prop="tenantTypeName" label="材料名称" />
                <el-table-column v-if="route.query.type == '1'" prop="tenantTypeName" label="提交时间" /> -->
                <el-table-column prop="schoolReviewCompleteTime" label="评审完成时间" />
                <el-table-column prop="" label="状态">
                    <template #default='scope'> 
                        <p v-if="scope.row.isCompleted" class="flex_start green"><img src="@/assets/pc/jky/review-success.png" class="review-icon" alt=""> 已评审</p>
                        <p v-else class="flex_start orange"><img src="@/assets/pc/jky/review-wait.png" class="review-icon" alt="">待评审</p>
                    </template>
                </el-table-column>
                <el-table-column prop="address" label="操作" width="240">
                    <template #default='scope'>
                        <div class="flex_start" style="height:100%">
                            <el-button type="primary" link style="font-size:16px;font-weight:400;" @click="goDetail(scope.row)">查看 </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    background
                    layout="total,prev, pager, next"
                    :total="total"
                    class="mt-4"
                    :current-page='search_form.page'
                    @current-change='currentChange'
                />
        </div>
    </div>
</template> 
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import { CirclePlusFilled } from '@element-plus/icons-vue'
    import { reviewSchList, reviewAreaList } from '@/api/pc/jky'
    import {useRoute,useRouter} from 'vue-router'
    let route = useRoute()
    let router = useRouter()

    const formObj = function(obj = {}){
        this.name = obj.name || ''
        this.pageNum = obj.pageNum || 1
        this.pageSize = obj.pageSize || 10
    }
    const search_form = ref(new formObj({}))
    const total = ref(0)
    const review_list = ref([{}])

    const getReviewSchList = async () => {
        let res = await reviewSchList(search_form.value)
        if(res){
            review_list.value = res.data.list
            total.value = res.data.total
        }
    }

    const getReviewAreaList = async () => {
        let res = await reviewAreaList(search_form.value)
        if(res){
            review_list.value = res.data.list
            total.value = res.data.total
        }
    }

    const searchClick = () => {

    }
    const currentChange = (page) => {
        search_form.value.pageNum = page
        if(route.query.type == '1'){
            getReviewAreaList()
        }else{
            getReviewSchList()
        }
    }
    const goDetail = (row) => {
        if(route.query.type == '1'){
            router.push(`/jky/review/area/detail/${row.taskMasterId}/${row.reviewUserId}/${row.schoolId}?type=${route.query.type}`)
        }else{
            router.push(`/jky/review/sch/detail/${row.taskMasterId}/${row.schoolReviewUserId}/${row.schoolId}?type=${route.query.type}`)
        }
    }
    onMounted(() => {
        console.log(route.query,'routesdfg')
        if(route.query.type == '1'){
            getReviewAreaList()
        }else{
            getReviewSchList()
        }
    })
</script>
<style lang="scss" scoped>
.jky-main{max-width: 1128px;margin:0 auto;font-family: PingFangSC, PingFang SC;
    .radius-box{border-radius: 12px;background: #fff;padding: 24px 24px 6px;margin: 10px 0;}
    .review-icon{width: 14px;height: 14px;margin-right: 8px;}
}
</style> 
<style lang="scss">
    .el-table th.el-table__cell.is-leaf{border: none!important;}
</style>
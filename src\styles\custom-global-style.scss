/* 主题颜色 */
:root {
  --blue1: #508CFF;
  --blue2: #3381ff;
  --blue3: #6e4aff;
  --blue4: #496eee;
  --yellow1: #edd00f;
  --grey1: #2b2c33;
  --grey2: #61636e;
  --grey3: #6D6F75;
  --grey4: #b4b6be;
  --grey5: #94959c;
  --red1: #FF6767;
  --green1: #28c94f;
  --orange1: #FFA833;
  --line: #e2e3e6;
  --border-1: #e6e6e6;
  --custom-blue-button-hover-color: #ebf5ff;
  --custom-green-button-hover-color: #eafaf2;
  --custom-red-button-hover-color: #fff2f4;
  --box-bg-color: #ffffff;
  --wait-status-bg-color: #f2fbff;
  --pass-status-bg-color: #f3fffa;
  --reject-status-bg-color: #fff2f4;
}

.blue1 {
  color: var(--blue1);
}

.blue2 {
  color: var(--blue2);
}

.blue3 {
  color: var(--blue3);
}

.blue4 {
  color: var(--blue4);
}

.yellow1 {
  color: var(--yellow1);
}

.grey1 {
  color: var(--grey1);
}

.grey2 {
  color: var(--grey2);
}

.grey3 {
  color: var(--grey3);
}

.grey4 {
  color: var(--grey4);
}

.grey5 {
  color: var(--grey5);
}

.red1 {
  color: var(--red1);
}

.green1 {
  color: var(--green1);
}

.white {
  color: #ffffff;
}

.orange1 {
  color: var(--orange1);
}

/* 鼠标cursor */
.pointer {
  cursor: pointer;
}

.not-allowed {
  cursor: not-allowed;
}

/* 相关字体 */
.text-24-32-400 {
  font-size: 24px;
  line-height: 32px;
  font-weight: 400;
}

.text-22-30-400 {
  font-size: 22px;
  line-height: 30px;
  font-weight: 400;
}

.text-18-26-600 {
  font-size: 18px;
  line-height: 26px;
  font-weight: 600;
}

.text-18-26-400 {
  font-size: 18px;
  line-height: 26px;
  font-weight: 400;
}

.text-16-24-600 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
}

.text-16-24-400 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
}

.text-14-20-600 {
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
}

.text-14-20-400 {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.text-12-18-600 {
  font-size: 12px;
  line-height: 18px;
  font-weight: 600;
}

.text-12-18-400 {
  font-size: 12px;
  line-height: 18px;
  font-weight: 400;
}

/* 全局间距 */
// 循环创建全局变量 形如 mb ml mr mt
@for $i from 0 through 120 {
  .m#{$i} {
    margin: #{$i}px !important;
  }
  .mt#{$i} {
    margin-top: #{$i}px !important;
  }
  .mr#{$i} {
    margin-right: #{$i}px !important;
  }
  .mb#{$i} {
    margin-bottom: #{$i}px !important;
  }
  .ml#{$i} {
    margin-left: #{$i}px !important;
  }
  .p#{$i} {
    padding: #{$i}px !important;
  }
  .pt#{$i} {
    padding-top: #{$i}px !important;
  }
  .pr#{$i} {
    padding-right: #{$i}px !important;
  }
  .pb#{$i} {
    padding-bottom: #{$i}px !important;
  }
  .pl#{$i} {
    padding-left: #{$i}px !important;
  }
}

/* 常用布局 */
/* flex */
.flex-start-start {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}

.flex-start-center {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.flex-start-end {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
}

.flex-start-stretch {
  display: flex;
  justify-content: flex-start;
  align-items: stretch;
}

.flex-center-start {
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.flex-center-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-center-end {
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

.flex-between-start {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.flex-between-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-between-end {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.flex-end-start {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
}

.flex-end-center {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.flex-end-end {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}

.flex-column-start-start {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}

.flex-column-start-center {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.flex-column-center-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.flex-column-center-start {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.flex-column-between-end {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
}

.flex-column-end-end {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-end;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

/* 通用 内容 box */
.box {
  padding: 20px;
  background-color: var(--box-bg-color);
  border-radius: 8px;
}

.box-24 {
  padding: 24px;
  background-color: var(--box-bg-color);
  border-radius: 8px;
}

.box-20-16 {
  padding: 20px 16px;
  background-color: var(--box-bg-color);
  border-radius: 8px;
}

// grid 布局
.grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  // gap: 40px 30px;
  column-gap: 40px;
}

/* 溢出隐藏 */
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.w100 {
  width: 100%;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.pointer-none {
  pointer-events: none;
}

.pointer {
  cursor: pointer !important;
}

// 下划线
.underline {
  text-decoration: underline;
}

.border-color-grey {
  border-color: var(--border-1);
}

.display-none {
  display: none;
}

<script setup>
import useResize from "@/layout/hooks/useResize"
import { useDevice } from "@/hooks/useDevice"
import Pc from "@/layout/Pc.vue"
import Mobile from "@/layout/Mobile.vue"

useResize()


const MAX_MOBILE_WIDTH = 750
const _isMobile = () => {
  const rect = document.body.getBoundingClientRect()
  return rect.width - 1 < MAX_MOBILE_WIDTH
}


</script>

<template>
  <div>
    <Pc v-if="!_isMobile()" />
    <Mobile v-else />
  </div>
</template>

<style lang="scss" scoped>
  
</style>
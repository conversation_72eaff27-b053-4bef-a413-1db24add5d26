<template>
		<div class="select-view">
			<div >
				<div class="flex_between">
					<p class="tips">您希望该教案包含以下哪些部分？</p>
					<img src="@/assets/pc/bkzs/back.png" alt="" class="back" @click="back" />
				</div>
	
				<div class="flex_between tabs">
					<ul class="flex_start">
						<li class="flex_end" v-for="(item, index) in tabList" :key="index">
							<div :class="choicedList.indexOf(item.title) > -1 ? 'active' : ''">
								<p @click="choiceTab(item)">{{ item.title }}</p>
								<img src="@/assets/pc/bkzs/del-tab.png" alt="" class="candel" v-if="item.canDel"
									@click="goDelChoiced(item)" />
							</div>
						</li>
					</ul>
					<div class="add-btn" @click="goTab" style="max-height: 30px">
						<img src="https://cdn.tqxxkj.cn/static/images/ai/add.png" alt="" />
					</div>
				</div>
			</div>
			<div class="card">
				<div class="send-select-m">
					<div class="flex_between margin_bottom_8">
						<p class="text_14 grey1  margin_right_8" >我是一名</p>
					<div class="flex_start send-select-item flex_1">
						<el-select v-model="select_form.stage" placeholder="学段" class="margin_right_8" @change="changeStage">
							<el-option v-for="item in select_list" :key="item.id" :label="item.title"
								:value="item.id" />
						</el-select>
						<el-select v-model="select_form.grade" placeholder="年级" class="margin_right_8" @change="changeGrade">
							<el-option v-for="item in grade_list" :key="item.id" :label="item.title" :value="item.id" />
						</el-select>
						<el-select v-model="select_form.subject" placeholder="学科" class="margin_right_8" @change="changeSubject">
							<el-option v-for="item in subject_list" :key="item.id" :label="item.title"
								:value="item.id" />
						</el-select>
					<p class="text_14 grey1">教师</p>

					</div>

					</div>
					<div class="flex_between margin_bottom_8">
						<p class="text_14 grey1  margin_right_8" >正在使用</p>
					<div class="flex_start send-select-item flex_1">
						<el-input class="margin_right_8" v-model="select_form.version"  placeholder="教材版本"></el-input>
						<el-input class="margin_right_8" v-model="select_form.volume"  placeholder="分册"></el-input>
						<!-- <el-select v-model="select_form.version" placeholder="教材"
						class="margin_right_8" @change="changeVersion">
							<el-option v-for="item in version_list" :key="item.id" :label="item.title"
								:value="item.id" />
						</el-select>
						<el-select v-model="select_form.volume" placeholder="分册"
						class="margin_right_8">
							<el-option v-for="item in volume_list" :key="item.id" :label="item.title"
								:value="item.title" />
						</el-select> -->
						<p class="text_14 grey1">教材</p>
					</div>
					</div>
					<div class="flex_between margin_bottom_8">
						<p class="text_14 grey1  margin_right_8" >我正在备</p>

					<div class="flex_start send-select-item flex_1">

						<el-input v-model="select_form.chapter" placeholder="输入课文/章节名称"
							style="margin-right: 6px"></el-input>
						<p class="text_14 grey1">这节课</p>
						<img src="@/assets/pc/bkzs/send-grey.png" alt="" class="send-img" v-show="!complete_msg" />
						<img src="@/assets/pc/bkzs/send.png" alt="" class="send-img" v-show="complete_msg" @click="sendChat" />
					</div>
					</div>
				</div>
			</div>
			<el-dialog v-model="dialogVisible" title="添加" width="460" :before-close="cancelAdd"
			:close-on-click-modal="false">
			<el-form label-position="top" label-width="auto" :model="addForm" >
				<el-form-item label="教案组成部分">
					<el-input v-model="addForm.title" placeholder="请输入" />
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="cancelAdd" class="button_120_48_cancel">取消</el-button>
					<el-button type="primary" @click="confirmAdd" class="button_120_48">
						确认
					</el-button>
				</div>
			</template>
		</el-dialog>
		</div>

</template>
<script setup>
	import { ref, onMounted, watch, defineEmits } from "vue";
	import { select_list } from "./util.js";
	import { useRoute } from "vue-router";
	import { ElMessage } from "element-plus";
	let dialogVisible = ref(false);
	const is_send = ref(true);
	const isText = ref(true);
	let content = ref("");
	const route = useRoute();
	let emits = defineEmits('sendMsg', 'backCommon')
	let tabList = ref([
  { title: "学情分析", canDel: false },
  { title: "教学分析", canDel: false },
  { title: "教学目标", canDel: false },
  { title: "教学重难点", canDel: false },
  { title: "教学准备", canDel: false },
  { title: "教学过程", canDel: false },
	]);
	let choicedList = ref([]);
	let addForm = ref({
		title: "",
	});
	let complete_msg = ref(false);
	let select_form = ref({
		stage: "",
		stage_title: "",
		grade: "",
		grade_title: "",
		subject: "",
		subject_title: "",
		version: "",
		version_title: "",
		volume: "",
		chapter: "",
	});
	watch(
		() => select_form.value,
		(newval) => {
			if (
				newval.stage &&
				newval.grade &&
				newval.subject &&
				newval.version &&
				newval.volume &&
				newval.chapter
			) {
				complete_msg.value = true;
			} else {
				complete_msg.value = false;
			}
		},
		{
			deep: true,
		}
	);
	let grade_list = ref([]);
	let subject_list = ref([]);
	let version_list = ref([]);
	var volume_list = ref([
		{ id: 1, title: "上册" },
		{ id: 2, title: "下册" },
	]);
	let sendChat = () => {
		let message = `我是一名${select_form.value.stage_title}${select_form.value.grade_title
			}${select_form.value.subject_title}教师,正在使用${select_form.value.version
			}${select_form.value.volume}教材,我正在备${select_form.value.chapter
			}这节课,${choicedList.value && choicedList.value.length > 0
				? "包含" + choicedList.value.join(",") + "这几个部分"
				: ""
			}`;
		emits('sendMsg', message)
		back()
	};
	let back = () => {
		choicedList.value = [];
		tabList.value = [
			{ title: "教学重难点", canDel: false },
			{ title: "教学准备", canDel: false },
			{ title: "教学过程", canDel: false },
		];
		select_form.value = {
			stage: "",
			stage_title: "",
			grade: "",
			grade_title: "",
			subject: "",
			subject_title: "",
			version: "",
			version_title: "",
			volume: "",
			chapter: "",
		};
		grade_list.value = [];
		subject_list.value = [];
		version_list.value = [];
		emits('backCommon')
	};
	let goTab = () => {
		dialogVisible.value = true;
	};
	let choiceTab = (row) => {
		if (choicedList.value.indexOf(row.title) < 0) {
			choicedList.value.push(row.title);
		} else {
			choicedList.value = choicedList.value.filter((item) => {
				return item != row.title;
			});
		}
	};
	const goDelChoiced = (row) => {
		tabList.value = tabList.value.filter(item => {
			return item.title != row.title
		})
		choicedList.value = choicedList.value.filter(item => {
			return item != row.title
		})
	}
	let confirmAdd = () => {
		if (addForm.value.title) {
			tabList.value.push({
				title: addForm.value.title,
				canDel: true,
			});
			choicedList.value.push(addForm.value.title);
			cancelAdd();
		} else {
			ElMessage.warning("请输入教案组成部分");
		}
	};
	let cancelAdd = () => {
		dialogVisible.value = false;
		addForm.value.title = "";
	};
	const changeStage = () => {
		select_form.value.stage_title =
			select_list[select_form.value.stage - 1].title;
		grade_list.value = select_list[select_form.value.stage - 1].grade;
	};
	const changeGrade = () => {
		console.log(select_form.value.grade - 1, "select_form.value.grade - 1");
		select_form.value.grade_title =
			select_list[select_form.value.stage - 1].grade[
				select_form.value.grade - 1
			].title;
		subject_list.value =
			select_list[select_form.value.stage - 1].grade[
				select_form.value.grade - 1
			].subject;
	};
	const changeSubject = () => {
		select_form.value.version_title =
			select_list[select_form.value.stage - 1].grade[
				select_form.value.grade - 1
			].subject[select_form.value.subject - 1].title;
		version_list.value =
			select_list[select_form.value.stage - 1].grade[
				select_form.value.grade - 1
			].subject[select_form.value.subject - 1].version;
	};
	const changeVersion = () => {
		select_form.value.version_title =
			select_list[select_form.value.stage - 1].grade[
				select_form.value.grade - 1
			].subject[select_form.value.subject - 1].version[
				select_form.value.version - 1
			].title;
	};
	onMounted(() => {

	});
</script>
<style lang="scss" scoped>
	.margin_bottom_8{margin-bottom: 8px;}
	.margin_right_8{margin-right: 8px;}
	::v-deep .el-input__inner{color: #2B2C33;}
	::v-deep .el-checkbox__label{color: #2B2C33;}
	::v-deep .el-select__selected-item{color: #2B2C33;}
	::v-deep .el-select__placeholder.is-transparent{color:rgb(168 171 178)}
	.send-img{
		width: 24px;cursor: pointer;margin-left: 24px;
	}
	/* :deep(.el-dialog) {
		width: 90%;
		max-width: 750px;
	} */

	:deep(.el-dialog .el-input__wrapper) {
		height: 48px;
		border-radius: 8px;
	}
	::v-deep .el-select__wrapper{border-radius: 8px;height: 40px;}
	::v-deep .el-input__wrapper{border-radius: 8px;height: 40px;}
	.button_120_48_cancel {
		min-width: 120px;
		height: 48px;
		font-weight: 400;
		font-size: 18px;
		color: #94959c;
		line-height: 26px;
		background: #f6f7fa;
		border-radius: 12px;
	}

	.button_120_48 {
		min-width: 120px;
		height: 48px;
		font-weight: 700;
		font-size: 18px;
		color: #fff;
		line-height: 26px;
		background: linear-gradient(90deg, #2668ff, #33affb);
		border-radius: 12px;
	}
.mobile_shdown{

	width: 10px;
					height: 24px;
					background: linear-gradient(
					  270deg,
					  rgba(255, 255, 255, 0) 0%,
					  rgb(229, 230, 233) 100%
					)
}
	.select-view {
		background: #FFFFFF;
border-radius: 12px 12px 12px 12px;
border: 1px solid #E5E7EB;
		width: 100%;
		padding: 16px;
		.back {
			width: 50px;
			height: auto;
			cursor: pointer;
		}

		.tips {
			font-size: 14px;
			color: #6d6f75;
		}

		.tabs {
			margin: 9px 0 12px 0;

			ul {
				width: calc(100vw - 78px);
				overflow-x: scroll;

				&::-webkit-scrollbar {
					height: 0;
				}
			}

			li {
				height: 35px;
				margin: 0 8px 0px 0;

				div {
					position: relative;
					white-space: nowrap;
					margin-bottom: 0;
					line-height: 16px;
					padding: 4px 8px;
					background: #fff;
					border-radius: 8px;
					font-weight: 400;
					font-size: 12px;
					color: #94959c;

					cursor: pointer;
					border: 1px solid #e2e3e6;

					&.active {
						background: #2668ff;
						color: #fff;
					}
				}

				.candel {
					position: absolute;
					top: 0;
					right: 0;
					transform: translate(50%, -50%);
					width: 12px;
					height: auto;
				}
			}

			.add-btn {
				background: #2668ff !important;
				border-radius: 8px;
				font-weight: 400;
				font-size: 12px;
				color: #94959c;
				display: inline-flex;
				justify-content: center;
				align-items: center;
				margin: 0 !important;
				padding: 7px 12px;
				cursor: pointer;
				line-height: normal !important;

				img {
					width: 14px;
					height: auto;
				}
			}
		}

		.card {
			background: #fff;
			/* padding: 16px; */
			border-radius: 12px 12px 12px 12px;
			.send-select-item>p {
				/* font-size: 12px;
				color: #000; */
				white-space: nowrap;
			}

			.send-select-item {
				/* margin-bottom: 8px;
				font-weight: 400; */
				/* font-size: 16px; */
				color: #2B2C33;

				span {
					margin-right: 8px;
				}
			}

			.send-select-item:last-child {
				margin-bottom: 0;
			}

			.send-select-m {
				font-size: 12px;
				width: 100%;

				span {
					line-height: 18px;
				}
			}

			.bottom-img {
				margin-top: 10px;

				.send-img {
					width: 32px;
					height: auto;
				}
			}
		}
	}
	.select-view-mobile{
			box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
			border: 0;
			border-radius: 12px 12px 0 0;
		}

	.send_view {
		background: #ffffff;
		box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
		min-height: 70px;
		padding: 12px 15px 22px;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		max-width: 750px;
		margin: 0 auto;
		width: 100%;

		.send_img {
			width: 24px;
			height: 24px;
			cursor: pointer;
		}

		.grey_bottom {
			background: #f9f9f9;
			border-radius: 8px;
			padding: 0 14px 0 0;
			position: relative;
			margin-right: 14px;
		}

		.picture_img {
			height: 20px;
			cursor: pointer;
			object-fit: cover;
		}

		::v-deep .el-input__wrapper {
			height: 40px;
			background: #f7f8fa;
			border-radius: 8px;
			border: 0;
			box-shadow: none;
			font-size: 15px;
			border-radius: 8px;
		}

		::v-deep .el-button--primary {
			width: 56px;
			height: 40px;
			background: #347aff;
			border-radius: 8px;
			font-size: 14px;
			color: #ffffff;
			margin-left: 10px;
		}

	}
</style>
<template>
    <div class="content-box msjt-detail">
        <!-- <p class="text_20_bold flex_start mr_40 pointer mt_16" @click="router.back()"><el-icon><ArrowLeftBold /></el-icon>直播详情</p>  -->
        <div v-if="!isDeleted" class="flex_between_stretch">
            <div class="video-img-box msjt-live-detail mt_16" v-if="!detail.open && !userInfo.user">
                <img class="live-bg mt_16" src="@/assets/pc/msjt/live-bg.png" alt="">
                <p class="video-text video-text-position">请先登录</p>
            </div>
            <div v-else class="msjt-live-detail mt_16">
                <p class="text_20_bold flex_start mr_40 pointer" style="display:inline-flex;" @click="router.back()"><el-icon><ArrowLeftBold /></el-icon>直播详情</p> 
                <!-- <video id="msjt_live" width="414" height="270" preload="auto" playsinline webkit-playsinline></video> -->
                <!-- 直播未开始 -->
                <div class="video-img-box" v-if="live_state == 0">
                    <img class="live-bg mt_16" src="@/assets/pc/msjt/live-bg.png" alt="">
                    <p class="video-text video-text-position">直播暂未开始</p>
                </div>
                <!-- 直播已结束 -->
                <div class="video-img-box" v-else-if="live_state == 2">
                    <!-- 正在生成回放中 -->
                    <div v-if="!detail.fileUrl">
                        <img class="live-bg mt_16" src="@/assets/pc/msjt/live-bg.png" alt="">
                        <div class="video-text-position">
                            <p class="video-text">直播已结束</p>
                            <p class="hf-text">回放生成中</p>
                        </div>
                    </div>
                    <!-- 回放生成完成 -->
                    <video v-else ref="videoRef" :src="detail.fileUrl" controls width="100%" height="500" autoplay loop muted :poster="detail.sealUrl" ></video>
                </div>
                <div v-else class="living-box">
                    <div id="msjt_live" class="mt_16" :key="live_state" style="width:100%; height:100%;min-height:500px!important;" controls="false"></div>
                    <div v-if="showTip" class="tip-box">
                        <p class="tip-text">{{tip}}</p>
                        <p class="tip-btn">直播即将开始</p>
                    </div>
                </div>
                <div class="flex_between mt_18">
                    <p class="text_20_bold">{{detail.title}}</p>
                    <div class="flex_start">
                        <p class="flex_start text_16 grey3 mr_40"><img class="person-icon" src="@/assets/pc/msjt/person.png" alt="">{{view}}人</p>
                        <el-button class="primary-btn btn_150_40 flex_start" type="primary" @click="copyClick"><img class="copy-icon" src="@/assets/pc/msjt/copy.png" alt="">复制分享链接</el-button>
                    </div>
                </div>
            </div>
            <Chat class="comment-box mt_16 " :live_state="live_state" :im_info="detail"></Chat>
        </div>
        <div v-else class="mt_16">
            <p class="text_20_bold flex_start mr_40 pointer" style="display:inline-flex;" @click="router.back()"><el-icon><ArrowLeftBold /></el-icon>直播详情</p> 
            <div class="video-delete-box content-box">
                <img class="live-bg mt_16" src="@/assets/pc/msjt/live-bg.png" alt="">
                <p class="video-text video-text-position">直播间已被删除</p>
            </div>
        </div>
        
    </div>
    
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted, onUnmounted, watch} from 'vue'
    import { formateLeftTime } from '@/utils/datetime.js'
    import { toRaw } from "@vue/reactivity"
    import Chat from './chat.vue'
    import { msjtDetail, msjtView } from '@/api/pc/msjt'
    import { useRouter, useRoute } from 'vue-router'
    import { ElMessage } from 'element-plus'
    import { storeToRefs } from 'pinia'
    import { useUserInfoStore } from "@/store/modules/pc"

    let useUserInfo = useUserInfoStore()
    let { loginVisibleShow, userInfo } = storeToRefs(useUserInfo)
    const router = useRouter()
    const route = useRoute()
    const comment_list = ref([])
    const detail = ref({})
    const live_state = ref(null) // 0：未开始  1：正在直播中 2：已结束
    const is_start = ref(false)
    const is_end = ref(false)
    const player = ref(null)
    const view = ref(0)
    const tip = ref('老师正在赶来的路上…')
    const showTip = ref(true)
    const isDeleted = ref(false)
    const time_left = reactive({
        day:'',
        hours:[],
        mins:[],
        seconds:[]
    })
    const left_timer = ref(null)
    const timer = ref(null)
    const view_timer = ref(null)
    const im_info = ref({})

    const getDetail = async()=> {
        let res = await msjtDetail(route.params.id)
        if(res){
            detail.value = res.data
            // detail.value.startTime = '2025-05-16 15:57:40'
            // detail.value.endTime = '2025-05-16 16:45:29'
            // var box=document.getElementsByClassName("vcp-player");
            // for (var i = 0; i < box.length; i++) {
            //     box[i].remove();
            // }
            console.log(userInfo.value.user,'userInfo.value.user')
            console.log(!userInfo.value.user && !res.data.open,'userInfo.value.user')
            if(!userInfo.value.user && !res.data.open){
                loginVisibleShow.value=true
                return
            }
            loginVisibleShow.value = false
            var startTime = new Date(detail.value.startTime.replace(/-/g, "/"));
            var endTime = new Date(detail.value.endTime.replace(/-/g, "/"));
            var nowTime = new Date();
            if (nowTime < startTime) {
                live_state.value = 0;
                is_start.value = false;
                is_end.value = false;
            } else if (nowTime > endTime) {
                live_state.value = 2;
                is_start.value = true; // 直播已结束，视为“开始过”
                is_end.value = true;
            } else {
                live_state.value = 1;
                is_start.value = true; // 直播中，触发拉流
                is_end.value = false;
            }
            //判断直播是否已经结束
            
            console.log(live_state.value,'live_state.value')
            var lefttime = parseInt((endTime.getTime() - nowTime.getTime()) / 1000);
            if (lefttime>0){
                mistiming();
            }else{
                is_start.value = true;
                is_end.value = true;
            }
        }
    }
    const timeLeft = () => {
        var startTime = new Date(detail.value.startTime.replace(/-/g, "/"));
        var nowTime = new Date();
        var lefttime = parseInt((startTime.getTime() - nowTime.getTime()) / 1000);
        if (lefttime > 0) {
            var time_lefts = formateLeftTime(lefttime);
            time_left['day'] = time_lefts.day;
            time_left['hours'] = String(time_lefts.hour).split('');
            time_left['mins'] = String(time_lefts.min).split('');
            time_left['seconds'] = String(time_lefts.second).split('');
        }else{//再次获取数据
            clearInterval(left_timer.value);
            is_start.value = true;
        }
    }
    // 获得距离活动开始还剩余的时间
    const mistiming = () => {
        timeLeft();
        left_timer.value = setInterval(function(){
            timeLeft();
        }, 1000)
    }
    /**
     * 视频类型播放优先级
     * mobile ：m3u8>mp4
     * PC ：RTMP>flv>m3u8>mp4
    */
    const getTcPlayerPc = () => {
        console.log('触发了播放')
        // try {
            player.value =  new TcPlayer('msjt_live', {
                "m3u8": detail.value.livePushList[0].streamList[0].hls,
                "flv": detail.value.livePushList[0].streamList[0].flv, 
                "rtmp":detail.value.livePushList[0].streamList[0].rtmp,
                "autoplay" :true,      //iOS 下 safari 浏览器，以及大部分移动端浏览器是不开放视频自动播放这个能力的
                "width" :  '100%',//视频的显示宽度，请尽量使用视频分辨率宽度
                "height" : 500,//视频的显示高度，请尽量使用视频分辨率高度
                'live': 'live',
                'poster':{"style": "cover", "src": detail.value.sealUrl},
                'clarity':'hd',
                'clarityLabel':{'od': '超清', 'hd': '高清'},
                'volume':'0.6',
                listener: function (msg) {
                    getTcPlayerResult(msg);
                }
            });
        // }catch(e){
        //     showTip.value = true;
        //     console.log(e,'e1erfergtrter')
        // }
        
    
    }
    const getTcPlayerResult = (msg) => {
        console.log('触发了getTcPlayerResult')
        try {
            if (msg.type=='loadedmetadata') {
                showTip.value = true;
                tip.value = '直播已开始';
            }
            if (msg.type=='load'&&player.value) {
                // showTip.value = false;
                var a = document.querySelectorAll('video');
                if (a[0]) {
                    a[0].setAttribute('x5-playsinline','');
                }
                console.log('初始化拉流');
            }else if (msg.type=='error') {
                showTip.value = true;
                switch(msg.detail.code){
                    case 1:
                    tip.value = "网络错误，请检查网络配置";
                    console.log("网络错误，请检查网络配置或者播放链接是否正确。");
                    break;
                    case 2:
                    tip.value = "老师正在赶来的路上…";
                    console.log("视频格式 Web 播放器无法解码。");
                    break;
                    case 3:
                    tip.value = "老师正在赶来的路上…";
                    console.log("视频解码错误。");
                    break;
                    case 4:
                    tip.value = "老师正在赶来的路上…";
                    console.log("当前系统环境不支持播放该视频格式。");
                    break;
                    case 5:
                    tip.value = "播放器判断当前浏览器环境不支持播放传入的视频";
                    console.log("播放器判断当前浏览器环境不支持播放传入的视频，可能是当前浏览器不支持 MSE 或者 Flash 插件未启用。");
                    break;
                    case 13:
                    tip.value = "直播已结束，请稍后再来。";
                    console.log("直播已结束，请稍后再来。");
                    break;
                    case 1001:
                    tip.value = "断网了";
                    console.log("断网了");
                    break;
                    case 1002:
                    tip.value = "老师正在赶来的路上…";
                    console.log("获取视频失败，请检查播放链接是否有效。");
                    break;
                    case 2048:
                    tip.value = "无法加载视频文件，跨域访问被拒绝。";
                    console.log("无法加载视频文件，跨域访问被拒绝。");
                    break;
                    default:
                    tip.value = "出错了";
                    console.log("出错了");
                    break;
                }

                var endTime=new Date(detail.value.endTime.replace(/-/g, "/"));
                var nowTime=new Date();
                var lefttime = parseInt((endTime.getTime() - nowTime.getTime())/1000);
                if (player.value && lefttime>0){
                    showTip.value = true;
                    timer.value = setTimeout(function(){
                        player.value && player.value.load();
                        // console.log('重试拉流');
                    }, 2000);
                }else if (player.value) {
                    clearTimeout(timer.value);
                    // getDetail();
                }
                
            }else if (msg.type == 'play') {
                console.log(msg, '拉流成功');
                // 清除拉流的定时器
                setTimeout(function(){
                    showTip.value = false;
                    if (document.getElementsByClassName('marker').length!=0) {
                        document.getElementsByClassName('marker')[0].style.display='none';
                    }
                },500)
                clearInterval(left_timer.value);
                
            }else if (msg.type == "pause") {
                tip.value = '直播已暂停';
                showTip.value = true
            }else if (msg.type == "play") {
                
            }else if (msg.type=='playing') {
                showTip.value = false;
                // if (document.getElementsByClassName('marker').length!=0) {
                //     document.getElementsByClassName('marker')[0].style.display='none';
                // }
            }
        } catch (error) {
            showTip.value = true;
        }
        
    }
    const getView = async()=> {
        let res = await msjtView(route.params.id)
        if(res){
            view.value = res.data.view
            isDeleted.value = res.data.isDeleted
        }
    }
    const copyClick = () => {
        console.log(location,'location')
        navigator.clipboard.writeText(location.href).then(() => {
            ElMessage.success('分享链接已复制');
        }).catch(err => {
            ElMessage.error('复制失败，请重试');
        });
    }
    watch(() => is_start.value, (newVal) => {
        console.log(newVal,'newVal')
        console.log(is_end.value,'is_end newVal')
        console.log(live_state.value,'live_state.value newVal')
        if (newVal&&!is_end.value) {
            live_state.value = 1;
            setTimeout(() => {
                getTcPlayerPc();
            },1000)
        }
        
    },{ deep: true })
    onMounted(() => {
        getDetail()
        getView()
        view_timer.value = setInterval(() => {
            getView()
        }, 3000)
    })
    onUnmounted(() => {
        clearInterval(left_timer.value)
        clearInterval(timer.value)
        clearInterval(view_timer.value)
        player.value && player.value.destroy()
    })
</script>
<style lang="scss" scoped>
.msjt-detail{
    .msjt-live-detail{background: #FFFFFF;border-radius: 8px;padding: 24px;width: calc(100% - 281px);}
    // height: calc(100vh - 150px); 
    .comment-box{width: 271px;border-radius: 8px;background: #FFFFFF;padding: 24px 16px;}
    .live-bg{width: 100%;height: calc(100% - 90px);}
    
    
    .copy-icon{width: 14px;margin-right: 8px;}
    .person-icon{width: 15px;height: 16px;margin-right: 8px;}
    .video-hf{width: 100%;}
    .video-img-box{position: relative;
        .video-text-position{position: absolute;top: 50%;left: 10%;transform: translateY(-50%);}
        .video-text{font-family: PingFangSC, PingFang SC; font-weight: 600; font-size: 40px; color: #224BC9; line-height: 56px; text-align: left;}
        .hf-text{margin-top: 32px;background: #508CFF;font-family: PingFangSC, PingFang SC;padding: 12px 50px; font-weight: 400; font-size: 20px; color: #FFFFFF; line-height: 24px; text-align: left; border-radius: 24px;}
    }
    .living-box{position: relative;
    .tip-box{background: rgba(255,255,255,0.7);position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);width: 100%;text-align: center;padding: 16px 0;
        .tip-text{font-weight: 600; font-size: 28px; color: #224BC9; line-height: 40px; text-align: center;}
        .tip-btn{background: #508CFF;display: inline-block;color: #fff;border-radius: 24px;padding: 12px 4ch;margin-top: 13px;font-weight: 400; font-size: 20px; color: #FFFFFF; line-height: 24px; text-align: left;}
    }
    }
}
.video-delete-box{position: relative;
    img{width: 100%;}
    .video-text-position{position: absolute;top: 50%;left: 10%;transform: translateY(-50%);}
    .video-text{font-family: PingFangSC, PingFang SC; font-weight: 600; font-size: 40px; color: #224BC9; line-height: 56px; text-align: left;}
}
</style>
<style lang="scss">
.tcplayer{width: 100%;}
.vcp-error-tips{display: none!important;}
.el-textarea.is-disabled .el-textarea__inner{box-shadow:none;}
</style>
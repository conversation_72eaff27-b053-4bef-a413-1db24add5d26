<template>
  <slot v-if="showImagePreview"></slot>
</template>

<script setup>
import { computed } from "vue";
import { hasPermission } from "@/directives/permission"

const props = defineProps({
  permission: {
    type: [Array, String],
    default: () => []
  }
})

const showImagePreview = computed(() => {
  const { permission } = props
  if (Array.isArray(permission)) {
    return permission.some((role) => hasPermission(role))
  } else {
    return hasPermission(permission)
  }
})
</script>

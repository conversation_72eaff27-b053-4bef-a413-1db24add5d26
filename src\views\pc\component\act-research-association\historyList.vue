<template>
     <el-dialog
        v-model="historyListVisible"
        title="通知公告"
        width="850"
        class="notice-dialog"
         :close-on-click-modal='false'
        :before-close="handleClose"
    >
     <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        style="margin-bottom:20px"
        value-format="YYYY-MM-DD"
        @change="init"
      />
     <el-empty description="暂无通知" style="height:250px;background:rgba(0,0,0,0)" v-if="tableData?.length==0"  :image="noticeEmpty" image-size='130'/>
     <ul v-if="tableData?.length>0">
        <li class="item" v-for="item in tableData" :key="item.noticeId" @click="goView(item)">
             <div class="line1 flex_start">
                <img src="@/assets/pc/loud.png" alt="">
                <p>{{item.noticeTitle}}</p>
                <div v-show="!item.isViewed">
                    新
                </div>
             </div>
             <div class="line2 flex_between">
                 <div class="flex_start left-part">
                     <p>{{item.userName}}</p>
                     <p>{{item.postTime}}</p>
                 </div>
                 <div class="right-part">
                      {{item.noticeRangeLabel}}
                 </div>
             </div>
        </li>
     </ul>
     <el-pagination v-model:current-page="pageNum" background v-show="total!=0"  style="margin-bottom:10px" layout="prev, pager, next" :page-size='pageSize' :total="total" @current-change='handlelCurrentChange' />
  </el-dialog>
</template>
<script setup>
import noticeEmpty from '@/assets/pc/notice-empty.png'
import {ref,defineProps, defineEmits,onMounted, watch,} from 'vue'
import {noticeHistory,noticeView} from '@/api/pc/index.js'
import { useUserInfoStore } from "@/store/modules/pc"
import {storeToRefs} from 'pinia'
let userStore=useUserInfoStore()
let {historyListVisible,historyItemVisible}=storeToRefs(userStore)
// let props=defineProps(['historyListVisible'])
let emits=defineEmits(['emitDetail'])
let pageNum=ref(1);
let pageSize=ref(10)
let total=ref(0)
let tableData=ref([])
let dateRange=ref([])
let handleClose=()=>{
   historyListVisible.value=false
   dateRange.value=[]
   init()
}
let goView=(row)=>{
    historyListVisible.value=false
    historyItemVisible.value=true
    emits('emitDetail',row,'list')
}
let handlelCurrentChange=(val)=>{
    pageNum.value=val
    getList()
}
let getList=async()=>{
    let res=await noticeHistory({
        pageNum:pageNum.value,
        pageSize:pageSize.value,
        beginCreateTime:(dateRange.value&&dateRange.value[0])?dateRange.value[0]:'',
        endCreateTime:(dateRange.value&&dateRange.value[1])?dateRange.value[1]:'',
    })
    if(res){
        tableData.value=res.data.list
        total.value=res.data.total
    }
}
let init=()=>{
  pageNum.value=1
  getList()
}
onMounted(()=>{
    getList()
})
watch(()=>historyListVisible.value,(newvalue)=>{
    if(newvalue){
        getList()
    }
})
</script>
<style lang='scss' scoped>
ul{
    height: 400px;
    overflow: auto;
 .item{
    padding: 0 16px;
    border-radius: 8px;
    background: #fff;
    margin-bottom: 10px;
    cursor: pointer;
    &:last-child{
        margin-bottom: 0;
    }
    .line1{
        padding: 12px 0;
        border-bottom: 1px solid #F1F2F5;
        img{
                width: 24px;
                height: auto;
                
            }
            p{
                font-size: 16px;
                color: #2B2C33;
                line-height: 22px;
                font-weight: bold;
                margin:0 8px 0 4px;
            }
            div{
                width: 16px;
                height: 16px;
                line-height: 16px;
                color: #fff;
                text-align: center;
                background: #EF6556;
                font-size: 12px;
            }
    }
    .line2{
        padding: 12px 0;
        .left-part{
            p{
                font-size: 14px;
                color: #6D6F75;
                margin-right: 16px;
            }
        }
        .right-part{
            font-size: 14px;
            line-height: 14px;
            color: #508CFF;
            background: #DFEAFF;
            border-radius:200px;
            padding: 6px 16px;
        }
    }
  }
}
 
</style>
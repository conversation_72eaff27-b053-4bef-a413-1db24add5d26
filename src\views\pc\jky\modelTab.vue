<template>
<div class="tab-box">
   <div class="model-tab flex_between">
        <div class="flex_1  left"></div>
        <div class="middle flex_between">
             <h3 class="logo">教科研管理</h3>
             <ul class="flex_center tab-list" >
                <li v-if="jky_role.userRole == 'TOP_ADMIN'" :class="['flex_center','flex_column',$route.meta.model==1?'active':'']"  @click="goModel(1)">
                    <img src="@/assets/pc/jky/lxpj-active.png" alt="" v-show="$route.meta.model==1">
                    <img src="@/assets/pc/jky/lxpj.png" alt="" v-show="$route.meta.model!=1">
                    <p :style="$route.meta.model==1?'color:#1E78E6':''">立项评鉴</p>
                </li>
                <li v-if="jky_role.userRole == 'BOTTOM_TEACHER'" :class="['flex_center','flex_column',$route.meta.model==2?'active':'']"  @click="goModel(2)">
                    <img src="@/assets/pc/jky/lxsq-active.png" alt="" v-show="$route.meta.model==2">
                    <img src="@/assets/pc/jky/publish-tab.png" alt="" v-show="$route.meta.model!=2">
                    <p :style="$route.meta.model==2?'color:#1E78E6':''">立项申请</p>
                </li>
                <li v-if="jky_role.userRole == 'BOTTOM_ADMIN'" :class="['flex_center','flex_column',$route.meta.model==4?'active':'']"  @click="goModel(4)">
                    <img src="@/assets/pc/jky/lxcs.png" alt=""  v-show="$route.meta.model!=4"> 
                     <img src="@/assets/pc/jky/lxcs-active.png" alt=""  v-show="$route.meta.model==4">
                    <p :style="$route.meta.model==4?'color:#1E78E6':''">立项初审</p>
                </li>
                <li v-if="jky_role.userRole == 'UNIT_ADMIN' || jky_role.userRole == 'UNIT_EXPERT'" :class="['flex_center','flex_column',$route.meta.model==5?'active':'']"  @click="goModel(5)">
                    <img src="@/assets/pc/jky/lxcp.png" alt=""  v-show="$route.meta.model!=5"> 
                     <img src="@/assets/pc/jky/lxcp-active.png" alt=""  v-show="$route.meta.model==5">
                    <p :style="$route.meta.model==5?'color:#1E78E6':''">立项初评</p>
                </li>
                <li v-if="jky_role.userRole == 'TOP_ADMIN'" :class="['flex_center','flex_column',$route.meta.model==3?'active':'']"  @click="goModel(3)">
                    <img src="@/assets/pc/jky/cgpj.png" alt=""  v-show="$route.meta.model!=3">
                     <img src="@/assets/pc/jky/cgpj-active.png" alt=""  v-show="$route.meta.model==3">
                    <p :style="$route.meta.model==3?'color:#1E78E6':''">成果评鉴</p>
                </li>
                <li v-if="jky_role.userRole == 'BOTTOM_TEACHER'" :class="['flex_center','flex_column',$route.meta.model==7?'active':'']"  @click="goModel(7)">
                    <img src="@/assets/pc/jky/cgsb.png" alt=""  v-show="$route.meta.model!=7">
                     <img src="@/assets/pc/jky/cgsb-active.png" alt=""  v-show="$route.meta.model==7">
                    <p :style="$route.meta.model==7?'color:#1E78E6':''">成果报送</p>
                </li>
                <li v-if="jky_role.userRole == 'UNIT_ADMIN' || jky_role.userRole == 'UNIT_EXPERT'" :class="['flex_center','flex_column',$route.meta.model==6?'active':'']"  @click="goModel(6)">
                    <img src="@/assets/pc/jky/cgcp.png" alt=""  v-show="$route.meta.model!=6"> 
                     <img src="@/assets/pc/jky/cgcp-active.png" alt=""  v-show="$route.meta.model==6">
                    <p :style="$route.meta.model==6?'color:#1E78E6':''">成果初评</p>
                </li>
             </ul>
            <div>
                <el-dropdown @command="handleCommand">
                <div class="flex_start">
                    <el-avatar :src="userInfo.user?.avatarUrl || DefaultAvatar" class="avatar head" :size="32"  style="margin-right:8px" />
                    <p>{{ userInfo && userInfo.user ? userInfo.user.name : '' }}</p>
                    <p class="role-flag" :class="jky_role.userRole == 'TOP_ADMIN' ? 'role-yellow' : jky_role.userRole == 'UNIT_ADMIN' ? 'role-orange' : jky_role.userRole == 'UNIT_EXPERT' ? 'role-purple' : jky_role.userRole == 'BOTTOM_ADMIN' ? 'role-green' : 'role-blue'"> {{ jky_role.userRoleName }}</p>
                </div>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item v-for="(item,index) in role_list" :command="item" :key="index" :class="[item.userRole == jky_role.userRole ? 'active-role':'']">{{item.userRoleName}}</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
            </div>
        </div>
        <div class="flex_1  right flex_start">
        </div>
    </div>
</div>
  
</template>
<script setup>
    import {ref, onMounted,watch} from 'vue'
    import { toRaw } from "@vue/reactivity"
    import DefaultAvatar from "@/assets/layouts/default-avatar.png"
    import {useRouter} from 'vue-router'
    import {storeToRefs} from 'pinia'
    import { useUserInfoStore } from "@/store/modules/pc"
    let useUserInfo=useUserInfoStore()
    let { userInfo, jky_role } = storeToRefs(useUserInfo)
    let router=useRouter()
    let toastShow=ref(false)
    let show_sch_export = ref(false)
    const role = ref([])
    const role_list = ref([])
    // 权限
    // 教师发展中心管理员：TOP_ADMIN
    // 单位管理员：UNIT_ADMIN
    // 单位专家：UNIT_EXPERT
    // 学校管理员：BOTTOM_ADMIN
    // 学校教师：BOTTOM_TEACHER
    let goModel=(type)=>{
        if(type==1){
            router.push('/jky/lxpj/index')
        }else if(type==2){
            router.push('/jky/lxsq/index')
        }else if(type==3){
            router.push('/jky/cgpj/index')
        }else if(type==4){
            router.push('/jky/lxcs/index')
        }else if(type==5){
            router.push('/jky/lxcp/index')
        }else if(type==6){
            router.push('/jky/cgcp/index')
        }else if(type==7){
            router.push('/jky/cgbs/index')
        }
    } 
    let goUpNotice=()=>{ 
        router.push('/actResearchAssociation/notice/upload')
    }
    const handleCommand = (role) => {
        useUserInfo.changeJkyRole(role)
    }
    watch(() => jky_role.value,
        (newVal) => {
            if(newVal.userRole == 'TOP_ADMIN'){
                router.push('/jky/lxpj/index')
            }else if(newVal.userRole == 'UNIT_ADMIN' || newVal.userRole == 'UNIT_EXPERT'){
                router.push('/jky/lxcp/index')
            }else if(newVal.userRole == 'BOTTOM_ADMIN'){
                router.push('/jky/lxcs/index')
            }else{
                router.push('/jky/lxsq/index')
            }
        }, {deep: true}
    )
    onMounted(() => {
        role_list.value = userInfo.value && userInfo.value.jiaoKeYan && userInfo.value.jiaoKeYan.unitUsers && userInfo.value.jiaoKeYan.unitUsers.length > 0 ? userInfo.value.jiaoKeYan.unitUsers : '';
    })
</script>
<style lang='scss' scoped>
.tab-box{
 background: #fff;
}
.model-tab{font-family: PingFangSC, PingFang SC;

    margin: 0 auto;
    .middle{
        width: 100%;
         max-width: 1040px;
       .logo{
        font-size: 22px;
        color: #2B2C33;
        font-weight: bold;
       }
       .tab-list{
        padding: 11px 0 0 0;
        min-width: 480px;
        li{
            font-size: 12px;
            cursor: pointer;
            color: #94959C;
            margin-right: 60px;
            // margin-right: 130px;
            &:last-child{
                margin-right: 0;
            }
            &::after{
                content: '';
                display: block;
                height: 4px;
                width: 100%;
                background: #fff;
                // background: linear-gradient( 270deg, #496EEE 0%, #0AC1FA 100%);
                margin-top: 6px;
            }
            img{
                width: 18px;
                height: auto;
                margin-bottom: 6px
            }
        }
        .active{
              &::after{
                content: '';
                display: block;
                height: 4px;
                width: 100%;
                // background: #fff;
                background: linear-gradient( 270deg, #496EEE 0%, #0AC1FA 100%);
                margin-top: 6px;
            }
        }
       }
    }
    .right{
        font-size: 16px;
       color: #2B2C33;
       font-weight: bold;
       position: relative;
       white-space:nowrap;
        img{
            width: 32px;
            height: auto;
            margin-right: 8px;
        }
      
        .up-notice{
            font-size: 14px;
            color: #FFFFFF;
            cursor: pointer;
            background: linear-gradient( 270deg, #508CFF 0%, #535DFF 100%);
             border-radius: 12px;
            padding: 0 10px;
             height: 24px;
             cursor: pointer;
             margin-right: 32px;
             white-space: nowrap;
             img{
                width: 14px;
                height: auto;
             }
        }
    }
}
.avatar{}
.role-flag{border-radius: 12px;font-weight: 400; font-size: 14px; line-height: 20px; text-align: left;padding: 2px 12px;cursor: pointer;margin-left: 8px;}
.role-yellow{background: #FFCA00; color: #FFFFFF;}
.role-orange{background: #FFA532; color: #FFFFFF;}
.role-purple{background: #C197FF; color: #FFFFFF;}
.role-green{background: #5EE1C1; color: #FFFFFF;}
.role-blue{background: #83BCFE; color: #FFFFFF;}
:deep(.active-role.el-dropdown-menu__item){background:#ecf5ff;color: #409eff;}
</style>
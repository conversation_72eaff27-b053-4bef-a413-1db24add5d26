import { request } from "@/utils/service"

// 发布通知
export function publishNoticeApi(data) {
  return request({
    url: "xzs/club-notice",
    method: "post",
    data
  })
}

// 通知详情
export function getNoticeDetailApi(id) {
  return request({
    url: `xzs/club-notice/${id}`,
    method: "get"
  })
}

// 浏览
export function viewNoticeApi(id) {
  return request({
    url: `xzs/club-notice/${id}/view`,
    method: "get"
  })
}

// 总社团通知
export function getClubNoticeListApi(data) {
  return request({
    url: "xzs/club-notice/me/club-notice",
    method: "get",
    params: data
  })
}

// 组通知
export function getGroupNoticeListApi(data) {
  return request({
    url: "xzs/club-notice/me/group-notice",
    method: "get",
    params: data
  })
}

// 通知未读数量
export function getUnreadMessageApi() {
  return request({
    url: "xzs/club-notice/me/not-viewed/number",
    method: "get"
  })
}

// 历史消息
export function getHistoryMessageApi(data) {
  return request({
    url: "xzs/club-notice/me/received-history-notice",
    method: "get",
    params: data
  })
}

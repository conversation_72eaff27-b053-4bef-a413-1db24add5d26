<script setup>
import { ref, watch, onMounted } from "vue"
import MileageDistributionChart from "./MileageDistributionChart.vue"

const props = defineProps({
  chartData: {
    type: Object,
    default: () => ({
      data: [],
      color: []
    })
  }
})
</script>

<template>
  <div class="mileage-chart-container">
    <MileageDistributionChart :chart-data="chartData" />
  </div>
</template>

<style scoped>
.mileage-chart-container {
  width: 100%;
  height: 100%;
}
</style> 
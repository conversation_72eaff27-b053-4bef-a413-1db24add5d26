 <template>
    <div class="cgbs-main">
        <div class="radius-box">
            <el-form inline :model="search_form" class="search_form flex_start">
                <el-form-item label="任务名称" style="margin-right: 16px;width: calc(33% - 16px);">
                    <el-input v-model="search_form.taskTitle" @input="changeUserName" class="input_40" clearable placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="任务状态" style="margin-right: 16px;width: calc(33% - 16px);">
                    <el-select class="select_40" placeholder="请选择" @change="changeExtend">
                        <el-option v-for="item in expert_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select> 
                </el-form-item>
                <el-form-item label="操作" class="hidden-label" style="width: 132px;margin-right: 0;" >
                    <div class="flex_end" style="width: 100%"> 
                        <el-button class="primary-btn btn_132_40" type="primary" @click="searchClick">查询</el-button>
                    </div>
                </el-form-item>
            </el-form>
        </div>
        <div class="radius-box">
            <div class="flex_between">
                <p class="text_20_bold">任务列表</p>
                <el-button class="primary-btn btn_132_40" type="primary" :icon="CirclePlusFilled" @click="add_visible = true, taskMasterId = 0">自选新建</el-button>
            </div>
            <el-table :data="task_list" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F9F9F9"}'>
                <el-table-column type="index" label="序号" width="80"  />
                <el-table-column prop="taskTitle" label="课题任务" show-overflow-tooltip></el-table-column> 
                <el-table-column prop="createTime" label="评鉴开启日期" />
                <el-table-column prop="deadline" label="报送截止日期" />
                <el-table-column prop="reviewUserNames" label="任务状态">
                    <template #default='scope'>
                        <div class="flex_start">
                            <img v-if="scope.row.state == 1" src="@/assets/pc/jky/state-blue.png" class="state-icon" alt="">
                            <img v-else-if="scope.row.state == 2" src="@/assets/pc/jky/state-orange.png" class="state-icon" alt="">
                            <img v-else-if="scope.row.state == 3" src="@/assets/pc/jky/state-green.png" class="state-icon" alt="">
                            <img v-else src="@/assets/pc/jky/state-grey.png" class="state-icon" alt="">
                            <span :class="scope.row.state == 1 ? 'blue' : scope.row.state == 2 ? 'orange' : scope.row.state == 3 ? 'green' : 'grey'">{{scope.row.state == 1 ? '报送中' : scope.row.state == 2 ? '待分配' : scope.row.state == 3 ? '评鉴中' : scope.row.state == 4 ?  '已结束' : '被驳回'}}</span>
                        </div>
                    </template>
                </el-table-column> 
                <el-table-column label="操作" width="200">
                    <template #default='scope'>
                        <div class="flex_start" style="height:100%">
                            <el-button type="primary" link style="font-size:16px;font-weight:400;" @click="editClick(scope.row)">修改</el-button>
                            <el-button type="primary" link style="font-size:16px;font-weight:400;" @click="goAdd(scope.row)">去报送</el-button>
                            <el-button type="primary" link style="font-size:16px;font-weight:400;" @click="goDetail(scope.row)">查看</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    background
                    layout="total,prev, pager, next"
                    :total="total"
                    class="mt-4"
                    :current-page='search_form.pageNum'
                    :page-size="search_form.pageSize"
                    @current-change='currentChange'
                />
        </div>
        <el-dialog v-model="add_visible" v-if="add_visible" title="新建任务" width="456px" center class="my-dialog">
            <Add :id="taskMasterId" @close="add_visible = false, init()"></Add>
        </el-dialog>
        <!-- <el-dialog v-model="examine_visible" v-if="examine_visible" title="查看结果" width="456px" center class="my-dialog">
            <Result></Result>
        </el-dialog> -->
        
    </div>
</template> 
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import { CirclePlusFilled } from '@element-plus/icons-vue'
    import {ElMessage,ElLoading,ElMessageBox} from 'element-plus'
    import { taskList, taskDel } from '@/api/pc/jky'
    import Add from './add.vue' 
    // import Result from './result.vue'
    // import Allocation from './allocation.vue'
    import { useRouter, useRoute } from 'vue-router'
    const router = useRouter()
    const route = useRoute()
    const taskMasterId = ref(0)

    const formObj = function(obj = {}){
        this.taskTitle = obj.taskTitle || ''
        this.reviewUserName = obj.reviewUserName || ''
        this.name = obj.name || ''
        this.pageNum = obj.pageNum || 1
        this.pageSize = obj.pageSize || 10
    }
    const search_form = ref(new formObj({}))
    const total = ref(0)
    const task_list = ref([
        {state:1,taskTitle:'xxx课题研究',createTime:'2025-02-23',deadline:'2025-04-09'},
        {state:2,taskTitle:'xxx课题研究',createTime:'2025-02-23',deadline:'2025-04-09'},
        {state:3,taskTitle:'xxx课题研究',createTime:'2025-02-23',deadline:'2025-04-09'},
        {state:4,taskTitle:'xxx课题研究',createTime:'2025-02-23',deadline:'2025-04-09'},
        {state:1,taskTitle:'xxx课题研究',createTime:'2025-02-23',deadline:'2025-04-09'},
    ])
    const add_visible = ref(false)
    const examine_visible = ref(false)
    const allocation_visible = ref(false)

    const getTaskList = async () => {
        let params = search_form.value
        console.log(params,'paramsss')
        let res = await taskList(params)
        if(res){
            // task_list.value = res.data.list
            // total.value = res.data.total
        }
    }

    const searchClick = () => {
        init()
    }

    const init = () => {
        search_form.value.pageNum = 1
        getTaskList()
    }

    const goAdd = () => {
        router.push(`/jky/cgbs/resultadd/0`)
    }

    const goDetail = (row) => {
        router.push(`/jky/lxpj/detail/${row.taskMasterId}`)
    }
    const deleteClick = (row) => {
        ElMessageBox.confirm(
            '确认删除该条任务吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        ).then(async () => {
            let res = await taskDel(row.taskMasterId)
            if(res){
                ElMessage.success('删除成功')
                init()
            }
        })
    }
    const currentChange = (page) => {
        search_form.value.pageNum = page
        getTaskList()
    }
    const editClick = (row) => {
        add_visible.value = true
        taskMasterId.value = row.taskMasterId
    }
    onMounted(() => {
        getTaskList()
    })
</script>
<style lang="scss" scoped>
.cgbs-main{max-width: 1128px;margin:0 auto;font-family: PingFangSC, PingFang SC;
    .radius-box{border-radius: 12px;background: #fff;padding: 24px 24px 6px;margin: 10px 0;}
    .state-icon{width: 14px;margin-right: 5px;}
}
</style> 
<style lang="scss">
    .el-table th.el-table__cell.is-leaf{border: none!important;}
</style>
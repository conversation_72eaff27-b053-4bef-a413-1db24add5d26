<template>
    <div  class="content-box  question-set mt10" >
        <div class="flex_between">
            <div class="flex_start title" @click="goCancel">
                <el-icon style="margin-right: 4px;"><ArrowLeft /></el-icon>
                返回
            </div>
            <div class="ipt-box flex_start">
                    <input type="text" class="flex_1" placeholder="搜索题集名称" v-model="searchForm.keyword"  @keyup.enter="goSearch"/>
                <el-icon @click="goSearch"><Search /></el-icon>
            </div>
            <div></div>
        </div>
      
       <div class="flex_start date-search">
    
          <img src="@/assets/pc/date.png" alt="" />
            <el-date-picker
             
                v-model="searchForm.beginCreateTime"
                type="datetime"
                placeholder="开始日期"
                value-format="YYYY-MM-DD HH:mm:ss"
            />
            <p>-</p>
             <el-date-picker
              
                v-model="searchForm.endCreateTime"
                type="datetime"
                placeholder="开始日期"
                value-format="YYYY-MM-DD HH:mm:ss"
            />
       </div>
       <!-- 11 -->
        <el-table :data="tableData" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F9F9F9"}'>
            <el-table-column prop="name" label="题集名称"  />
            <el-table-column prop="questionNumber" label="题目数量"  />
            <el-table-column prop="createTime" label="创建时间" />
            <el-table-column prop="address" label="操作">
                <template #default='scope'>
                    <el-button type="primary" link style="font-size:16px" @click="goDownload(scope.row)">下载</el-button>
                    <el-button type="primary" link style="font-size:16px" @click="goEdit(scope.row)">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="total,prev, pager, next"
            :total="total"
            class="mt-4"
            :current-page='searchForm.pageNum'
            @current-change='currentChange'
        />
    </div>
</template>

<script setup>
import {ref,reactive, onMounted, watch} from 'vue'
import {questionSetList}  from '@/api/pc/index.js'
import {useRouter} from 'vue-router'
let tableData=ref([])
let router=useRouter()
let searchForm=reactive({
    keyword:'',
    beginCreateTime:'',
    endCreateTime:'',
    pageSize:10,
    pageNum:1
})
let total=ref(0)
let currentChange=(val)=>{
    Object.assign(searchForm,{
        pageNum:val
    })
    getList()
}
watch(()=>searchForm.beginCreateTime,()=>{
    goSearch()
})
watch(()=>searchForm.endCreateTime,()=>{
    goSearch()
})
let goSearch=()=>{
  Object.assign(searchForm,{
        pageNum:1
    })
    getList()
}
let goDownload=(row)=>{
      router.push('/resource/question/preview?id='+row.id)
}
let goEdit=(row)=>{
    router.push('/resource/question/set?id='+row.id)
}
let goCancel=()=>{
    router.go(-1)
}
let getList=async()=>{

    
//     console.log(searchForm.beginCreateTime.replace (" ", "%20"))
//   Object.assign(searchForm,{
//      beginCreateTime:searchForm.beginCreateTime.replace (" ", "%20"),
//      endCreateTime:searchForm.beginCreateTime.replace (" ", "%20"),
//   })
//   console.log()
  let res=await questionSetList(searchForm) 
  if(res){
     tableData.value=res.data.list
     total.value=res.data.total
  }
}
onMounted(()=>{
    getList()
})
</script>
<style lang='scss' scoped>
.question-set{
    padding: 16px 24px;
    background: #FFFFFF;
    border-radius: 12px;
        .title{
        font-size: 20px;
        color: #2B2C33;
        font-weight: bold;
        cursor: pointer;
    }
    .ipt-box{
        cursor: pointer;
        width: 50%;
        border-radius: 24px;
        border: 1px solid #E2E3E6;
        margin: auto;
        height: 48px;
        padding: 0 16px;
        input{
            border: none;
        }
    }
    .date-search{
        margin-top: 24px;
        img{
            width: 18px;
            height: auto;
            margin-right: 8px;
        }
        p{
            margin: 0 4px;
            color: #94959C;
        }
    }
    :deep(.el-input__wrapper),:deep(.el-date-editor.el-input){
        height: 40px;
    }
   
}
</style>
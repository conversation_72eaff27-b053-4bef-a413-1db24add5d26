import { getJsSignatureApi, getJsAgentSignatureApi } from "@/api/wexin"
import { ref } from "vue"

const agentJsSdk = ref({})
const JsSdk = ref({}) 

export const getJsSdk = async () => {
  try {
    const res = await getJsSignatureApi({ url: location.href?.split("#")[0] })
    console.log("getJsSdk--res", res)

    if (res) {
      JsSdk.value = res.data
      config()
    }
  } catch (err) {
    console.log("getJsSdk -- err", err)
    return
  }
}

export const config = () => {
  console.log("config", JsSdk.value)
  window.wx.config({
    beta: true,
    debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来
    appId: JsSdk.value.appId, // 必填，公众号的唯一标识
    timestamp: JsSdk.value.timestamp, // 必填，生成签名的时间戳
    nonceStr: JsSdk.value.nonceStr, // 必填，生成签名的随机串
    signature: JsSdk.value.signature, // 必填，签名，见附录1
    jsApiList: [""] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
  })
  window.wx.ready(function () {
    //如果要使用到agent_config相关接口 初始化agentConfig配置
    getAgentJsSdk()
  })
  window.wx.error(function (res) {
    console.log("config error", res)
    console.warn("Config出错啦", res.errMsg)
  })
}

export const getAgentJsSdk = async () => {
  try {
    const res = await getJsAgentSignatureApi({ url: location.href?.split("#")[0] })
    if (res) {
      agentJsSdk.value = res.data
      agentConfig()
    }
  } catch (err) {
    console.log("getAgentJsSdk -- err", err)
    return
  }
}
export const agentConfig = () => {
  window.wx.agentConfig({
    corpid: agentJsSdk.value.appId, // 必填，企业微信的corpid，必须与当前登录的企业一致
    agentid: agentJsSdk.value.agentId, // 必填，企业微信的应用id （e.g. 1000247）
    timestamp: agentJsSdk.value.timestamp, // 必填，生成签名的时间戳
    nonceStr: agentJsSdk.value.nonceStr, // 必填，生成签名的随机串
    signature: agentJsSdk.value.signature, // 必填，签名，见附录1
    jsApiList: [""], //必填
    success: function (res) {
      // 回调
      window.WWOpenData.bindAll(document.querySelectorAll("ww-open-data"))
      console.info("agentConfig", res)
    },
    fail: function (res) {
      console.log("agentConfig出错啦", res)
      if (res.errMsg.indexOf("function not exist") > -1) {
        alert("版本过低请升级")
      }
    }
  })
}

export const injectConfig = async () => {
  if (/MicroMessenger/i.test(navigator.userAgent)) {
    await getJsSdk()
  } else {
    await getAgentJsSdk()
  }
}

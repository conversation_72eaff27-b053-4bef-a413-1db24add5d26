<script setup>
import { AppMain, NavigationBar } from "./components"
</script>

<template>
  <div class="app-wrapper">
    <!-- 头部导航栏和标签栏 -->
    <div class="fixed-header layout-header">
      <div class="content">
        <NavigationBar class="navigation-bar" />
      </div>
    </div>
    <!-- 主容器 -->
    <div class="main-container">
      <!-- 页面主体内容 -->
      <AppMain class="app-main" :style="$route.path=='/index'?'padding-top:64px':''" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "@/styles/mixins.scss";
$transition-time: 0.35s;

.app-wrapper {
  @extend %clearfix;
  width: 100%;
}

.fixed-header {
  position: fixed;
  top: 0;
  z-index: 1002;
  width: 100%;
  .logo {
    width: var(--v3-sidebar-width);
  }
  .content {
    display: flex;
    .navigation-bar {
      flex: 1;
    }
  }
}

.layout-header {
  background-color: var(--v3-header-bg-color);
  /* box-shadow: var(--v3-header-box-shadow);
  border-bottom: var(--v3-header-border-bottom); */
}

.main-container {
  min-height: 100%;
}

.app-main {
  transition: padding-left $transition-time;
  padding-top: 120px;
  height: 100vh;
  overflow: auto;
}

.hasTagsView {
  .app-main {
    padding-top: var(--v3-header-height);
  }
}
</style>

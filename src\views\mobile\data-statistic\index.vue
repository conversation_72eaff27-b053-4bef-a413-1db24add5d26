<script setup>
import { ref, onMounted, computed } from "vue"
import <PERSON><PERSON><PERSON> from "./components/PieChart.vue"
import GroupBarChart from "./components/GroupBarChart.vue"
import PersonalBarChart from "./components/PersonalBarChart.vue"
import { useUserStore } from "@/store/modules/user"
import { getCountByAgeApi, getCountByGroupApi, getCountByStageApi, getCountBySubjectApi, getCountByUnitApi, getCountByGroupDiaryApi, getCountByPersonalDiaryApi, exportByAgeApi, exportByGroupApi, exportByStageApi, exportBySubjectApi, exportByUnitApi, exportByGroupDiaryApi, exportByPersonalDiaryApi, getWeekGroupRankApi, getMonthGroupRankApi, getHistoryGroupRankApi, getWeekClubRankApi, getMonthClubRankApi, getHistoryClubRank<PERSON><PERSON>, getWeekGroupMileageRank<PERSON><PERSON>, getMonthGroupMileageRankA<PERSON>, getHistoryGroupMileageRankApi, getWeekHonorRankApi, getMonthHonorRankApi, getHistoryHonorRankApi, getDirectorWeekClubMileageDistributionApi, getDirectorMonthClubMileageDistributionApi, getDirectorHistoryClubMileageDistributionApi, getDirectorThirtyDaysTotalMileageTrendApi, getTutorWeekGroupMileageDistributionApi, getTutorMonthGroupMileageDistributionApi, getTutorHistoryGroupMileageDistributionApi, getTutorThirtyDaysTotalMileageTrendApi } from "@/api/mobile/dataStatistic"
import { getTempTicketApi } from "@/api/mobile/common"
import { downloadExcelFile } from "@/utils"
import { CaretBottom } from "@element-plus/icons-vue"
import GroupMileageBarChart from "./components/GroupMileageBarChart.vue"
import HonorBarChart from "./components/HonorBarChart.vue"
import MileageTrend from "./components/MileageTrend.vue"
import GroupMileageRank from "./components/GroupMileageRank.vue"
import MileageDistributionChart from "./components/MileageDistributionChart.vue"

const userStore = useUserStore()

const isTutor = computed(() => {
  return userStore.roles.includes('TUTOR')
})
const isLeader = computed(() => {
  return userStore.roles.includes('LEADER')
})
const isViceDirector = computed(() => {
  return userStore.roles.includes('VICE_DIRECTOR')
})
const isDirector = computed(() => {
  return userStore.roles.includes('DIRECTOR')
})

// 获取临时票据
const tempTicket = ref('')
const getTempTicket = async () => {
  const res = await getTempTicketApi()
  if (res) {
    tempTicket.value = res.data
  }
}

// 升/降序处理
const handleRise = (data, rise) => {
  if (rise) {
    return data.sort((a, b) => a.value - b.value)
  } else {
    return data.sort((a, b) => b.value - a.value)
  }
}

const handleRiseOrDown = (type) => {
  if (type === 'age') {
    ageRise.value = !ageRise.value
    ageChartData.value.data = handleRise(ageChartData.value.data, ageRise.value)
  } else if (type === 'group') {
    groupRise.value = !groupRise.value
    groupChartData.value.data = handleRise(groupChartData.value.data, groupRise.value)
  } else if (type === 'stage') {
    stageRise.value = !stageRise.value
    stageChartData.value.data = handleRise(stageChartData.value.data, stageRise.value)
  } else if (type === 'subject') {
    subjectPersonRise.value = !subjectPersonRise.value
    subjectPersonData.value = handleRise(subjectPersonData.value, subjectPersonRise.value)
  } else if (type === 'unit') {
    unitPersonRise.value = !unitPersonRise.value
    unitPersonData.value = handleRise(unitPersonData.value, unitPersonRise.value)
  } else if (type === 'groupDiary') {
    groupDiaryRise.value = !groupDiaryRise.value
    groupDiaryChartData.value.data = handleRise(groupDiaryChartData.value.data, groupDiaryRise.value)
  } else if (type === 'personalDiary') {
    personalDiaryRise.value = !personalDiaryRise.value
    personalDiaryChartData.value.data = handleRise(personalDiaryChartData.value.data, personalDiaryRise.value)
  }
}

const activeTab = ref('3')
const handleClickTab = (tab) => {
  activeTab.value = tab.name
  if (activeTab.value === '1') {
    getAgeChartData()
    getGroupChartData()
    getStageChartData()
    getSubjectPerson()
    getUnitPerson()
  } else if (activeTab.value === '2') {
    getGroupDiaryChartData()
    getPersonalDiaryChartData()
  } else if (activeTab.value === '3') {
    getMemberRankData()
    getGroupRankData()
    getHonorData()
    getMileageData()
    getMileageTrendData()
    getGroupMileageData()
  }
}

const ageChartData = ref({
  data: [],
  color: ['#3B78FF', '#7DAEFF', '#C7E4FF', '#A2EFFF']
})
const ageRise = ref(false)
const getAgeChartData = async () => {
  try {
    const res = await getCountByAgeApi()
    if (res) {
      ageChartData.value.data = res.data.map(item => ({
        value: item.numberOfUser,
        name: item.ageRange
      }))
      ageChartData.value.data = handleRise(ageChartData.value.data, ageRise.value)
    }
  } catch (error) {
    console.log(error)
  }
}

const groupChartData = ref({
  data: [],
  color: ['#FF883F', '#FFAB39', '#FFD837', '#C2E227']
})
const groupRise = ref(false)
const getGroupChartData = async () => {
  try {
    const res = await getCountByGroupApi()
    if (res) {
      groupChartData.value.data = res.data.map(item => ({
        value: item.numberOfUser,
        name: item.groupName
      }))
      groupChartData.value.data = handleRise(groupChartData.value.data, groupRise.value)
    }
  } catch (error) {
    console.log(error)
  }
}

const stageChartData = ref({
  data: [],
  color: ['#FF72B6', '#FF7985', '#FFB6B6', '#FFC7E2']
})
const stageRise = ref(false)
const getStageChartData = async () => {
  try {
    const res = await getCountByStageApi()
    if (res) {
      stageChartData.value.data = res.data.map(item => ({
        value: item.numberOfUser,
        name: item.stageName
      }))
      stageChartData.value.data = handleRise(stageChartData.value.data, stageRise.value)
    }
  } catch (error) {
    console.log(error)
  }
}

const subjectPersonData = ref([])
const subjectPersonRise = ref(false)
const getSubjectPerson = async () => {
  try {
    const res = await getCountBySubjectApi()
    if (res) {
      subjectPersonData.value = res.data.map(item => ({
        value: item.numberOfUser,
        name: item.subjectName || ''
      }))
      subjectPersonData.value = handleRise(subjectPersonData.value, subjectPersonRise.value)

      const maxValue = Math.max(...subjectPersonData.value.map(item => item.value))
      subjectPersonData.value.forEach(item => {
        const percentage = item.value / maxValue * 100
        item.percentage = Number(percentage.toFixed(0))
      })
    }
  } catch (error) {
    console.log(error)
  }
}
const formatPercentage = (percentage, value) => {
  return `${value}`
}

const unitPersonData = ref([])
const unitPersonRise = ref(false)

const getUnitPerson = async () => {
  try {
    const res = await getCountByUnitApi()
    if (res) {
      unitPersonData.value = res.data.map(item => ({
        value: item.numberOfUser,
        name: item.tenantName || ''
      }))
      unitPersonData.value = handleRise(unitPersonData.value, unitPersonRise.value)

      const maxValue = Math.max(...unitPersonData.value.map(item => item.value))
      unitPersonData.value.forEach(item => {
        const percentage = item.value / maxValue * 100
        item.percentage = Number(percentage.toFixed(0))
      })
    }
  } catch (error) {
    console.log(error)
  }
}

const groupDiaryChartData = ref({
  data: [],
  color: ['#FF8940', '#FFAC38', '#FFD738', '#C4E326']
})
const groupDiaryRise = ref(false)
const getGroupDiaryChartData = async () => {
  try {
    const res = await getCountByGroupDiaryApi()
    if (res) {
      groupDiaryChartData.value.data = res.data.map(item => ({
        value: item.numberOfRecord,
        name: item.groupName
      }))
      groupDiaryChartData.value.data = handleRise(groupDiaryChartData.value.data, groupDiaryRise.value)
    }
  } catch (error) {
    console.log(error)
  }
}

const personalDiaryChartData = ref({
  data: [],
  color: ['#FF8940', '#FFAC38', '#FFD738', '#C4E326']
})
const personalDiaryRise = ref(false)
const getPersonalDiaryChartData = async () => {
  try {
    const res = await getCountByPersonalDiaryApi()
    if (res) {
      personalDiaryChartData.value.data = res.data.map(item => ({
        teacherName: item.userName,
        diaryCount: item.numberOfRecord,
        sixStarCount: item.numberOfFullScoreRecord,
        recommendCount: item.numberOfRecommendedRecord,
        groupName: item.groupName,
        value: item.numberOfRecord
      }))
      personalDiaryChartData.value.data = handleRise(personalDiaryChartData.value.data, personalDiaryRise.value)
    }
  } catch (error) {
    console.log(error)
  }
}

const exportFile = async (type) => {
  try {
    let res, fileName
    if (type === 'age') {
      // res = await exportByAgeApi()
      fileName = '年龄情况统计'
      await getTempTicket()
      window.open(`${import.meta.env.VITE_BASE_URL}/xzs/dashboard/statistics/number-of-user-group-by-age-range/file?secret-ticket=${tempTicket.value}`)
      return
    } else if (type === 'group') {
      res = await exportByGroupApi()
      fileName = '小组人员情况统计'
    } else if (type === 'stage') {
      res = await exportByStageApi()
      fileName = '任教学段人员情况统计'
    } else if (type === 'subject') {
      res = await exportBySubjectApi()
      fileName = '学科人员情况统计'
    } else if (type === 'unit') {
      res = await exportByUnitApi()
      fileName = '单位人员情况统计'
    } else if (type === 'groupDiary') {
      res = await exportByGroupDiaryApi()
      fileName = '日记情况统计'
    } else if (type === 'personalDiary') {
      res = await exportByPersonalDiaryApi()
      fileName = '个人日记情况统计'
    }
    if (res) {
      downloadExcelFile(res, fileName)
    }
  } catch (error) {
    console.log(error)
  }
}

// 里程情况统计
const timeRangeOptions = ref([
  { text: '本周', value: '1' },
  { text: '本月', value: '2' },
  { text: '历史', value: '3' },
])

// 社员里程排名
const showMemberTimeRangePicker = ref(false)
const memberTimeRange = ref(['1'])
const memberTimeRangeText = ref('本周')
const handleMemberTimeRangeConfirm = ({ selectedOptions }) => { 
  memberTimeRange.value = selectedOptions[0].value
  memberTimeRangeText.value = selectedOptions[0].text
  showMemberTimeRangePicker.value = false
  getMemberRankData()
}

const memberRankData = ref([])
const getMemberRankData = async () => {
  try {
    let res
    if (isDirector.value || isViceDirector.value) {
      if (memberTimeRange.value?.[0] === '1') {
        res = await getWeekClubRankApi()
      } else if (memberTimeRange.value?.[0] === '2') {
        res = await getMonthClubRankApi()
      } else if (memberTimeRange.value?.[0] === '3') {
        res = await getHistoryClubRankApi()
      }
    } else if (isTutor.value || isLeader.value) {
      if (memberTimeRange.value?.[0] === '1') {
        res = await getWeekGroupMileageRankApi()
      } else if (memberTimeRange.value?.[0] === '2') {
        res = await getMonthGroupMileageRankApi()
      } else if (memberTimeRange.value?.[0] === '3') {
        res = await getHistoryGroupMileageRankApi()
      }
    }

    if (res) {
      memberRankData.value = res.data
    }

    const maxScore = memberRankData.value[0].points
    memberRankData.value.forEach(item => {
      item.percentage = item.points ? Math.round(item.points / maxScore * 100) : 0
    })
  } catch (error) {
    console.log(error)
  }
}

// 小组里程排名
const showGroupTimeRangePicker = ref(false)
const groupTimeRange = ref(['1'])
const groupTimeRangeText = ref('本周')
const handleGroupTimeRangeConfirm = ({ selectedOptions }) => { 
  groupTimeRange.value = selectedOptions[0].value
  groupTimeRangeText.value = selectedOptions[0].text
  showGroupTimeRangePicker.value = false
  getGroupRankData()
}

const groupRankData = ref({
  data: [],
  color: ['#FF8940', '#FFAC38', '#FFD738', '#C4E326']
})
const getGroupRankData = async () => {
  try {
    let res
    if (groupTimeRange.value?.[0] === '1') {
      res = await getWeekGroupRankApi()
    } else if (groupTimeRange.value?.[0] === '2') {
      res = await getMonthGroupRankApi()
    } else if (groupTimeRange.value?.[0] === '3') {
      res = await getHistoryGroupRankApi()
    }
    if (res) {
      groupRankData.value.data = res.data
      
      console.log('小组里程排名', groupRankData.value)
    }
  } catch (error) {
    console.log(error)
  }
} 

// 荣誉统计
const showHonorTimeRangePicker = ref(false)
const honorTimeRange = ref(['1'])
const honorTimeRangeText = ref('本周')
const handleHonorTimeRangeConfirm = ({ selectedOptions }) => { 
  honorTimeRange.value = selectedOptions[0].value
  honorTimeRangeText.value = selectedOptions[0].text
  showHonorTimeRangePicker.value = false
  getHonorData()
}
const honorData = ref({
  data: [],
  color: ['linear-gradient( 180deg, #FFDD75 0%, #FF9C48 100%', 'linear-gradient( 180deg, #88C0FF 0%, #508CFF 100%);']
})
const getHonorData = async () => {
  try {
    let res
    if (honorTimeRange.value?.[0] === '1') {
      res = await getWeekHonorRankApi()
    } else if (honorTimeRange.value?.[0] === '2') {
      res = await getMonthHonorRankApi()
    } else if (honorTimeRange.value?.[0] === '3') {
      res = await getHistoryHonorRankApi()
    }
    if (res) {
      honorData.value.data = res.data
      console.log('荣誉统计', honorData.value)
    }
  } catch (error) {
    console.log(error)
  }
}

// 里程分布
const showMileageTimeRangePicker = ref(false)
const mileageTimeRange = ref(['1'])
const mileageTimeRangeText = ref('本周')
const handleMileageTimeRangeConfirm = ({ selectedOptions }) => { 
  mileageTimeRange.value = selectedOptions[0].value
  mileageTimeRangeText.value = selectedOptions[0].text
  showMileageTimeRangePicker.value = false
  getMileageData()
}

const mileageData = ref({
  data: [],
  color: ['#EB58B8', '#FD9623', '#35C0D8', '#78C613', '#6C86FB'],
  pieColor: ['linear-gradient( 288deg, #F45487 0%, #DD5DFF 100%);', 'linear-gradient( 180deg, #FFA952 0%, #FFE700 100%);', 'linear-gradient( 277deg, #72DCF8 0%, #5AE0C7 100%);', 'linear-gradient( 207deg, #ECFF4A 0%, #A9F457 100%);', 'linear-gradient( 203deg, #5674F5 0%, #7C92FF 100%);']
})
const getMileageData = async () => {
  try {
    let res
    if (mileageTimeRange.value?.[0] === '1') {
      res = await getDirectorWeekClubMileageDistributionApi()
    } else if (mileageTimeRange.value?.[0] === '2') {
      res = await getDirectorMonthClubMileageDistributionApi()
    } else if (mileageTimeRange.value?.[0] === '3') {
      res = await getDirectorHistoryClubMileageDistributionApi()
    }
    if (res) {
      mileageData.value.data = res.data
      console.log('里程分布', mileageData.value)
    }
  } catch (error) {
    console.log(error)
  }
}

// 小组里程排名 （导师）
const showGroupMileageTimeRangePicker = ref(false)
const groupMileageTimeRange = ref(['1'])
const groupMileageTimeRangeText = ref('本周')
const handleGroupMileageTimeRangeConfirm = ({ selectedOptions }) => { 
  groupMileageTimeRange.value = selectedOptions[0].value
  groupMileageTimeRangeText.value = selectedOptions[0].text
  showGroupMileageTimeRangePicker.value = false
  getGroupMileageData()
}

const groupMileageData = ref({
  data: [],
  color: []
})
const getGroupMileageData = async () => {
  try {
    let res
    if (groupMileageTimeRange.value?.[0] === '1') {
      res = await getTutorWeekGroupMileageDistributionApi()
    } else if (groupMileageTimeRange.value?.[0] === '2') {
      res = await getTutorMonthGroupMileageDistributionApi()
    } else if (groupMileageTimeRange.value?.[0] === '3') {
      res = await getTutorHistoryGroupMileageDistributionApi()
    }

    if (res) {
      groupMileageData.value.data = res.data.map(item => {
        return {
          ...item,
          value: item.points,
          name: item.eventGroupLabel
        }
      })
    }
  } catch (error) {
    console.log(error)
  }
}

// 近20日总里程趋势
const mileageTrendData = ref({
  data: [],
  color: []
})
const getMileageTrendData = async () => {
  try {
    let res
    if (isDirector.value || isViceDirector.value) {
      res = await getDirectorThirtyDaysTotalMileageTrendApi()
    } else if (isTutor.value || isLeader.value) {
      res = await getTutorThirtyDaysTotalMileageTrendApi()
    }

    if (res) {
      mileageTrendData.value.data = res.data
    }
  } catch (error) {
    console.log(error)
  }
}

onMounted(() => {

  if (isTutor.value || isLeader.value) {
    activeTab.value = '3'
  }

  if (activeTab.value === '1') {
    getAgeChartData()
    getGroupChartData()
    getStageChartData()
    getSubjectPerson()
    getUnitPerson()
  } else if (activeTab.value === '2') {
    getGroupDiaryChartData()
    getPersonalDiaryChartData()
  } else if (activeTab.value === '3') {
    getMemberRankData()
    getGroupRankData()
    getHonorData()
    if (isDirector.value || isViceDirector.value) {
      getMileageData()
    }
    if (isTutor.value || isLeader.value) { 
      getGroupMileageData()
    }
    getMileageTrendData()
  }
})
</script>

<template>
  <div class="data-statistic">
    <header class="header-area flex-between-center">
      <van-tabs v-model:active="activeTab" @click-tab="handleClickTab" swipe-threshold="3">
        <van-tab v-if="isTutor || isLeader || isDirector || isViceDirector" title="里程情况统计" name="3" />
        <van-tab title="日记情况统计" name="2" />
        <van-tab v-if="!isTutor && !isLeader" title="社员情况统计" name="1" />
      </van-tabs>
    </header>
    <section class="content-area" v-if="activeTab === '1'">
      <div class="content-item">
        <div class="content-item-title flex-between-center">
          <span class="m-text-14-20-600 dark1">年龄情况</span>
          <van-button class="export-btn" @click="exportFile('age')">导出</van-button>
        </div>
        <div class="content-item-content">
          <PieChart :chart-data="ageChartData" />
          <div class="flex-start-center m-mb12" @click="handleRiseOrDown('age')">
            <img v-if="ageRise" src="@/assets/mobile/data-statistic/rise.png" alt="" width="14" height="12">
            <img v-else src="@/assets/mobile/data-statistic/down.png" alt="" width="14" height="12">
            <span class="m-text-12-18-400 blue1 m-pl4">
              {{ ageRise ? '升序' : '降序' }}
            </span>
          </div>
          <el-table :data="ageChartData.data" style="width: 100%">
            <el-table-column prop="name" label="年龄段" align="center">
              <template #default="scope">
                <span class="table-text">
                  {{ scope.row?.name?.split('-')[0] + '岁-' + scope.row?.name?.split('-')[1] + '岁' }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="人员数量" align="center">
              <template #default="scope">
                <span class="table-text">
                  {{ scope.row?.value }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="content-item">
        <div class="content-item-title flex-between-center">
          <span class="m-text-14-20-600 dark1">小组人员情况</span>
          <van-button class="export-btn" @click="exportFile('group')">导出</van-button>
        </div>
        <div class="content-item-content">
          <PieChart :chart-data="groupChartData" />
          <div class="flex-start-center m-mb12" @click="handleRiseOrDown('group')">
            <img v-if="groupRise" src="@/assets/mobile/data-statistic/rise.png" alt="" width="14" height="12">
            <img v-else src="@/assets/mobile/data-statistic/down.png" alt="" width="14" height="12">
            <span class="m-text-12-18-400 blue1 m-pl4">
              {{ groupRise ? '升序' : '降序' }}
            </span>
          </div>
          <el-table :data="groupChartData.data" style="width: 100%">
            <el-table-column prop="name" label="小组" align="center">
              <template #default="scope">
                <span class="table-text">
                  {{ scope.row?.name }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="人员数量" align="center">
              <template #default="scope">
                <span class="table-text">
                  {{ scope.row?.value }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="content-item">
        <div class="content-item-title flex-between-center">
          <span class="m-text-14-20-600 dark1">任教学段人员情况</span>
          <van-button class="export-btn" @click="exportFile('stage')">导出</van-button>
        </div>
        <div class="content-item-content">
          <PieChart :chart-data="stageChartData" />
          <div class="flex-start-center m-mb12" @click="handleRiseOrDown('stage')">
            <img v-if="stageRise" src="@/assets/mobile/data-statistic/rise.png" alt="" width="14" height="12">
            <img v-else src="@/assets/mobile/data-statistic/down.png" alt="" width="14" height="12">
            <span class="m-text-12-18-400 blue1 m-pl4">
              {{ stageRise ? '升序' : '降序' }}
            </span>
          </div>
          <el-table :data="stageChartData.data" style="width: 100%">
            <el-table-column prop="name" label="学段" align="center">
              <template #default="scope">
                <span class="table-text">
                  {{ scope.row?.name }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="人员数量" align="center">
              <template #default="scope">
                <span class="table-text">
                  {{ scope.row?.value }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="content-item">
        <div class="content-item-title flex-between-center">
          <span class="m-text-14-20-600 dark1">学科人员情况</span>
          <van-button class="export-btn" @click="exportFile('subject')">导出</van-button>
        </div>
        <div class="content-item-content">
          <div class="progress-area">
            <div class="progress-item" v-for="item in subjectPersonData" :key="item.name">
              <div class="progress-item-title">
                <span class="m-text-12-18-400 dark1">{{ item.name }}</span>
              </div>
              <el-progress :percentage="item.percentage" :stroke-width="6"
                :format="(p) => formatPercentage(p, item.value)"></el-progress>
            </div>
          </div>
          <div class="flex-start-center m-mb12" @click="handleRiseOrDown('subject')">
            <img v-if="subjectPersonRise" src="@/assets/mobile/data-statistic/rise.png" alt="" width="14" height="12">
            <img v-else src="@/assets/mobile/data-statistic/down.png" alt="" width="14" height="12">
            <span class="m-text-12-18-400 blue1 m-pl4">
              {{ subjectPersonRise ? '升序' : '降序' }}
            </span>
          </div>
          <el-table :data="subjectPersonData" style="width: 100%">
            <el-table-column prop="name" label="学科" align="center">
              <template #default="scope">
                <span class="table-text">
                  {{ scope.row?.name }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="人员数量" align="center">
              <template #default="scope">
                <span class="table-text">
                  {{ scope.row?.value }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="content-item">
        <div class="content-item-title flex-between-center">
          <span class="m-text-14-20-600 dark1">单位人员情况</span>
          <van-button class="export-btn" @click="exportFile('unit')">导出</van-button>
        </div>
        <div class="content-item-content">
          <div class="progress-area">
            <div class="progress-item" v-for="item in unitPersonData" :key="item.name">
              <div class="progress-item-title">
                <span class="m-text-12-18-400 dark1">{{ item.name }}</span>
              </div>
              <el-progress :percentage="item.percentage" :stroke-width="6"
                :format="(p) => formatPercentage(p, item.value)"></el-progress>
            </div>
          </div>
          <div class="flex-start-center m-mb12" @click="handleRiseOrDown('unit')">
            <img v-if="unitPersonRise" src="@/assets/mobile/data-statistic/rise.png" alt="" width="14" height="12">
            <img v-else src="@/assets/mobile/data-statistic/down.png" alt="" width="14" height="12">
            <span class="m-text-12-18-400 blue1 m-pl4">
              {{ unitPersonRise ? '升序' : '降序' }}
            </span>
          </div>
          <el-table :data="unitPersonData" style="width: 100%">
            <el-table-column prop="name" label="所在单位" align="center">
              <template #default="scope">
                <span class="table-text">
                  {{ scope.row?.name }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="人员数量" align="center">
              <template #default="scope">
                <span class="table-text">
                  {{ scope.row?.value }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </section>
    <section class="content-area" v-if="activeTab === '2'">
      <div class="content-item">
        <div class="content-item-title flex-between-center">
          <span class="m-text-14-20-600 dark1">日记情况统计</span>
          <van-button class="export-btn" @click="exportFile('groupDiary')">导出</van-button>
        </div>
        <div class="content-item-content">
          <GroupBarChart :chart-data="groupDiaryChartData" />
          <div class="flex-start-center m-mb12" @click="handleRiseOrDown('groupDiary')">
            <img v-if="groupDiaryRise" src="@/assets/mobile/data-statistic/rise.png" alt="" width="14" height="12">
            <img v-else src="@/assets/mobile/data-statistic/down.png" alt="" width="14" height="12">
            <span class="m-text-12-18-400 blue1 m-pl4">
              {{ groupDiaryRise ? '升序' : '降序' }}
            </span>
          </div>
          <el-table :data="groupDiaryChartData.data" style="width: 100%">
            <el-table-column prop="name" label="所在小组" align="center">
              <template #default="scope">
                <span class="table-text">
                  {{ scope.row?.name }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="数量" align="center">
              <template #default="scope">
                <span class="table-text">
                  {{ scope.row?.value }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="content-item">
        <div class="content-item-title flex-between-center">
          <span class="m-text-14-20-600 dark1">个人日记情况</span>
          <van-button class="export-btn" @click="exportFile('personalDiary')">导出</van-button>
        </div>
        <div class="content-item-content">
          <PersonalBarChart :chart-data="personalDiaryChartData" />
          <div class="flex-start-center m-mb12" @click="handleRiseOrDown('personalDiary')">
            <img v-if="personalDiaryRise" src="@/assets/mobile/data-statistic/rise.png" alt="" width="14" height="12">
            <img v-else src="@/assets/mobile/data-statistic/down.png" alt="" width="14" height="12">
            <span class="m-text-12-18-400 blue1 m-pl4">
              {{ personalDiaryRise ? '升序' : '降序' }}
            </span>
          </div>
          <el-table class="personal-diary-table" :data="personalDiaryChartData.data" style="width: 100%">
            <el-table-column prop="teacherName" label="教师姓名" align="center">
              <template #default="scope">
                <span class="table-text">
                  {{ scope.row?.teacherName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="diaryCount" label="日记数量" align="center">
              <template #default="scope">
                <span class="table-text">
                  {{ scope.row?.diaryCount }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="sixStarCount" label="六星数量" align="center">
              <template #default="scope">
                <span class="table-text">
                  {{ scope.row?.sixStarCount }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="recommendCount" label="导师推荐数量" align="center">
              <template #default="scope">
                <span class="table-text">
                  {{ scope.row?.recommendCount }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </section>
    <section class="content-area" v-if="activeTab === '3'">
      <div class="content-item">
        <div class="content-item-title flex-between-center">
          <span class="m-text-14-20-600 dark1">社员里程排名</span>
          <span class="m-text-14-20-400 dark1 flex-end-center" @click="showMemberTimeRangePicker = true">
            {{ memberTimeRangeText }}
            <el-icon color="#B4B6BE">
              <CaretBottom />
            </el-icon>
          </span>
        </div>
        <div class="content-item-content">
          <div class="member-rank-list">
            <div class="member-rank-item" v-for="item in memberRankData" :key="item.userId">
              <div class="member-rank-item-left">
                <img v-if="item.ranking === 1" src="@/assets/mobile/data-statistic/first.png" alt="" width="28" height="32">
                <div v-else class="member-rank-item-left-number">
                  {{ item.ranking.toString().padStart(2, '0') }}
                </div>
              </div>
              <div class="member-rank-item-right">
                <div class="member-rank-item-right-top flex-between-center m-mb6">
                  <div class="flex-start-center">
                    <span class="m-text-14-20-600 dark1">{{ item.userName }}</span>
                    <img class="m-ml10" v-if="item.isActiveStar" src="@/assets/mobile/data-statistic/active-star.png"
                      alt="" width="18" height="20">
                    <img class="m-ml10" v-if="item.isAwesomeAuthor"
                      src="@/assets/mobile/data-statistic/good-creator.png" alt="" width="19" height="20">
                  </div>
                  <div class="member-rank-item-right-top-right">
                    <span class="m-text-14-20-600 dark1">{{ item.points }}</span>
                  </div>
                </div>
                <div class="member-rank-item-right-bottom">
                  <el-progress :percentage="item.percentage" :show-text="false"></el-progress>
                </div>
              </div>
            </div>
          </div>
        </div>

        <van-popup v-model:show="showMemberTimeRangePicker" position="bottom">
          <van-picker :columns="timeRangeOptions" @confirm="handleMemberTimeRangeConfirm"
            @cancel="showMemberTimeRangePicker = false" />
        </van-popup>
      </div>
      <div class="content-item" v-if="isDirector || isViceDirector || isTutor || isLeader">
        <div class="content-item-title flex-between-center">
          <span class="m-text-14-20-600 dark1">小组里程排名</span>
          <span class="m-text-14-20-400 dark1 flex-end-center" @click="showGroupTimeRangePicker = true">
            {{ groupTimeRangeText }}
            <el-icon color="#B4B6BE">
              <CaretBottom />
            </el-icon>
          </span>
        </div>
        <div class="content-item-content">
          <GroupMileageBarChart :chart-data="groupRankData" />
        </div>

        <van-popup v-model:show="showGroupTimeRangePicker" position="bottom">
          <van-picker :columns="timeRangeOptions" @confirm="handleGroupTimeRangeConfirm"
            @cancel="showGroupTimeRangePicker = false" />
        </van-popup>
      </div>
      <div class="content-item" v-if="isDirector || isViceDirector || isTutor || isLeader">
        <div class="content-item-title flex-between-center">
          <span class="m-text-14-20-600 dark1">荣誉统计</span>
          <span class="m-text-14-20-400 dark1 flex-end-center" @click="showHonorTimeRangePicker = true">
            {{ honorTimeRangeText }}
            <el-icon color="#B4B6BE">
              <CaretBottom />
            </el-icon>
          </span>
        </div>
        <div class="content-item-content">
          <HonorBarChart :chart-data="honorData" />
        </div>

        <van-popup v-model:show="showHonorTimeRangePicker" position="bottom">
          <van-picker :columns="timeRangeOptions" @confirm="handleHonorTimeRangeConfirm"
            @cancel="showHonorTimeRangePicker = false" />
        </van-popup>
      </div>
      <div class="content-item" v-if="isDirector || isViceDirector">
        <div class="content-item-title flex-between-center">
          <span class="m-text-14-20-600 dark1">里程分布</span>
          <span class="m-text-14-20-400 dark1 flex-end-center" @click="showMileageTimeRangePicker = true">
            {{ mileageTimeRangeText }}
            <el-icon color="#B4B6BE">
              <CaretBottom />
            </el-icon>
          </span>
        </div>
        <div class="content-item-content">
          <MileageDistributionChart :chart-data="mileageData" />
        </div>

        <van-popup v-model:show="showMileageTimeRangePicker" position="bottom">
          <van-picker :columns="timeRangeOptions" @confirm="handleMileageTimeRangeConfirm"
            @cancel="showMileageTimeRangePicker = false" />
        </van-popup>
      </div>
      <div class="content-item" v-if="isTutor || isLeader">
        <div class="content-item-title flex-between-center">
          <span class="m-text-14-20-600 dark1">小组里程排名</span>
          <span class="m-text-14-20-400 dark1 flex-end-center" @click="showGroupMileageTimeRangePicker = true">
            {{ groupMileageTimeRangeText }}
            <el-icon color="#B4B6BE">
              <CaretBottom />
            </el-icon>
          </span>
        </div>
        <div class="content-item-content">
          <GroupMileageRank :chart-data="groupMileageData" />
        </div>

        <van-popup v-model:show="showGroupMileageTimeRangePicker" position="bottom">
          <van-picker :columns="timeRangeOptions" @confirm="handleGroupMileageTimeRangeConfirm"
            @cancel="showGroupMileageTimeRangePicker = false" />
        </van-popup>
      </div>
      <div class="content-item">
        <div class="content-item-title flex-between-center">
          <span class="m-text-14-20-600 dark1">近30日总里程趋势</span>
        </div>
        <div class="content-item-content">
          <MileageTrend :chart-data="mileageTrendData" />
        </div>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
@use "@/views/mobile/styles/custom-global-mobile-style.scss";

.data-statistic {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .header-area {
    width: 100%;
    height: 57px;
    background: linear-gradient(270deg, #508cff 0%, #535dff 100%), #508cff;
    padding: 12px 20px 14px;

    :deep(.van-tabs) {
      flex: 1;
      padding-right: 6px;

      .van-tabs__nav {
        background: transparent;
      }

      .van-tab {
        color: #e2e3e6;
        font-size: 14px;

        &--active {
          color: #ffffff !important;
          font-weight: 600 !important;
        }
      }

      .van-tabs__wrap {
        height: 30px;
      }

      .van-tabs__line {
        background: #fff;
        border-radius: 3px;
      }
    }

    :deep(.van-checkbox) {
      .van-checkbox__label {
        font-size: 14px;
        color: #fff;
      }

      .van-checkbox__icon {
        .van-icon {
          border: 1px solid #c2c9ce;
          background-color: #fff;
        }

        &--checked {
          .van-icon {
            color: #508cff;
          }
        }
      }
    }
  }

  .content-area {
    flex: 1;
    overflow-y: auto;

    .content-item {

      .content-item-title {
        padding: 10px 15px;
      }

      .content-item-content {
        padding: 15px;
        background-color: #fff;
      }

      .progress-area {
        padding: 0 9px;

        .progress-item {
          display: flex;
          flex-direction: column;
          justify-content: start;
          align-items: start;

          .progress-item-title {
            font-size: 14px;
            color: #8da2b5;
            margin-bottom: 2px;
          }
          
          :deep(.el-progress) {
            width: 100%;

            .el-progress-bar__inner {
              background: linear-gradient(270deg, #88C0FF 0%, #508CFF 100%), #508CFF !important;
            }
          }
        }
      }
    }

    .export-btn {
      width: 48px;
      height: 24px;
      border-radius: 4px;
      background: #508cff;
      color: #fff;
      font-size: 14px;
      padding: 0;
    }
  }

  .member-rank-list {
    max-height: 300px;
    overflow-y: auto;

    .member-rank-item {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 25px;

      &:last-child {
        margin-bottom: 0;
      }

      &-left {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 38px;
        height: 32px;
        padding-right: 10px;

        &-number {
          font-family: DINAlternate, DINAlternate;
          font-weight: bold;
          font-size: 16px;
          color: #2B2C33;
          line-height: 19px;
          text-align: left;
        }
        
      }

      &-right {
        flex: 1;
      }
    }
  }

  :deep(.el-progress) {
    .el-progress-bar__inner {
      background-color: transparent;
      background: linear-gradient(270deg, #508CFF 0%, #55C2F5 100%) !important;
    }
  }
}

.table-text {
  color: #8da2b5;
  font-size: 14px;
  font-weight: 400;
}

:deep(.el-table) {
  .el-table__header {
    .el-table__cell {
      background-color: #F3F7FF;

      .cell {
        color: #2b2c33;
        font-size: 14px;
        font-weight: 600;
      }
    }
  }
}

::-webkit-scrollbar {
  display: none;
}
</style>
import { request } from "@/utils/service"
//创建会话
export function createConversations() {
  return request({
    url: "/ai/conversations/bk/create",
    method: "post",
  })
}
//常见问题
export function faqList(data) {
    return request({
      url: `/ai/conversations/bk/faq/${data.type}`,
      method: "get",
    })
  }
 //消息列表
  export function messagesList(data) {
    return request({
      url: `/ai/conversations/${data.id}/bk/messages`,
      method: "get",
      
    })
  }
  //清空上下文
  export function clearContext(data) {
    return request({
      url: `/ai/conversations/${data.id}/bk/clear-context`,
      method: "delete",
      
    })
  }
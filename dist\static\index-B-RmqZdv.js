import{o as g}from"./index-DCRFd5B4.js";import{aC as R,r as a,aE as A,az as C,h as I,ag as n,z as i,A as r,B as e,P as k,a6 as w,u,Q as l,E as B,O as b,I as y}from"./vue-CelzWiMA.js";/* empty css                                                                   */import{_ as z}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./element-BwwoSxMX.js";import"./vant-C4eZMtet.js";const E={class:"content-box flex_start_0 mt10"},S={class:"left"},L={class:"person-column"},N={class:"list"},V=["onClick"],D={class:"flex_start"},F={class:"flex_1 right"},H={__name:"index",setup(M){let d=g(),{personcenterCurrentInfo:O,userInfo:P}=R(d),p=a([{id:"1",title:"规则设置"},{id:"2",title:"线下录入"}]),t=a(1),c=A(),f=C();a("");const m=o=>{t.value=o.id,o.id==1?c.push("/actResearchAssociation/mileage/ruleset"):c.push("/actResearchAssociation/mileage/inline/ipt")};return I(()=>{f.path=="/actResearchAssociation/mileage/ruleset"?t.value=1:t.value=2}),(o,_)=>{const h=n("ArrowRight"),v=n("el-icon"),x=n("router-view");return r(),i("div",E,[e("div",S,[e("div",L,[_[0]||(_[0]=e("p",{class:"title"},"里程管理",-1)),e("ul",N,[(r(!0),i(k,null,w(u(p),s=>(r(),i("li",{class:B(["flex_between",s.id==u(t)?"active":""]),key:s.id,onClick:Q=>m(s)},[e("div",D,[e("p",null,b(s.title),1)]),l(v,null,{default:y(()=>[l(h)]),_:1})],10,V))),128))])])]),e("div",F,[l(x)])])}}},J=z(H,[["__scopeId","data-v-be9a99fa"]]);export{J as default};

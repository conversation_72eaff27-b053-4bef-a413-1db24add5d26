<script setup>
import { showConfirmDialog, showSuccessToast } from "vant"
import { ref, onMounted, nextTick, onBeforeUnmount, watch, reactive, computed } from "vue"
import { useRouter } from "vue-router"
import { getTopicConversationListApi, deleteTopicConversationApi, editTopicConversationApi } from "@/api/mobile/topic-conversation"
import { debounce } from "lodash-es"
import { useUserStore } from "@/store/modules/user"
import DefaultAvatar from "@/assets/layouts/default-avatar.png"
const userStore = useUserStore()

const router = useRouter()


// 添加计算属性来判断用户角色
const isDirector = computed(() => userStore.roles?.includes("DIRECTOR"))
const isMember = computed(() => userStore.roles?.includes("MEMBER"))
const isTutor = computed(() => userStore.roles?.includes("TUTOR"))

const searchForm = ref({
  searchInput: "",
  clubPostType: "2"
})

// 获取列表
const page = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

const changeRadio = () => {
  finished.value = false
  page.pageNum = 1
  getList()
}

const list = ref([])
const loading = ref(false)
const finished = ref(false)
const scrollTop = ref(0) // 记录滚动位置

// 重新加载列表数据但保持当前浏览位置
const reloadListWithPosition = async () => {
  try {
    loading.value = true
    
    // 记录当前滚动位置和分页数据
    const listArea = document.querySelector('.list-area')
    if (listArea) {
      scrollTop.value = listArea.scrollTop
    }
    
    const currentPage = page.pageNum
    const currentSize = page.pageSize
    const totalItems = currentPage * currentSize
    
    // 重置页码但请求所有数据
    const tempParams = {
      pageNum: 1,
      pageSize: totalItems,
      keyword: searchForm.value.searchInput,
      postType: searchForm.value.clubPostType
    }
    
    const res = await getTopicConversationListApi(tempParams)
    if (res) {
      list.value = res.data.list || []
      page.total = res.data.total
      
      // 恢复原来的页码
      page.pageNum = currentPage
      
      // 检查是否已加载完成
      if (list.value.length >= page.total) {
        finished.value = true
      }
      
      // 恢复滚动位置
      nextTick(() => {
        const listArea = document.querySelector('.list-area')
        if (listArea) {
          listArea.scrollTop = scrollTop.value
        }
        // checkTextOverflow()
      })
    }
    loading.value = false
  } catch (error) {
    console.log('重新加载列表失败', error)
    loading.value = false
  }
}

const getList = async () => {
  try {
    loading.value = true
    const res = await getTopicConversationListApi({
      pageNum: page.pageNum,
      pageSize: page.pageSize,
      keyword: searchForm.value.searchInput,
      postType: searchForm.value.clubPostType
    })
    if (res) {
      if (page.pageNum === 1) {
        list.value = res.data.list || []
      } else {
        list.value = [...list.value, ...res.data.list]
      }

      loading.value = false
      page.total = res.data.total

      if (list.value.length >= page.total) {
        finished.value = true
      } else {
        page.pageNum++
      }
    }
  } catch (error) {
    console.log('++++', error)
  }
}

const debounceGetList = debounce(() => {
  page.pageNum = 1
  list.value = []
  finished.value = false
  loading.value = false
  getList()
}, 300)

// 存储溢出状态的对象
const textOverflowMap = ref({})

// 检查文本是否溢出
// const isTextOverflow = (id) => {
//   return textOverflowMap.value[id] || false
// }

// 添加跳转到详情页的方法
const goToDetail = (id) => {
  router.push({
    path: "/topicConversation/detail",
    query: {
      id
    }
  })
}

// 检测文本是否溢出并更新状态
// const checkTextOverflow = () => {
//   nextTick(() => {
//     const items = document.querySelectorAll(".list-item")
//     if (items.length > 0) {
//       items.forEach((item) => {
//         const id = item.getAttribute("data-id")
//         const contentText = item.querySelector(".content-text")
//         if (contentText) {
//           // 检查元素是否溢出
//           const isOverflow = contentText.scrollHeight > contentText.clientHeight
//           textOverflowMap.value[id] = isOverflow
//         }
//       })
//     }
//   })
// }

const handleDelete = (id) => {
  showConfirmDialog({
    title: "删除",
    message: "确定删除该主题吗？",
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    confirmButtonColor: "#508CFF",
    cancelButtonColor: "#C7CFDF",
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      try {
        const res = await deleteTopicConversationApi(id)
        if (res) {
          showSuccessToast("删除成功")
          // 删除后重新加载列表，保持当前位置
          reloadListWithPosition()
        }
      } catch (err) {
        console.log(err)
      }
    })
    .catch(() => { })
}

const goEdit = (id) => {
  router.push({
    path: "/topicConversation/publish",
    query: {
      id
    }
  })
}

watch(
  () => list.value,
  () => {
    nextTick(() => {
      // checkTextOverflow()
    })
  },
  { deep: true }
)

const goToPublish = () => {
  router.push({
    path: "/topicConversation/publish",
  })
}

onMounted(() => {
  // 在列表加载后检查文本溢出
  nextTick(() => {
    // checkTextOverflow()
    // window.addEventListener("resize", checkTextOverflow)
  })
  getList()
})

onBeforeUnmount(() => {
  // window.removeEventListener("resize", checkTextOverflow)
})
</script>

<template>
  <div class="topic-conversation">
    <header class="header flex-between-center">
      <van-field v-model="searchForm.searchInput" placeholder="输入关键词搜索" right-icon="search"
        @update:modelValue="debounceGetList" />
      <div class="flex-end-center pointer" v-if="!isMember" @click="goToPublish">
        <img src="@/assets/mobile/topic-conversation/publish.png" alt="" width="12" height="12" />
        <span class="m-text-14-20-400 white m-ml4">发布讨论</span>
      </div>
    </header>
    <div class="form-item flex-column-start-start">
      <van-radio-group v-model="searchForm.clubPostType" direction="horizontal" @change="changeRadio">
        <van-radio name="2">全社讨论</van-radio>
        <van-radio name="1">小组讨论</van-radio>
      </van-radio-group>
    </div>
    <section class="list-area">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="getList"
        :immediate-check="false" :offset="300">
        <div class="list-item flex-start-start m-mb12" v-for="(item, index) in list" :key="item.id" :data-id="item.id"
          @click="goToDetail(item.id)">
          <div class="left-box">
            <img :src="item.userAvatarUrl || DefaultAvatar" alt="" width="32" height="32" style="border-radius: 50%;" />
          </div>
          <div class="right-box flex-1 m-pl5">
            <div class="right-top flex-between-center">
              <span class="flex-start-center">
                <span class="m-text-14-20-600 grey1 m-pr8">{{ item.userName }}</span>
                <span class="m-text-12-18-400 grey3">{{ item.postTime }}</span>
              </span>
            </div>
            <div class="right-middle">
              <div class="content-wrapper">
                <div class="m-text-14-20-400 grey1 content-text" v-html="item.content">
                </div>
                <!-- 注释掉"全部"按钮相关代码
                <div class="ellipsis-container" v-if="isTextOverflow(item.id)">
                  <span class="ellipsis">...</span>
                  <span class="m-text-14-20-400 blue1 show-all" @click.stop="goToDetail(item.id)">全部</span>
                </div>
                -->
              </div>
            </div>
            <div class="right-bottom flex-between-center flex-wrap">
              <div class="label m-text-12-18-400 grey1 m-mr8 flex-start-center">
                <img src="@/assets/mobile/personal-space/label.png" alt="" width="12" height="12" />
                <span class="m-pl4">{{ item.clubTopicName }}</span>
              </div>
              <div class="flex-end-center">
                <van-button v-if="userStore?.userInfo?.user?.id === item.userId" class="inner-btn delete-btn"
                  @click.stop="handleDelete(item.id)">
                  <template #icon>
                    <img src="@/assets/mobile/personal-space/delete.png" alt="" width="11" height="11" />
                  </template>
                  <span class="m-text-12-18-400 red1">删除</span>
                </van-button>
                <van-button v-if="userStore?.userInfo?.user?.id === item.userId" class="inner-btn delete-btn m-ml6"
                  @click.stop="goEdit(item.id)">
                  <template #icon>
                    <img src="@/assets/mobile/personal-space/edit.png" alt="" width="11" height="11" />
                  </template>
                  <span class="m-text-12-18-400 blue1">编辑</span>
                </van-button>
              </div>
            </div>
          </div>
        </div>
      </van-list>
    </section>
  </div>
</template>

<style lang="scss" scoped>
@use "@/views/mobile/styles/custom-global-mobile-style.scss";

.topic-conversation {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .header {
    height: 56px;
    padding: 12px 15px;
    background: linear-gradient(270deg, #508cff 0%, #535dff 100%);
    border-radius: 0px 0px 8px 8px;

    .van-field {
      width: 228px;
      height: 32px;
      padding: 4px 16px;
      border-radius: 8px;
    }
  }

  .form-item {
    padding: 0 16px;

    :deep(.van-field) {
      padding-left: 0;
      padding-right: 0;
    }

    :deep(.van-radio-group) {
      height: 44px;
      line-height: 44px;

      .van-radio {

        &__label {
          font-size: 14px;
        }
      }
    }
  }
  

  .list-area {
    position: relative;
    flex: 1;
    overflow-y: auto;

    .list-item {
      width: 100%;
      background-color: #fff;
      padding: 15px;

      .right-middle {
        position: relative;

        .content-wrapper {
          display: inline;
        }

        .content-text {
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-all;
          margin: 0;
        }

        /* 注释掉"全部"按钮相关样式
        .ellipsis-container {
          position: absolute;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          background: #fff;

          .ellipsis {
            color: #333;
          }

          .show-all {
            color: #508cff;
            margin-left: 2px;
            cursor: pointer;
          }
        }
        */
      }

      .right-bottom {
        padding-top: 8px;

        .label {
          padding: 0 7px;
          border-radius: 19px;
          border: 1px solid #e2e3e6;
          height: 24px;
          line-height: 24px;
        }

        .clocking-time {
          padding: 0 11px;
          border-radius: 19px;
          background-color: #fff5e7;
          height: 24px;
          line-height: 24px;
        }
      }

            .inner-btn {
              width: 56px;
              height: 24px;
              line-height: 24px;
              border-radius: 4px;
              border: 1px solid #e2e3e6;
              padding: 0 4px;
      
              &.delete-btn {
                /* margin-right: 6px; */
              }
      
              &.has-hidden-btn {
                width: 68px;
              }
            }

      .bottom {
        .count-item {
          /* margin-right: 24px; */
        }
      }
    }
  }

  ::-webkit-scrollbar {
    display: none;
  }
}
</style>

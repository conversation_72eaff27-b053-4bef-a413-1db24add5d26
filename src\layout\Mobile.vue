<script setup>
import { ref, h } from "vue"
import { useRoute } from "vue-router"
import TabBar from "./components/TabBar/index.vue"

const route = useRoute()

function isIPhoneWithHomeIndicator() {
  return (
    /iPhone/.test(navigator.userAgent) &&
    /AppleWebKit/.test(navigator.userAgent) &&
    !/CriOS/.test(navigator.userAgent) &&
    (screen.height === window.innerHeight || window.devicePixelRatio >= 3)
  )
}

const haveBottom = ref(false)
if (isIPhoneWithHomeIndicator()) {
  console.log("This is an iPhone with a bottom home indicator.")
  haveBottom.value = true
}
</script>

<template>
  <div class="mobile-app-wrapper">
    <!-- 主容器 -->
    <div class="main-container" :class="haveBottom ? 'have-bottom' : ''">
      <!-- 页面主体内容 -->
      <section :class="route.meta?.showBar ? 'app-main' : 'app-main-no-bar'">
        <!-- key 采用 route.path 和 route.fullPath 有着不同的效果，大多数时候 path 更通用 -->
        <router-view v-slot="{ Component, route }">
          <transition name="el-fade-in" mode="out-in">
            <div style="width: 100%; height: 100%;" :class="{ 'keep-alive-container': route.meta.keepAlive }">
              <KeepAlive v-if="route.meta.keepAlive">
                <component :is="Component" :key="route.path" class="app-container-grow" />
              </KeepAlive>
              <component v-if="!route.meta.keepAlive" :is="Component" :key="route.path" class="app-container-grow" />
            </div>
          </transition>
        </router-view>
      </section>
      <tab-bar v-if="route.meta?.showBar" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "@/styles/mixins.scss";
@use "@/views/mobile/styles/custom-global-mobile-style.scss";
$transition-time: 0.35s;

.mobile-app-wrapper {
  @extend %clearfix;
  width: 100%;
}

.fixed-header {
  position: fixed;
  top: 0;
  z-index: 1002;
  width: 100%;

  .logo {
    width: var(--v3-sidebar-width);
  }

  .content {
    display: flex;

    .navigation-bar {
      flex: 1;
    }
  }
}

.layout-header {
  background-color: var(--v3-header-bg-color);
  box-shadow: var(--v3-header-box-shadow);
  border-bottom: var(--v3-header-border-bottom);
}

.main-container {
  // display: flex;
  // flex-direction: column;
  min-height: 100%;
  // padding-bottom: var(--van-safe-area-inset-bottom);
}

.app-main {
  width: 100%;
  display: flex;
  transition: padding-left $transition-time;
  // padding-top: var(--v3-navigationbar-height);
  height: calc(100vh - 50px);
  overflow: auto;
}

.have-bottom {
  .app-main {
    height: calc(100vh - 50px - env(safe-area-inset-bottom));
  }
}

.app-main-no-bar {
  transition: padding-left $transition-time;
  // padding-top: var(--v3-navigationbar-height);
  height: 100vh;
  overflow: auto;
}

::-webkit-scrollbar {
  display: none;
}

.hasTagsView {
  .app-main {
    padding-top: var(--v3-header-height);
  }
}
</style>

import { ref } from "vue"
import { pinia } from "@/store"
import { defineStore } from "pinia"
import { getToken, removeToken, setToken } from "@/utils/cache/cookies"
import { resetRouter, router } from "@/router"
import { getUserInfoApi, loginApi, logoutapi } from "@/api/login"
import routeSettings from "@/config/route"
import { showToast } from "vant"

// import { getHashSearchParam } from "@/utils"
import { wxLogin } from "@/api/common/index.js"
import { showSuccessToast } from "vant"
import { useUserInfoStoreHook } from "./pc"

export const useUserStore = defineStore(
  "user",
  () => {
    const token = ref(getToken() || "")
    const userInfo = ref({})
    const roles = ref([])
    const avatar = ref("")
    const rolesName = ref([])
    const userInfoStore = useUserInfoStoreHook()
    /** 登录 */
    const login = async (row) => {
      /* 企微内的相关登录 */

      // const appId = getHashSearchParam("appId") || ""
      // const corpId = getHashSearchParam("corpId") || ""
      // const code = getHashSearchParam("code") || ""
      // const type = getHashSearchParam("type") || ""

      // const { data } = await loginApi({ appId, corpId, code, type })
      // setToken(data.tokenValue)
      // token.value = data.tokenValue

      try {
        const res = await loginApi({
          username: row?.username,
          password: row?.password,
          code: row?.code,
          codeKey: row?.codeKey
        })
        if (res) {
          if (res.successful) {
            if (res.data.code == 20002) {
              sessionStorage.setItem("m", res.data.mobile)
              router.push("/reset")
            } else {
              setToken(res.data.token)
              token.value = res.data.token
              showSuccessToast("登录成功" + (res.actionPoint ? ` ${res.actionPoint}步` : ""))
              if (router.currentRoute.value.query.redirect_uri && router.currentRoute.value.query.redirect_uri != '/reset') {
                router.push(router.currentRoute.value.query.redirect_uri)
              } else {
                router.push("/")
              }
            }

            return Promise.resolve(res)
          } else {
            if (res.data.msg) {
              showToast(res.data.msg)
            } else {
              showToast(res.msg)
            }
            return Promise.resolve(res)
          }
        }
      } catch (error) {
        console.log(error)
        return Promise.reject(error)
      }
    }
    const wxLoginByCode = async (code) => {
      try {
        let res = await wxLogin({
          code
        })
        if (res) {
          
          if (res.successful) {
            if (res.data.code == 20002) {
              sessionStorage.setItem("m", res.data.mobile)
              router.push("/reset")
            } else {
              setToken(res.data.token)
              token.value = res.data.token
              showSuccessToast("登录成功" + (res.actionPoint ? ` ${res.actionPoint}步` : ""))
              router.push("/")
            }
          } else {
            if (res.data.msg) {
              showToast(res.data.msg)
            } else {
              showToast(res.msg)
            }
          }

          // setToken(res.data.token)
          // token.value = res.data.token
          // showSuccessToast("登录成功" + (res.actionPoint ? ` ${res.actionPoint}步` : ""))
          // getInfo()
          // router.push({
          //   path: "/"
          // })
        }
      } catch (error) {
        router.push({
          path: "/login"
        })
      }
    }
    /** 获取用户详情 */
    const getInfo = async () => {
      const { data } = await getUserInfoApi()
      if (data) {
        userInfo.value = data

        /**
         * clubRole
         * 成员: "MEMBER",
         * 组长： "LEADER",
         * 导师： "TUTOR",
         * 社长: "DIRECTOR",
         * 副社长： "VICE_DIRECTOR",
         *
         * 在此处根据 clubRole 的值来设置 roles 的值， 可通过v-permission指令来控制权限
         */
        sessionStorage.setItem("userInfo", JSON.stringify(data))
        roles.value = data?.xingZhiShe.clubRole ? [data?.xingZhiShe.clubRole] : routeSettings.defaultRoles
        rolesName.value = data?.xingZhiShe.clubRoleLabel ? [data?.xingZhiShe.clubRoleLabel] : ["成员"]
        avatar.value = data?.user?.avatarUrl

        // userInfoStore.getInfo('mobile')
      }
    }
    /** 模拟角色变化 */
    const changeRoles = async (role) => {
      const newToken = "token-" + role
      token.value = newToken
      setToken(newToken)
      // 用刷新页面代替重新登录
      window.location.reload()
    }
    /** 登出 */
    const logout = async () => {
      const res = await logoutapi()

      if (res) {
        removeToken()
        token.value = ""
        roles.value = []
        sessionStorage.removeItem('userInfo')
        resetRouter()
      }
    }
    /** 重置 Token */
    const resetToken = () => {
      removeToken()
      token.value = ""
      roles.value = []
      sessionStorage.removeItem("userInfo")
    }

    return {
      token,
      roles,
      userInfo,
      rolesName,
      login,
      getInfo,
      changeRoles,
      logout,
      resetToken,
      wxLoginByCode,
      avatar
    }
  },
  {
    persist: {
      // 指定存储位置，默认是 localStorage
      storage: localStorage,
      // 指定存储的键名
      key: "mobileUserInfo"
      // 可以指定哪些状态需要持久
    }
  }
)

/**
 * 在 SPA 应用中可用于在 pinia 实例被激活前使用 store
 * 在 SSR 应用中可用于在 setup 外使用 store
 */
export function useUserStoreHook() {
  return useUserStore(pinia)
}

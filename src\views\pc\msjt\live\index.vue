<template>
    <div class="msjt-live content-box">
        <div class="flex_between mt_16" style="margin-top:16px;">
            <ul class="flex_start state-ul">
                <li v-for="item in state_list" :class="item.id == search_form.status ? 'active-state' : ''" :key="item.id" @click="changeState(item)">{{item.title}}</li>
            </ul>
            <el-input class="flex_1 input_40 search_input mlr_10" v-model="search_form.title" :suffix-icon="Search" @input="searchClick" placeholder="搜索资源"></el-input>
            <el-button class="primary-btn btn_132_40 " type="primary" :icon="CirclePlusFilled" @click="router.push('/msjt/live/add/0')">新增直播</el-button>
        </div>
        <ul class="live-ul flex_start flex_wrap" v-if="live_List && live_List.length > 0">
            <li v-for="item in live_List" :key="item.id" @click="goDetail(item)">
                <div class="seal-box">
                    <img class="seal-img" :src="item.sealUrl" alt="">
                    <p class="view-box">{{item.view ? item.view : 0}}人</p>
                </div>
                <div class="live-content flex_between" style="width: 100%;">
                    <div class="flex_1" style="width: calc(100% - 140px);">
                        <p class="flex_start text_16_bold mb_13"><img class="live-icon" src="@/assets/pc/msjt/title-icon.png" alt=""><span class="ellipsis1">{{item.title}}</span></p>
                        <p class="flex_start grey2 text_12" :title="item.startTime + ' - ' + item.endTime"><img class="live-icon" src="@/assets/pc/msjt/date-icon.png" alt=""><span class="ellipsis1">{{item.startTime}} - {{item.endTime}}</span></p>
                    </div>
                    <el-button v-if="item.resourceId" class="primary-btn2 btn_112_40" type="primary" @click="searchClick">回放</el-button>
                </div>
            </li>
        </ul>
        <el-pagination v-if="live_List && live_List.length > 0" background layout="total,prev, pager, next" :total="total" class="mt-4" :current-page='search_form.pageNum' :page-size="search_form.pageSize" @current-change='currentChange' />
        <div class="flex_column_center empty-box" v-else>
            <img src="@/assets/pc/msjt/empty.png" alt="">
            <p>暂无直播</p>
        </div>
    </div>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted, onActivated} from 'vue'
    import { CirclePlusFilled, Search } from '@element-plus/icons-vue'
    import { msjtList } from '@/api/pc/msjt'
    import {useRoute,useRouter} from 'vue-router'
    let route = useRoute()
    let router = useRouter()
    const formObj = function(obj = {}){
        this.status = obj.status || 1
        this.title = obj.title || ''
        this.pageNum = obj.pageNum || 1
        this.pageSize = obj.pageSize || 6
    }
    const search_form = ref(new formObj({}))
    const state_list = ref([
        {
            id:1,
            title:'进行中'
        },
        {
            id:2,
            title:'未开始'
        },
        {
            id:3,
            title:'已结束'
        }
    ])
    const live_List = ref([])
    const total = ref(0)

    const getLiveList = async () => {
        let res = await msjtList(search_form.value)
        if(res){
            live_List.value = res.data.list
            total.value = res.data.total
        }
    }
    const goDetail = (item) => {
        router.push(`/msjt/live/detail/${item.liveId}`)
    }

    const changeState = (item) => {
        search_form.value.status = item.id
        getLiveList()
    }

    const searchClick = () => {
        search_form.value.page = 1
        getLiveList()
    }

    const currentChange = (pageNum) => {
        search_form.value.pageNum = pageNum
        getLiveList()
    }
    onActivated(() => {
        getLiveList()
    })
</script>
<style lang="scss" scoped>
.msjt-live{
    .state-ul{border-radius: 8px;background: #FFFFFF;overflow: hidden;color: #6D6F75;height:40px;
        li{padding: 8px 38px;height: 100%;line-height: normal;cursor: pointer;}
        .active-state{background: #508CFF;font-size: 16px;color: #FFFFFF;font-weight: 600;}
    }
    .live-ul{margin: 10px 0;
        li{width: calc((100% - 20px) / 3);border-radius: 8px;overflow:hidden;background: #FFFFFF;margin-right:10px;margin-bottom:10px;cursor: pointer;
            .seal-img{height: 188px;width: 100%;object-fit: cover;}
            .live-content{padding: 16px;
                .live-icon{width: 12px;margin-right: 8px;}
            }
            .seal-box{position: relative;
                .view-box{position:absolute;bottom: 16px;left: 16px;
                background: rgba(0,0,0,0.3);font-weight: 400;display: inline-block;padding: 5px 16px; font-size: 16px; color: #FFFFFF; line-height: 22px; text-align: left; border-radius: 16px;}
            }
        }
        li:nth-child(3n){margin-right: 0;}
    }
    .empty-box{font-weight: 400; font-size: 20px; color: #8C99AB; line-height: 28px; text-align: left;margin-top:56px;
        img{width: 360px;height: auto;margin-bottom: 10px;}
    }
}
</style>

<template>
    <div class="allocation-main">
        <ul class="flex_start tab-ul">
            <li v-for="item in tab_list" :key="item.id" :class="current_tab === item.id? 'active-tab' : ''" @click="current_tab = item.id">{{item.title}}</li>
        </ul>
        <div class="flex_start_0" v-if="current_tab == 1">
            <div>
                <ul class="group-ul">
                    <li v-for="(item,index) in group_list" :key="item.id" class="flex_start" :class="current_group === item.id? 'active-group' : ''" @click="current_group = item.id">
                        <p>第{{letters[index]}}组</p>
                        <el-icon class="ml_10"><ArrowRight /></el-icon>
                    </li>
                </ul>
                <div class="flex_center group-add-btn pointer" @click="addGroup">
                    <img src="@/assets/pc/jky/add.png" class="state-icon" alt="">
                </div>
            </div>
            <div class="select-pw">
                <p class="text_16_bold">选题价值</p>
                <div class="select-box flex_start_start mb_24 flex_wrap pointer" :class="current_category === 'xtjz'? 'active-select' : ''" @click="changeCategory('xtjz')">
                    <p v-if="currentGroupData.xtjz && currentGroupData.xtjz.length > 0" class="selected-user flex_center" v-for="item in currentGroupData.xtjz" :key="item.id">
                        <span class="mr_10">{{item.title}}</span>
                        <el-icon @click.stop="removeExpert(currentGroupData.xtjz, item.id)"><Close /></el-icon>
                    </p>
                    <p v-else>请选择</p>
                </div>
                <p class="text_16_bold">设计与论证</p>
                <div class="select-box flex_start_start mb_24 flex_wrap pointer" :class="current_category === 'sjylz'? 'active-select' : ''" @click="changeCategory('sjylz')">
                    <p v-if="currentGroupData.sjylz && currentGroupData.sjylz.length > 0" class="selected-user flex_center" v-for="item in currentGroupData.sjylz" :key="item.id">
                        <span class="mr_10">{{item.title}}</span>
                        <el-icon @click.stop="removeExpert(currentGroupData.sjylz, item.id)"><Close /></el-icon>
                    </p>
                    <p v-else>请选择</p>
                </div>
                <p class="text_16_bold">可行性分析</p>
                <div class="select-box flex_start_start flex_wrap pointer" :class="current_category === 'kxxfx'? 'active-select' : ''" @click="changeCategory('kxxfx')">
                    <p v-if="currentGroupData.kxxfx && currentGroupData.kxxfx.length > 0" class="selected-user flex_center" v-for="item in currentGroupData.kxxfx" :key="item.id">
                        <span class="mr_10">{{item.title}}</span>
                        <el-icon @click.stop="removeExpert(currentGroupData.kxxfx, item.id)"><Close /></el-icon>
                    </p>
                    <p v-else>请选择</p>
                </div>
                <el-icon v-if="group_list.length > 1" class="remove-group-btn red2" size="16" @click="removeGroup"><Delete /></el-icon>
            </div>
            <div class="half-box">
                <div class="flex_between">
                    <el-select class="select_40 mr_24" v-model="search_form.sch" placeholder="所在单位" @change="changeExtend">
                        <el-option v-for="item in expert_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                    <el-input v-model="search_form.name" @input="changeUserName" class="input_40" clearable placeholder="搜索评委姓名"></el-input>
                </div>
                <el-table :data="teacher_list" :row-key="item => item.id" class="my-table" ref="multipleTableRef" stripe @selection-change="handleSelectionChange" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F8F9FF"}'>
                    <el-table-column type="selection" width="55" />
                    <el-table-column prop="title" label="评委姓名" show-overflow-tooltip></el-table-column> 
                    <el-table-column prop="sch" label="所在单位" show-overflow-tooltip></el-table-column> 
                </el-table>
                
            </div>
        </div>
        <el-pagination v-if="current_tab == 1" background layout="total,prev, pager, next" :total="total" class="mt-4" :current-page='search_form.pageNum' :page-size="search_form.pageSize" @current-change='currentChange' />
        <Material v-else></Material>
    </div>
</template>

<script setup>
import { reactive, ref, computed, onBeforeMount, onMounted, nextTick, watch, toRefs } from 'vue'
import { toRaw } from "@vue/reactivity"
import Material from './Material.vue'
import { taskList, taskDel } from '@/api/pc/jky'
import { applyList, msjtDel } from '@/api/pc/msjt'
import { ArrowRight, Delete, Close } from '@element-plus/icons-vue'

// 表单对象构造函数
const formObj = function(obj = {}){
    this.sch = obj.sch || ''
    this.name = obj.name || ''
    this.pageNum = obj.pageNum || 1
    this.pageSize = obj.pageSize || 8
}

// 响应式数据
const search_form = ref(new formObj({}))
const total = ref(0)
const tab_list = ref([{id:1,title:'评委分组'},{id:2,title:'材料分配'}])
const current_tab = ref(1)
const letters = ['一','二','三','四','五','六','七','八','九','十','十一','十二','十三','十四','十五','十六','十七','十八','十九','二十']
const group_list = ref([{id:1,xtjz:[],sjylz:[],kxxfx:[]}])
const current_group = ref(1)
const current_category = ref('xtjz')
const teacher_list = ref([])
const expert_select = ref([
    {value: '山东中医药大学', label: '山东中医药大学'},
    {value: '其他院校', label: '其他院校'}
])

// 表格引用
const multipleTableRef = ref(null)

// 用于存储所有选中的评委，结构: { groupId: { category: { teacherId: teacherData } } }
const allSelectedTeachers = ref({})

// 分页操作标记
const isPaging = ref(false)

// 获取当前选中组的数据
const currentGroupData = computed(() => {
    return group_list.value.find(item => item.id === current_group.value) || {}
})

// 获取当前类别下所有已选择的评委ID
const getSelectedIds = () => {
    if (!currentGroupData.value) return []
    
    return currentGroupData.value[current_category.value]?.map(item => item.id) || []
}

// 获取评委列表
const getTeacherList = async () => {
    try {
        isPaging.value = true // 标记为分页操作
        let res = await applyList(search_form.value)
        if(res){
            teacher_list.value = res.data.list
            total.value = res.data.total
        }
        
        // 恢复已选择的评委
        nextTick(() => {
            restoreSelectedTeachers()
            isPaging.value = false // 分页操作完成
        })
    } catch (error) {
        console.error('获取评委列表失败:', error)
        // 这里可以添加错误提示
        isPaging.value = false // 出错时也需要重置标记
    }
}

// 恢复已选择的评委
const restoreSelectedTeachers = () => {
    if (!multipleTableRef.value || !currentGroupData.value) return;
    
    const groupId = current_group.value;
    const category = current_category.value;
    
    // 从全局存储中获取该组、该类别的已选中数据
    const storedTeachers = allSelectedTeachers.value[groupId]?.[category] || {};
    const selectedIds = Object.keys(storedTeachers);
    
    // 1. 清空表格选择
    multipleTableRef.value.clearSelection();
    
    // 2. 重新选中已保存的数据
    teacher_list.value.forEach(teacher => {
        if (selectedIds.includes(teacher.id)) {
            multipleTableRef.value.toggleRowSelection(teacher, true);
        }
    });
    
    // 3. 更新界面显示（确保DOM已更新）
    nextTick(() => {
        // 将全局存储的数据同步到currentGroupData（响应式更新界面）
        currentGroupData.value[category] = Object.values(storedTeachers);
    });
};
// 处理表格选择变化
const handleSelectionChange = (val) => {
    // 如果是分页操作导致的选择变化，不更新数据
    if (isPaging.value) return
    
    if (!currentGroupData.value) return
    
    // 更新当前页的选择状态到全局存储
    const groupId = current_group.value
    const category = current_category.value
    
    // 初始化组和类别的存储
    if (!allSelectedTeachers.value[groupId]) {
        allSelectedTeachers.value[groupId] = {}
    }
    if (!allSelectedTeachers.value[groupId][category]) {
        allSelectedTeachers.value[groupId][category] = {}
    }
    
    // 获取当前页选中的评委ID
    const currentPageSelectedIds = val.map(item => item.id)
    
    // 更新全局存储
    teacher_list.value.forEach(teacher => {
        if (currentPageSelectedIds.includes(teacher.id)) {
            // 添加到选中列表
            allSelectedTeachers.value[groupId][category][teacher.id] = teacher
        } else {
            // 从选中列表移除
            delete allSelectedTeachers.value[groupId][category][teacher.id]
        }
    })
    
    // 更新当前组数据
    const selectedList = Object.values(allSelectedTeachers.value[groupId][category] || {})
    currentGroupData.value[category] = selectedList
    
    console.log(selectedList, '选中的评委')
}

// 页码变化处理
const currentChange = (page) => {
    search_form.value.pageNum = page
    getTeacherList()
}

const changeCategory = (newCategory) => {
    const prevCategory = current_category.value;
    const groupId = current_group.value;
    
    // 1. 保存当前类别（旧类别）的选中状态到全局存储（防止切换时被表格清空）
    if (prevCategory && groupId) {
        // 获取表格当前选中的行（可能包含旧类别未提交的临时选择）
        const currentSelectedRows = multipleTableRef.value?.selection || [];
        const currentSelectedIds = currentSelectedRows.map(item => item.id);
        
        // 初始化组和类别的存储（如果不存在）
        if (!allSelectedTeachers.value[groupId]) {
            allSelectedTeachers.value[groupId] = {};
        }
        if (!allSelectedTeachers.value[groupId][prevCategory]) {
            allSelectedTeachers.value[groupId][prevCategory] = {};
        }
        
        // 更新旧类别的全局存储：保留已选中的（包括当前页未提交的临时选择）
        teacher_list.value.forEach(teacher => {
            if (currentSelectedIds.includes(teacher.id)) {
                allSelectedTeachers.value[groupId][prevCategory][teacher.id] = teacher;
            } else {
                // 仅移除当前页未选中的（已提交的选中项不清除）
                if (!getSelectedIds().includes(teacher.id)) {
                    delete allSelectedTeachers.value[groupId][prevCategory][teacher.id];
                }
            }
        });
    }
    
    // 2. 切换类别
    current_category.value = newCategory;
    
    // 3. 恢复新类别的选中状态
    nextTick(() => {
        restoreSelectedTeachers(); // 从全局存储中读取新类别的数据并渲染
    });
};

// 添加新分组
const addGroup = () => {
    const newId = Math.max(0, ...group_list.value.map(item => item.id)) + 1
    group_list.value.push({
        id: newId,
        xtjz: [],
        sjylz: [],
        kxxfx: []
    })
    current_group.value = newId
}

// 移除分组
const removeGroup = () => {
    if (group_list.value.length <= 1) return
    const index = group_list.value.findIndex(item => item.id === current_group.value)
    if (index !== -1) {
        // 删除全局存储中的该组数据
        if (allSelectedTeachers.value[current_group.value]) {
        delete allSelectedTeachers.value[current_group.value]
        }
        group_list.value.splice(index, 1)
        // ✅ 直接取剩余分组的最后一个元素的ID（无需判断ID与数量关系）
        current_group.value = group_list.value[group_list.value.length - 1].id
    }
}

// 移除专家
const removeExpert = (list, id) => {
    const index = list.findIndex(item => item.id === id)
    if (index !== -1) {
        list.splice(index, 1)
        
        // 从全局存储中移除
        const groupId = current_group.value
        const category = current_category.value
        
        if (allSelectedTeachers.value[groupId] && allSelectedTeachers.value[groupId][category]) {
            delete allSelectedTeachers.value[groupId][category][id]
        }
        
        // 更新表格选择状态
        nextTick(() => {
            if (multipleTableRef.value) {
                const row = teacher_list.value.find(item => item.id === id)
                if (row) {
                    multipleTableRef.value.toggleRowSelection(row, false)
                }
            }
        })
    }
}

// 更改搜索条件
const changeExtend = () => {
    search_form.value.pageNum = 1
    getTeacherList()
}

// 更改用户名搜索
const changeUserName = () => {
    search_form.value.pageNum = 1
    getTeacherList()
}

// 生命周期钩子
onMounted(() => { 
    getTeacherList()
})

// 监听当前组或类别的变化，恢复已选择的评委
watch([current_group, current_category], () => {
    nextTick(() => {
        restoreSelectedTeachers()
    })
})
</script>


<style lang="scss" scoped>
.allocation-main{
    .tab-ul{ border-bottom: 1px solid #E2E3E6; margin-bottom:20px; display:inline-flex;
        li{ margin-right: 32px; color: #94959C; padding: 6px 0; cursor: pointer; }
        .active-tab{ font-weight: bold; color: #508CFF; border-bottom: 2px solid #508CFF; }
    }
    
    .group-ul{ width: 112px;
        li{ font-weight: 400; font-size: 16px; color: #2B2C33; line-height: 22px; text-align: left; padding: 10px 18px; cursor: pointer; }
        .active-group{ color: #508CFF; background: #DFEAFF; font-weight: bold; }
    }
    
    .group-add-btn{ margin-top: 20px;
        img{ width: 16px; }
    }
    
    .half-box{ width: 50%; padding-left: 20px; }
    
    .select-pw{ border: 1px solid #E2E3E6; border-radius: 0px 8px 8px 8px; padding: 16px 16px 46px; width: calc(50% - 112px); position:relative;
        .select-box{ border: 1px solid #E2E3E6; border-radius: 8px; padding: 8px; height: 88px; margin-top: 8px; overflow-y: auto;
            .selected-user{ background: #DFEAFF; border-radius: 8px; font-weight: 400; padding: 5px 16px; margin: 0 8px 8px 0; font-size: 16px; color: #508CFF; line-height: 22px; text-align: left;
                el-icon{
                    cursor: pointer;
                }
            }
        }
        .active-select{ border: 1px solid #508CFF; }
        .remove-group-btn{ position: absolute; bottom: 16px; right: 17px; cursor: pointer; }
    }
}
</style>    
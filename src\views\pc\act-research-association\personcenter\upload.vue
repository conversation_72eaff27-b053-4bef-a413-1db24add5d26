<template>
    <div>
        <!-- {{}} -->
        <div class="content-box  upload mt10" >
          <div class="flex_start title" @click="goCancel">
            <el-icon style="margin-right: 4px;"><ArrowLeft /></el-icon>
            上传日记
          </div>
               <el-form
                ref="addRef"
                :model="form"
                :rules="rules"
                label-width="auto"
                class="demo-ruleForm"
              style="margin-top:20px"
            >

            
            <!-- 1 -->
               <el-form-item label="日记标题"  label-position='top'>
                    <el-input v-model="form.title" placeholder="请输入日记标题"></el-input>
                </el-form-item>
                <el-form-item label="日记内容" prop="content" label-position='top'>
                     <WangEditor :html='form.content' @emitHtml='emitHtml' />
                </el-form-item>
                  <el-form-item label="选择主题" prop="clubTopicIds" v-if="userInfo.roles.indexOf('DIRECTOR')<0">
                    <div class="theme-value flex_start flex_wrap">
                        <ul class="flex_start tag-list flex_wrap">
                            <li style="margin-bottom:6px" :class="['flex_start',form.clubTopicIds.indexOf(item.id)>-1?'active':'']" v-for="item in themesList" :key="item.id" @click="choiceTag(item)">
                                <img src="@/assets/pc/tag-2.png" alt="" v-show="form.clubTopicIds.indexOf(item.id)>-1">
                                <img src="@/assets/pc/tag.png" alt="" v-show="form.clubTopicIds.indexOf(item.id)<0">
                                <p>{{item.name}}</p>
                            </li>
                        </ul>
                     </div>
                </el-form-item>
                <el-form-item :label="userInfo.roles.indexOf('DIRECTOR')>-1||userInfo.roles.indexOf('MEMBER')>-1?'发布日期':'打卡日期'" prop="recordDate">
                   <el-date-picker
                        v-model="form.recordDate"
                        type="date"
                        value-format="YYYY-MM-DD"
                        :disabled-date="disabledDate"
                        placeholder="选择日期">
                    </el-date-picker>
                </el-form-item>
            </el-form>
        </div>
        <div class="btn-list flex_center">
             <el-button class="primary" @click="goSubmit(1)" :loading='loading'>提交</el-button>
             <!-- <el-button class="save" @click="goSubmit(2)" :loading='loading'>保存</el-button> -->
             <el-button class="cancel" @click="goCancel" :loading='loading'>取消</el-button>
        </div>
    </div>
  
</template>
<script setup>
import WangEditor from '@/views/pc/component/wangeditor.vue'
import {themeEdit,themes,draftSave,draftSubmit,draftGet,squareDetail,diaryEdit} from '@/api/pc/index.js'
import {onMounted, ref} from 'vue'
import {useRouter,useRoute} from 'vue-router'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
let userInfo=useUserInfoStoreHook()
import {ElMessageBox,ElMessage} from 'element-plus'
let form=ref({
    recordDate:'',
    content:'',
    clubTopicIds:[],
    title:''
})
let recordId=ref('')
let router=useRouter()
let route=useRoute()
let loading=ref(false)
let addRef=ref()
let rules=ref({
    recordDate:[{required: true,message: '请选择打卡日期',trigger: 'change'}],
    content:[{required: true,message: '请输入日记内容',trigger: 'change'}],
    clubTopicIds:[{required: true,message: '请选择主题',trigger: 'change'}],
})
let themesList=ref([])
let getThemes=async()=>{
    let res=await themes()
    if(res){
       themesList.value=res.data
    }
}
let getDetail=async()=>{
    let res=await squareDetail({
          recordId:recordId.value
    })
    if(res){
       
        res.data.topics=res.data.topics?(res.data.topics.map(item=>{
            return item.id
        })):[]
        
        form.value={
             recordDate:res.data.recordDate?res.data.recordDate:'',
            content:res.data.content?res.data.content:'',
            clubTopicIds:res.data.topics,
            title:res.data.title
        }
    }
}
let emitHtml=(val)=>{
    form.value.content=val
}
let goSubmit=async(type)=>{
   if (!addRef.value) return
  await addRef.value.validate((valid, fields) => {
    if (valid) {
       ElMessageBox.confirm(
            recordId.value?'确认编辑该日记吗?':'确认提交该日记吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
             loading.value=true
            let params={
                recordDate: form.value.recordDate,
                content: form.value.content,
                clubTopicIds: form.value.clubTopicIds,
                title:form.value.title
            }
           // if(type==1){
            if(recordId.value){
                params.recordId=recordId.value
                 diaryEdit(params).then(data=>{
                    loading.value=false
                    ElMessage({
                        type: 'success',
                        message: '编辑成功',
                    })
                    router.go(-1)
                }).catch(err=>{
                    loading.value=false
                })
            }else{
               draftSubmit(params).then(data=>{
                    loading.value=false
                     let msg='提交成功  '+(data.actionPoint?(data.actionPoint+'分'):'')
                       ElMessage.success(msg)
                        ElMessage({
                            type: 'success',
                            message: msg,
                        })
                    router.go(-1)
                }).catch(err=>{
                    loading.value=false
                })
            }
               

           
            // }else{
            //       draftSave(params).then(data=>{
            //         loading.value=false
            //         ElMessage({
            //                 type: 'success',
            //                 message: '保存成功',
            //             })
            //             router.go(-1)
            //     }).catch(err=>{
            //         loading.value=false
            //     })
               
            // }
            
            
        })
        .catch(() => {
      
        })
    } else {
      console.log('error submit!', fields)
    }
  })
}

let goCancel=()=>{
     router.push('/actResearchAssociation/personcenter')
}
let choiceTag=(row)=>{
  form.value.clubTopicIds=[row.id]
  
    // if(form.value.clubTopicIds.indexOf(row.id)>-1){
    //    form.value.clubTopicIds=form.value.clubTopicIds.filter(item=>{
    //          return item!=row.id
    //    })
    // }else{
    //     form.value.clubTopicIds.push(row.id)
    // }
}
let disabledDate=(time)=>{
   
    const today = new Date();
          // 计算七天前的日期（包含今天的前七天）
          const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
          // 禁用早于七天前或晚于今天的日期
          return time.getTime() > today.getTime();
    
}
onMounted(()=>{
    recordId.value=route.query.id
    getThemes()
    if(recordId.value){
        getDetail()
    }
    
})
</script>
<style lang="scss" scoped>
.upload{
    background: #fff;
    border-radius: 4px;
    padding: 18px 24px 24px 24px;
    .title{
        font-weight: bold;
        font-size: 20px;
         color: #2B2C33;
    }
    .label{
            font-size: 18px;
            color: #2B2C33;
            font-weight: bold;
            margin-right: 10px;
        }
    .editor{
        margin-top: 20px;
    }
     .tag-list{
            li{
                padding: 4px 10px;
                border-radius: 100px;
                font-size: 16px;
                border: 1px solid #E2E3E6;
                color: #2B2C33;
                cursor: pointer;
                margin-right: 10px;
                cursor: pointer;
                img{
                    width: 16px;
                    height: auto;
                    margin-right: 8px;  
                }
               p{
                line-height: 100%;
               }
               &.active{
                background: linear-gradient( 270deg, #535DFF 0%, #508CFF 100%);
                color: #fff;
               }
            }
            
        }
    // .theme{
    //     margin-top: 30px;
       
    //     .add{
    //              width: 30px;
    //              height: auto;cursor: pointer;
    //         }
    // }

}
.btn-list{
    margin-top: 24px;
    margin-bottom: 10px;
    .primary{
        width: 120px;
        height: 44px;
        background: #508CFF;
        border-radius: 4px;font-weight: 500;
        font-size: 18px;
        color: #FFFFFF;
        outline: none;border:none; cursor: pointer;margin-right: 24px;
    }
    .save{
        width: 120px;
        height: 44px;
        background: #fff;border: 1px solid #508CFF; cursor: pointer;
        border-radius: 4px;font-weight: 500;
        font-size: 18px;
        color: #508CFF; cursor: pointer;
        outline: none;margin-right: 24px;
    }
    .cancel{
        cursor: pointer;
        width: 120px;
        height: 44px;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E2E3E6;
        font-size: 18px;
        color: #94959C;
    }
}
.app-scrollbar .app-container-grow{
    flex-grow: 0 !important;
}
:deep(.el-form-item__label){
    font-weight: bold;
    font-size: 18px;
}
:deep(.el-input__wrapper),:deep(.el-input__wrapper),:deep(.el-date-editor.el-input){
    height: 40px;
}
</style>
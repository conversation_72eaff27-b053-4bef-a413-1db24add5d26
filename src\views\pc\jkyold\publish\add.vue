<template>
    <div class="publish-add-main">
        <el-form :model="add_form" ref="addRef" :rules="rules" class="search_form">
            <el-form-item label="课题名称" prop="taskTitle" style="margin-right: 16px;">
                <el-input v-model="add_form.taskTitle" @input="changeUserName" class="input_48" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="备注" style="margin-right: 16px;">
                <el-input v-model="add_form.taskContent" @input="changeUserName" class="input_48" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="材料清单" style="margin-right: 16px;" prop="reviewSections">
                <p class="text_12 grey upload-tip">（格式为word、pdf）</p>
                <div class="upload-file-box">
                    <div class="flex_between mb_16" v-for="(item,index) in add_form.reviewSections" :key="index" style="width:100%;">
                        <p class="flex_start text_16 blue" style="width:calc(100% - 24px)"><img src="@/assets/pc/jky/file.png" class="file-icon" alt=""><span class="ellipsis1 file-name">{{item.name}}</span></p>
                        <img @click="removeFile(index)" src="@/assets/pc/del-file.png" class="file-close-icon pointer" alt="">
                    </div>
                </div>
                <el-upload
                    :file-list="add_form.reviewSections"
                    action="Fake Action"
                    show-file-list="false"
                    multiple
                    :http-request="uploadFile"
                    :on-preview="handlePictureCardPreview"
                    :on-remove="handleRemove"
                    :before-upload='beforeUpload'
                >
                    <!-- :on-change="changeFile" -->

                    <el-button type="plain" class="upload-plain">本地上传</el-button>
                </el-upload>
            </el-form-item>
        </el-form>
        <div class="flex_end">
            <el-button class="plain-btn btn_96_48" type="plain" @click="emit('close')">取消</el-button>
            <el-button class="primary-btn btn_96_48" type="primary" @click="submitClick">确认</el-button>
        </div>
    </div>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import {uploadFileFunc} from '@/utils/pc-upload.js'
    import {ElMessage,ElLoading,ElMessageBox} from 'element-plus'
    import { taskAdd, taskEdit, taskDetail } from '@/api/pc/jky'
    const formObj = function(obj = {}){
        this.taskTitle = obj.taskTitle || ''
        this.taskContent = obj.taskContent || ''
        this.reviewSections = obj.reviewSections || []
    }
    const props = defineProps({
        id: 0, // 默认值
    });
    const addRef = ref(null)
    const rules = ref({
        taskTitle:[{required: true,message: '请输入课题名称',trigger: 'blur'}],
        reviewSections:[{required: true,message: '请上传材料清单',trigger: 'blur'}]
    })
    const emit = defineEmits(['close'])
    const add_form = ref(new formObj({}))
    function getQueryParams(src,type) {
        const url = new URL(src);
        const params = url.searchParams.get(type);
        return params
    }
    const getExtensionString = (str) => {
        var index = str.lastIndexOf('.')
        // 如果找到了第一个"."并且它不在字符串的起始处或结尾处
        if (index != -1 && index != 0 && index + 1 != str.length) {
            return str.substr(index + 1)
        } else {
            return ''
        }
    }
    const beforeUpload=(file)=>{
        let type = getExtensionString(file.name)
        let allowType=['doc','docx','pdf']
        if(allowType.indexOf(type)>-1){
            return true
        }else{
            ElMessage.warning('请上传以下格式的文件'+allowType.join('，'))
            return false
        }
    }
    const uploadFile = (file) => {
        uploadFileFunc(file.file,(url) => {
            add_form.value.reviewSections.push({
                url:url,
                name:file.file.name,
                fileId:getQueryParams(url,'fileId')
            })
            console.log(add_form.value.reviewSections,'上传文件')

        })
    }
    const removeFile = (index) => {
        add_form.value.reviewSections.splice(index, 1)
    }
    const getDetail = async () => {
        let res = await taskDetail(props.id)
        if(res){
            add_form.value = new formObj(res.data)
            add_form.value.reviewSections.forEach(item => {
                item.name = item.fileName
            })
            // add_form.value.reviewSections = add_form.value.reviewSections.map(item => item.name = item.fileName)
        }
    }
    const submitClick = async () => {
        if (!addRef.value) return
        await addRef.value.validate(async(valid, fields) => {
            if (valid) {
                let res = await (props.id && props.id != 0 ? taskEdit(add_form.value, props.id) : taskAdd(add_form.value))
                if(res){
                    ElMessage.success('发布成功')
                    emit('close')
                }
            } else {
                console.log('error submit!', fields)
            }
        })
        
    }
    const closeClick = () => {
        emit('close')
    }
    onMounted(() => {
        if(props.id && props.id != 0){
            getDetail()
        }
    })
</script>
<style lang="scss" scoped>
.publish-add-main{
    .upload-file-box{border: 1px solid #E2E3E6;border-radius: 8px;padding: 14px 16px 0;flex:1;min-height: 48px;margin-right: 8px;width: calc(100% - 104px );}
    .file-icon{width: 17px;height: auto;margin-right: 8px;}
    .file-close-icon{width: 24px;height: auto;}
    .file-name{width: calc(100% - 40px);}
    .upload-tip{position: absolute;top: -29px;left: 74px;}
}
</style>
<style lang="scss">
    .el-upload-list{display: none;}
</style>
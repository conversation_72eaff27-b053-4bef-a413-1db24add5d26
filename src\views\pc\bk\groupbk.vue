<template>
    <div class="content-box  question mt10">
       <div class="flex_between top">
            <div class="flex_start title">
               <!-- <el-icon style="font-size:20px"><ArrowLeftBold /></el-icon> -->
                <h3>集体备课</h3>
            </div>
       </div>
       <el-table :data="tableData" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F9F9F9"}'>
           <el-table-column prop="name" label="序号">
                <template #default='scope'>
                    {{((form.pageNum-1)*form.pageSize)+(scope.$index+1)}}
                </template>
             </el-table-column>
            <el-table-column prop="name" label="备课组名称"  />
            <el-table-column prop="subjectName" label="学科" />
            <el-table-column prop="gradeName" label="年级" />
            <el-table-column prop="leaderName" label="备课组组长" />
            <el-table-column prop="memberNum" label="备课组人员数量" />  
            <el-table-column prop="address" label="操作">
                <template #default='scope'>
                    <el-button type="primary" link style="font-size:16px" @click="goOnline(scope.row)">在线研讨</el-button>
                </template>
            </el-table-column>
        </el-table>
          <el-pagination
                background
                layout="total,prev, pager, next"
                :total="total"
                v-model:page-size='form.pageSize'
                class="mt-4"
                @current-change='currentChange'
         />
     
    </div>
</template>
<script setup>
import {onMounted, reactive, ref} from 'vue'
import {bkGropuMy,makeWhiteBoard} from '@/api/pc/bk.js'
let tableData=ref([{}])
let total=ref(0)
let form=reactive({
    pageSize:10,
    pageNum:1
})

let goOnline=async(row)=>{
  let res=await makeWhiteBoard({
    groupId:row.id
  })
  if(res){
    console.log(res)
    window.open(res.data)
  }
}

let currentChange=(val)=>{
    Object.assign(form,{
        pageNum:val
    })
     getList()
}
let getList=async()=>{
    let res=await bkGropuMy(form)
    if(res){
        tableData.value=res.data.list
        total.value=res.data.total
    }
}
onMounted(()=>{
    getList()
})
</script>
<style scoped lang='scss'>
.content-box{
    background: #fff;
    padding: 24px;
    .top{
        .title{
            cursor: pointer;
           h3{
            font-size: 20px;
             color: #2B2C33;
           }
        }
        button{
            width: 132px;
            height: 40px;
            background: #508CFF;
            border-radius: 8px;
            border: 1px solid #508CFF;
            border:none;
            cursor: pointer;
            color: #FFFFFF;
            img{
                width: 16px;
                height: auto;
                margin-right: 8px;
            }
        }
    }
}
</style>
var Di=Object.defineProperty;var Ei=(a,e,t)=>e in a?Di(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t;var te=(a,e,t)=>Ei(a,typeof e!="symbol"?e+"":e,t);import{r as ie,az as va,j as Ni,h as ga,ag as Ce,z as ae,A as Q,B as F,Q as be,u as X,P as i0,a6 as n0,E as qi,K as Be,O as rt,I as Oe,H as w0,J as Pt,R as Vt,M as Gt,m as Ii,aE as Li,W as Oi,n as zt}from"./vue-CelzWiMA.js";import{_ as Hi}from"./logo-CxUTONFc.js";import{_ as Fi}from"./cleanall-Da4K8AGg.js";import{_ as ba}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{a as Pi}from"./element-BwwoSxMX.js";import{l as dt,c as Vi}from"./index-DCRFd5B4.js";import"./vant-C4eZMtet.js";const ya="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAAAoCAYAAABgi917AAAAAXNSR0IArs4c6QAABN9JREFUaEPtmU1SG0cUx/9vZMfsrCMMVZYqu4gTxNwAThBY2Mwu6ATYJ7DZCXsBN4Ab4JzAyi4WrkK5gbJTsJmXea1u0e70fGnaonCNNqrStF53/973G0L7CUqAgkprhaEFGtgIWqAt0MAEAotrLbQFGphAYHGthd4H0J8TjudzzKZnNAu8/w8nrtRCBWYKXIIxu/kX2y3UYhsoBNp7wQPq4BxArMW8mozo9brMSpT514imdfaL97i7sYFummLAhF8JiCPC0JUjsuvILVtr5OcCFZiIcEmE7rphCpSfnuAShG4EbLsw+i95BxF+YUZXnY/VGWNZbyl/yYCBD1/m2LW9q58wl0Gq8zwzNMXSC7SX8B4Bp5bAtVqm7Jtd+Cj7fgVg6kLtJXxJwHPnwlMJSyCMefH9JwPTr3OMfWFKgMo6IlzUAeeuZcaOKDUX6LOXfBhFeGOpd8yE47JNKcXsJsJ4WtNFi+QuoTLGdvw2QCNgs25IMPtpC51ORrRZdreSM16LV3iBPkv4eSQJqMFH3ItSHE/eUSPNLy9+wOeZ1e0gxa6RaYCaS6xy3LUAdVxtlXPacevsyxzDplWBxNNHGxh8HtEHI/xBAc2BWhpD44TjRyl2IsJvIAz05Ru7lE+rOTE01wBSYNtWiL6jJKVvzqdDTGH2Z+CPqxGdWaEj3+XtEzVJTL0DfkOEQ5GXZeK3Vyc0bGTuzp8toMuL+eSbhOGLtT6Xr6ios8mI9msDlT8IVDAEjiqd0hTDz+/obRU4NlSfhRTJkJKNO8tyTS21Lax/wB/FC4piqG5IriWmX41o292vCKhPrpGXJaDVgSqod/XozFcTVsmA7iHKFNJPWLmRvc6+pH4OydDipgz8bbuhdmkp+/ayZ/vuszyXL4rNwYAaqJ0OZnVLFNtK62RkXbo91SHj0K7zDAyxvA6wr9riBfxlnLcunxvD781Cy6yp0EIXHY20rrgBNlepUY21GoXodvgjGBeTE9oVeLfAqSr0GRcp4ThaNCVxUZ36IIHadW3dOJoX9E2ydOO51VmZvxZWJg8SqO65w1qobjv5FltX72ls6Em9+vgJrk0CdZ/XSUoSPtz1em4glUuzpNTI5e/6cUkgpaNC3162y+fFRnsqJrE1c/2BgKUUw085Vcm9lE1NYH5jMTrerSLPBtpPWGVuA0pNpTbwu7Eo87szKfO6vg+oGf0VndMdtLsxfiWrqQKml7Akij1Vv3o6lSoydEZXJZTI0HMGlblVucRQFYB0PCmwb9eqy8E4EPssdW29fNWL5q3TlnkUolOSpEaMc4EmGVv2/ArEAs64OQOvfXWmKfcowmlE2P3fTHUxD23cGn8XC1UQHyNGBwMCZI65KMoZ48kJba2iJGPharYpg1tCV9rYDuG4bj2cE5sVUGlWVjmf+Y+pg+sOmGvvuWoPb03rB6IQmYOqVxoLRakQIr8LDBkmLy+W4p9sjquaAeujFMtawXYLet8T++pAdXHtTneqC1AzBJnIxzdzbNnjPx0XjySLWxOtaqKdxLi2ib2cTncf7iuG3IPLtD6NMMt73VDtxnerzAu0MteWdRJTXfl0i5m0yuZ33ytwiX3yisQ3OKlzXqN8M/n/blm+zqF+pLUt0MDabIG2QAMTCCyutdAWaGACgcW1FtoCDUwgsLj/AG91JlZWOGv7AAAAAElFTkSuQmCC",xa="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAyVJREFUaEPVmj9o1FAYwH9fzp61WJo7RXEVC8X6rybtpCDiIg66VBcdFBV0cRHb5BCqONTNRQfFDnXRSQdxEaHoJIg4KA7irIOiHQQV208T22uvSZvk7pK7ZMmf+/Lu93vfe3mPvIhd0UlmmHg1LlPkcBPb0fsIx1CmUK7kTUQsVy8I3KhWfs5ExK7oEMrLQOvJiYhYZ7VD1jGNsCa0C7S5iHjQlqvPBfau2IfbVMQXGHR1XGEk1kOozUR8Abuih1EexRKYD2oTEV9gx0XdUCzyGfDPE20tFqkCW65+ENiSCH5xcItEqgK2o5MIJ+oWaFHTWsiAo+dEuNWwQMYiVYGBEd1ZKPCmaQIZiSx02mEt2L18A7qbLuEVmFIfqXnq2I4+RTiQikBKGakRsFy9KnA5VYEmi9RmwNWDwJNMBJokUiOwa0zNwm++ChiZSjTQRwIjr+XqO4GtmQvUmZEwgTsCp1smkFAkIGC7egq423KBmCLBDDjaJ8L7thGIEAmZfapYDkOqlIwCpVmlLEoJKAmUVPx92ftdxL9eBroyE14yICafPoeQ9o9psesH5RmhVOigNDNL2fBkDUqevHryStnfi3/uV8pcZayuS35OpCkCjKmx/Sc98gezuApTDUydxUQwDcFU9Y97UEwRTKhe8443CRQTSywvoGK57FsMAPQomGEA/17JmArdmY0dUU1ocET7tcDbxDWS9g3LTAYDTWi3o2cM4XbaPLHLj5jFBscBRycQTsb+g7QCY06/wwYybwzoS4srstyY4PPl1Aj0j2m58xdfROp4OxFJFhGQEDxUwHL1kMDjRlkS3V8neKiA7eo1oJIIoN7gBsHDM+DoMxH218sU674mgQcFhrVg9fJdYG0skKRBTQYPCNijOoDB66RckfEpgQcErFE9LwY3I4HiBqQMHsyAq/eA43H5lo3LCDwo4OhHhM11C2QMXiOwzdWNnfApt6/XLUePiPAwUe23qMaXMv5foXH0OsKlWAJtAl7ThCxXXwjsWVGgzcCrAv4y63qmIafLrPlf6M77pwaWqw8Ejqa1ABHrwdBAkPetRK4/t/kLJg6kFvotFZUAAAAASUVORK5CYII=",wa="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAkxJREFUaEPV2u9RwjAYBvDnBY+vdgNZwDtGwA0YgRHcQJnAETwncITUCXqnlK+O4AT6SppSDU1LmjRtmi+eB6HPL3/61gilOT/zD17ubinFBBulOb8C2ICQ8jd2U4OQ2PM9EZ6qwZ8YhMQHr2kGUVs9E4GQDJ7mzI3LP3KIAuxZHPfAunUPRwopAG8HfmLGvdVNKDJIARDvvKE55N3IvkUCUYCME1rgyz79v3eODCkA5UbOAKycELLTSJD/AFXQfNvAkApQK2gTgfwBcl4RIJdRvy3wjFSAch/IjZz0Kyg/LRBEB9gUNF9dzxAN8Jbzw/GZ4tE3o1X/niAaoPHBziqR45s8ITrAp6A55q+6OUI0QC8FbWCICfAMYOubw7u/5YzUACLnLQESEUe7ADEBwhQ03+FogNQA8joi5xV+kGCGpfx9BtyAkDAXRS6B/EnqtbLwhSl+JvQZxAhwGSyRsQJdYVkA5wo6I1y34E+D0P2SJaRPgJodCZCtCcHl61TOpnp06T6DjJQZO/MS+uB18ADdx1z1KIOfzq/qmzjjJS3w6fr5wfqdBT9dJ/7baEPwRoA8K42ikF0I3gaQy8f97uC7hiyDGwFepxMDBzcDXM6HRgpuBHQ6oRs5uBGQHlgc77PtZ6SRBDcDcg73R33HzWk7TuGPVQIFr81A7wdbgYPXAL0VsIGCmwB+BWzg4BrAq4CNFFwHuBSwkYNrgE4FLJLgGsCqgEUWXAe0FbBIg1cAeQJh/L9A5MH/AOdfNZhI8ApQFbCJBdcAU/66zS9hU67i1WjH2gAAAABJRU5ErkJggg==",Gi="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAedJREFUSEu9Vs1ZwkAQnUmAs3aAN76EQ4AC1AqEDqADqACoADuQDsQKxALAHCAfN+jA3Mnu+O0mi0F246If5ro7783P2zdBKPhqtaDquk4bEB8AIACAK3GdiHaAGCLRW7nMp2EYxiYY1B2kwO4QELpFCagzApjxhA02m3D3/f4Jgec1u+jgRGVrQ5DdiYnTIIqW03zMEYFXbw4RcHQG6MlVAhpFq+VYHRwIssyf/gJ+aBmnnqpEEsiel9z3X7TFlE/MEtYQM5EEXr31jABtze0wU48RiIhiRKxqejVdrxc9DILgap+4Hzrw9WrRKJhLTJzdizh0ZPUnH0vYDfp+sw8oVWMcmIZEglcqsNsn7quxSqIBFrRHEipV5EjswNN05+j5za22h7l6FImolojPf8z8KzZGv94iG2kqkmxm5rZ8A7MlkG2JolCoCs55kIJAKEiamOE76jkBzcRLtSERpigIRLl3NuBKLZrBa8OFCRbLlLOGaaBWJEKmmU1sdSlwhI5DMDTqHOkx1TL2dfHlEruWVuH7rSdb77dRXPp+YBatFp3/MTspvXTRXMauVdk20vupRcaFcyC55MpUJEJZTsmdGHaEznanjLGx1dLPR0vf2TtdQrwFoiBniuI3JQSiF8b4TAescD4BwVIjyrRn6gEAAAAASUVORK5CYII=",c0=[{id:1,title:"小学",grade:[{id:1,title:"一年级",subject:[{id:1,title:"语文",version:[{id:"1",title:"人教版"}]},{id:2,title:"数学",version:[{id:"1",title:"苏教版"}]},{id:3,title:"英语",version:[{id:"1",title:"译林"}]},{id:4,title:"道德与法治",version:[{id:"1",title:"人教版"}]},{id:5,title:"科学",version:[{id:"1",title:"苏教版"}]},{id:6,title:"美术",version:[{id:"1",title:"人美版"},{id:"2",title:"江苏凤凰少年儿童出版社"}]},{id:7,title:"体育与健康",version:[{id:"1",title:"人教版"}]},{id:8,title:"音乐",version:[{id:"1",title:"苏少版"}]},{id:9,title:"劳动与技术",version:[{id:"1",title:"江苏凤凰科学技术出版社"}]}]},{id:2,title:"二年级",subject:[{id:1,title:"语文",version:[{id:"1",title:"人教版"}]},{id:2,title:"数学",version:[{id:"1",title:"苏科版"}]},{id:3,title:"英语",version:[{id:"1",title:"译林"}]},{id:4,title:"道德与法治",version:[{id:"1",title:"人教版"}]},{id:5,title:"科学",version:[{id:"1",title:"教科版"}]},{id:6,title:"美术",version:[{id:"1",title:"人美版"}]},{id:7,title:"体育与健康",version:[{id:"1",title:"人教版"}]},{id:8,title:"音乐",version:[{id:"1",title:"苏少版"}]},{id:9,title:"劳动与技术",version:[{id:"1",title:"江苏凤凰科学技术出版社"}]}]},{id:3,title:"三年级",subject:[{id:1,title:"语文",version:[{id:"1",title:"人教版"}]},{id:2,title:"数学",version:[{id:"1",title:"苏教版"}]},{id:3,title:"英语",version:[{id:"1",title:"译林版"}]},{id:4,title:"道德与法治",version:[{id:"1",title:"人教版"}]},{id:5,title:"科学",version:[{id:"1",title:"苏教版"}]},{id:6,title:"美术",version:[{id:"1",title:"江苏凤凰少年儿童出版社"}]},{id:7,title:"体育与健康",version:[{id:"1",title:"人教版"}]},{id:8,title:"音乐",version:[{id:"1",title:"苏少版"}]},{id:9,title:"劳动与技术",version:[{id:"1",title:"苏少版"}]},{id:10,title:"综合实践活动",version:[{id:"1",title:"苏教版"}]},{id:11,title:"信息技术",version:[{id:"1",title:"凤凰科技"}]}]},{id:4,title:"四年级",subject:[{id:1,title:"语文",version:[{id:"1",title:"人教版"}]},{id:2,title:"数学",version:[{id:"1",title:"苏教版"}]},{id:3,title:"英语",version:[{id:"1",title:"译林版"}]},{id:4,title:"道德与法治",version:[{id:"1",title:"人教版"}]},{id:5,title:"科学",version:[{id:"1",title:"苏科版"}]},{id:6,title:"美术",version:[{id:"1",title:"人教版"}]},{id:7,title:"体育与健康",version:[{id:"1",title:"人教版"}]},{id:8,title:"音乐",version:[{id:"1",title:"苏少版"}]},{id:9,title:"劳动与技术",version:[{id:"1",title:"凤凰科技"}]},{id:11,title:"信息技术",version:[{id:"1",title:"2018苏教版"}]}]},{id:5,title:"五年级",subject:[{id:1,title:"语文",version:[{id:"1",title:"人教版"}]},{id:2,title:"数学",version:[{id:"1",title:"苏教版"}]},{id:3,title:"英语",version:[{id:"1",title:"译林版"}]},{id:4,title:"道德与法治",version:[{id:"1",title:"人教版"}]},{id:5,title:"科学",version:[{id:"1",title:"凤凰教育出版社"}]},{id:6,title:"美术",version:[{id:"1",title:"江苏凤凰少年儿童出版社"}]},{id:7,title:"体育与健康",version:[{id:"1",title:"人教版"}]},{id:8,title:"音乐",version:[{id:"1",title:"苏少版"}]},{id:9,title:"劳动与技术",version:[{id:"1",title:"江苏凤凰科学技术出版社"}]},{id:11,title:"信息技术",version:[{id:"1",title:"江苏凤凰科学技术出版社"}]}]},{id:6,title:"六年级",subject:[{id:1,title:"语文",version:[{id:"1",title:"人教版"}]},{id:2,title:"数学",version:[{id:"1",title:"苏教版"}]},{id:3,title:"英语",version:[{id:"1",title:"译林版"}]},{id:4,title:"道德与法治",version:[{id:"1",title:"人教版"}]},{id:5,title:"科学",version:[{id:"1",title:"苏教版"}]},{id:6,title:"美术",version:[{id:"1",title:"江苏凤凰少年儿童出版社"}]},{id:7,title:"体育与健康",version:[{id:"1",title:"人教版"}]},{id:8,title:"音乐",version:[{id:"1",title:"人教版"}]},{id:9,title:"劳动与技术",version:[{id:"1",title:"人教版"}]},{id:10,title:"综合实践活动",version:[{id:"1",title:"江苏凤凰少年儿童出版社"}]},{id:11,title:"信息技术",version:[{id:"1",title:"江苏凤凰科学技术出版社"}]}]}]},{id:2,title:"初中",grade:[{id:1,title:"初一年级",subject:[{id:1,title:"语文",version:[{id:"1",title:"人教版"}]},{id:2,title:"数学",version:[{id:"1",title:"苏教版"}]},{id:3,title:"英语",version:[{id:1,title:"译林版"}]},{id:4,title:"历史",version:[{id:1,title:"人教版"}]},{id:5,title:"地理",version:[{id:1,title:"人教版"}]},{id:6,title:"生物",version:[{id:1,title:"苏教版"},{id:2,title:"苏科版"}]},{id:7,title:"道德与法治",version:[{id:1,title:"人教版"}]},{id:8,title:"美术",version:[{id:"1",title:"苏少版"}]},{id:9,title:"体育与健康",version:[{id:"1",title:"人教版"},{id:"2",title:"华师大版"}]},{id:10,title:"音乐",version:[{id:"1",title:"苏少版"},{id:"2",title:"人音版"}]},{id:11,title:"综合实践活动",version:[{id:"1",title:"江苏凤凰教育出版社"}]},{id:12,title:"信息技术",version:[{id:"1",title:"江苏凤凰科学技术出版社"}]},{id:13,title:"劳动与技术",version:[{id:"1",title:"苏教版"}]}]},{id:2,title:"初二年级",subject:[{id:1,title:"语文",version:[{id:"1",title:"人教版"}]},{id:2,title:"数学",version:[{id:"1",title:"人教版"},{id:"2",title:"苏科版"}]},{id:3,title:"英语",version:[{id:"1",title:"译林版"}]},{id:4,title:"物理",version:[{id:"1",title:"苏科版"}]},{id:5,title:"历史",version:[{id:"1",title:"人教版"}]},{id:6,title:"地理",version:[{id:"1",title:"人教版"}]},{id:7,title:"生物",version:[{id:"1",title:"苏科版"}]},{id:8,title:"道德与法治",version:[{id:"1",title:"人教版"}]},{id:9,title:"美术",version:[{id:"1",title:"人教版"},{id:"2",title:"江苏凤凰少年儿童出版社"}]},{id:10,title:"体育与健康",version:[{id:"1",title:"体育与健康教学设计探微（体育教师备课用书）"},{id:"2",title:"人教版"}]},{id:11,title:"音乐",version:[{id:"1",title:"苏少版"}]},{id:12,title:"综合实践活动",version:[{id:"1",title:"苏科版"},{id:"2",title:"校本"}]},{id:13,title:"信息技术",version:[{id:"1",title:"江苏凤凰科学技术出版社"}]},{id:14,title:"劳动与技术",version:[{id:"1",title:"苏科版"}]}]},{id:3,title:"初三年级",subject:[{id:1,title:"语文",version:[{id:"1",title:"人教版"}]},{id:2,title:"数学",version:[{id:"1",title:"苏科版"}]},{id:3,title:"英语",version:[{id:1,title:"译林版"}]},{id:4,title:"物理",version:[{id:"1",title:"苏科版"}]},{id:5,title:"历史",version:[{id:1,title:"人教版"}]},{id:6,title:"化学",version:[{id:1,title:"人教版"}]},{id:8,title:"道德与法治",version:[{id:1,title:"人教版"}]},{id:9,title:"美术",version:[{id:1,title:"江苏凤凰少年儿童出版社"}]},{id:11,title:"音乐",version:[{id:1,title:"人教版"}]},{id:13,title:"信息技术",version:[{id:1,title:"人教版"}]},{id:14,title:"劳动与技术",version:[{id:1,title:"人教版"}]}]}]},{id:3,title:"高中",grade:[{id:1,title:"高一年级",subject:[{id:1,title:"语文",version:[{id:1,title:"人教版"}]},{id:2,title:"数学",version:[{id:1,title:"苏教版"}]},{id:3,title:"英语",version:[{id:1,title:"译林版"}]},{id:4,title:"物理",version:[{id:"1",title:"人教版"}]},{id:5,title:"历史",version:[{id:"1",title:"人教版"},{id:"2",title:"部编版"}]},{id:6,title:"化学",version:[{id:1,title:"苏教版"}]},{id:7,title:"地理",version:[{id:"1",title:"人教版"}]},{id:8,title:"生物",version:[{id:"1",title:"人教版"}]},{id:9,title:"思想政治",version:[{id:"1",title:"人教版"}]},{id:10,title:"美术",version:[{id:"1",title:"人教版"},{id:"2",title:"人美版"}]},{id:11,title:"体育与健康",version:[{id:"1",title:"人教版"}]},{id:12,title:"音乐",version:[{id:"1",title:"人音版"}]},{id:13,title:"信息技术",version:[{id:"1",title:"教科版"}]},{id:14,title:"通用技术",version:[{id:"1",title:"苏教版"}]}]},{id:2,title:"高二年级",subject:[{id:1,title:"语文",version:[{id:"1",title:"部编版"},{id:"2",title:"苏教版"}]},{id:2,title:"数学",version:[{id:"1",title:"部编版"},{id:"2",title:"苏教版"}]},{id:3,title:"英语",version:[{id:1,title:"译林出版社"},{id:2,title:"译林版"}]},{id:4,title:"物理",version:[{id:1,title:"人教版"},{id:2,title:"人民教育出版社"}]},{id:5,title:"历史",version:[{id:1,title:"人教版"},{id:"2",title:"部编版"}]},{id:6,title:"化学",version:[{id:1,title:"人教版"}]},{id:7,title:"地理",version:[{id:1,title:"湘教版"},{id:2,title:"人教版"}]},{id:8,title:"生物",version:[{id:1,title:"人教版"},{id:2,title:"苏教版"}]},{id:9,title:"思想政治",version:[{id:1,title:"人教版"},{id:2,title:"统编版"}]},{id:13,title:"信息技术",version:[{id:1,title:"教科版"}]}]},{id:3,title:"高三年级",subject:[{id:1,title:"语文",version:[{id:1,title:"复习"},{id:2,title:"唐宋八大家散文选读（选修）"}]},{id:2,title:"数学",version:[{id:"1",title:"苏教版"}]},{id:3,title:"英语",version:[{id:1,title:"译林版"}]},{id:4,title:"物理",version:[{id:1,title:"人教版"}]},{id:5,title:"历史",version:[{id:"1",title:"人教版历史必修1"},{id:"2",title:"人教版历史必修2"},{id:"3",title:"人教版历史必修3"},{id:"4",title:"人教版历史选修1"},{id:"5",title:"人教版历史选修4"}]},{id:6,title:"化学",version:[{id:"1",title:"苏教版"}]},{id:7,title:"地理",version:[{id:1,title:"人教版"}]},{id:8,title:"生物",version:[{id:1,title:"人教版"}]},{id:9,title:"思想政治",version:[{id:1,title:"人教版"}]},{id:10,title:"体育与健康",version:[{id:1,title:"普通高中体育与健康模块教学设计探究（高中提予教师备用书）"}]},{id:11,title:"音乐",version:[{id:"1",title:"人音社"}]}]}]}],Ui={class:"select-view"},$i={class:"flex_between"},Yi={class:"flex_between tabs"},ji={class:"flex_start"},Wi=["onClick"],_i=["onClick"],Xi={class:"card"},Ji={class:"send-select-m"},Qi={class:"flex_between margin_bottom_8"},Zi={class:"flex_start send-select-item flex_1"},Ki={class:"flex_between margin_bottom_8"},en={class:"flex_start send-select-item flex_1"},tn={class:"flex_between margin_bottom_8"},rn={class:"flex_start send-select-item flex_1"},an={src:wa,alt:"",class:"send-img"},nn={class:"dialog-footer"},sn={__name:"bkzs",emits:"sendMsg",setup(a,{emit:e}){let t=ie(!1);ie(!0),ie(!0),ie(""),va();let r=e,i=ie([{title:"学情分析",canDel:!1},{title:"教学分析",canDel:!1},{title:"教学目标",canDel:!1},{title:"教学重难点",canDel:!1},{title:"教学准备",canDel:!1},{title:"教学过程",canDel:!1}]),n=ie([]),l=ie({title:""}),u=ie(!1),h=ie({stage:"",stage_title:"",grade:"",grade_title:"",subject:"",subject_title:"",version:"",version_title:"",volume:"",chapter:""});Ni(()=>h.value,H=>{H.stage&&H.grade&&H.subject&&H.version&&H.volume&&H.chapter?u.value=!0:u.value=!1},{deep:!0});let d=ie([]),p=ie([]),g=ie([]);ie([{id:1,title:"上册"},{id:2,title:"下册"}]);let y=()=>{let H=`我是一名${h.value.stage_title}${h.value.grade_title}${h.value.subject_title}教师,正在使用${h.value.version}${h.value.volume}教材,我正在备${h.value.chapter}这节课,${n.value&&n.value.length>0?"包含"+n.value.join(",")+"这几个部分":""}`;r("sendMsg",H),x()},x=()=>{n.value=[],i.value=[{title:"教学重难点",canDel:!1},{title:"教学准备",canDel:!1},{title:"教学过程",canDel:!1}],h.value={stage:"",stage_title:"",grade:"",grade_title:"",subject:"",subject_title:"",version:"",version_title:"",volume:"",chapter:""},d.value=[],p.value=[],g.value=[],r("backCommon")},k=()=>{t.value=!0},S=H=>{n.value.indexOf(H.title)<0?n.value.push(H.title):n.value=n.value.filter(T=>T!=H.title)};const C=H=>{i.value=i.value.filter(T=>T.title!=H.title),n.value=n.value.filter(T=>T!=H.title)};let E=()=>{l.value.title?(i.value.push({title:l.value.title,canDel:!0}),n.value.push(l.value.title),L()):Pi.warning("请输入教案组成部分")},L=()=>{t.value=!1,l.value.title=""};const V=()=>{h.value.stage_title=c0[h.value.stage-1].title,d.value=c0[h.value.stage-1].grade},j=()=>{h.value.grade-1,h.value.grade_title=c0[h.value.stage-1].grade[h.value.grade-1].title,p.value=c0[h.value.stage-1].grade[h.value.grade-1].subject},G=()=>{h.value.version_title=c0[h.value.stage-1].grade[h.value.grade-1].subject[h.value.subject-1].title,g.value=c0[h.value.stage-1].grade[h.value.grade-1].subject[h.value.subject-1].version};return ga(()=>{}),(H,T)=>{const I=Ce("el-option"),R=Ce("el-select"),_=Ce("el-input"),W=Ce("el-form-item"),ee=Ce("el-form"),ve=Ce("el-button"),le=Ce("el-dialog");return Q(),ae("div",Ui,[F("div",null,[F("div",$i,[T[11]||(T[11]=F("p",{class:"tips"},"您希望该教案包含以下哪些部分？",-1)),F("img",{src:ya,alt:"",class:"back",onClick:T[0]||(T[0]=(...D)=>X(x)&&X(x)(...D))})]),F("div",Yi,[F("ul",ji,[(Q(!0),ae(i0,null,n0(X(i),(D,de)=>(Q(),ae("li",{class:"flex_end",key:de},[F("div",{class:qi(X(n).indexOf(D.title)>-1?"active":"")},[F("p",{onClick:fe=>X(S)(D)},rt(D.title),9,Wi),D.canDel?(Q(),ae("img",{key:0,src:Gi,alt:"",class:"candel",onClick:fe=>C(D)},null,8,_i)):Be("",!0)],2)]))),128))]),F("div",{class:"add-btn",onClick:T[1]||(T[1]=(...D)=>X(k)&&X(k)(...D)),style:{"max-height":"30px"}},T[12]||(T[12]=[F("img",{src:"https://cdn.tqxxkj.cn/static/images/ai/add.png",alt:""},null,-1)]))])]),F("div",Xi,[F("div",Ji,[F("div",Qi,[T[14]||(T[14]=F("p",{class:"text_14 grey1 margin_right_8"},"我是一名",-1)),F("div",Zi,[be(R,{modelValue:X(h).stage,"onUpdate:modelValue":T[2]||(T[2]=D=>X(h).stage=D),placeholder:"学段",class:"margin_right_8",onChange:V},{default:Oe(()=>[(Q(!0),ae(i0,null,n0(X(c0),D=>(Q(),w0(I,{key:D.id,label:D.title,value:D.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),be(R,{modelValue:X(h).grade,"onUpdate:modelValue":T[3]||(T[3]=D=>X(h).grade=D),placeholder:"年级",class:"margin_right_8",onChange:j},{default:Oe(()=>[(Q(!0),ae(i0,null,n0(X(d),D=>(Q(),w0(I,{key:D.id,label:D.title,value:D.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),be(R,{modelValue:X(h).subject,"onUpdate:modelValue":T[4]||(T[4]=D=>X(h).subject=D),placeholder:"学科",class:"margin_right_8",onChange:G},{default:Oe(()=>[(Q(!0),ae(i0,null,n0(X(p),D=>(Q(),w0(I,{key:D.id,label:D.title,value:D.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),T[13]||(T[13]=F("p",{class:"text_14 grey1"},"教师",-1))])]),F("div",Ki,[T[16]||(T[16]=F("p",{class:"text_14 grey1 margin_right_8"},"正在使用",-1)),F("div",en,[be(_,{class:"margin_right_8",modelValue:X(h).version,"onUpdate:modelValue":T[5]||(T[5]=D=>X(h).version=D),placeholder:"教材版本"},null,8,["modelValue"]),be(_,{class:"margin_right_8",modelValue:X(h).volume,"onUpdate:modelValue":T[6]||(T[6]=D=>X(h).volume=D),placeholder:"分册"},null,8,["modelValue"]),T[15]||(T[15]=F("p",{class:"text_14 grey1"},"教材",-1))])]),F("div",tn,[T[18]||(T[18]=F("p",{class:"text_14 grey1 margin_right_8"},"我正在备",-1)),F("div",rn,[be(_,{modelValue:X(h).chapter,"onUpdate:modelValue":T[7]||(T[7]=D=>X(h).chapter=D),placeholder:"输入课文/章节名称",style:{"margin-right":"6px"}},null,8,["modelValue"]),T[17]||(T[17]=F("p",{class:"text_14 grey1"},"这节课",-1)),Pt(F("img",an,null,512),[[Vt,!X(u)]]),Pt(F("img",{src:xa,alt:"",class:"send-img",onClick:T[8]||(T[8]=(...D)=>X(y)&&X(y)(...D))},null,512),[[Vt,X(u)]])])])])]),be(le,{modelValue:X(t),"onUpdate:modelValue":T[10]||(T[10]=D=>Ii(t)?t.value=D:t=D),title:"添加",width:"460","before-close":X(L),"close-on-click-modal":!1},{footer:Oe(()=>[F("div",nn,[be(ve,{onClick:X(L),class:"button_120_48_cancel"},{default:Oe(()=>T[19]||(T[19]=[Gt("取消")])),_:1},8,["onClick"]),be(ve,{type:"primary",onClick:X(E),class:"button_120_48"},{default:Oe(()=>T[20]||(T[20]=[Gt(" 确认 ")])),_:1},8,["onClick"])])]),default:Oe(()=>[be(ee,{"label-position":"top","label-width":"auto",model:X(l)},{default:Oe(()=>[be(W,{label:"教案组成部分"},{default:Oe(()=>[be(_,{modelValue:X(l).title,"onUpdate:modelValue":T[9]||(T[9]=D=>X(l).title=D),placeholder:"请输入"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","before-close"])])}}},ln=ba(sn,[["__scopeId","data-v-d4e91861"]]);function on(){return dt({url:"/ai/conversations/bk/create",method:"post"})}function un(a){return dt({url:`/ai/conversations/bk/faq/${a.type}`,method:"get"})}function hn(a){return dt({url:`/ai/conversations/${a.id}/bk/messages`,method:"get"})}function cn(a){return dt({url:`/ai/conversations/${a.id}/bk/clear-context`,method:"delete"})}function Kt(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let f0=Kt();function ka(a){f0=a}const R0={exec:()=>null};function Z(a,e=""){let t=typeof a=="string"?a:a.source;const r={replace:(i,n)=>{let l=typeof n=="string"?n:n.source;return l=l.replace(ye.caret,"$1"),t=t.replace(i,l),r},getRegex:()=>new RegExp(t,e)};return r}const ye={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:a=>new RegExp(`^( {0,3}${a})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:a=>new RegExp(`^ {0,${Math.min(3,a-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:a=>new RegExp(`^ {0,${Math.min(3,a-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:a=>new RegExp(`^ {0,${Math.min(3,a-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:a=>new RegExp(`^ {0,${Math.min(3,a-1)}}#`),htmlBeginRegex:a=>new RegExp(`^ {0,${Math.min(3,a-1)}}<(?:[a-z].*>|!--)`,"i")},mn=/^(?:[ \t]*(?:\n|$))+/,dn=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,pn=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,O0=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,fn=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,er=/(?:[*+-]|\d{1,9}[.)])/,Sa=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,Aa=Z(Sa).replace(/bull/g,er).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),vn=Z(Sa).replace(/bull/g,er).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),tr=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,gn=/^[^\n]+/,rr=/(?!\s*\])(?:\\.|[^\[\]\\])+/,bn=Z(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",rr).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),yn=Z(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,er).getRegex(),pt="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",ar=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,xn=Z("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",ar).replace("tag",pt).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),za=Z(tr).replace("hr",O0).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",pt).getRegex(),wn=Z(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",za).getRegex(),ir={blockquote:wn,code:dn,def:bn,fences:pn,heading:fn,hr:O0,html:xn,lheading:Aa,list:yn,newline:mn,paragraph:za,table:R0,text:gn},Dr=Z("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",O0).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",pt).getRegex(),kn={...ir,lheading:vn,table:Dr,paragraph:Z(tr).replace("hr",O0).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Dr).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",pt).getRegex()},Sn={...ir,html:Z(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",ar).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:R0,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:Z(tr).replace("hr",O0).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Aa).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},An=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,zn=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Ma=/^( {2,}|\\)\n(?!\s*$)/,Mn=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,ft=/[\p{P}\p{S}]/u,nr=/[\s\p{P}\p{S}]/u,Ta=/[^\s\p{P}\p{S}]/u,Tn=Z(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,nr).getRegex(),Ba=/(?!~)[\p{P}\p{S}]/u,Bn=/(?!~)[\s\p{P}\p{S}]/u,Cn=/(?:[^\s\p{P}\p{S}]|~)/u,Rn=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,Ca=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,Dn=Z(Ca,"u").replace(/punct/g,ft).getRegex(),En=Z(Ca,"u").replace(/punct/g,Ba).getRegex(),Ra="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",Nn=Z(Ra,"gu").replace(/notPunctSpace/g,Ta).replace(/punctSpace/g,nr).replace(/punct/g,ft).getRegex(),qn=Z(Ra,"gu").replace(/notPunctSpace/g,Cn).replace(/punctSpace/g,Bn).replace(/punct/g,Ba).getRegex(),In=Z("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Ta).replace(/punctSpace/g,nr).replace(/punct/g,ft).getRegex(),Ln=Z(/\\(punct)/,"gu").replace(/punct/g,ft).getRegex(),On=Z(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Hn=Z(ar).replace("(?:-->|$)","-->").getRegex(),Fn=Z("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Hn).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),nt=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Pn=Z(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",nt).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Da=Z(/^!?\[(label)\]\[(ref)\]/).replace("label",nt).replace("ref",rr).getRegex(),Ea=Z(/^!?\[(ref)\](?:\[\])?/).replace("ref",rr).getRegex(),Vn=Z("reflink|nolink(?!\\()","g").replace("reflink",Da).replace("nolink",Ea).getRegex(),sr={_backpedal:R0,anyPunctuation:Ln,autolink:On,blockSkip:Rn,br:Ma,code:zn,del:R0,emStrongLDelim:Dn,emStrongRDelimAst:Nn,emStrongRDelimUnd:In,escape:An,link:Pn,nolink:Ea,punctuation:Tn,reflink:Da,reflinkSearch:Vn,tag:Fn,text:Mn,url:R0},Gn={...sr,link:Z(/^!?\[(label)\]\((.*?)\)/).replace("label",nt).getRegex(),reflink:Z(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",nt).getRegex()},Ut={...sr,emStrongRDelimAst:qn,emStrongLDelim:En,url:Z(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Un={...Ut,br:Z(Ma).replace("{2,}","*").getRegex(),text:Z(Ut.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},Y0={normal:ir,gfm:kn,pedantic:Sn},T0={normal:sr,gfm:Ut,breaks:Un,pedantic:Gn},$n={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Er=a=>$n[a];function He(a,e){if(e){if(ye.escapeTest.test(a))return a.replace(ye.escapeReplace,Er)}else if(ye.escapeTestNoEncode.test(a))return a.replace(ye.escapeReplaceNoEncode,Er);return a}function Nr(a){try{a=encodeURI(a).replace(ye.percentDecode,"%")}catch{return null}return a}function qr(a,e){var n;const t=a.replace(ye.findPipe,(l,u,h)=>{let d=!1,p=u;for(;--p>=0&&h[p]==="\\";)d=!d;return d?"|":" |"}),r=t.split(ye.splitPipe);let i=0;if(r[0].trim()||r.shift(),r.length>0&&!((n=r.at(-1))!=null&&n.trim())&&r.pop(),e)if(r.length>e)r.splice(e);else for(;r.length<e;)r.push("");for(;i<r.length;i++)r[i]=r[i].trim().replace(ye.slashPipe,"|");return r}function B0(a,e,t){const r=a.length;if(r===0)return"";let i=0;for(;i<r&&a.charAt(r-i-1)===e;)i++;return a.slice(0,r-i)}function Yn(a,e){if(a.indexOf(e[1])===-1)return-1;let t=0;for(let r=0;r<a.length;r++)if(a[r]==="\\")r++;else if(a[r]===e[0])t++;else if(a[r]===e[1]&&(t--,t<0))return r;return t>0?-2:-1}function Ir(a,e,t,r,i){const n=e.href,l=e.title||null,u=a[1].replace(i.other.outputLinkReplace,"$1");r.state.inLink=!0;const h={type:a[0].charAt(0)==="!"?"image":"link",raw:t,href:n,title:l,text:u,tokens:r.inlineTokens(u)};return r.state.inLink=!1,h}function jn(a,e,t){const r=a.match(t.other.indentCodeCompensation);if(r===null)return e;const i=r[1];return e.split(`
`).map(n=>{const l=n.match(t.other.beginningSpace);if(l===null)return n;const[u]=l;return u.length>=i.length?n.slice(i.length):n}).join(`
`)}class st{constructor(e){te(this,"options");te(this,"rules");te(this,"lexer");this.options=e||f0}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const r=t[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?r:B0(r,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const r=t[0],i=jn(r,t[3]||"",this.rules);return{type:"code",raw:r,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:i}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let r=t[2].trim();if(this.rules.other.endingHash.test(r)){const i=B0(r,"#");(this.options.pedantic||!i||this.rules.other.endingSpaceChar.test(i))&&(r=i.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:r,tokens:this.lexer.inline(r)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:B0(t[0],`
`)}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){let r=B0(t[0],`
`).split(`
`),i="",n="";const l=[];for(;r.length>0;){let u=!1;const h=[];let d;for(d=0;d<r.length;d++)if(this.rules.other.blockquoteStart.test(r[d]))h.push(r[d]),u=!0;else if(!u)h.push(r[d]);else break;r=r.slice(d);const p=h.join(`
`),g=p.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");i=i?`${i}
${p}`:p,n=n?`${n}
${g}`:g;const y=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(g,l,!0),this.lexer.state.top=y,r.length===0)break;const x=l.at(-1);if((x==null?void 0:x.type)==="code")break;if((x==null?void 0:x.type)==="blockquote"){const k=x,S=k.raw+`
`+r.join(`
`),C=this.blockquote(S);l[l.length-1]=C,i=i.substring(0,i.length-k.raw.length)+C.raw,n=n.substring(0,n.length-k.text.length)+C.text;break}else if((x==null?void 0:x.type)==="list"){const k=x,S=k.raw+`
`+r.join(`
`),C=this.list(S);l[l.length-1]=C,i=i.substring(0,i.length-x.raw.length)+C.raw,n=n.substring(0,n.length-k.raw.length)+C.raw,r=S.substring(l.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:i,tokens:l,text:n}}}list(e){let t=this.rules.block.list.exec(e);if(t){let r=t[1].trim();const i=r.length>1,n={type:"list",raw:"",ordered:i,start:i?+r.slice(0,-1):"",loose:!1,items:[]};r=i?`\\d{1,9}\\${r.slice(-1)}`:`\\${r}`,this.options.pedantic&&(r=i?r:"[*+-]");const l=this.rules.other.listItemRegex(r);let u=!1;for(;e;){let d=!1,p="",g="";if(!(t=l.exec(e))||this.rules.block.hr.test(e))break;p=t[0],e=e.substring(p.length);let y=t[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,L=>" ".repeat(3*L.length)),x=e.split(`
`,1)[0],k=!y.trim(),S=0;if(this.options.pedantic?(S=2,g=y.trimStart()):k?S=t[1].length+1:(S=t[2].search(this.rules.other.nonSpaceChar),S=S>4?1:S,g=y.slice(S),S+=t[1].length),k&&this.rules.other.blankLine.test(x)&&(p+=x+`
`,e=e.substring(x.length+1),d=!0),!d){const L=this.rules.other.nextBulletRegex(S),V=this.rules.other.hrRegex(S),j=this.rules.other.fencesBeginRegex(S),G=this.rules.other.headingBeginRegex(S),H=this.rules.other.htmlBeginRegex(S);for(;e;){const T=e.split(`
`,1)[0];let I;if(x=T,this.options.pedantic?(x=x.replace(this.rules.other.listReplaceNesting,"  "),I=x):I=x.replace(this.rules.other.tabCharGlobal,"    "),j.test(x)||G.test(x)||H.test(x)||L.test(x)||V.test(x))break;if(I.search(this.rules.other.nonSpaceChar)>=S||!x.trim())g+=`
`+I.slice(S);else{if(k||y.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||j.test(y)||G.test(y)||V.test(y))break;g+=`
`+x}!k&&!x.trim()&&(k=!0),p+=T+`
`,e=e.substring(T.length+1),y=I.slice(S)}}n.loose||(u?n.loose=!0:this.rules.other.doubleBlankLine.test(p)&&(u=!0));let C=null,E;this.options.gfm&&(C=this.rules.other.listIsTask.exec(g),C&&(E=C[0]!=="[ ] ",g=g.replace(this.rules.other.listReplaceTask,""))),n.items.push({type:"list_item",raw:p,task:!!C,checked:E,loose:!1,text:g,tokens:[]}),n.raw+=p}const h=n.items.at(-1);if(h)h.raw=h.raw.trimEnd(),h.text=h.text.trimEnd();else return;n.raw=n.raw.trimEnd();for(let d=0;d<n.items.length;d++)if(this.lexer.state.top=!1,n.items[d].tokens=this.lexer.blockTokens(n.items[d].text,[]),!n.loose){const p=n.items[d].tokens.filter(y=>y.type==="space"),g=p.length>0&&p.some(y=>this.rules.other.anyLine.test(y.raw));n.loose=g}if(n.loose)for(let d=0;d<n.items.length;d++)n.items[d].loose=!0;return n}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const r=t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),i=t[2]?t[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",n=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:r,raw:t[0],href:i,title:n}}}table(e){var u;const t=this.rules.block.table.exec(e);if(!t||!this.rules.other.tableDelimiter.test(t[2]))return;const r=qr(t[1]),i=t[2].replace(this.rules.other.tableAlignChars,"").split("|"),n=(u=t[3])!=null&&u.trim()?t[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],l={type:"table",raw:t[0],header:[],align:[],rows:[]};if(r.length===i.length){for(const h of i)this.rules.other.tableAlignRight.test(h)?l.align.push("right"):this.rules.other.tableAlignCenter.test(h)?l.align.push("center"):this.rules.other.tableAlignLeft.test(h)?l.align.push("left"):l.align.push(null);for(let h=0;h<r.length;h++)l.header.push({text:r[h],tokens:this.lexer.inline(r[h]),header:!0,align:l.align[h]});for(const h of n)l.rows.push(qr(h,l.header.length).map((d,p)=>({text:d,tokens:this.lexer.inline(d),header:!1,align:l.align[p]})));return l}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const r=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:r,tokens:this.lexer.inline(r)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:t[1]}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&this.rules.other.startATag.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const r=t[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(r)){if(!this.rules.other.endAngleBracket.test(r))return;const l=B0(r.slice(0,-1),"\\");if((r.length-l.length)%2===0)return}else{const l=Yn(t[2],"()");if(l===-2)return;if(l>-1){const h=(t[0].indexOf("!")===0?5:4)+t[1].length+l;t[2]=t[2].substring(0,l),t[0]=t[0].substring(0,h).trim(),t[3]=""}}let i=t[2],n="";if(this.options.pedantic){const l=this.rules.other.pedanticHrefTitle.exec(i);l&&(i=l[1],n=l[3])}else n=t[3]?t[3].slice(1,-1):"";return i=i.trim(),this.rules.other.startAngleBracket.test(i)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(r)?i=i.slice(1):i=i.slice(1,-1)),Ir(t,{href:i&&i.replace(this.rules.inline.anyPunctuation,"$1"),title:n&&n.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer,this.rules)}}reflink(e,t){let r;if((r=this.rules.inline.reflink.exec(e))||(r=this.rules.inline.nolink.exec(e))){const i=(r[2]||r[1]).replace(this.rules.other.multipleSpaceGlobal," "),n=t[i.toLowerCase()];if(!n){const l=r[0].charAt(0);return{type:"text",raw:l,text:l}}return Ir(r,n,r[0],this.lexer,this.rules)}}emStrong(e,t,r=""){let i=this.rules.inline.emStrongLDelim.exec(e);if(!i||i[3]&&r.match(this.rules.other.unicodeAlphaNumeric))return;if(!(i[1]||i[2]||"")||!r||this.rules.inline.punctuation.exec(r)){const l=[...i[0]].length-1;let u,h,d=l,p=0;const g=i[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(g.lastIndex=0,t=t.slice(-1*e.length+l);(i=g.exec(t))!=null;){if(u=i[1]||i[2]||i[3]||i[4]||i[5]||i[6],!u)continue;if(h=[...u].length,i[3]||i[4]){d+=h;continue}else if((i[5]||i[6])&&l%3&&!((l+h)%3)){p+=h;continue}if(d-=h,d>0)continue;h=Math.min(h,h+d+p);const y=[...i[0]][0].length,x=e.slice(0,l+i.index+y+h);if(Math.min(l,h)%2){const S=x.slice(1,-1);return{type:"em",raw:x,text:S,tokens:this.lexer.inlineTokens(S)}}const k=x.slice(2,-2);return{type:"strong",raw:x,text:k,tokens:this.lexer.inlineTokens(k)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let r=t[2].replace(this.rules.other.newLineCharGlobal," ");const i=this.rules.other.nonSpaceChar.test(r),n=this.rules.other.startingSpaceChar.test(r)&&this.rules.other.endingSpaceChar.test(r);return i&&n&&(r=r.substring(1,r.length-1)),{type:"codespan",raw:t[0],text:r}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let r,i;return t[2]==="@"?(r=t[1],i="mailto:"+r):(r=t[1],i=r),{type:"link",raw:t[0],text:r,href:i,tokens:[{type:"text",raw:r,text:r}]}}}url(e){var r;let t;if(t=this.rules.inline.url.exec(e)){let i,n;if(t[2]==="@")i=t[0],n="mailto:"+i;else{let l;do l=t[0],t[0]=((r=this.rules.inline._backpedal.exec(t[0]))==null?void 0:r[0])??"";while(l!==t[0]);i=t[0],t[1]==="www."?n="http://"+t[0]:n=t[0]}return{type:"link",raw:t[0],text:i,href:n,tokens:[{type:"text",raw:i,text:i}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){const r=this.lexer.state.inRawBlock;return{type:"text",raw:t[0],text:t[0],escaped:r}}}}class De{constructor(e){te(this,"tokens");te(this,"options");te(this,"state");te(this,"tokenizer");te(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||f0,this.options.tokenizer=this.options.tokenizer||new st,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={other:ye,block:Y0.normal,inline:T0.normal};this.options.pedantic?(t.block=Y0.pedantic,t.inline=T0.pedantic):this.options.gfm&&(t.block=Y0.gfm,this.options.breaks?t.inline=T0.breaks:t.inline=T0.gfm),this.tokenizer.rules=t}static get rules(){return{block:Y0,inline:T0}}static lex(e,t){return new De(t).lex(e)}static lexInline(e,t){return new De(t).inlineTokens(e)}lex(e){e=e.replace(ye.carriageReturn,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const r=this.inlineQueue[t];this.inlineTokens(r.src,r.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],r=!1){var i,n,l;for(this.options.pedantic&&(e=e.replace(ye.tabCharGlobal,"    ").replace(ye.spaceLine,""));e;){let u;if((n=(i=this.options.extensions)==null?void 0:i.block)!=null&&n.some(d=>(u=d.call({lexer:this},e,t))?(e=e.substring(u.raw.length),t.push(u),!0):!1))continue;if(u=this.tokenizer.space(e)){e=e.substring(u.raw.length);const d=t.at(-1);u.raw.length===1&&d!==void 0?d.raw+=`
`:t.push(u);continue}if(u=this.tokenizer.code(e)){e=e.substring(u.raw.length);const d=t.at(-1);(d==null?void 0:d.type)==="paragraph"||(d==null?void 0:d.type)==="text"?(d.raw+=`
`+u.raw,d.text+=`
`+u.text,this.inlineQueue.at(-1).src=d.text):t.push(u);continue}if(u=this.tokenizer.fences(e)){e=e.substring(u.raw.length),t.push(u);continue}if(u=this.tokenizer.heading(e)){e=e.substring(u.raw.length),t.push(u);continue}if(u=this.tokenizer.hr(e)){e=e.substring(u.raw.length),t.push(u);continue}if(u=this.tokenizer.blockquote(e)){e=e.substring(u.raw.length),t.push(u);continue}if(u=this.tokenizer.list(e)){e=e.substring(u.raw.length),t.push(u);continue}if(u=this.tokenizer.html(e)){e=e.substring(u.raw.length),t.push(u);continue}if(u=this.tokenizer.def(e)){e=e.substring(u.raw.length);const d=t.at(-1);(d==null?void 0:d.type)==="paragraph"||(d==null?void 0:d.type)==="text"?(d.raw+=`
`+u.raw,d.text+=`
`+u.raw,this.inlineQueue.at(-1).src=d.text):this.tokens.links[u.tag]||(this.tokens.links[u.tag]={href:u.href,title:u.title});continue}if(u=this.tokenizer.table(e)){e=e.substring(u.raw.length),t.push(u);continue}if(u=this.tokenizer.lheading(e)){e=e.substring(u.raw.length),t.push(u);continue}let h=e;if((l=this.options.extensions)!=null&&l.startBlock){let d=1/0;const p=e.slice(1);let g;this.options.extensions.startBlock.forEach(y=>{g=y.call({lexer:this},p),typeof g=="number"&&g>=0&&(d=Math.min(d,g))}),d<1/0&&d>=0&&(h=e.substring(0,d+1))}if(this.state.top&&(u=this.tokenizer.paragraph(h))){const d=t.at(-1);r&&(d==null?void 0:d.type)==="paragraph"?(d.raw+=`
`+u.raw,d.text+=`
`+u.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=d.text):t.push(u),r=h.length!==e.length,e=e.substring(u.raw.length);continue}if(u=this.tokenizer.text(e)){e=e.substring(u.raw.length);const d=t.at(-1);(d==null?void 0:d.type)==="text"?(d.raw+=`
`+u.raw,d.text+=`
`+u.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=d.text):t.push(u);continue}if(e){const d="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(d);break}else throw new Error(d)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){var u,h,d;let r=e,i=null;if(this.tokens.links){const p=Object.keys(this.tokens.links);if(p.length>0)for(;(i=this.tokenizer.rules.inline.reflinkSearch.exec(r))!=null;)p.includes(i[0].slice(i[0].lastIndexOf("[")+1,-1))&&(r=r.slice(0,i.index)+"["+"a".repeat(i[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(i=this.tokenizer.rules.inline.anyPunctuation.exec(r))!=null;)r=r.slice(0,i.index)+"++"+r.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(i=this.tokenizer.rules.inline.blockSkip.exec(r))!=null;)r=r.slice(0,i.index)+"["+"a".repeat(i[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let n=!1,l="";for(;e;){n||(l=""),n=!1;let p;if((h=(u=this.options.extensions)==null?void 0:u.inline)!=null&&h.some(y=>(p=y.call({lexer:this},e,t))?(e=e.substring(p.raw.length),t.push(p),!0):!1))continue;if(p=this.tokenizer.escape(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.tag(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.link(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(p.raw.length);const y=t.at(-1);p.type==="text"&&(y==null?void 0:y.type)==="text"?(y.raw+=p.raw,y.text+=p.text):t.push(p);continue}if(p=this.tokenizer.emStrong(e,r,l)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.codespan(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.br(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.del(e)){e=e.substring(p.raw.length),t.push(p);continue}if(p=this.tokenizer.autolink(e)){e=e.substring(p.raw.length),t.push(p);continue}if(!this.state.inLink&&(p=this.tokenizer.url(e))){e=e.substring(p.raw.length),t.push(p);continue}let g=e;if((d=this.options.extensions)!=null&&d.startInline){let y=1/0;const x=e.slice(1);let k;this.options.extensions.startInline.forEach(S=>{k=S.call({lexer:this},x),typeof k=="number"&&k>=0&&(y=Math.min(y,k))}),y<1/0&&y>=0&&(g=e.substring(0,y+1))}if(p=this.tokenizer.inlineText(g)){e=e.substring(p.raw.length),p.raw.slice(-1)!=="_"&&(l=p.raw.slice(-1)),n=!0;const y=t.at(-1);(y==null?void 0:y.type)==="text"?(y.raw+=p.raw,y.text+=p.text):t.push(p);continue}if(e){const y="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(y);break}else throw new Error(y)}}return t}}class lt{constructor(e){te(this,"options");te(this,"parser");this.options=e||f0}space(e){return""}code({text:e,lang:t,escaped:r}){var l;const i=(l=(t||"").match(ye.notSpaceStart))==null?void 0:l[0],n=e.replace(ye.endingNewline,"")+`
`;return i?'<pre><code class="language-'+He(i)+'">'+(r?n:He(n,!0))+`</code></pre>
`:"<pre><code>"+(r?n:He(n,!0))+`</code></pre>
`}blockquote({tokens:e}){return`<blockquote>
${this.parser.parse(e)}</blockquote>
`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>
`}hr(e){return`<hr>
`}list(e){const t=e.ordered,r=e.start;let i="";for(let u=0;u<e.items.length;u++){const h=e.items[u];i+=this.listitem(h)}const n=t?"ol":"ul",l=t&&r!==1?' start="'+r+'"':"";return"<"+n+l+`>
`+i+"</"+n+`>
`}listitem(e){var r;let t="";if(e.task){const i=this.checkbox({checked:!!e.checked});e.loose?((r=e.tokens[0])==null?void 0:r.type)==="paragraph"?(e.tokens[0].text=i+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&e.tokens[0].tokens[0].type==="text"&&(e.tokens[0].tokens[0].text=i+" "+He(e.tokens[0].tokens[0].text),e.tokens[0].tokens[0].escaped=!0)):e.tokens.unshift({type:"text",raw:i+" ",text:i+" ",escaped:!0}):t+=i+" "}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>
`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>
`}table(e){let t="",r="";for(let n=0;n<e.header.length;n++)r+=this.tablecell(e.header[n]);t+=this.tablerow({text:r});let i="";for(let n=0;n<e.rows.length;n++){const l=e.rows[n];r="";for(let u=0;u<l.length;u++)r+=this.tablecell(l[u]);i+=this.tablerow({text:r})}return i&&(i=`<tbody>${i}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+i+`</table>
`}tablerow({text:e}){return`<tr>
${e}</tr>
`}tablecell(e){const t=this.parser.parseInline(e.tokens),r=e.header?"th":"td";return(e.align?`<${r} align="${e.align}">`:`<${r}>`)+t+`</${r}>
`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${He(e,!0)}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:r}){const i=this.parser.parseInline(r),n=Nr(e);if(n===null)return i;e=n;let l='<a href="'+e+'"';return t&&(l+=' title="'+He(t)+'"'),l+=">"+i+"</a>",l}image({href:e,title:t,text:r,tokens:i}){i&&(r=this.parser.parseInline(i,this.parser.textRenderer));const n=Nr(e);if(n===null)return He(r);e=n;let l=`<img src="${e}" alt="${r}"`;return t&&(l+=` title="${He(t)}"`),l+=">",l}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):"escaped"in e&&e.escaped?e.text:He(e.text)}}class lr{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}}class Ee{constructor(e){te(this,"options");te(this,"renderer");te(this,"textRenderer");this.options=e||f0,this.options.renderer=this.options.renderer||new lt,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new lr}static parse(e,t){return new Ee(t).parse(e)}static parseInline(e,t){return new Ee(t).parseInline(e)}parse(e,t=!0){var i,n;let r="";for(let l=0;l<e.length;l++){const u=e[l];if((n=(i=this.options.extensions)==null?void 0:i.renderers)!=null&&n[u.type]){const d=u,p=this.options.extensions.renderers[d.type].call({parser:this},d);if(p!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(d.type)){r+=p||"";continue}}const h=u;switch(h.type){case"space":{r+=this.renderer.space(h);continue}case"hr":{r+=this.renderer.hr(h);continue}case"heading":{r+=this.renderer.heading(h);continue}case"code":{r+=this.renderer.code(h);continue}case"table":{r+=this.renderer.table(h);continue}case"blockquote":{r+=this.renderer.blockquote(h);continue}case"list":{r+=this.renderer.list(h);continue}case"html":{r+=this.renderer.html(h);continue}case"paragraph":{r+=this.renderer.paragraph(h);continue}case"text":{let d=h,p=this.renderer.text(d);for(;l+1<e.length&&e[l+1].type==="text";)d=e[++l],p+=`
`+this.renderer.text(d);t?r+=this.renderer.paragraph({type:"paragraph",raw:p,text:p,tokens:[{type:"text",raw:p,text:p,escaped:!0}]}):r+=p;continue}default:{const d='Token with "'+h.type+'" type was not found.';if(this.options.silent)return console.error(d),"";throw new Error(d)}}}return r}parseInline(e,t=this.renderer){var i,n;let r="";for(let l=0;l<e.length;l++){const u=e[l];if((n=(i=this.options.extensions)==null?void 0:i.renderers)!=null&&n[u.type]){const d=this.options.extensions.renderers[u.type].call({parser:this},u);if(d!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(u.type)){r+=d||"";continue}}const h=u;switch(h.type){case"escape":{r+=t.text(h);break}case"html":{r+=t.html(h);break}case"link":{r+=t.link(h);break}case"image":{r+=t.image(h);break}case"strong":{r+=t.strong(h);break}case"em":{r+=t.em(h);break}case"codespan":{r+=t.codespan(h);break}case"br":{r+=t.br(h);break}case"del":{r+=t.del(h);break}case"text":{r+=t.text(h);break}default:{const d='Token with "'+h.type+'" type was not found.';if(this.options.silent)return console.error(d),"";throw new Error(d)}}}return r}}class D0{constructor(e){te(this,"options");te(this,"block");this.options=e||f0}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?De.lex:De.lexInline}provideParser(){return this.block?Ee.parse:Ee.parseInline}}te(D0,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));class Wn{constructor(...e){te(this,"defaults",Kt());te(this,"options",this.setOptions);te(this,"parse",this.parseMarkdown(!0));te(this,"parseInline",this.parseMarkdown(!1));te(this,"Parser",Ee);te(this,"Renderer",lt);te(this,"TextRenderer",lr);te(this,"Lexer",De);te(this,"Tokenizer",st);te(this,"Hooks",D0);this.use(...e)}walkTokens(e,t){var i,n;let r=[];for(const l of e)switch(r=r.concat(t.call(this,l)),l.type){case"table":{const u=l;for(const h of u.header)r=r.concat(this.walkTokens(h.tokens,t));for(const h of u.rows)for(const d of h)r=r.concat(this.walkTokens(d.tokens,t));break}case"list":{const u=l;r=r.concat(this.walkTokens(u.items,t));break}default:{const u=l;(n=(i=this.defaults.extensions)==null?void 0:i.childTokens)!=null&&n[u.type]?this.defaults.extensions.childTokens[u.type].forEach(h=>{const d=u[h].flat(1/0);r=r.concat(this.walkTokens(d,t))}):u.tokens&&(r=r.concat(this.walkTokens(u.tokens,t)))}}return r}use(...e){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(r=>{const i={...r};if(i.async=this.defaults.async||i.async||!1,r.extensions&&(r.extensions.forEach(n=>{if(!n.name)throw new Error("extension name required");if("renderer"in n){const l=t.renderers[n.name];l?t.renderers[n.name]=function(...u){let h=n.renderer.apply(this,u);return h===!1&&(h=l.apply(this,u)),h}:t.renderers[n.name]=n.renderer}if("tokenizer"in n){if(!n.level||n.level!=="block"&&n.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const l=t[n.level];l?l.unshift(n.tokenizer):t[n.level]=[n.tokenizer],n.start&&(n.level==="block"?t.startBlock?t.startBlock.push(n.start):t.startBlock=[n.start]:n.level==="inline"&&(t.startInline?t.startInline.push(n.start):t.startInline=[n.start]))}"childTokens"in n&&n.childTokens&&(t.childTokens[n.name]=n.childTokens)}),i.extensions=t),r.renderer){const n=this.defaults.renderer||new lt(this.defaults);for(const l in r.renderer){if(!(l in n))throw new Error(`renderer '${l}' does not exist`);if(["options","parser"].includes(l))continue;const u=l,h=r.renderer[u],d=n[u];n[u]=(...p)=>{let g=h.apply(n,p);return g===!1&&(g=d.apply(n,p)),g||""}}i.renderer=n}if(r.tokenizer){const n=this.defaults.tokenizer||new st(this.defaults);for(const l in r.tokenizer){if(!(l in n))throw new Error(`tokenizer '${l}' does not exist`);if(["options","rules","lexer"].includes(l))continue;const u=l,h=r.tokenizer[u],d=n[u];n[u]=(...p)=>{let g=h.apply(n,p);return g===!1&&(g=d.apply(n,p)),g}}i.tokenizer=n}if(r.hooks){const n=this.defaults.hooks||new D0;for(const l in r.hooks){if(!(l in n))throw new Error(`hook '${l}' does not exist`);if(["options","block"].includes(l))continue;const u=l,h=r.hooks[u],d=n[u];D0.passThroughHooks.has(l)?n[u]=p=>{if(this.defaults.async)return Promise.resolve(h.call(n,p)).then(y=>d.call(n,y));const g=h.call(n,p);return d.call(n,g)}:n[u]=(...p)=>{let g=h.apply(n,p);return g===!1&&(g=d.apply(n,p)),g}}i.hooks=n}if(r.walkTokens){const n=this.defaults.walkTokens,l=r.walkTokens;i.walkTokens=function(u){let h=[];return h.push(l.call(this,u)),n&&(h=h.concat(n.call(this,u))),h}}this.defaults={...this.defaults,...i}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return De.lex(e,t??this.defaults)}parser(e,t){return Ee.parse(e,t??this.defaults)}parseMarkdown(e){return(r,i)=>{const n={...i},l={...this.defaults,...n},u=this.onError(!!l.silent,!!l.async);if(this.defaults.async===!0&&n.async===!1)return u(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof r>"u"||r===null)return u(new Error("marked(): input parameter is undefined or null"));if(typeof r!="string")return u(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(r)+", string expected"));l.hooks&&(l.hooks.options=l,l.hooks.block=e);const h=l.hooks?l.hooks.provideLexer():e?De.lex:De.lexInline,d=l.hooks?l.hooks.provideParser():e?Ee.parse:Ee.parseInline;if(l.async)return Promise.resolve(l.hooks?l.hooks.preprocess(r):r).then(p=>h(p,l)).then(p=>l.hooks?l.hooks.processAllTokens(p):p).then(p=>l.walkTokens?Promise.all(this.walkTokens(p,l.walkTokens)).then(()=>p):p).then(p=>d(p,l)).then(p=>l.hooks?l.hooks.postprocess(p):p).catch(u);try{l.hooks&&(r=l.hooks.preprocess(r));let p=h(r,l);l.hooks&&(p=l.hooks.processAllTokens(p)),l.walkTokens&&this.walkTokens(p,l.walkTokens);let g=d(p,l);return l.hooks&&(g=l.hooks.postprocess(g)),g}catch(p){return u(p)}}}onError(e,t){return r=>{if(r.message+=`
Please report this to https://github.com/markedjs/marked.`,e){const i="<p>An error occurred:</p><pre>"+He(r.message+"",!0)+"</pre>";return t?Promise.resolve(i):i}if(t)return Promise.reject(r);throw r}}}const p0=new Wn;function K(a,e){return p0.parse(a,e)}K.options=K.setOptions=function(a){return p0.setOptions(a),K.defaults=p0.defaults,ka(K.defaults),K};K.getDefaults=Kt;K.defaults=f0;K.use=function(...a){return p0.use(...a),K.defaults=p0.defaults,ka(K.defaults),K};K.walkTokens=function(a,e){return p0.walkTokens(a,e)};K.parseInline=p0.parseInline;K.Parser=Ee;K.parser=Ee.parse;K.Renderer=lt;K.TextRenderer=lr;K.Lexer=De;K.lexer=De.lex;K.Tokenizer=st;K.Hooks=D0;K.parse=K;K.options;K.setOptions;K.use;K.walkTokens;K.parseInline;Ee.parse;De.lex;class ze{constructor(e,t,r){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=e,this.start=t,this.end=r}static range(e,t){return t?!e||!e.loc||!t.loc||e.loc.lexer!==t.loc.lexer?null:new ze(e.loc.lexer,e.loc.start,t.loc.end):e&&e.loc}}class Ve{constructor(e,t){this.text=void 0,this.loc=void 0,this.noexpand=void 0,this.treatAsRelax=void 0,this.text=e,this.loc=t}range(e,t){return new Ve(t,ze.range(this,e))}}class M{constructor(e,t){this.name=void 0,this.position=void 0,this.length=void 0,this.rawMessage=void 0;var r="KaTeX parse error: "+e,i,n,l=t&&t.loc;if(l&&l.start<=l.end){var u=l.lexer.input;i=l.start,n=l.end,i===u.length?r+=" at end of input: ":r+=" at position "+(i+1)+": ";var h=u.slice(i,n).replace(/[^]/g,"$&̲"),d;i>15?d="…"+u.slice(i-15,i):d=u.slice(0,i);var p;n+15<u.length?p=u.slice(n,n+15)+"…":p=u.slice(n),r+=d+h+p}var g=new Error(r);return g.name="ParseError",g.__proto__=M.prototype,g.position=i,i!=null&&n!=null&&(g.length=n-i),g.rawMessage=e,g}}M.prototype.__proto__=Error.prototype;var _n=function(e,t){return e.indexOf(t)!==-1},Xn=function(e,t){return e===void 0?t:e},Jn=/([A-Z])/g,Qn=function(e){return e.replace(Jn,"-$1").toLowerCase()},Zn={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},Kn=/[&><"']/g;function es(a){return String(a).replace(Kn,e=>Zn[e])}var Na=function a(e){return e.type==="ordgroup"||e.type==="color"?e.body.length===1?a(e.body[0]):e:e.type==="font"?a(e.body):e},ts=function(e){var t=Na(e);return t.type==="mathord"||t.type==="textord"||t.type==="atom"},rs=function(e){if(!e)throw new Error("Expected non-null, but got "+String(e));return e},as=function(e){var t=/^\s*([^\\/#]*?)(?::|&#0*58|&#x0*3a)/i.exec(e);return t!=null?t[1]:"_relative"},U={contains:_n,deflt:Xn,escape:es,hyphenate:Qn,getBaseElem:Na,isCharacterBox:ts,protocolFromUrl:as},at={displayMode:{type:"boolean",description:"Render math in display mode, which puts the math in display style (so \\int and \\sum are large, for example), and centers the math on the page on its own line.",cli:"-d, --display-mode"},output:{type:{enum:["htmlAndMathml","html","mathml"]},description:"Determines the markup language of the output.",cli:"-F, --format <type>"},leqno:{type:"boolean",description:"Render display math in leqno style (left-justified tags)."},fleqn:{type:"boolean",description:"Render display math flush left."},throwOnError:{type:"boolean",default:!0,cli:"-t, --no-throw-on-error",cliDescription:"Render errors (in the color given by --error-color) instead of throwing a ParseError exception when encountering an error."},errorColor:{type:"string",default:"#cc0000",cli:"-c, --error-color <color>",cliDescription:"A color string given in the format 'rgb' or 'rrggbb' (no #). This option determines the color of errors rendered by the -t option.",cliProcessor:a=>"#"+a},macros:{type:"object",cli:"-m, --macro <def>",cliDescription:"Define custom macro of the form '\\foo:expansion' (use multiple -m arguments for multiple macros).",cliDefault:[],cliProcessor:(a,e)=>(e.push(a),e)},minRuleThickness:{type:"number",description:"Specifies a minimum thickness, in ems, for fraction lines, `\\sqrt` top lines, `{array}` vertical lines, `\\hline`, `\\hdashline`, `\\underline`, `\\overline`, and the borders of `\\fbox`, `\\boxed`, and `\\fcolorbox`.",processor:a=>Math.max(0,a),cli:"--min-rule-thickness <size>",cliProcessor:parseFloat},colorIsTextColor:{type:"boolean",description:"Makes \\color behave like LaTeX's 2-argument \\textcolor, instead of LaTeX's one-argument \\color mode change.",cli:"-b, --color-is-text-color"},strict:{type:[{enum:["warn","ignore","error"]},"boolean","function"],description:"Turn on strict / LaTeX faithfulness mode, which throws an error if the input uses features that are not supported by LaTeX.",cli:"-S, --strict",cliDefault:!1},trust:{type:["boolean","function"],description:"Trust the input, enabling all HTML features such as \\url.",cli:"-T, --trust"},maxSize:{type:"number",default:1/0,description:"If non-zero, all user-specified sizes, e.g. in \\rule{500em}{500em}, will be capped to maxSize ems. Otherwise, elements and spaces can be arbitrarily large",processor:a=>Math.max(0,a),cli:"-s, --max-size <n>",cliProcessor:parseInt},maxExpand:{type:"number",default:1e3,description:"Limit the number of macro expansions to the specified number, to prevent e.g. infinite macro loops. If set to Infinity, the macro expander will try to fully expand as in LaTeX.",processor:a=>Math.max(0,a),cli:"-e, --max-expand <n>",cliProcessor:a=>a==="Infinity"?1/0:parseInt(a)},globalGroup:{type:"boolean",cli:!1}};function is(a){if(a.default)return a.default;var e=a.type,t=Array.isArray(e)?e[0]:e;if(typeof t!="string")return t.enum[0];switch(t){case"boolean":return!1;case"string":return"";case"number":return 0;case"object":return{}}}class or{constructor(e){this.displayMode=void 0,this.output=void 0,this.leqno=void 0,this.fleqn=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.minRuleThickness=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.trust=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.globalGroup=void 0,e=e||{};for(var t in at)if(at.hasOwnProperty(t)){var r=at[t];this[t]=e[t]!==void 0?r.processor?r.processor(e[t]):e[t]:is(r)}}reportNonstrict(e,t,r){var i=this.strict;if(typeof i=="function"&&(i=i(e,t,r)),!(!i||i==="ignore")){if(i===!0||i==="error")throw new M("LaTeX-incompatible input and strict mode is set to 'error': "+(t+" ["+e+"]"),r);i==="warn"?typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(t+" ["+e+"]")):typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+i+"': "+t+" ["+e+"]"))}}useStrictBehavior(e,t,r){var i=this.strict;if(typeof i=="function")try{i=i(e,t,r)}catch{i="error"}return!i||i==="ignore"?!1:i===!0||i==="error"?!0:i==="warn"?(typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(t+" ["+e+"]")),!1):(typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+i+"': "+t+" ["+e+"]")),!1)}isTrusted(e){e.url&&!e.protocol&&(e.protocol=U.protocolFromUrl(e.url));var t=typeof this.trust=="function"?this.trust(e):this.trust;return!!t}}class r0{constructor(e,t,r){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=e,this.size=t,this.cramped=r}sup(){return Fe[ns[this.id]]}sub(){return Fe[ss[this.id]]}fracNum(){return Fe[ls[this.id]]}fracDen(){return Fe[os[this.id]]}cramp(){return Fe[us[this.id]]}text(){return Fe[hs[this.id]]}isTight(){return this.size>=2}}var ur=0,ot=1,k0=2,We=3,q0=4,Ne=5,S0=6,xe=7,Fe=[new r0(ur,0,!1),new r0(ot,0,!0),new r0(k0,1,!1),new r0(We,1,!0),new r0(q0,2,!1),new r0(Ne,2,!0),new r0(S0,3,!1),new r0(xe,3,!0)],ns=[q0,Ne,q0,Ne,S0,xe,S0,xe],ss=[Ne,Ne,Ne,Ne,xe,xe,xe,xe],ls=[k0,We,q0,Ne,S0,xe,S0,xe],os=[We,We,Ne,Ne,xe,xe,xe,xe],us=[ot,ot,We,We,Ne,Ne,xe,xe],hs=[ur,ot,k0,We,k0,We,k0,We],O={DISPLAY:Fe[ur],TEXT:Fe[k0],SCRIPT:Fe[q0],SCRIPTSCRIPT:Fe[S0]},$t=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"armenian",blocks:[[1328,1423]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}];function cs(a){for(var e=0;e<$t.length;e++)for(var t=$t[e],r=0;r<t.blocks.length;r++){var i=t.blocks[r];if(a>=i[0]&&a<=i[1])return t.name}return null}var it=[];$t.forEach(a=>a.blocks.forEach(e=>it.push(...e)));function qa(a){for(var e=0;e<it.length;e+=2)if(a>=it[e]&&a<=it[e+1])return!0;return!1}var x0=80,ms=function(e,t){return"M95,"+(622+e+t)+`
c-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14
c0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54
c44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10
s173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429
c69,-144,104.5,-217.7,106.5,-221
l`+e/2.075+" -"+e+`
c5.3,-9.3,12,-14,20,-14
H400000v`+(40+e)+`H845.2724
s-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7
c-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z
M`+(834+e)+" "+t+"h400000v"+(40+e)+"h-400000z"},ds=function(e,t){return"M263,"+(601+e+t)+`c0.7,0,18,39.7,52,119
c34,79.3,68.167,158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120
c340,-704.7,510.7,-1060.3,512,-1067
l`+e/2.084+" -"+e+`
c4.7,-7.3,11,-11,19,-11
H40000v`+(40+e)+`H1012.3
s-271.3,567,-271.3,567c-38.7,80.7,-84,175,-136,283c-52,108,-89.167,185.3,-111.5,232
c-22.3,46.7,-33.8,70.3,-34.5,71c-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1
s-109,-253,-109,-253c-72.7,-168,-109.3,-252,-110,-252c-10.7,8,-22,16.7,-34,26
c-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26s76,-59,76,-59s76,-60,76,-60z
M`+(1001+e)+" "+t+"h400000v"+(40+e)+"h-400000z"},ps=function(e,t){return"M983 "+(10+e+t)+`
l`+e/3.13+" -"+e+`
c4,-6.7,10,-10,18,-10 H400000v`+(40+e)+`
H1013.1s-83.4,268,-264.1,840c-180.7,572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7
s-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744
c-10,12,-21,25,-33,39s-32,39,-32,39c-6,-5.3,-15,-14,-27,-26s25,-30,25,-30
c26.7,-32.7,52,-63,76,-91s52,-60,52,-60s208,722,208,722
c56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,-658.5
c53.7,-170.3,84.5,-266.8,92.5,-289.5z
M`+(1001+e)+" "+t+"h400000v"+(40+e)+"h-400000z"},fs=function(e,t){return"M424,"+(2398+e+t)+`
c-1.3,-0.7,-38.5,-172,-111.5,-514c-73,-342,-109.8,-513.3,-110.5,-514
c0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,25c-5.7,9.3,-9.8,16,-12.5,20
s-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,-13s76,-122,76,-122s77,-121,77,-121
s209,968,209,968c0,-2,84.7,-361.7,254,-1079c169.3,-717.3,254.7,-1077.7,256,-1081
l`+e/4.223+" -"+e+`c4,-6.7,10,-10,18,-10 H400000
v`+(40+e)+`H1014.6
s-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185
c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2z M`+(1001+e)+" "+t+`
h400000v`+(40+e)+"h-400000z"},vs=function(e,t){return"M473,"+(2713+e+t)+`
c339.3,-1799.3,509.3,-2700,510,-2702 l`+e/5.298+" -"+e+`
c3.3,-7.3,9.3,-11,18,-11 H400000v`+(40+e)+`H1017.7
s-90.5,478,-276.2,1466c-185.7,988,-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200
c0,-1.3,-5.3,8.7,-16,30c-10.7,21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26
s76,-153,76,-153s77,-151,77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,
606zM`+(1001+e)+" "+t+"h400000v"+(40+e)+"H1017.7z"},gs=function(e){var t=e/2;return"M400000 "+e+" H0 L"+t+" 0 l65 45 L145 "+(e-80)+" H400000z"},bs=function(e,t,r){var i=r-54-t-e;return"M702 "+(e+t)+"H400000"+(40+e)+`
H742v`+i+`l-4 4-4 4c-.667.7 -2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1
h-12l-28-84c-16.667-52-96.667 -294.333-240-727l-212 -643 -85 170
c-4-3.333-8.333-7.667-13 -13l-13-13l77-155 77-156c66 199.333 139 419.667
219 661 l218 661zM702 `+t+"H400000v"+(40+e)+"H742z"},ys=function(e,t,r){t=1e3*t;var i="";switch(e){case"sqrtMain":i=ms(t,x0);break;case"sqrtSize1":i=ds(t,x0);break;case"sqrtSize2":i=ps(t,x0);break;case"sqrtSize3":i=fs(t,x0);break;case"sqrtSize4":i=vs(t,x0);break;case"sqrtTall":i=bs(t,x0,r)}return i},xs=function(e,t){switch(e){case"⎜":return"M291 0 H417 V"+t+" H291z M291 0 H417 V"+t+" H291z";case"∣":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z";case"∥":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z"+("M367 0 H410 V"+t+" H367z M367 0 H410 V"+t+" H367z");case"⎟":return"M457 0 H583 V"+t+" H457z M457 0 H583 V"+t+" H457z";case"⎢":return"M319 0 H403 V"+t+" H319z M319 0 H403 V"+t+" H319z";case"⎥":return"M263 0 H347 V"+t+" H263z M263 0 H347 V"+t+" H263z";case"⎪":return"M384 0 H504 V"+t+" H384z M384 0 H504 V"+t+" H384z";case"⏐":return"M312 0 H355 V"+t+" H312z M312 0 H355 V"+t+" H312z";case"‖":return"M257 0 H300 V"+t+" H257z M257 0 H300 V"+t+" H257z"+("M478 0 H521 V"+t+" H478z M478 0 H521 V"+t+" H478z");default:return""}},Lr={doubleleftarrow:`M262 157
l10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3
 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28
 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5
c2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5
 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87
-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7
-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z
m8 0v40h399730v-40zm0 194v40h399730v-40z`,doublerightarrow:`M399738 392l
-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5
 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88
-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68
-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18
-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782
c-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3
-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z`,leftarrow:`M400000 241H110l3-3c68.7-52.7 113.7-120
 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8
-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247
c-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208
 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3
 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202
 l-3-3h399890zM100 241v40h399900v-40z`,leftbrace:`M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117
-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7
 5-6 9-10 13-.7 1-7.3 1-20 1H6z`,leftbraceunder:`M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13
 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688
 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7
-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z`,leftgroup:`M400000 80
H435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0
 435 0h399565z`,leftgroupunder:`M400000 262
H435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219
 435 219h399565z`,leftharpoon:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3
-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5
-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7
-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z`,leftharpoonplus:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5
 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3
-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7
-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z
m0 0v40h400000v-40z`,leftharpoondown:`M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333
 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5
 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667
-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z`,leftharpoondownplus:`M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12
 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7
-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0
v40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z`,lefthook:`M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5
-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3
-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21
 71.5 23h399859zM103 281v-40h399897v40z`,leftlinesegment:`M40 281 V428 H0 V94 H40 V241 H400000 v40z
M40 281 V428 H0 V94 H40 V241 H400000 v40z`,leftmapsto:`M40 281 V448H0V74H40V241H400000v40z
M40 281 V448H0V74H40V241H400000v40z`,leftToFrom:`M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23
-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8
c28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3
 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z`,longequal:`M0 50 h400000 v40H0z m0 194h40000v40H0z
M0 50 h400000 v40H0z m0 194h40000v40H0z`,midbrace:`M200428 334
c-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14
-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7
 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11
 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z`,midbraceunder:`M199572 214
c100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14
 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3
 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0
-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z`,oiintSize1:`M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6
-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z
m368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8
60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z`,oiintSize2:`M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8
-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z
m502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2
c0 110 84 276 504 276s502.4-166 502.4-276z`,oiiintSize1:`M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6
-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z
m525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0
85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z`,oiiintSize2:`M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8
-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z
m770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1
c0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z`,rightarrow:`M0 241v40h399891c-47.3 35.3-84 78-110 128
-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20
 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7
 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85
-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
 151.7 139 205zm0 0v40h399900v-40z`,rightbrace:`M400000 542l
-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5
s-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1
c124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z`,rightbraceunder:`M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3
 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237
-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z`,rightgroup:`M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0
 3-1 3-3v-38c-76-158-257-219-435-219H0z`,rightgroupunder:`M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18
 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z`,rightharpoon:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3
-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2
-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58
 69.2 92 94.5zm0 0v40h399900v-40z`,rightharpoonplus:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11
-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7
 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z
m0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z`,rightharpoondown:`M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8
 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5
-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95
-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z`,rightharpoondownplus:`M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8
 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3
 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3
-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z
m0-194v40h400000v-40zm0 0v40h400000v-40z`,righthook:`M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3
 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0
-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21
 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z`,rightlinesegment:`M399960 241 V94 h40 V428 h-40 V281 H0 v-40z
M399960 241 V94 h40 V428 h-40 V281 H0 v-40z`,rightToFrom:`M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23
 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32
-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142
-167z M100 147v40h399900v-40zM0 341v40h399900v-40z`,twoheadleftarrow:`M0 167c68 40
 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69
-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3
-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19
-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101
 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z`,twoheadrightarrow:`M400000 167
c-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3
 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42
 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333
-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70
 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z`,tilde1:`M200 55.538c-77 0-168 73.953-177 73.953-3 0-7
-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0
 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0
 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128
-68.267.847-113-73.952-191-73.952z`,tilde2:`M344 55.266c-142 0-300.638 81.316-311.5 86.418
-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9
 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114
c1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751
 181.476 676 181.476c-149 0-189-126.21-332-126.21z`,tilde3:`M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457
-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0
 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697
 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696
 -338 0-409-156.573-744-156.573z`,tilde4:`M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345
-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409
 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9
 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409
 -175.236-744-175.236z`,vec:`M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5
3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11
10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63
-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1
-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59
H213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359
c-16-25.333-24-45-24-59z`,widehat1:`M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22
c-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z`,widehat2:`M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat3:`M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat4:`M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widecheck1:`M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,
-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z`,widecheck2:`M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck3:`M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck4:`M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,baraboveleftarrow:`M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202
c4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5
c-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130
s-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47
121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6
s2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11
c0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z
M100 620v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z`,rightarrowabovebar:`M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32
-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0
13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39
-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5
-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z`,baraboveshortleftharpoon:`M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17
c2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21
c-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40
c-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z
M0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z`,rightharpoonaboveshortbar:`M0,241 l0,40c399126,0,399993,0,399993,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z`,shortbaraboveleftharpoon:`M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,
1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,
-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z
M93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z`,shortrightharpoonabovebar:`M53,241l0,40c398570,0,399437,0,399437,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z`},ws=function(e,t){switch(e){case"lbrack":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+` v1759 h347 v-84
H403z M403 1759 V0 H319 V1759 v`+t+" v1759 h84z";case"rbrack":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+` v1759 H0 v84 H347z
M347 1759 V0 H263 V1759 v`+t+" v1759 h84z";case"vert":return"M145 15 v585 v"+t+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-t+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+t+" v585 h43z";case"doublevert":return"M145 15 v585 v"+t+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-t+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+t+` v585 h43z
M367 15 v585 v`+t+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-t+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M410 15 H367 v585 v`+t+" v585 h43z";case"lfloor":return"M319 602 V0 H403 V602 v"+t+` v1715 h263 v84 H319z
MM319 602 V0 H403 V602 v`+t+" v1715 H319z";case"rfloor":return"M319 602 V0 H403 V602 v"+t+` v1799 H0 v-84 H319z
MM319 602 V0 H403 V602 v`+t+" v1715 H319z";case"lceil":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+` v602 h84z
M403 1759 V0 H319 V1759 v`+t+" v602 h84z";case"rceil":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+` v602 h84z
M347 1759 V0 h-84 V1759 v`+t+" v602 h84z";case"lparen":return`M863,9c0,-2,-2,-5,-6,-9c0,0,-17,0,-17,0c-12.7,0,-19.3,0.3,-20,1
c-5.3,5.3,-10.3,11,-15,17c-242.7,294.7,-395.3,682,-458,1162c-21.3,163.3,-33.3,349,
-36,557 l0,`+(t+84)+`c0.2,6,0,26,0,60c2,159.3,10,310.7,24,454c53.3,528,210,
949.7,470,1265c4.7,6,9.7,11.7,15,17c0.7,0.7,7,1,19,1c0,0,18,0,18,0c4,-4,6,-7,6,-9
c0,-2.7,-3.3,-8.7,-10,-18c-135.3,-192.7,-235.5,-414.3,-300.5,-665c-65,-250.7,-102.5,
-544.7,-112.5,-882c-2,-104,-3,-167,-3,-189
l0,-`+(t+92)+`c0,-162.7,5.7,-314,17,-454c20.7,-272,63.7,-513,129,-723c65.3,
-210,155.3,-396.3,270,-559c6.7,-9.3,10,-15.3,10,-18z`;case"rparen":return`M76,0c-16.7,0,-25,3,-25,9c0,2,2,6.3,6,13c21.3,28.7,42.3,60.3,
63,95c96.7,156.7,172.8,332.5,228.5,527.5c55.7,195,92.8,416.5,111.5,664.5
c11.3,139.3,17,290.7,17,454c0,28,1.7,43,3.3,45l0,`+(t+9)+`
c-3,4,-3.3,16.7,-3.3,38c0,162,-5.7,313.7,-17,455c-18.7,248,-55.8,469.3,-111.5,664
c-55.7,194.7,-131.8,370.3,-228.5,527c-20.7,34.7,-41.7,66.3,-63,95c-2,3.3,-4,7,-6,11
c0,7.3,5.7,11,17,11c0,0,11,0,11,0c9.3,0,14.3,-0.3,15,-1c5.3,-5.3,10.3,-11,15,-17
c242.7,-294.7,395.3,-681.7,458,-1161c21.3,-164.7,33.3,-350.7,36,-558
l0,-`+(t+144)+`c-2,-159.3,-10,-310.7,-24,-454c-53.3,-528,-210,-949.7,
-470,-1265c-4.7,-6,-9.7,-11.7,-15,-17c-0.7,-0.7,-6.7,-1,-18,-1z`;default:throw new Error("Unknown stretchy delimiter.")}};class H0{constructor(e){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=e,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}hasClass(e){return U.contains(this.classes,e)}toNode(){for(var e=document.createDocumentFragment(),t=0;t<this.children.length;t++)e.appendChild(this.children[t].toNode());return e}toMarkup(){for(var e="",t=0;t<this.children.length;t++)e+=this.children[t].toMarkup();return e}toText(){var e=t=>t.toText();return this.children.map(e).join("")}}var Pe={"AMS-Regular":{32:[0,0,0,0,.25],65:[0,.68889,0,0,.72222],66:[0,.68889,0,0,.66667],67:[0,.68889,0,0,.72222],68:[0,.68889,0,0,.72222],69:[0,.68889,0,0,.66667],70:[0,.68889,0,0,.61111],71:[0,.68889,0,0,.77778],72:[0,.68889,0,0,.77778],73:[0,.68889,0,0,.38889],74:[.16667,.68889,0,0,.5],75:[0,.68889,0,0,.77778],76:[0,.68889,0,0,.66667],77:[0,.68889,0,0,.94445],78:[0,.68889,0,0,.72222],79:[.16667,.68889,0,0,.77778],80:[0,.68889,0,0,.61111],81:[.16667,.68889,0,0,.77778],82:[0,.68889,0,0,.72222],83:[0,.68889,0,0,.55556],84:[0,.68889,0,0,.66667],85:[0,.68889,0,0,.72222],86:[0,.68889,0,0,.72222],87:[0,.68889,0,0,1],88:[0,.68889,0,0,.72222],89:[0,.68889,0,0,.72222],90:[0,.68889,0,0,.66667],107:[0,.68889,0,0,.55556],160:[0,0,0,0,.25],165:[0,.675,.025,0,.75],174:[.15559,.69224,0,0,.94666],240:[0,.68889,0,0,.55556],295:[0,.68889,0,0,.54028],710:[0,.825,0,0,2.33334],732:[0,.9,0,0,2.33334],770:[0,.825,0,0,2.33334],771:[0,.9,0,0,2.33334],989:[.08167,.58167,0,0,.77778],1008:[0,.43056,.04028,0,.66667],8245:[0,.54986,0,0,.275],8463:[0,.68889,0,0,.54028],8487:[0,.68889,0,0,.72222],8498:[0,.68889,0,0,.55556],8502:[0,.68889,0,0,.66667],8503:[0,.68889,0,0,.44445],8504:[0,.68889,0,0,.66667],8513:[0,.68889,0,0,.63889],8592:[-.03598,.46402,0,0,.5],8594:[-.03598,.46402,0,0,.5],8602:[-.13313,.36687,0,0,1],8603:[-.13313,.36687,0,0,1],8606:[.01354,.52239,0,0,1],8608:[.01354,.52239,0,0,1],8610:[.01354,.52239,0,0,1.11111],8611:[.01354,.52239,0,0,1.11111],8619:[0,.54986,0,0,1],8620:[0,.54986,0,0,1],8621:[-.13313,.37788,0,0,1.38889],8622:[-.13313,.36687,0,0,1],8624:[0,.69224,0,0,.5],8625:[0,.69224,0,0,.5],8630:[0,.43056,0,0,1],8631:[0,.43056,0,0,1],8634:[.08198,.58198,0,0,.77778],8635:[.08198,.58198,0,0,.77778],8638:[.19444,.69224,0,0,.41667],8639:[.19444,.69224,0,0,.41667],8642:[.19444,.69224,0,0,.41667],8643:[.19444,.69224,0,0,.41667],8644:[.1808,.675,0,0,1],8646:[.1808,.675,0,0,1],8647:[.1808,.675,0,0,1],8648:[.19444,.69224,0,0,.83334],8649:[.1808,.675,0,0,1],8650:[.19444,.69224,0,0,.83334],8651:[.01354,.52239,0,0,1],8652:[.01354,.52239,0,0,1],8653:[-.13313,.36687,0,0,1],8654:[-.13313,.36687,0,0,1],8655:[-.13313,.36687,0,0,1],8666:[.13667,.63667,0,0,1],8667:[.13667,.63667,0,0,1],8669:[-.13313,.37788,0,0,1],8672:[-.064,.437,0,0,1.334],8674:[-.064,.437,0,0,1.334],8705:[0,.825,0,0,.5],8708:[0,.68889,0,0,.55556],8709:[.08167,.58167,0,0,.77778],8717:[0,.43056,0,0,.42917],8722:[-.03598,.46402,0,0,.5],8724:[.08198,.69224,0,0,.77778],8726:[.08167,.58167,0,0,.77778],8733:[0,.69224,0,0,.77778],8736:[0,.69224,0,0,.72222],8737:[0,.69224,0,0,.72222],8738:[.03517,.52239,0,0,.72222],8739:[.08167,.58167,0,0,.22222],8740:[.25142,.74111,0,0,.27778],8741:[.08167,.58167,0,0,.38889],8742:[.25142,.74111,0,0,.5],8756:[0,.69224,0,0,.66667],8757:[0,.69224,0,0,.66667],8764:[-.13313,.36687,0,0,.77778],8765:[-.13313,.37788,0,0,.77778],8769:[-.13313,.36687,0,0,.77778],8770:[-.03625,.46375,0,0,.77778],8774:[.30274,.79383,0,0,.77778],8776:[-.01688,.48312,0,0,.77778],8778:[.08167,.58167,0,0,.77778],8782:[.06062,.54986,0,0,.77778],8783:[.06062,.54986,0,0,.77778],8785:[.08198,.58198,0,0,.77778],8786:[.08198,.58198,0,0,.77778],8787:[.08198,.58198,0,0,.77778],8790:[0,.69224,0,0,.77778],8791:[.22958,.72958,0,0,.77778],8796:[.08198,.91667,0,0,.77778],8806:[.25583,.75583,0,0,.77778],8807:[.25583,.75583,0,0,.77778],8808:[.25142,.75726,0,0,.77778],8809:[.25142,.75726,0,0,.77778],8812:[.25583,.75583,0,0,.5],8814:[.20576,.70576,0,0,.77778],8815:[.20576,.70576,0,0,.77778],8816:[.30274,.79383,0,0,.77778],8817:[.30274,.79383,0,0,.77778],8818:[.22958,.72958,0,0,.77778],8819:[.22958,.72958,0,0,.77778],8822:[.1808,.675,0,0,.77778],8823:[.1808,.675,0,0,.77778],8828:[.13667,.63667,0,0,.77778],8829:[.13667,.63667,0,0,.77778],8830:[.22958,.72958,0,0,.77778],8831:[.22958,.72958,0,0,.77778],8832:[.20576,.70576,0,0,.77778],8833:[.20576,.70576,0,0,.77778],8840:[.30274,.79383,0,0,.77778],8841:[.30274,.79383,0,0,.77778],8842:[.13597,.63597,0,0,.77778],8843:[.13597,.63597,0,0,.77778],8847:[.03517,.54986,0,0,.77778],8848:[.03517,.54986,0,0,.77778],8858:[.08198,.58198,0,0,.77778],8859:[.08198,.58198,0,0,.77778],8861:[.08198,.58198,0,0,.77778],8862:[0,.675,0,0,.77778],8863:[0,.675,0,0,.77778],8864:[0,.675,0,0,.77778],8865:[0,.675,0,0,.77778],8872:[0,.69224,0,0,.61111],8873:[0,.69224,0,0,.72222],8874:[0,.69224,0,0,.88889],8876:[0,.68889,0,0,.61111],8877:[0,.68889,0,0,.61111],8878:[0,.68889,0,0,.72222],8879:[0,.68889,0,0,.72222],8882:[.03517,.54986,0,0,.77778],8883:[.03517,.54986,0,0,.77778],8884:[.13667,.63667,0,0,.77778],8885:[.13667,.63667,0,0,.77778],8888:[0,.54986,0,0,1.11111],8890:[.19444,.43056,0,0,.55556],8891:[.19444,.69224,0,0,.61111],8892:[.19444,.69224,0,0,.61111],8901:[0,.54986,0,0,.27778],8903:[.08167,.58167,0,0,.77778],8905:[.08167,.58167,0,0,.77778],8906:[.08167,.58167,0,0,.77778],8907:[0,.69224,0,0,.77778],8908:[0,.69224,0,0,.77778],8909:[-.03598,.46402,0,0,.77778],8910:[0,.54986,0,0,.76042],8911:[0,.54986,0,0,.76042],8912:[.03517,.54986,0,0,.77778],8913:[.03517,.54986,0,0,.77778],8914:[0,.54986,0,0,.66667],8915:[0,.54986,0,0,.66667],8916:[0,.69224,0,0,.66667],8918:[.0391,.5391,0,0,.77778],8919:[.0391,.5391,0,0,.77778],8920:[.03517,.54986,0,0,1.33334],8921:[.03517,.54986,0,0,1.33334],8922:[.38569,.88569,0,0,.77778],8923:[.38569,.88569,0,0,.77778],8926:[.13667,.63667,0,0,.77778],8927:[.13667,.63667,0,0,.77778],8928:[.30274,.79383,0,0,.77778],8929:[.30274,.79383,0,0,.77778],8934:[.23222,.74111,0,0,.77778],8935:[.23222,.74111,0,0,.77778],8936:[.23222,.74111,0,0,.77778],8937:[.23222,.74111,0,0,.77778],8938:[.20576,.70576,0,0,.77778],8939:[.20576,.70576,0,0,.77778],8940:[.30274,.79383,0,0,.77778],8941:[.30274,.79383,0,0,.77778],8994:[.19444,.69224,0,0,.77778],8995:[.19444,.69224,0,0,.77778],9416:[.15559,.69224,0,0,.90222],9484:[0,.69224,0,0,.5],9488:[0,.69224,0,0,.5],9492:[0,.37788,0,0,.5],9496:[0,.37788,0,0,.5],9585:[.19444,.68889,0,0,.88889],9586:[.19444,.74111,0,0,.88889],9632:[0,.675,0,0,.77778],9633:[0,.675,0,0,.77778],9650:[0,.54986,0,0,.72222],9651:[0,.54986,0,0,.72222],9654:[.03517,.54986,0,0,.77778],9660:[0,.54986,0,0,.72222],9661:[0,.54986,0,0,.72222],9664:[.03517,.54986,0,0,.77778],9674:[.11111,.69224,0,0,.66667],9733:[.19444,.69224,0,0,.94445],10003:[0,.69224,0,0,.83334],10016:[0,.69224,0,0,.83334],10731:[.11111,.69224,0,0,.66667],10846:[.19444,.75583,0,0,.61111],10877:[.13667,.63667,0,0,.77778],10878:[.13667,.63667,0,0,.77778],10885:[.25583,.75583,0,0,.77778],10886:[.25583,.75583,0,0,.77778],10887:[.13597,.63597,0,0,.77778],10888:[.13597,.63597,0,0,.77778],10889:[.26167,.75726,0,0,.77778],10890:[.26167,.75726,0,0,.77778],10891:[.48256,.98256,0,0,.77778],10892:[.48256,.98256,0,0,.77778],10901:[.13667,.63667,0,0,.77778],10902:[.13667,.63667,0,0,.77778],10933:[.25142,.75726,0,0,.77778],10934:[.25142,.75726,0,0,.77778],10935:[.26167,.75726,0,0,.77778],10936:[.26167,.75726,0,0,.77778],10937:[.26167,.75726,0,0,.77778],10938:[.26167,.75726,0,0,.77778],10949:[.25583,.75583,0,0,.77778],10950:[.25583,.75583,0,0,.77778],10955:[.28481,.79383,0,0,.77778],10956:[.28481,.79383,0,0,.77778],57350:[.08167,.58167,0,0,.22222],57351:[.08167,.58167,0,0,.38889],57352:[.08167,.58167,0,0,.77778],57353:[0,.43056,.04028,0,.66667],57356:[.25142,.75726,0,0,.77778],57357:[.25142,.75726,0,0,.77778],57358:[.41951,.91951,0,0,.77778],57359:[.30274,.79383,0,0,.77778],57360:[.30274,.79383,0,0,.77778],57361:[.41951,.91951,0,0,.77778],57366:[.25142,.75726,0,0,.77778],57367:[.25142,.75726,0,0,.77778],57368:[.25142,.75726,0,0,.77778],57369:[.25142,.75726,0,0,.77778],57370:[.13597,.63597,0,0,.77778],57371:[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{32:[0,0,0,0,.25],65:[0,.68333,0,.19445,.79847],66:[0,.68333,.03041,.13889,.65681],67:[0,.68333,.05834,.13889,.52653],68:[0,.68333,.02778,.08334,.77139],69:[0,.68333,.08944,.11111,.52778],70:[0,.68333,.09931,.11111,.71875],71:[.09722,.68333,.0593,.11111,.59487],72:[0,.68333,.00965,.11111,.84452],73:[0,.68333,.07382,0,.54452],74:[.09722,.68333,.18472,.16667,.67778],75:[0,.68333,.01445,.05556,.76195],76:[0,.68333,0,.13889,.68972],77:[0,.68333,0,.13889,1.2009],78:[0,.68333,.14736,.08334,.82049],79:[0,.68333,.02778,.11111,.79611],80:[0,.68333,.08222,.08334,.69556],81:[.09722,.68333,0,.11111,.81667],82:[0,.68333,0,.08334,.8475],83:[0,.68333,.075,.13889,.60556],84:[0,.68333,.25417,0,.54464],85:[0,.68333,.09931,.08334,.62583],86:[0,.68333,.08222,0,.61278],87:[0,.68333,.08222,.08334,.98778],88:[0,.68333,.14643,.13889,.7133],89:[.09722,.68333,.08222,.08334,.66834],90:[0,.68333,.07944,.13889,.72473],160:[0,0,0,0,.25]},"Fraktur-Regular":{32:[0,0,0,0,.25],33:[0,.69141,0,0,.29574],34:[0,.69141,0,0,.21471],38:[0,.69141,0,0,.73786],39:[0,.69141,0,0,.21201],40:[.24982,.74947,0,0,.38865],41:[.24982,.74947,0,0,.38865],42:[0,.62119,0,0,.27764],43:[.08319,.58283,0,0,.75623],44:[0,.10803,0,0,.27764],45:[.08319,.58283,0,0,.75623],46:[0,.10803,0,0,.27764],47:[.24982,.74947,0,0,.50181],48:[0,.47534,0,0,.50181],49:[0,.47534,0,0,.50181],50:[0,.47534,0,0,.50181],51:[.18906,.47534,0,0,.50181],52:[.18906,.47534,0,0,.50181],53:[.18906,.47534,0,0,.50181],54:[0,.69141,0,0,.50181],55:[.18906,.47534,0,0,.50181],56:[0,.69141,0,0,.50181],57:[.18906,.47534,0,0,.50181],58:[0,.47534,0,0,.21606],59:[.12604,.47534,0,0,.21606],61:[-.13099,.36866,0,0,.75623],63:[0,.69141,0,0,.36245],65:[0,.69141,0,0,.7176],66:[0,.69141,0,0,.88397],67:[0,.69141,0,0,.61254],68:[0,.69141,0,0,.83158],69:[0,.69141,0,0,.66278],70:[.12604,.69141,0,0,.61119],71:[0,.69141,0,0,.78539],72:[.06302,.69141,0,0,.7203],73:[0,.69141,0,0,.55448],74:[.12604,.69141,0,0,.55231],75:[0,.69141,0,0,.66845],76:[0,.69141,0,0,.66602],77:[0,.69141,0,0,1.04953],78:[0,.69141,0,0,.83212],79:[0,.69141,0,0,.82699],80:[.18906,.69141,0,0,.82753],81:[.03781,.69141,0,0,.82699],82:[0,.69141,0,0,.82807],83:[0,.69141,0,0,.82861],84:[0,.69141,0,0,.66899],85:[0,.69141,0,0,.64576],86:[0,.69141,0,0,.83131],87:[0,.69141,0,0,1.04602],88:[0,.69141,0,0,.71922],89:[.18906,.69141,0,0,.83293],90:[.12604,.69141,0,0,.60201],91:[.24982,.74947,0,0,.27764],93:[.24982,.74947,0,0,.27764],94:[0,.69141,0,0,.49965],97:[0,.47534,0,0,.50046],98:[0,.69141,0,0,.51315],99:[0,.47534,0,0,.38946],100:[0,.62119,0,0,.49857],101:[0,.47534,0,0,.40053],102:[.18906,.69141,0,0,.32626],103:[.18906,.47534,0,0,.5037],104:[.18906,.69141,0,0,.52126],105:[0,.69141,0,0,.27899],106:[0,.69141,0,0,.28088],107:[0,.69141,0,0,.38946],108:[0,.69141,0,0,.27953],109:[0,.47534,0,0,.76676],110:[0,.47534,0,0,.52666],111:[0,.47534,0,0,.48885],112:[.18906,.52396,0,0,.50046],113:[.18906,.47534,0,0,.48912],114:[0,.47534,0,0,.38919],115:[0,.47534,0,0,.44266],116:[0,.62119,0,0,.33301],117:[0,.47534,0,0,.5172],118:[0,.52396,0,0,.5118],119:[0,.52396,0,0,.77351],120:[.18906,.47534,0,0,.38865],121:[.18906,.47534,0,0,.49884],122:[.18906,.47534,0,0,.39054],160:[0,0,0,0,.25],8216:[0,.69141,0,0,.21471],8217:[0,.69141,0,0,.21471],58112:[0,.62119,0,0,.49749],58113:[0,.62119,0,0,.4983],58114:[.18906,.69141,0,0,.33328],58115:[.18906,.69141,0,0,.32923],58116:[.18906,.47534,0,0,.50343],58117:[0,.69141,0,0,.33301],58118:[0,.62119,0,0,.33409],58119:[0,.47534,0,0,.50073]},"Main-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.35],34:[0,.69444,0,0,.60278],35:[.19444,.69444,0,0,.95833],36:[.05556,.75,0,0,.575],37:[.05556,.75,0,0,.95833],38:[0,.69444,0,0,.89444],39:[0,.69444,0,0,.31944],40:[.25,.75,0,0,.44722],41:[.25,.75,0,0,.44722],42:[0,.75,0,0,.575],43:[.13333,.63333,0,0,.89444],44:[.19444,.15556,0,0,.31944],45:[0,.44444,0,0,.38333],46:[0,.15556,0,0,.31944],47:[.25,.75,0,0,.575],48:[0,.64444,0,0,.575],49:[0,.64444,0,0,.575],50:[0,.64444,0,0,.575],51:[0,.64444,0,0,.575],52:[0,.64444,0,0,.575],53:[0,.64444,0,0,.575],54:[0,.64444,0,0,.575],55:[0,.64444,0,0,.575],56:[0,.64444,0,0,.575],57:[0,.64444,0,0,.575],58:[0,.44444,0,0,.31944],59:[.19444,.44444,0,0,.31944],60:[.08556,.58556,0,0,.89444],61:[-.10889,.39111,0,0,.89444],62:[.08556,.58556,0,0,.89444],63:[0,.69444,0,0,.54305],64:[0,.69444,0,0,.89444],65:[0,.68611,0,0,.86944],66:[0,.68611,0,0,.81805],67:[0,.68611,0,0,.83055],68:[0,.68611,0,0,.88194],69:[0,.68611,0,0,.75555],70:[0,.68611,0,0,.72361],71:[0,.68611,0,0,.90416],72:[0,.68611,0,0,.9],73:[0,.68611,0,0,.43611],74:[0,.68611,0,0,.59444],75:[0,.68611,0,0,.90138],76:[0,.68611,0,0,.69166],77:[0,.68611,0,0,1.09166],78:[0,.68611,0,0,.9],79:[0,.68611,0,0,.86388],80:[0,.68611,0,0,.78611],81:[.19444,.68611,0,0,.86388],82:[0,.68611,0,0,.8625],83:[0,.68611,0,0,.63889],84:[0,.68611,0,0,.8],85:[0,.68611,0,0,.88472],86:[0,.68611,.01597,0,.86944],87:[0,.68611,.01597,0,1.18888],88:[0,.68611,0,0,.86944],89:[0,.68611,.02875,0,.86944],90:[0,.68611,0,0,.70277],91:[.25,.75,0,0,.31944],92:[.25,.75,0,0,.575],93:[.25,.75,0,0,.31944],94:[0,.69444,0,0,.575],95:[.31,.13444,.03194,0,.575],97:[0,.44444,0,0,.55902],98:[0,.69444,0,0,.63889],99:[0,.44444,0,0,.51111],100:[0,.69444,0,0,.63889],101:[0,.44444,0,0,.52708],102:[0,.69444,.10903,0,.35139],103:[.19444,.44444,.01597,0,.575],104:[0,.69444,0,0,.63889],105:[0,.69444,0,0,.31944],106:[.19444,.69444,0,0,.35139],107:[0,.69444,0,0,.60694],108:[0,.69444,0,0,.31944],109:[0,.44444,0,0,.95833],110:[0,.44444,0,0,.63889],111:[0,.44444,0,0,.575],112:[.19444,.44444,0,0,.63889],113:[.19444,.44444,0,0,.60694],114:[0,.44444,0,0,.47361],115:[0,.44444,0,0,.45361],116:[0,.63492,0,0,.44722],117:[0,.44444,0,0,.63889],118:[0,.44444,.01597,0,.60694],119:[0,.44444,.01597,0,.83055],120:[0,.44444,0,0,.60694],121:[.19444,.44444,.01597,0,.60694],122:[0,.44444,0,0,.51111],123:[.25,.75,0,0,.575],124:[.25,.75,0,0,.31944],125:[.25,.75,0,0,.575],126:[.35,.34444,0,0,.575],160:[0,0,0,0,.25],163:[0,.69444,0,0,.86853],168:[0,.69444,0,0,.575],172:[0,.44444,0,0,.76666],176:[0,.69444,0,0,.86944],177:[.13333,.63333,0,0,.89444],184:[.17014,0,0,0,.51111],198:[0,.68611,0,0,1.04166],215:[.13333,.63333,0,0,.89444],216:[.04861,.73472,0,0,.89444],223:[0,.69444,0,0,.59722],230:[0,.44444,0,0,.83055],247:[.13333,.63333,0,0,.89444],248:[.09722,.54167,0,0,.575],305:[0,.44444,0,0,.31944],338:[0,.68611,0,0,1.16944],339:[0,.44444,0,0,.89444],567:[.19444,.44444,0,0,.35139],710:[0,.69444,0,0,.575],711:[0,.63194,0,0,.575],713:[0,.59611,0,0,.575],714:[0,.69444,0,0,.575],715:[0,.69444,0,0,.575],728:[0,.69444,0,0,.575],729:[0,.69444,0,0,.31944],730:[0,.69444,0,0,.86944],732:[0,.69444,0,0,.575],733:[0,.69444,0,0,.575],915:[0,.68611,0,0,.69166],916:[0,.68611,0,0,.95833],920:[0,.68611,0,0,.89444],923:[0,.68611,0,0,.80555],926:[0,.68611,0,0,.76666],928:[0,.68611,0,0,.9],931:[0,.68611,0,0,.83055],933:[0,.68611,0,0,.89444],934:[0,.68611,0,0,.83055],936:[0,.68611,0,0,.89444],937:[0,.68611,0,0,.83055],8211:[0,.44444,.03194,0,.575],8212:[0,.44444,.03194,0,1.14999],8216:[0,.69444,0,0,.31944],8217:[0,.69444,0,0,.31944],8220:[0,.69444,0,0,.60278],8221:[0,.69444,0,0,.60278],8224:[.19444,.69444,0,0,.51111],8225:[.19444,.69444,0,0,.51111],8242:[0,.55556,0,0,.34444],8407:[0,.72444,.15486,0,.575],8463:[0,.69444,0,0,.66759],8465:[0,.69444,0,0,.83055],8467:[0,.69444,0,0,.47361],8472:[.19444,.44444,0,0,.74027],8476:[0,.69444,0,0,.83055],8501:[0,.69444,0,0,.70277],8592:[-.10889,.39111,0,0,1.14999],8593:[.19444,.69444,0,0,.575],8594:[-.10889,.39111,0,0,1.14999],8595:[.19444,.69444,0,0,.575],8596:[-.10889,.39111,0,0,1.14999],8597:[.25,.75,0,0,.575],8598:[.19444,.69444,0,0,1.14999],8599:[.19444,.69444,0,0,1.14999],8600:[.19444,.69444,0,0,1.14999],8601:[.19444,.69444,0,0,1.14999],8636:[-.10889,.39111,0,0,1.14999],8637:[-.10889,.39111,0,0,1.14999],8640:[-.10889,.39111,0,0,1.14999],8641:[-.10889,.39111,0,0,1.14999],8656:[-.10889,.39111,0,0,1.14999],8657:[.19444,.69444,0,0,.70277],8658:[-.10889,.39111,0,0,1.14999],8659:[.19444,.69444,0,0,.70277],8660:[-.10889,.39111,0,0,1.14999],8661:[.25,.75,0,0,.70277],8704:[0,.69444,0,0,.63889],8706:[0,.69444,.06389,0,.62847],8707:[0,.69444,0,0,.63889],8709:[.05556,.75,0,0,.575],8711:[0,.68611,0,0,.95833],8712:[.08556,.58556,0,0,.76666],8715:[.08556,.58556,0,0,.76666],8722:[.13333,.63333,0,0,.89444],8723:[.13333,.63333,0,0,.89444],8725:[.25,.75,0,0,.575],8726:[.25,.75,0,0,.575],8727:[-.02778,.47222,0,0,.575],8728:[-.02639,.47361,0,0,.575],8729:[-.02639,.47361,0,0,.575],8730:[.18,.82,0,0,.95833],8733:[0,.44444,0,0,.89444],8734:[0,.44444,0,0,1.14999],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.31944],8741:[.25,.75,0,0,.575],8743:[0,.55556,0,0,.76666],8744:[0,.55556,0,0,.76666],8745:[0,.55556,0,0,.76666],8746:[0,.55556,0,0,.76666],8747:[.19444,.69444,.12778,0,.56875],8764:[-.10889,.39111,0,0,.89444],8768:[.19444,.69444,0,0,.31944],8771:[.00222,.50222,0,0,.89444],8773:[.027,.638,0,0,.894],8776:[.02444,.52444,0,0,.89444],8781:[.00222,.50222,0,0,.89444],8801:[.00222,.50222,0,0,.89444],8804:[.19667,.69667,0,0,.89444],8805:[.19667,.69667,0,0,.89444],8810:[.08556,.58556,0,0,1.14999],8811:[.08556,.58556,0,0,1.14999],8826:[.08556,.58556,0,0,.89444],8827:[.08556,.58556,0,0,.89444],8834:[.08556,.58556,0,0,.89444],8835:[.08556,.58556,0,0,.89444],8838:[.19667,.69667,0,0,.89444],8839:[.19667,.69667,0,0,.89444],8846:[0,.55556,0,0,.76666],8849:[.19667,.69667,0,0,.89444],8850:[.19667,.69667,0,0,.89444],8851:[0,.55556,0,0,.76666],8852:[0,.55556,0,0,.76666],8853:[.13333,.63333,0,0,.89444],8854:[.13333,.63333,0,0,.89444],8855:[.13333,.63333,0,0,.89444],8856:[.13333,.63333,0,0,.89444],8857:[.13333,.63333,0,0,.89444],8866:[0,.69444,0,0,.70277],8867:[0,.69444,0,0,.70277],8868:[0,.69444,0,0,.89444],8869:[0,.69444,0,0,.89444],8900:[-.02639,.47361,0,0,.575],8901:[-.02639,.47361,0,0,.31944],8902:[-.02778,.47222,0,0,.575],8968:[.25,.75,0,0,.51111],8969:[.25,.75,0,0,.51111],8970:[.25,.75,0,0,.51111],8971:[.25,.75,0,0,.51111],8994:[-.13889,.36111,0,0,1.14999],8995:[-.13889,.36111,0,0,1.14999],9651:[.19444,.69444,0,0,1.02222],9657:[-.02778,.47222,0,0,.575],9661:[.19444,.69444,0,0,1.02222],9667:[-.02778,.47222,0,0,.575],9711:[.19444,.69444,0,0,1.14999],9824:[.12963,.69444,0,0,.89444],9825:[.12963,.69444,0,0,.89444],9826:[.12963,.69444,0,0,.89444],9827:[.12963,.69444,0,0,.89444],9837:[0,.75,0,0,.44722],9838:[.19444,.69444,0,0,.44722],9839:[.19444,.69444,0,0,.44722],10216:[.25,.75,0,0,.44722],10217:[.25,.75,0,0,.44722],10815:[0,.68611,0,0,.9],10927:[.19667,.69667,0,0,.89444],10928:[.19667,.69667,0,0,.89444],57376:[.19444,.69444,0,0,0]},"Main-BoldItalic":{32:[0,0,0,0,.25],33:[0,.69444,.11417,0,.38611],34:[0,.69444,.07939,0,.62055],35:[.19444,.69444,.06833,0,.94444],37:[.05556,.75,.12861,0,.94444],38:[0,.69444,.08528,0,.88555],39:[0,.69444,.12945,0,.35555],40:[.25,.75,.15806,0,.47333],41:[.25,.75,.03306,0,.47333],42:[0,.75,.14333,0,.59111],43:[.10333,.60333,.03306,0,.88555],44:[.19444,.14722,0,0,.35555],45:[0,.44444,.02611,0,.41444],46:[0,.14722,0,0,.35555],47:[.25,.75,.15806,0,.59111],48:[0,.64444,.13167,0,.59111],49:[0,.64444,.13167,0,.59111],50:[0,.64444,.13167,0,.59111],51:[0,.64444,.13167,0,.59111],52:[.19444,.64444,.13167,0,.59111],53:[0,.64444,.13167,0,.59111],54:[0,.64444,.13167,0,.59111],55:[.19444,.64444,.13167,0,.59111],56:[0,.64444,.13167,0,.59111],57:[0,.64444,.13167,0,.59111],58:[0,.44444,.06695,0,.35555],59:[.19444,.44444,.06695,0,.35555],61:[-.10889,.39111,.06833,0,.88555],63:[0,.69444,.11472,0,.59111],64:[0,.69444,.09208,0,.88555],65:[0,.68611,0,0,.86555],66:[0,.68611,.0992,0,.81666],67:[0,.68611,.14208,0,.82666],68:[0,.68611,.09062,0,.87555],69:[0,.68611,.11431,0,.75666],70:[0,.68611,.12903,0,.72722],71:[0,.68611,.07347,0,.89527],72:[0,.68611,.17208,0,.8961],73:[0,.68611,.15681,0,.47166],74:[0,.68611,.145,0,.61055],75:[0,.68611,.14208,0,.89499],76:[0,.68611,0,0,.69777],77:[0,.68611,.17208,0,1.07277],78:[0,.68611,.17208,0,.8961],79:[0,.68611,.09062,0,.85499],80:[0,.68611,.0992,0,.78721],81:[.19444,.68611,.09062,0,.85499],82:[0,.68611,.02559,0,.85944],83:[0,.68611,.11264,0,.64999],84:[0,.68611,.12903,0,.7961],85:[0,.68611,.17208,0,.88083],86:[0,.68611,.18625,0,.86555],87:[0,.68611,.18625,0,1.15999],88:[0,.68611,.15681,0,.86555],89:[0,.68611,.19803,0,.86555],90:[0,.68611,.14208,0,.70888],91:[.25,.75,.1875,0,.35611],93:[.25,.75,.09972,0,.35611],94:[0,.69444,.06709,0,.59111],95:[.31,.13444,.09811,0,.59111],97:[0,.44444,.09426,0,.59111],98:[0,.69444,.07861,0,.53222],99:[0,.44444,.05222,0,.53222],100:[0,.69444,.10861,0,.59111],101:[0,.44444,.085,0,.53222],102:[.19444,.69444,.21778,0,.4],103:[.19444,.44444,.105,0,.53222],104:[0,.69444,.09426,0,.59111],105:[0,.69326,.11387,0,.35555],106:[.19444,.69326,.1672,0,.35555],107:[0,.69444,.11111,0,.53222],108:[0,.69444,.10861,0,.29666],109:[0,.44444,.09426,0,.94444],110:[0,.44444,.09426,0,.64999],111:[0,.44444,.07861,0,.59111],112:[.19444,.44444,.07861,0,.59111],113:[.19444,.44444,.105,0,.53222],114:[0,.44444,.11111,0,.50167],115:[0,.44444,.08167,0,.48694],116:[0,.63492,.09639,0,.385],117:[0,.44444,.09426,0,.62055],118:[0,.44444,.11111,0,.53222],119:[0,.44444,.11111,0,.76777],120:[0,.44444,.12583,0,.56055],121:[.19444,.44444,.105,0,.56166],122:[0,.44444,.13889,0,.49055],126:[.35,.34444,.11472,0,.59111],160:[0,0,0,0,.25],168:[0,.69444,.11473,0,.59111],176:[0,.69444,0,0,.94888],184:[.17014,0,0,0,.53222],198:[0,.68611,.11431,0,1.02277],216:[.04861,.73472,.09062,0,.88555],223:[.19444,.69444,.09736,0,.665],230:[0,.44444,.085,0,.82666],248:[.09722,.54167,.09458,0,.59111],305:[0,.44444,.09426,0,.35555],338:[0,.68611,.11431,0,1.14054],339:[0,.44444,.085,0,.82666],567:[.19444,.44444,.04611,0,.385],710:[0,.69444,.06709,0,.59111],711:[0,.63194,.08271,0,.59111],713:[0,.59444,.10444,0,.59111],714:[0,.69444,.08528,0,.59111],715:[0,.69444,0,0,.59111],728:[0,.69444,.10333,0,.59111],729:[0,.69444,.12945,0,.35555],730:[0,.69444,0,0,.94888],732:[0,.69444,.11472,0,.59111],733:[0,.69444,.11472,0,.59111],915:[0,.68611,.12903,0,.69777],916:[0,.68611,0,0,.94444],920:[0,.68611,.09062,0,.88555],923:[0,.68611,0,0,.80666],926:[0,.68611,.15092,0,.76777],928:[0,.68611,.17208,0,.8961],931:[0,.68611,.11431,0,.82666],933:[0,.68611,.10778,0,.88555],934:[0,.68611,.05632,0,.82666],936:[0,.68611,.10778,0,.88555],937:[0,.68611,.0992,0,.82666],8211:[0,.44444,.09811,0,.59111],8212:[0,.44444,.09811,0,1.18221],8216:[0,.69444,.12945,0,.35555],8217:[0,.69444,.12945,0,.35555],8220:[0,.69444,.16772,0,.62055],8221:[0,.69444,.07939,0,.62055]},"Main-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.12417,0,.30667],34:[0,.69444,.06961,0,.51444],35:[.19444,.69444,.06616,0,.81777],37:[.05556,.75,.13639,0,.81777],38:[0,.69444,.09694,0,.76666],39:[0,.69444,.12417,0,.30667],40:[.25,.75,.16194,0,.40889],41:[.25,.75,.03694,0,.40889],42:[0,.75,.14917,0,.51111],43:[.05667,.56167,.03694,0,.76666],44:[.19444,.10556,0,0,.30667],45:[0,.43056,.02826,0,.35778],46:[0,.10556,0,0,.30667],47:[.25,.75,.16194,0,.51111],48:[0,.64444,.13556,0,.51111],49:[0,.64444,.13556,0,.51111],50:[0,.64444,.13556,0,.51111],51:[0,.64444,.13556,0,.51111],52:[.19444,.64444,.13556,0,.51111],53:[0,.64444,.13556,0,.51111],54:[0,.64444,.13556,0,.51111],55:[.19444,.64444,.13556,0,.51111],56:[0,.64444,.13556,0,.51111],57:[0,.64444,.13556,0,.51111],58:[0,.43056,.0582,0,.30667],59:[.19444,.43056,.0582,0,.30667],61:[-.13313,.36687,.06616,0,.76666],63:[0,.69444,.1225,0,.51111],64:[0,.69444,.09597,0,.76666],65:[0,.68333,0,0,.74333],66:[0,.68333,.10257,0,.70389],67:[0,.68333,.14528,0,.71555],68:[0,.68333,.09403,0,.755],69:[0,.68333,.12028,0,.67833],70:[0,.68333,.13305,0,.65277],71:[0,.68333,.08722,0,.77361],72:[0,.68333,.16389,0,.74333],73:[0,.68333,.15806,0,.38555],74:[0,.68333,.14028,0,.525],75:[0,.68333,.14528,0,.76888],76:[0,.68333,0,0,.62722],77:[0,.68333,.16389,0,.89666],78:[0,.68333,.16389,0,.74333],79:[0,.68333,.09403,0,.76666],80:[0,.68333,.10257,0,.67833],81:[.19444,.68333,.09403,0,.76666],82:[0,.68333,.03868,0,.72944],83:[0,.68333,.11972,0,.56222],84:[0,.68333,.13305,0,.71555],85:[0,.68333,.16389,0,.74333],86:[0,.68333,.18361,0,.74333],87:[0,.68333,.18361,0,.99888],88:[0,.68333,.15806,0,.74333],89:[0,.68333,.19383,0,.74333],90:[0,.68333,.14528,0,.61333],91:[.25,.75,.1875,0,.30667],93:[.25,.75,.10528,0,.30667],94:[0,.69444,.06646,0,.51111],95:[.31,.12056,.09208,0,.51111],97:[0,.43056,.07671,0,.51111],98:[0,.69444,.06312,0,.46],99:[0,.43056,.05653,0,.46],100:[0,.69444,.10333,0,.51111],101:[0,.43056,.07514,0,.46],102:[.19444,.69444,.21194,0,.30667],103:[.19444,.43056,.08847,0,.46],104:[0,.69444,.07671,0,.51111],105:[0,.65536,.1019,0,.30667],106:[.19444,.65536,.14467,0,.30667],107:[0,.69444,.10764,0,.46],108:[0,.69444,.10333,0,.25555],109:[0,.43056,.07671,0,.81777],110:[0,.43056,.07671,0,.56222],111:[0,.43056,.06312,0,.51111],112:[.19444,.43056,.06312,0,.51111],113:[.19444,.43056,.08847,0,.46],114:[0,.43056,.10764,0,.42166],115:[0,.43056,.08208,0,.40889],116:[0,.61508,.09486,0,.33222],117:[0,.43056,.07671,0,.53666],118:[0,.43056,.10764,0,.46],119:[0,.43056,.10764,0,.66444],120:[0,.43056,.12042,0,.46389],121:[.19444,.43056,.08847,0,.48555],122:[0,.43056,.12292,0,.40889],126:[.35,.31786,.11585,0,.51111],160:[0,0,0,0,.25],168:[0,.66786,.10474,0,.51111],176:[0,.69444,0,0,.83129],184:[.17014,0,0,0,.46],198:[0,.68333,.12028,0,.88277],216:[.04861,.73194,.09403,0,.76666],223:[.19444,.69444,.10514,0,.53666],230:[0,.43056,.07514,0,.71555],248:[.09722,.52778,.09194,0,.51111],338:[0,.68333,.12028,0,.98499],339:[0,.43056,.07514,0,.71555],710:[0,.69444,.06646,0,.51111],711:[0,.62847,.08295,0,.51111],713:[0,.56167,.10333,0,.51111],714:[0,.69444,.09694,0,.51111],715:[0,.69444,0,0,.51111],728:[0,.69444,.10806,0,.51111],729:[0,.66786,.11752,0,.30667],730:[0,.69444,0,0,.83129],732:[0,.66786,.11585,0,.51111],733:[0,.69444,.1225,0,.51111],915:[0,.68333,.13305,0,.62722],916:[0,.68333,0,0,.81777],920:[0,.68333,.09403,0,.76666],923:[0,.68333,0,0,.69222],926:[0,.68333,.15294,0,.66444],928:[0,.68333,.16389,0,.74333],931:[0,.68333,.12028,0,.71555],933:[0,.68333,.11111,0,.76666],934:[0,.68333,.05986,0,.71555],936:[0,.68333,.11111,0,.76666],937:[0,.68333,.10257,0,.71555],8211:[0,.43056,.09208,0,.51111],8212:[0,.43056,.09208,0,1.02222],8216:[0,.69444,.12417,0,.30667],8217:[0,.69444,.12417,0,.30667],8220:[0,.69444,.1685,0,.51444],8221:[0,.69444,.06961,0,.51444],8463:[0,.68889,0,0,.54028]},"Main-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.27778],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.77778],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.19444,.10556,0,0,.27778],45:[0,.43056,0,0,.33333],46:[0,.10556,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.64444,0,0,.5],49:[0,.64444,0,0,.5],50:[0,.64444,0,0,.5],51:[0,.64444,0,0,.5],52:[0,.64444,0,0,.5],53:[0,.64444,0,0,.5],54:[0,.64444,0,0,.5],55:[0,.64444,0,0,.5],56:[0,.64444,0,0,.5],57:[0,.64444,0,0,.5],58:[0,.43056,0,0,.27778],59:[.19444,.43056,0,0,.27778],60:[.0391,.5391,0,0,.77778],61:[-.13313,.36687,0,0,.77778],62:[.0391,.5391,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.77778],65:[0,.68333,0,0,.75],66:[0,.68333,0,0,.70834],67:[0,.68333,0,0,.72222],68:[0,.68333,0,0,.76389],69:[0,.68333,0,0,.68056],70:[0,.68333,0,0,.65278],71:[0,.68333,0,0,.78472],72:[0,.68333,0,0,.75],73:[0,.68333,0,0,.36111],74:[0,.68333,0,0,.51389],75:[0,.68333,0,0,.77778],76:[0,.68333,0,0,.625],77:[0,.68333,0,0,.91667],78:[0,.68333,0,0,.75],79:[0,.68333,0,0,.77778],80:[0,.68333,0,0,.68056],81:[.19444,.68333,0,0,.77778],82:[0,.68333,0,0,.73611],83:[0,.68333,0,0,.55556],84:[0,.68333,0,0,.72222],85:[0,.68333,0,0,.75],86:[0,.68333,.01389,0,.75],87:[0,.68333,.01389,0,1.02778],88:[0,.68333,0,0,.75],89:[0,.68333,.025,0,.75],90:[0,.68333,0,0,.61111],91:[.25,.75,0,0,.27778],92:[.25,.75,0,0,.5],93:[.25,.75,0,0,.27778],94:[0,.69444,0,0,.5],95:[.31,.12056,.02778,0,.5],97:[0,.43056,0,0,.5],98:[0,.69444,0,0,.55556],99:[0,.43056,0,0,.44445],100:[0,.69444,0,0,.55556],101:[0,.43056,0,0,.44445],102:[0,.69444,.07778,0,.30556],103:[.19444,.43056,.01389,0,.5],104:[0,.69444,0,0,.55556],105:[0,.66786,0,0,.27778],106:[.19444,.66786,0,0,.30556],107:[0,.69444,0,0,.52778],108:[0,.69444,0,0,.27778],109:[0,.43056,0,0,.83334],110:[0,.43056,0,0,.55556],111:[0,.43056,0,0,.5],112:[.19444,.43056,0,0,.55556],113:[.19444,.43056,0,0,.52778],114:[0,.43056,0,0,.39167],115:[0,.43056,0,0,.39445],116:[0,.61508,0,0,.38889],117:[0,.43056,0,0,.55556],118:[0,.43056,.01389,0,.52778],119:[0,.43056,.01389,0,.72222],120:[0,.43056,0,0,.52778],121:[.19444,.43056,.01389,0,.52778],122:[0,.43056,0,0,.44445],123:[.25,.75,0,0,.5],124:[.25,.75,0,0,.27778],125:[.25,.75,0,0,.5],126:[.35,.31786,0,0,.5],160:[0,0,0,0,.25],163:[0,.69444,0,0,.76909],167:[.19444,.69444,0,0,.44445],168:[0,.66786,0,0,.5],172:[0,.43056,0,0,.66667],176:[0,.69444,0,0,.75],177:[.08333,.58333,0,0,.77778],182:[.19444,.69444,0,0,.61111],184:[.17014,0,0,0,.44445],198:[0,.68333,0,0,.90278],215:[.08333,.58333,0,0,.77778],216:[.04861,.73194,0,0,.77778],223:[0,.69444,0,0,.5],230:[0,.43056,0,0,.72222],247:[.08333,.58333,0,0,.77778],248:[.09722,.52778,0,0,.5],305:[0,.43056,0,0,.27778],338:[0,.68333,0,0,1.01389],339:[0,.43056,0,0,.77778],567:[.19444,.43056,0,0,.30556],710:[0,.69444,0,0,.5],711:[0,.62847,0,0,.5],713:[0,.56778,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.66786,0,0,.27778],730:[0,.69444,0,0,.75],732:[0,.66786,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.68333,0,0,.625],916:[0,.68333,0,0,.83334],920:[0,.68333,0,0,.77778],923:[0,.68333,0,0,.69445],926:[0,.68333,0,0,.66667],928:[0,.68333,0,0,.75],931:[0,.68333,0,0,.72222],933:[0,.68333,0,0,.77778],934:[0,.68333,0,0,.72222],936:[0,.68333,0,0,.77778],937:[0,.68333,0,0,.72222],8211:[0,.43056,.02778,0,.5],8212:[0,.43056,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5],8224:[.19444,.69444,0,0,.44445],8225:[.19444,.69444,0,0,.44445],8230:[0,.123,0,0,1.172],8242:[0,.55556,0,0,.275],8407:[0,.71444,.15382,0,.5],8463:[0,.68889,0,0,.54028],8465:[0,.69444,0,0,.72222],8467:[0,.69444,0,.11111,.41667],8472:[.19444,.43056,0,.11111,.63646],8476:[0,.69444,0,0,.72222],8501:[0,.69444,0,0,.61111],8592:[-.13313,.36687,0,0,1],8593:[.19444,.69444,0,0,.5],8594:[-.13313,.36687,0,0,1],8595:[.19444,.69444,0,0,.5],8596:[-.13313,.36687,0,0,1],8597:[.25,.75,0,0,.5],8598:[.19444,.69444,0,0,1],8599:[.19444,.69444,0,0,1],8600:[.19444,.69444,0,0,1],8601:[.19444,.69444,0,0,1],8614:[.011,.511,0,0,1],8617:[.011,.511,0,0,1.126],8618:[.011,.511,0,0,1.126],8636:[-.13313,.36687,0,0,1],8637:[-.13313,.36687,0,0,1],8640:[-.13313,.36687,0,0,1],8641:[-.13313,.36687,0,0,1],8652:[.011,.671,0,0,1],8656:[-.13313,.36687,0,0,1],8657:[.19444,.69444,0,0,.61111],8658:[-.13313,.36687,0,0,1],8659:[.19444,.69444,0,0,.61111],8660:[-.13313,.36687,0,0,1],8661:[.25,.75,0,0,.61111],8704:[0,.69444,0,0,.55556],8706:[0,.69444,.05556,.08334,.5309],8707:[0,.69444,0,0,.55556],8709:[.05556,.75,0,0,.5],8711:[0,.68333,0,0,.83334],8712:[.0391,.5391,0,0,.66667],8715:[.0391,.5391,0,0,.66667],8722:[.08333,.58333,0,0,.77778],8723:[.08333,.58333,0,0,.77778],8725:[.25,.75,0,0,.5],8726:[.25,.75,0,0,.5],8727:[-.03472,.46528,0,0,.5],8728:[-.05555,.44445,0,0,.5],8729:[-.05555,.44445,0,0,.5],8730:[.2,.8,0,0,.83334],8733:[0,.43056,0,0,.77778],8734:[0,.43056,0,0,1],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.27778],8741:[.25,.75,0,0,.5],8743:[0,.55556,0,0,.66667],8744:[0,.55556,0,0,.66667],8745:[0,.55556,0,0,.66667],8746:[0,.55556,0,0,.66667],8747:[.19444,.69444,.11111,0,.41667],8764:[-.13313,.36687,0,0,.77778],8768:[.19444,.69444,0,0,.27778],8771:[-.03625,.46375,0,0,.77778],8773:[-.022,.589,0,0,.778],8776:[-.01688,.48312,0,0,.77778],8781:[-.03625,.46375,0,0,.77778],8784:[-.133,.673,0,0,.778],8801:[-.03625,.46375,0,0,.77778],8804:[.13597,.63597,0,0,.77778],8805:[.13597,.63597,0,0,.77778],8810:[.0391,.5391,0,0,1],8811:[.0391,.5391,0,0,1],8826:[.0391,.5391,0,0,.77778],8827:[.0391,.5391,0,0,.77778],8834:[.0391,.5391,0,0,.77778],8835:[.0391,.5391,0,0,.77778],8838:[.13597,.63597,0,0,.77778],8839:[.13597,.63597,0,0,.77778],8846:[0,.55556,0,0,.66667],8849:[.13597,.63597,0,0,.77778],8850:[.13597,.63597,0,0,.77778],8851:[0,.55556,0,0,.66667],8852:[0,.55556,0,0,.66667],8853:[.08333,.58333,0,0,.77778],8854:[.08333,.58333,0,0,.77778],8855:[.08333,.58333,0,0,.77778],8856:[.08333,.58333,0,0,.77778],8857:[.08333,.58333,0,0,.77778],8866:[0,.69444,0,0,.61111],8867:[0,.69444,0,0,.61111],8868:[0,.69444,0,0,.77778],8869:[0,.69444,0,0,.77778],8872:[.249,.75,0,0,.867],8900:[-.05555,.44445,0,0,.5],8901:[-.05555,.44445,0,0,.27778],8902:[-.03472,.46528,0,0,.5],8904:[.005,.505,0,0,.9],8942:[.03,.903,0,0,.278],8943:[-.19,.313,0,0,1.172],8945:[-.1,.823,0,0,1.282],8968:[.25,.75,0,0,.44445],8969:[.25,.75,0,0,.44445],8970:[.25,.75,0,0,.44445],8971:[.25,.75,0,0,.44445],8994:[-.14236,.35764,0,0,1],8995:[-.14236,.35764,0,0,1],9136:[.244,.744,0,0,.412],9137:[.244,.745,0,0,.412],9651:[.19444,.69444,0,0,.88889],9657:[-.03472,.46528,0,0,.5],9661:[.19444,.69444,0,0,.88889],9667:[-.03472,.46528,0,0,.5],9711:[.19444,.69444,0,0,1],9824:[.12963,.69444,0,0,.77778],9825:[.12963,.69444,0,0,.77778],9826:[.12963,.69444,0,0,.77778],9827:[.12963,.69444,0,0,.77778],9837:[0,.75,0,0,.38889],9838:[.19444,.69444,0,0,.38889],9839:[.19444,.69444,0,0,.38889],10216:[.25,.75,0,0,.38889],10217:[.25,.75,0,0,.38889],10222:[.244,.744,0,0,.412],10223:[.244,.745,0,0,.412],10229:[.011,.511,0,0,1.609],10230:[.011,.511,0,0,1.638],10231:[.011,.511,0,0,1.859],10232:[.024,.525,0,0,1.609],10233:[.024,.525,0,0,1.638],10234:[.024,.525,0,0,1.858],10236:[.011,.511,0,0,1.638],10815:[0,.68333,0,0,.75],10927:[.13597,.63597,0,0,.77778],10928:[.13597,.63597,0,0,.77778],57376:[.19444,.69444,0,0,0]},"Math-BoldItalic":{32:[0,0,0,0,.25],48:[0,.44444,0,0,.575],49:[0,.44444,0,0,.575],50:[0,.44444,0,0,.575],51:[.19444,.44444,0,0,.575],52:[.19444,.44444,0,0,.575],53:[.19444,.44444,0,0,.575],54:[0,.64444,0,0,.575],55:[.19444,.44444,0,0,.575],56:[0,.64444,0,0,.575],57:[.19444,.44444,0,0,.575],65:[0,.68611,0,0,.86944],66:[0,.68611,.04835,0,.8664],67:[0,.68611,.06979,0,.81694],68:[0,.68611,.03194,0,.93812],69:[0,.68611,.05451,0,.81007],70:[0,.68611,.15972,0,.68889],71:[0,.68611,0,0,.88673],72:[0,.68611,.08229,0,.98229],73:[0,.68611,.07778,0,.51111],74:[0,.68611,.10069,0,.63125],75:[0,.68611,.06979,0,.97118],76:[0,.68611,0,0,.75555],77:[0,.68611,.11424,0,1.14201],78:[0,.68611,.11424,0,.95034],79:[0,.68611,.03194,0,.83666],80:[0,.68611,.15972,0,.72309],81:[.19444,.68611,0,0,.86861],82:[0,.68611,.00421,0,.87235],83:[0,.68611,.05382,0,.69271],84:[0,.68611,.15972,0,.63663],85:[0,.68611,.11424,0,.80027],86:[0,.68611,.25555,0,.67778],87:[0,.68611,.15972,0,1.09305],88:[0,.68611,.07778,0,.94722],89:[0,.68611,.25555,0,.67458],90:[0,.68611,.06979,0,.77257],97:[0,.44444,0,0,.63287],98:[0,.69444,0,0,.52083],99:[0,.44444,0,0,.51342],100:[0,.69444,0,0,.60972],101:[0,.44444,0,0,.55361],102:[.19444,.69444,.11042,0,.56806],103:[.19444,.44444,.03704,0,.5449],104:[0,.69444,0,0,.66759],105:[0,.69326,0,0,.4048],106:[.19444,.69326,.0622,0,.47083],107:[0,.69444,.01852,0,.6037],108:[0,.69444,.0088,0,.34815],109:[0,.44444,0,0,1.0324],110:[0,.44444,0,0,.71296],111:[0,.44444,0,0,.58472],112:[.19444,.44444,0,0,.60092],113:[.19444,.44444,.03704,0,.54213],114:[0,.44444,.03194,0,.5287],115:[0,.44444,0,0,.53125],116:[0,.63492,0,0,.41528],117:[0,.44444,0,0,.68102],118:[0,.44444,.03704,0,.56666],119:[0,.44444,.02778,0,.83148],120:[0,.44444,0,0,.65903],121:[.19444,.44444,.03704,0,.59028],122:[0,.44444,.04213,0,.55509],160:[0,0,0,0,.25],915:[0,.68611,.15972,0,.65694],916:[0,.68611,0,0,.95833],920:[0,.68611,.03194,0,.86722],923:[0,.68611,0,0,.80555],926:[0,.68611,.07458,0,.84125],928:[0,.68611,.08229,0,.98229],931:[0,.68611,.05451,0,.88507],933:[0,.68611,.15972,0,.67083],934:[0,.68611,0,0,.76666],936:[0,.68611,.11653,0,.71402],937:[0,.68611,.04835,0,.8789],945:[0,.44444,0,0,.76064],946:[.19444,.69444,.03403,0,.65972],947:[.19444,.44444,.06389,0,.59003],948:[0,.69444,.03819,0,.52222],949:[0,.44444,0,0,.52882],950:[.19444,.69444,.06215,0,.50833],951:[.19444,.44444,.03704,0,.6],952:[0,.69444,.03194,0,.5618],953:[0,.44444,0,0,.41204],954:[0,.44444,0,0,.66759],955:[0,.69444,0,0,.67083],956:[.19444,.44444,0,0,.70787],957:[0,.44444,.06898,0,.57685],958:[.19444,.69444,.03021,0,.50833],959:[0,.44444,0,0,.58472],960:[0,.44444,.03704,0,.68241],961:[.19444,.44444,0,0,.6118],962:[.09722,.44444,.07917,0,.42361],963:[0,.44444,.03704,0,.68588],964:[0,.44444,.13472,0,.52083],965:[0,.44444,.03704,0,.63055],966:[.19444,.44444,0,0,.74722],967:[.19444,.44444,0,0,.71805],968:[.19444,.69444,.03704,0,.75833],969:[0,.44444,.03704,0,.71782],977:[0,.69444,0,0,.69155],981:[.19444,.69444,0,0,.7125],982:[0,.44444,.03194,0,.975],1009:[.19444,.44444,0,0,.6118],1013:[0,.44444,0,0,.48333],57649:[0,.44444,0,0,.39352],57911:[.19444,.44444,0,0,.43889]},"Math-Italic":{32:[0,0,0,0,.25],48:[0,.43056,0,0,.5],49:[0,.43056,0,0,.5],50:[0,.43056,0,0,.5],51:[.19444,.43056,0,0,.5],52:[.19444,.43056,0,0,.5],53:[.19444,.43056,0,0,.5],54:[0,.64444,0,0,.5],55:[.19444,.43056,0,0,.5],56:[0,.64444,0,0,.5],57:[.19444,.43056,0,0,.5],65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],160:[0,0,0,0,.25],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059],57649:[0,.43056,0,.02778,.32246],57911:[.19444,.43056,0,.08334,.38403]},"SansSerif-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.36667],34:[0,.69444,0,0,.55834],35:[.19444,.69444,0,0,.91667],36:[.05556,.75,0,0,.55],37:[.05556,.75,0,0,1.02912],38:[0,.69444,0,0,.83056],39:[0,.69444,0,0,.30556],40:[.25,.75,0,0,.42778],41:[.25,.75,0,0,.42778],42:[0,.75,0,0,.55],43:[.11667,.61667,0,0,.85556],44:[.10556,.13056,0,0,.30556],45:[0,.45833,0,0,.36667],46:[0,.13056,0,0,.30556],47:[.25,.75,0,0,.55],48:[0,.69444,0,0,.55],49:[0,.69444,0,0,.55],50:[0,.69444,0,0,.55],51:[0,.69444,0,0,.55],52:[0,.69444,0,0,.55],53:[0,.69444,0,0,.55],54:[0,.69444,0,0,.55],55:[0,.69444,0,0,.55],56:[0,.69444,0,0,.55],57:[0,.69444,0,0,.55],58:[0,.45833,0,0,.30556],59:[.10556,.45833,0,0,.30556],61:[-.09375,.40625,0,0,.85556],63:[0,.69444,0,0,.51945],64:[0,.69444,0,0,.73334],65:[0,.69444,0,0,.73334],66:[0,.69444,0,0,.73334],67:[0,.69444,0,0,.70278],68:[0,.69444,0,0,.79445],69:[0,.69444,0,0,.64167],70:[0,.69444,0,0,.61111],71:[0,.69444,0,0,.73334],72:[0,.69444,0,0,.79445],73:[0,.69444,0,0,.33056],74:[0,.69444,0,0,.51945],75:[0,.69444,0,0,.76389],76:[0,.69444,0,0,.58056],77:[0,.69444,0,0,.97778],78:[0,.69444,0,0,.79445],79:[0,.69444,0,0,.79445],80:[0,.69444,0,0,.70278],81:[.10556,.69444,0,0,.79445],82:[0,.69444,0,0,.70278],83:[0,.69444,0,0,.61111],84:[0,.69444,0,0,.73334],85:[0,.69444,0,0,.76389],86:[0,.69444,.01528,0,.73334],87:[0,.69444,.01528,0,1.03889],88:[0,.69444,0,0,.73334],89:[0,.69444,.0275,0,.73334],90:[0,.69444,0,0,.67223],91:[.25,.75,0,0,.34306],93:[.25,.75,0,0,.34306],94:[0,.69444,0,0,.55],95:[.35,.10833,.03056,0,.55],97:[0,.45833,0,0,.525],98:[0,.69444,0,0,.56111],99:[0,.45833,0,0,.48889],100:[0,.69444,0,0,.56111],101:[0,.45833,0,0,.51111],102:[0,.69444,.07639,0,.33611],103:[.19444,.45833,.01528,0,.55],104:[0,.69444,0,0,.56111],105:[0,.69444,0,0,.25556],106:[.19444,.69444,0,0,.28611],107:[0,.69444,0,0,.53056],108:[0,.69444,0,0,.25556],109:[0,.45833,0,0,.86667],110:[0,.45833,0,0,.56111],111:[0,.45833,0,0,.55],112:[.19444,.45833,0,0,.56111],113:[.19444,.45833,0,0,.56111],114:[0,.45833,.01528,0,.37222],115:[0,.45833,0,0,.42167],116:[0,.58929,0,0,.40417],117:[0,.45833,0,0,.56111],118:[0,.45833,.01528,0,.5],119:[0,.45833,.01528,0,.74445],120:[0,.45833,0,0,.5],121:[.19444,.45833,.01528,0,.5],122:[0,.45833,0,0,.47639],126:[.35,.34444,0,0,.55],160:[0,0,0,0,.25],168:[0,.69444,0,0,.55],176:[0,.69444,0,0,.73334],180:[0,.69444,0,0,.55],184:[.17014,0,0,0,.48889],305:[0,.45833,0,0,.25556],567:[.19444,.45833,0,0,.28611],710:[0,.69444,0,0,.55],711:[0,.63542,0,0,.55],713:[0,.63778,0,0,.55],728:[0,.69444,0,0,.55],729:[0,.69444,0,0,.30556],730:[0,.69444,0,0,.73334],732:[0,.69444,0,0,.55],733:[0,.69444,0,0,.55],915:[0,.69444,0,0,.58056],916:[0,.69444,0,0,.91667],920:[0,.69444,0,0,.85556],923:[0,.69444,0,0,.67223],926:[0,.69444,0,0,.73334],928:[0,.69444,0,0,.79445],931:[0,.69444,0,0,.79445],933:[0,.69444,0,0,.85556],934:[0,.69444,0,0,.79445],936:[0,.69444,0,0,.85556],937:[0,.69444,0,0,.79445],8211:[0,.45833,.03056,0,.55],8212:[0,.45833,.03056,0,1.10001],8216:[0,.69444,0,0,.30556],8217:[0,.69444,0,0,.30556],8220:[0,.69444,0,0,.55834],8221:[0,.69444,0,0,.55834]},"SansSerif-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.05733,0,.31945],34:[0,.69444,.00316,0,.5],35:[.19444,.69444,.05087,0,.83334],36:[.05556,.75,.11156,0,.5],37:[.05556,.75,.03126,0,.83334],38:[0,.69444,.03058,0,.75834],39:[0,.69444,.07816,0,.27778],40:[.25,.75,.13164,0,.38889],41:[.25,.75,.02536,0,.38889],42:[0,.75,.11775,0,.5],43:[.08333,.58333,.02536,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,.01946,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,.13164,0,.5],48:[0,.65556,.11156,0,.5],49:[0,.65556,.11156,0,.5],50:[0,.65556,.11156,0,.5],51:[0,.65556,.11156,0,.5],52:[0,.65556,.11156,0,.5],53:[0,.65556,.11156,0,.5],54:[0,.65556,.11156,0,.5],55:[0,.65556,.11156,0,.5],56:[0,.65556,.11156,0,.5],57:[0,.65556,.11156,0,.5],58:[0,.44444,.02502,0,.27778],59:[.125,.44444,.02502,0,.27778],61:[-.13,.37,.05087,0,.77778],63:[0,.69444,.11809,0,.47222],64:[0,.69444,.07555,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,.08293,0,.66667],67:[0,.69444,.11983,0,.63889],68:[0,.69444,.07555,0,.72223],69:[0,.69444,.11983,0,.59722],70:[0,.69444,.13372,0,.56945],71:[0,.69444,.11983,0,.66667],72:[0,.69444,.08094,0,.70834],73:[0,.69444,.13372,0,.27778],74:[0,.69444,.08094,0,.47222],75:[0,.69444,.11983,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,.08094,0,.875],78:[0,.69444,.08094,0,.70834],79:[0,.69444,.07555,0,.73611],80:[0,.69444,.08293,0,.63889],81:[.125,.69444,.07555,0,.73611],82:[0,.69444,.08293,0,.64584],83:[0,.69444,.09205,0,.55556],84:[0,.69444,.13372,0,.68056],85:[0,.69444,.08094,0,.6875],86:[0,.69444,.1615,0,.66667],87:[0,.69444,.1615,0,.94445],88:[0,.69444,.13372,0,.66667],89:[0,.69444,.17261,0,.66667],90:[0,.69444,.11983,0,.61111],91:[.25,.75,.15942,0,.28889],93:[.25,.75,.08719,0,.28889],94:[0,.69444,.0799,0,.5],95:[.35,.09444,.08616,0,.5],97:[0,.44444,.00981,0,.48056],98:[0,.69444,.03057,0,.51667],99:[0,.44444,.08336,0,.44445],100:[0,.69444,.09483,0,.51667],101:[0,.44444,.06778,0,.44445],102:[0,.69444,.21705,0,.30556],103:[.19444,.44444,.10836,0,.5],104:[0,.69444,.01778,0,.51667],105:[0,.67937,.09718,0,.23889],106:[.19444,.67937,.09162,0,.26667],107:[0,.69444,.08336,0,.48889],108:[0,.69444,.09483,0,.23889],109:[0,.44444,.01778,0,.79445],110:[0,.44444,.01778,0,.51667],111:[0,.44444,.06613,0,.5],112:[.19444,.44444,.0389,0,.51667],113:[.19444,.44444,.04169,0,.51667],114:[0,.44444,.10836,0,.34167],115:[0,.44444,.0778,0,.38333],116:[0,.57143,.07225,0,.36111],117:[0,.44444,.04169,0,.51667],118:[0,.44444,.10836,0,.46111],119:[0,.44444,.10836,0,.68334],120:[0,.44444,.09169,0,.46111],121:[.19444,.44444,.10836,0,.46111],122:[0,.44444,.08752,0,.43472],126:[.35,.32659,.08826,0,.5],160:[0,0,0,0,.25],168:[0,.67937,.06385,0,.5],176:[0,.69444,0,0,.73752],184:[.17014,0,0,0,.44445],305:[0,.44444,.04169,0,.23889],567:[.19444,.44444,.04169,0,.26667],710:[0,.69444,.0799,0,.5],711:[0,.63194,.08432,0,.5],713:[0,.60889,.08776,0,.5],714:[0,.69444,.09205,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,.09483,0,.5],729:[0,.67937,.07774,0,.27778],730:[0,.69444,0,0,.73752],732:[0,.67659,.08826,0,.5],733:[0,.69444,.09205,0,.5],915:[0,.69444,.13372,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,.07555,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,.12816,0,.66667],928:[0,.69444,.08094,0,.70834],931:[0,.69444,.11983,0,.72222],933:[0,.69444,.09031,0,.77778],934:[0,.69444,.04603,0,.72222],936:[0,.69444,.09031,0,.77778],937:[0,.69444,.08293,0,.72222],8211:[0,.44444,.08616,0,.5],8212:[0,.44444,.08616,0,1],8216:[0,.69444,.07816,0,.27778],8217:[0,.69444,.07816,0,.27778],8220:[0,.69444,.14205,0,.5],8221:[0,.69444,.00316,0,.5]},"SansSerif-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.31945],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.75834],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,0,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.65556,0,0,.5],49:[0,.65556,0,0,.5],50:[0,.65556,0,0,.5],51:[0,.65556,0,0,.5],52:[0,.65556,0,0,.5],53:[0,.65556,0,0,.5],54:[0,.65556,0,0,.5],55:[0,.65556,0,0,.5],56:[0,.65556,0,0,.5],57:[0,.65556,0,0,.5],58:[0,.44444,0,0,.27778],59:[.125,.44444,0,0,.27778],61:[-.13,.37,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,0,0,.66667],67:[0,.69444,0,0,.63889],68:[0,.69444,0,0,.72223],69:[0,.69444,0,0,.59722],70:[0,.69444,0,0,.56945],71:[0,.69444,0,0,.66667],72:[0,.69444,0,0,.70834],73:[0,.69444,0,0,.27778],74:[0,.69444,0,0,.47222],75:[0,.69444,0,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,0,0,.875],78:[0,.69444,0,0,.70834],79:[0,.69444,0,0,.73611],80:[0,.69444,0,0,.63889],81:[.125,.69444,0,0,.73611],82:[0,.69444,0,0,.64584],83:[0,.69444,0,0,.55556],84:[0,.69444,0,0,.68056],85:[0,.69444,0,0,.6875],86:[0,.69444,.01389,0,.66667],87:[0,.69444,.01389,0,.94445],88:[0,.69444,0,0,.66667],89:[0,.69444,.025,0,.66667],90:[0,.69444,0,0,.61111],91:[.25,.75,0,0,.28889],93:[.25,.75,0,0,.28889],94:[0,.69444,0,0,.5],95:[.35,.09444,.02778,0,.5],97:[0,.44444,0,0,.48056],98:[0,.69444,0,0,.51667],99:[0,.44444,0,0,.44445],100:[0,.69444,0,0,.51667],101:[0,.44444,0,0,.44445],102:[0,.69444,.06944,0,.30556],103:[.19444,.44444,.01389,0,.5],104:[0,.69444,0,0,.51667],105:[0,.67937,0,0,.23889],106:[.19444,.67937,0,0,.26667],107:[0,.69444,0,0,.48889],108:[0,.69444,0,0,.23889],109:[0,.44444,0,0,.79445],110:[0,.44444,0,0,.51667],111:[0,.44444,0,0,.5],112:[.19444,.44444,0,0,.51667],113:[.19444,.44444,0,0,.51667],114:[0,.44444,.01389,0,.34167],115:[0,.44444,0,0,.38333],116:[0,.57143,0,0,.36111],117:[0,.44444,0,0,.51667],118:[0,.44444,.01389,0,.46111],119:[0,.44444,.01389,0,.68334],120:[0,.44444,0,0,.46111],121:[.19444,.44444,.01389,0,.46111],122:[0,.44444,0,0,.43472],126:[.35,.32659,0,0,.5],160:[0,0,0,0,.25],168:[0,.67937,0,0,.5],176:[0,.69444,0,0,.66667],184:[.17014,0,0,0,.44445],305:[0,.44444,0,0,.23889],567:[.19444,.44444,0,0,.26667],710:[0,.69444,0,0,.5],711:[0,.63194,0,0,.5],713:[0,.60889,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.67937,0,0,.27778],730:[0,.69444,0,0,.66667],732:[0,.67659,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.69444,0,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,0,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,0,0,.66667],928:[0,.69444,0,0,.70834],931:[0,.69444,0,0,.72222],933:[0,.69444,0,0,.77778],934:[0,.69444,0,0,.72222],936:[0,.69444,0,0,.77778],937:[0,.69444,0,0,.72222],8211:[0,.44444,.02778,0,.5],8212:[0,.44444,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5]},"Script-Regular":{32:[0,0,0,0,.25],65:[0,.7,.22925,0,.80253],66:[0,.7,.04087,0,.90757],67:[0,.7,.1689,0,.66619],68:[0,.7,.09371,0,.77443],69:[0,.7,.18583,0,.56162],70:[0,.7,.13634,0,.89544],71:[0,.7,.17322,0,.60961],72:[0,.7,.29694,0,.96919],73:[0,.7,.19189,0,.80907],74:[.27778,.7,.19189,0,1.05159],75:[0,.7,.31259,0,.91364],76:[0,.7,.19189,0,.87373],77:[0,.7,.15981,0,1.08031],78:[0,.7,.3525,0,.9015],79:[0,.7,.08078,0,.73787],80:[0,.7,.08078,0,1.01262],81:[0,.7,.03305,0,.88282],82:[0,.7,.06259,0,.85],83:[0,.7,.19189,0,.86767],84:[0,.7,.29087,0,.74697],85:[0,.7,.25815,0,.79996],86:[0,.7,.27523,0,.62204],87:[0,.7,.27523,0,.80532],88:[0,.7,.26006,0,.94445],89:[0,.7,.2939,0,.70961],90:[0,.7,.24037,0,.8212],160:[0,0,0,0,.25]},"Size1-Regular":{32:[0,0,0,0,.25],40:[.35001,.85,0,0,.45834],41:[.35001,.85,0,0,.45834],47:[.35001,.85,0,0,.57778],91:[.35001,.85,0,0,.41667],92:[.35001,.85,0,0,.57778],93:[.35001,.85,0,0,.41667],123:[.35001,.85,0,0,.58334],125:[.35001,.85,0,0,.58334],160:[0,0,0,0,.25],710:[0,.72222,0,0,.55556],732:[0,.72222,0,0,.55556],770:[0,.72222,0,0,.55556],771:[0,.72222,0,0,.55556],8214:[-99e-5,.601,0,0,.77778],8593:[1e-5,.6,0,0,.66667],8595:[1e-5,.6,0,0,.66667],8657:[1e-5,.6,0,0,.77778],8659:[1e-5,.6,0,0,.77778],8719:[.25001,.75,0,0,.94445],8720:[.25001,.75,0,0,.94445],8721:[.25001,.75,0,0,1.05556],8730:[.35001,.85,0,0,1],8739:[-.00599,.606,0,0,.33333],8741:[-.00599,.606,0,0,.55556],8747:[.30612,.805,.19445,0,.47222],8748:[.306,.805,.19445,0,.47222],8749:[.306,.805,.19445,0,.47222],8750:[.30612,.805,.19445,0,.47222],8896:[.25001,.75,0,0,.83334],8897:[.25001,.75,0,0,.83334],8898:[.25001,.75,0,0,.83334],8899:[.25001,.75,0,0,.83334],8968:[.35001,.85,0,0,.47222],8969:[.35001,.85,0,0,.47222],8970:[.35001,.85,0,0,.47222],8971:[.35001,.85,0,0,.47222],9168:[-99e-5,.601,0,0,.66667],10216:[.35001,.85,0,0,.47222],10217:[.35001,.85,0,0,.47222],10752:[.25001,.75,0,0,1.11111],10753:[.25001,.75,0,0,1.11111],10754:[.25001,.75,0,0,1.11111],10756:[.25001,.75,0,0,.83334],10758:[.25001,.75,0,0,.83334]},"Size2-Regular":{32:[0,0,0,0,.25],40:[.65002,1.15,0,0,.59722],41:[.65002,1.15,0,0,.59722],47:[.65002,1.15,0,0,.81111],91:[.65002,1.15,0,0,.47222],92:[.65002,1.15,0,0,.81111],93:[.65002,1.15,0,0,.47222],123:[.65002,1.15,0,0,.66667],125:[.65002,1.15,0,0,.66667],160:[0,0,0,0,.25],710:[0,.75,0,0,1],732:[0,.75,0,0,1],770:[0,.75,0,0,1],771:[0,.75,0,0,1],8719:[.55001,1.05,0,0,1.27778],8720:[.55001,1.05,0,0,1.27778],8721:[.55001,1.05,0,0,1.44445],8730:[.65002,1.15,0,0,1],8747:[.86225,1.36,.44445,0,.55556],8748:[.862,1.36,.44445,0,.55556],8749:[.862,1.36,.44445,0,.55556],8750:[.86225,1.36,.44445,0,.55556],8896:[.55001,1.05,0,0,1.11111],8897:[.55001,1.05,0,0,1.11111],8898:[.55001,1.05,0,0,1.11111],8899:[.55001,1.05,0,0,1.11111],8968:[.65002,1.15,0,0,.52778],8969:[.65002,1.15,0,0,.52778],8970:[.65002,1.15,0,0,.52778],8971:[.65002,1.15,0,0,.52778],10216:[.65002,1.15,0,0,.61111],10217:[.65002,1.15,0,0,.61111],10752:[.55001,1.05,0,0,1.51112],10753:[.55001,1.05,0,0,1.51112],10754:[.55001,1.05,0,0,1.51112],10756:[.55001,1.05,0,0,1.11111],10758:[.55001,1.05,0,0,1.11111]},"Size3-Regular":{32:[0,0,0,0,.25],40:[.95003,1.45,0,0,.73611],41:[.95003,1.45,0,0,.73611],47:[.95003,1.45,0,0,1.04445],91:[.95003,1.45,0,0,.52778],92:[.95003,1.45,0,0,1.04445],93:[.95003,1.45,0,0,.52778],123:[.95003,1.45,0,0,.75],125:[.95003,1.45,0,0,.75],160:[0,0,0,0,.25],710:[0,.75,0,0,1.44445],732:[0,.75,0,0,1.44445],770:[0,.75,0,0,1.44445],771:[0,.75,0,0,1.44445],8730:[.95003,1.45,0,0,1],8968:[.95003,1.45,0,0,.58334],8969:[.95003,1.45,0,0,.58334],8970:[.95003,1.45,0,0,.58334],8971:[.95003,1.45,0,0,.58334],10216:[.95003,1.45,0,0,.75],10217:[.95003,1.45,0,0,.75]},"Size4-Regular":{32:[0,0,0,0,.25],40:[1.25003,1.75,0,0,.79167],41:[1.25003,1.75,0,0,.79167],47:[1.25003,1.75,0,0,1.27778],91:[1.25003,1.75,0,0,.58334],92:[1.25003,1.75,0,0,1.27778],93:[1.25003,1.75,0,0,.58334],123:[1.25003,1.75,0,0,.80556],125:[1.25003,1.75,0,0,.80556],160:[0,0,0,0,.25],710:[0,.825,0,0,1.8889],732:[0,.825,0,0,1.8889],770:[0,.825,0,0,1.8889],771:[0,.825,0,0,1.8889],8730:[1.25003,1.75,0,0,1],8968:[1.25003,1.75,0,0,.63889],8969:[1.25003,1.75,0,0,.63889],8970:[1.25003,1.75,0,0,.63889],8971:[1.25003,1.75,0,0,.63889],9115:[.64502,1.155,0,0,.875],9116:[1e-5,.6,0,0,.875],9117:[.64502,1.155,0,0,.875],9118:[.64502,1.155,0,0,.875],9119:[1e-5,.6,0,0,.875],9120:[.64502,1.155,0,0,.875],9121:[.64502,1.155,0,0,.66667],9122:[-99e-5,.601,0,0,.66667],9123:[.64502,1.155,0,0,.66667],9124:[.64502,1.155,0,0,.66667],9125:[-99e-5,.601,0,0,.66667],9126:[.64502,1.155,0,0,.66667],9127:[1e-5,.9,0,0,.88889],9128:[.65002,1.15,0,0,.88889],9129:[.90001,0,0,0,.88889],9130:[0,.3,0,0,.88889],9131:[1e-5,.9,0,0,.88889],9132:[.65002,1.15,0,0,.88889],9133:[.90001,0,0,0,.88889],9143:[.88502,.915,0,0,1.05556],10216:[1.25003,1.75,0,0,.80556],10217:[1.25003,1.75,0,0,.80556],57344:[-.00499,.605,0,0,1.05556],57345:[-.00499,.605,0,0,1.05556],57680:[0,.12,0,0,.45],57681:[0,.12,0,0,.45],57682:[0,.12,0,0,.45],57683:[0,.12,0,0,.45]},"Typewriter-Regular":{32:[0,0,0,0,.525],33:[0,.61111,0,0,.525],34:[0,.61111,0,0,.525],35:[0,.61111,0,0,.525],36:[.08333,.69444,0,0,.525],37:[.08333,.69444,0,0,.525],38:[0,.61111,0,0,.525],39:[0,.61111,0,0,.525],40:[.08333,.69444,0,0,.525],41:[.08333,.69444,0,0,.525],42:[0,.52083,0,0,.525],43:[-.08056,.53055,0,0,.525],44:[.13889,.125,0,0,.525],45:[-.08056,.53055,0,0,.525],46:[0,.125,0,0,.525],47:[.08333,.69444,0,0,.525],48:[0,.61111,0,0,.525],49:[0,.61111,0,0,.525],50:[0,.61111,0,0,.525],51:[0,.61111,0,0,.525],52:[0,.61111,0,0,.525],53:[0,.61111,0,0,.525],54:[0,.61111,0,0,.525],55:[0,.61111,0,0,.525],56:[0,.61111,0,0,.525],57:[0,.61111,0,0,.525],58:[0,.43056,0,0,.525],59:[.13889,.43056,0,0,.525],60:[-.05556,.55556,0,0,.525],61:[-.19549,.41562,0,0,.525],62:[-.05556,.55556,0,0,.525],63:[0,.61111,0,0,.525],64:[0,.61111,0,0,.525],65:[0,.61111,0,0,.525],66:[0,.61111,0,0,.525],67:[0,.61111,0,0,.525],68:[0,.61111,0,0,.525],69:[0,.61111,0,0,.525],70:[0,.61111,0,0,.525],71:[0,.61111,0,0,.525],72:[0,.61111,0,0,.525],73:[0,.61111,0,0,.525],74:[0,.61111,0,0,.525],75:[0,.61111,0,0,.525],76:[0,.61111,0,0,.525],77:[0,.61111,0,0,.525],78:[0,.61111,0,0,.525],79:[0,.61111,0,0,.525],80:[0,.61111,0,0,.525],81:[.13889,.61111,0,0,.525],82:[0,.61111,0,0,.525],83:[0,.61111,0,0,.525],84:[0,.61111,0,0,.525],85:[0,.61111,0,0,.525],86:[0,.61111,0,0,.525],87:[0,.61111,0,0,.525],88:[0,.61111,0,0,.525],89:[0,.61111,0,0,.525],90:[0,.61111,0,0,.525],91:[.08333,.69444,0,0,.525],92:[.08333,.69444,0,0,.525],93:[.08333,.69444,0,0,.525],94:[0,.61111,0,0,.525],95:[.09514,0,0,0,.525],96:[0,.61111,0,0,.525],97:[0,.43056,0,0,.525],98:[0,.61111,0,0,.525],99:[0,.43056,0,0,.525],100:[0,.61111,0,0,.525],101:[0,.43056,0,0,.525],102:[0,.61111,0,0,.525],103:[.22222,.43056,0,0,.525],104:[0,.61111,0,0,.525],105:[0,.61111,0,0,.525],106:[.22222,.61111,0,0,.525],107:[0,.61111,0,0,.525],108:[0,.61111,0,0,.525],109:[0,.43056,0,0,.525],110:[0,.43056,0,0,.525],111:[0,.43056,0,0,.525],112:[.22222,.43056,0,0,.525],113:[.22222,.43056,0,0,.525],114:[0,.43056,0,0,.525],115:[0,.43056,0,0,.525],116:[0,.55358,0,0,.525],117:[0,.43056,0,0,.525],118:[0,.43056,0,0,.525],119:[0,.43056,0,0,.525],120:[0,.43056,0,0,.525],121:[.22222,.43056,0,0,.525],122:[0,.43056,0,0,.525],123:[.08333,.69444,0,0,.525],124:[.08333,.69444,0,0,.525],125:[.08333,.69444,0,0,.525],126:[0,.61111,0,0,.525],127:[0,.61111,0,0,.525],160:[0,0,0,0,.525],176:[0,.61111,0,0,.525],184:[.19445,0,0,0,.525],305:[0,.43056,0,0,.525],567:[.22222,.43056,0,0,.525],711:[0,.56597,0,0,.525],713:[0,.56555,0,0,.525],714:[0,.61111,0,0,.525],715:[0,.61111,0,0,.525],728:[0,.61111,0,0,.525],730:[0,.61111,0,0,.525],770:[0,.61111,0,0,.525],771:[0,.61111,0,0,.525],776:[0,.61111,0,0,.525],915:[0,.61111,0,0,.525],916:[0,.61111,0,0,.525],920:[0,.61111,0,0,.525],923:[0,.61111,0,0,.525],926:[0,.61111,0,0,.525],928:[0,.61111,0,0,.525],931:[0,.61111,0,0,.525],933:[0,.61111,0,0,.525],934:[0,.61111,0,0,.525],936:[0,.61111,0,0,.525],937:[0,.61111,0,0,.525],8216:[0,.61111,0,0,.525],8217:[0,.61111,0,0,.525],8242:[0,.61111,0,0,.525],9251:[.11111,.21944,0,0,.525]}},j0={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2],arrayRuleWidth:[.04,.04,.04],fboxsep:[.3,.3,.3],fboxrule:[.04,.04,.04]},Or={Å:"A",Ð:"D",Þ:"o",å:"a",ð:"d",þ:"o",А:"A",Б:"B",В:"B",Г:"F",Д:"A",Е:"E",Ж:"K",З:"3",И:"N",Й:"N",К:"K",Л:"N",М:"M",Н:"H",О:"O",П:"N",Р:"P",С:"C",Т:"T",У:"y",Ф:"O",Х:"X",Ц:"U",Ч:"h",Ш:"W",Щ:"W",Ъ:"B",Ы:"X",Ь:"B",Э:"3",Ю:"X",Я:"R",а:"a",б:"b",в:"a",г:"r",д:"y",е:"e",ж:"m",з:"e",и:"n",й:"n",к:"n",л:"n",м:"m",н:"n",о:"o",п:"n",р:"p",с:"c",т:"o",у:"y",ф:"b",х:"x",ц:"n",ч:"n",ш:"w",щ:"w",ъ:"a",ы:"m",ь:"a",э:"e",ю:"m",я:"r"};function ks(a,e){Pe[a]=e}function hr(a,e,t){if(!Pe[e])throw new Error("Font metrics not found for font: "+e+".");var r=a.charCodeAt(0),i=Pe[e][r];if(!i&&a[0]in Or&&(r=Or[a[0]].charCodeAt(0),i=Pe[e][r]),!i&&t==="text"&&qa(r)&&(i=Pe[e][77]),i)return{depth:i[0],height:i[1],italic:i[2],skew:i[3],width:i[4]}}var Mt={};function Ss(a){var e;if(a>=5?e=0:a>=3?e=1:e=2,!Mt[e]){var t=Mt[e]={cssEmPerMu:j0.quad[e]/18};for(var r in j0)j0.hasOwnProperty(r)&&(t[r]=j0[r][e])}return Mt[e]}var As=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],Hr=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],Fr=function(e,t){return t.size<2?e:As[e-1][t.size-1]};class je{constructor(e){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this.minRuleThickness=void 0,this._fontMetrics=void 0,this.style=e.style,this.color=e.color,this.size=e.size||je.BASESIZE,this.textSize=e.textSize||this.size,this.phantom=!!e.phantom,this.font=e.font||"",this.fontFamily=e.fontFamily||"",this.fontWeight=e.fontWeight||"",this.fontShape=e.fontShape||"",this.sizeMultiplier=Hr[this.size-1],this.maxSize=e.maxSize,this.minRuleThickness=e.minRuleThickness,this._fontMetrics=void 0}extend(e){var t={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize,minRuleThickness:this.minRuleThickness};for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);return new je(t)}havingStyle(e){return this.style===e?this:this.extend({style:e,size:Fr(this.textSize,e)})}havingCrampedStyle(){return this.havingStyle(this.style.cramp())}havingSize(e){return this.size===e&&this.textSize===e?this:this.extend({style:this.style.text(),size:e,textSize:e,sizeMultiplier:Hr[e-1]})}havingBaseStyle(e){e=e||this.style.text();var t=Fr(je.BASESIZE,e);return this.size===t&&this.textSize===je.BASESIZE&&this.style===e?this:this.extend({style:e,size:t})}havingBaseSizing(){var e;switch(this.style.id){case 4:case 5:e=3;break;case 6:case 7:e=1;break;default:e=6}return this.extend({style:this.style.text(),size:e})}withColor(e){return this.extend({color:e})}withPhantom(){return this.extend({phantom:!0})}withFont(e){return this.extend({font:e})}withTextFontFamily(e){return this.extend({fontFamily:e,font:""})}withTextFontWeight(e){return this.extend({fontWeight:e,font:""})}withTextFontShape(e){return this.extend({fontShape:e,font:""})}sizingClasses(e){return e.size!==this.size?["sizing","reset-size"+e.size,"size"+this.size]:[]}baseSizingClasses(){return this.size!==je.BASESIZE?["sizing","reset-size"+this.size,"size"+je.BASESIZE]:[]}fontMetrics(){return this._fontMetrics||(this._fontMetrics=Ss(this.size)),this._fontMetrics}getColor(){return this.phantom?"transparent":this.color}}je.BASESIZE=6;var Yt={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:803/800,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:803/800},zs={ex:!0,em:!0,mu:!0},Ia=function(e){return typeof e!="string"&&(e=e.unit),e in Yt||e in zs||e==="ex"},ue=function(e,t){var r;if(e.unit in Yt)r=Yt[e.unit]/t.fontMetrics().ptPerEm/t.sizeMultiplier;else if(e.unit==="mu")r=t.fontMetrics().cssEmPerMu;else{var i;if(t.style.isTight()?i=t.havingStyle(t.style.text()):i=t,e.unit==="ex")r=i.fontMetrics().xHeight;else if(e.unit==="em")r=i.fontMetrics().quad;else throw new M("Invalid unit: '"+e.unit+"'");i!==t&&(r*=i.sizeMultiplier/t.sizeMultiplier)}return Math.min(e.number*r,t.maxSize)},B=function(e){return+e.toFixed(4)+"em"},l0=function(e){return e.filter(t=>t).join(" ")},La=function(e,t,r){if(this.classes=e||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=r||{},t){t.style.isTight()&&this.classes.push("mtight");var i=t.getColor();i&&(this.style.color=i)}},Oa=function(e){var t=document.createElement(e);t.className=l0(this.classes);for(var r in this.style)this.style.hasOwnProperty(r)&&(t.style[r]=this.style[r]);for(var i in this.attributes)this.attributes.hasOwnProperty(i)&&t.setAttribute(i,this.attributes[i]);for(var n=0;n<this.children.length;n++)t.appendChild(this.children[n].toNode());return t},Ha=function(e){var t="<"+e;this.classes.length&&(t+=' class="'+U.escape(l0(this.classes))+'"');var r="";for(var i in this.style)this.style.hasOwnProperty(i)&&(r+=U.hyphenate(i)+":"+this.style[i]+";");r&&(t+=' style="'+U.escape(r)+'"');for(var n in this.attributes)this.attributes.hasOwnProperty(n)&&(t+=" "+n+'="'+U.escape(this.attributes[n])+'"');t+=">";for(var l=0;l<this.children.length;l++)t+=this.children[l].toMarkup();return t+="</"+e+">",t};class F0{constructor(e,t,r,i){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,La.call(this,e,r,i),this.children=t||[]}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return U.contains(this.classes,e)}toNode(){return Oa.call(this,"span")}toMarkup(){return Ha.call(this,"span")}}class cr{constructor(e,t,r,i){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,La.call(this,t,i),this.children=r||[],this.setAttribute("href",e)}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return U.contains(this.classes,e)}toNode(){return Oa.call(this,"a")}toMarkup(){return Ha.call(this,"a")}}class Ms{constructor(e,t,r){this.src=void 0,this.alt=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.alt=t,this.src=e,this.classes=["mord"],this.style=r}hasClass(e){return U.contains(this.classes,e)}toNode(){var e=document.createElement("img");e.src=this.src,e.alt=this.alt,e.className="mord";for(var t in this.style)this.style.hasOwnProperty(t)&&(e.style[t]=this.style[t]);return e}toMarkup(){var e="<img  src='"+this.src+" 'alt='"+this.alt+"' ",t="";for(var r in this.style)this.style.hasOwnProperty(r)&&(t+=U.hyphenate(r)+":"+this.style[r]+";");return t&&(e+=' style="'+U.escape(t)+'"'),e+="'/>",e}}var Ts={î:"ı̂",ï:"ı̈",í:"ı́",ì:"ı̀"};class qe{constructor(e,t,r,i,n,l,u,h){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=e,this.height=t||0,this.depth=r||0,this.italic=i||0,this.skew=n||0,this.width=l||0,this.classes=u||[],this.style=h||{},this.maxFontSize=0;var d=cs(this.text.charCodeAt(0));d&&this.classes.push(d+"_fallback"),/[îïíì]/.test(this.text)&&(this.text=Ts[this.text])}hasClass(e){return U.contains(this.classes,e)}toNode(){var e=document.createTextNode(this.text),t=null;this.italic>0&&(t=document.createElement("span"),t.style.marginRight=B(this.italic)),this.classes.length>0&&(t=t||document.createElement("span"),t.className=l0(this.classes));for(var r in this.style)this.style.hasOwnProperty(r)&&(t=t||document.createElement("span"),t.style[r]=this.style[r]);return t?(t.appendChild(e),t):e}toMarkup(){var e=!1,t="<span";this.classes.length&&(e=!0,t+=' class="',t+=U.escape(l0(this.classes)),t+='"');var r="";this.italic>0&&(r+="margin-right:"+this.italic+"em;");for(var i in this.style)this.style.hasOwnProperty(i)&&(r+=U.hyphenate(i)+":"+this.style[i]+";");r&&(e=!0,t+=' style="'+U.escape(r)+'"');var n=U.escape(this.text);return e?(t+=">",t+=n,t+="</span>",t):n}}class Xe{constructor(e,t){this.children=void 0,this.attributes=void 0,this.children=e||[],this.attributes=t||{}}toNode(){var e="http://www.w3.org/2000/svg",t=document.createElementNS(e,"svg");for(var r in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,r)&&t.setAttribute(r,this.attributes[r]);for(var i=0;i<this.children.length;i++)t.appendChild(this.children[i].toNode());return t}toMarkup(){var e='<svg xmlns="http://www.w3.org/2000/svg"';for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+"='"+this.attributes[t]+"'");e+=">";for(var r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e+="</svg>",e}}class o0{constructor(e,t){this.pathName=void 0,this.alternate=void 0,this.pathName=e,this.alternate=t}toNode(){var e="http://www.w3.org/2000/svg",t=document.createElementNS(e,"path");return this.alternate?t.setAttribute("d",this.alternate):t.setAttribute("d",Lr[this.pathName]),t}toMarkup(){return this.alternate?"<path d='"+this.alternate+"'/>":"<path d='"+Lr[this.pathName]+"'/>"}}class jt{constructor(e){this.attributes=void 0,this.attributes=e||{}}toNode(){var e="http://www.w3.org/2000/svg",t=document.createElementNS(e,"line");for(var r in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,r)&&t.setAttribute(r,this.attributes[r]);return t}toMarkup(){var e="<line";for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+"='"+this.attributes[t]+"'");return e+="/>",e}}function Pr(a){if(a instanceof qe)return a;throw new Error("Expected symbolNode but got "+String(a)+".")}function Bs(a){if(a instanceof F0)return a;throw new Error("Expected span<HtmlDomNode> but got "+String(a)+".")}var Cs={bin:1,close:1,inner:1,open:1,punct:1,rel:1},Rs={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},ne={math:{},text:{}};function s(a,e,t,r,i,n){ne[a][i]={font:e,group:t,replace:r},n&&r&&(ne[a][r]=ne[a][i])}var o="math",A="text",c="main",f="ams",se="accent-token",q="bin",we="close",A0="inner",P="mathord",me="op-token",Me="open",vt="punct",v="rel",Ze="spacing",b="textord";s(o,c,v,"≡","\\equiv",!0);s(o,c,v,"≺","\\prec",!0);s(o,c,v,"≻","\\succ",!0);s(o,c,v,"∼","\\sim",!0);s(o,c,v,"⊥","\\perp");s(o,c,v,"⪯","\\preceq",!0);s(o,c,v,"⪰","\\succeq",!0);s(o,c,v,"≃","\\simeq",!0);s(o,c,v,"∣","\\mid",!0);s(o,c,v,"≪","\\ll",!0);s(o,c,v,"≫","\\gg",!0);s(o,c,v,"≍","\\asymp",!0);s(o,c,v,"∥","\\parallel");s(o,c,v,"⋈","\\bowtie",!0);s(o,c,v,"⌣","\\smile",!0);s(o,c,v,"⊑","\\sqsubseteq",!0);s(o,c,v,"⊒","\\sqsupseteq",!0);s(o,c,v,"≐","\\doteq",!0);s(o,c,v,"⌢","\\frown",!0);s(o,c,v,"∋","\\ni",!0);s(o,c,v,"∝","\\propto",!0);s(o,c,v,"⊢","\\vdash",!0);s(o,c,v,"⊣","\\dashv",!0);s(o,c,v,"∋","\\owns");s(o,c,vt,".","\\ldotp");s(o,c,vt,"⋅","\\cdotp");s(o,c,b,"#","\\#");s(A,c,b,"#","\\#");s(o,c,b,"&","\\&");s(A,c,b,"&","\\&");s(o,c,b,"ℵ","\\aleph",!0);s(o,c,b,"∀","\\forall",!0);s(o,c,b,"ℏ","\\hbar",!0);s(o,c,b,"∃","\\exists",!0);s(o,c,b,"∇","\\nabla",!0);s(o,c,b,"♭","\\flat",!0);s(o,c,b,"ℓ","\\ell",!0);s(o,c,b,"♮","\\natural",!0);s(o,c,b,"♣","\\clubsuit",!0);s(o,c,b,"℘","\\wp",!0);s(o,c,b,"♯","\\sharp",!0);s(o,c,b,"♢","\\diamondsuit",!0);s(o,c,b,"ℜ","\\Re",!0);s(o,c,b,"♡","\\heartsuit",!0);s(o,c,b,"ℑ","\\Im",!0);s(o,c,b,"♠","\\spadesuit",!0);s(o,c,b,"§","\\S",!0);s(A,c,b,"§","\\S");s(o,c,b,"¶","\\P",!0);s(A,c,b,"¶","\\P");s(o,c,b,"†","\\dag");s(A,c,b,"†","\\dag");s(A,c,b,"†","\\textdagger");s(o,c,b,"‡","\\ddag");s(A,c,b,"‡","\\ddag");s(A,c,b,"‡","\\textdaggerdbl");s(o,c,we,"⎱","\\rmoustache",!0);s(o,c,Me,"⎰","\\lmoustache",!0);s(o,c,we,"⟯","\\rgroup",!0);s(o,c,Me,"⟮","\\lgroup",!0);s(o,c,q,"∓","\\mp",!0);s(o,c,q,"⊖","\\ominus",!0);s(o,c,q,"⊎","\\uplus",!0);s(o,c,q,"⊓","\\sqcap",!0);s(o,c,q,"∗","\\ast");s(o,c,q,"⊔","\\sqcup",!0);s(o,c,q,"◯","\\bigcirc",!0);s(o,c,q,"∙","\\bullet",!0);s(o,c,q,"‡","\\ddagger");s(o,c,q,"≀","\\wr",!0);s(o,c,q,"⨿","\\amalg");s(o,c,q,"&","\\And");s(o,c,v,"⟵","\\longleftarrow",!0);s(o,c,v,"⇐","\\Leftarrow",!0);s(o,c,v,"⟸","\\Longleftarrow",!0);s(o,c,v,"⟶","\\longrightarrow",!0);s(o,c,v,"⇒","\\Rightarrow",!0);s(o,c,v,"⟹","\\Longrightarrow",!0);s(o,c,v,"↔","\\leftrightarrow",!0);s(o,c,v,"⟷","\\longleftrightarrow",!0);s(o,c,v,"⇔","\\Leftrightarrow",!0);s(o,c,v,"⟺","\\Longleftrightarrow",!0);s(o,c,v,"↦","\\mapsto",!0);s(o,c,v,"⟼","\\longmapsto",!0);s(o,c,v,"↗","\\nearrow",!0);s(o,c,v,"↩","\\hookleftarrow",!0);s(o,c,v,"↪","\\hookrightarrow",!0);s(o,c,v,"↘","\\searrow",!0);s(o,c,v,"↼","\\leftharpoonup",!0);s(o,c,v,"⇀","\\rightharpoonup",!0);s(o,c,v,"↙","\\swarrow",!0);s(o,c,v,"↽","\\leftharpoondown",!0);s(o,c,v,"⇁","\\rightharpoondown",!0);s(o,c,v,"↖","\\nwarrow",!0);s(o,c,v,"⇌","\\rightleftharpoons",!0);s(o,f,v,"≮","\\nless",!0);s(o,f,v,"","\\@nleqslant");s(o,f,v,"","\\@nleqq");s(o,f,v,"⪇","\\lneq",!0);s(o,f,v,"≨","\\lneqq",!0);s(o,f,v,"","\\@lvertneqq");s(o,f,v,"⋦","\\lnsim",!0);s(o,f,v,"⪉","\\lnapprox",!0);s(o,f,v,"⊀","\\nprec",!0);s(o,f,v,"⋠","\\npreceq",!0);s(o,f,v,"⋨","\\precnsim",!0);s(o,f,v,"⪹","\\precnapprox",!0);s(o,f,v,"≁","\\nsim",!0);s(o,f,v,"","\\@nshortmid");s(o,f,v,"∤","\\nmid",!0);s(o,f,v,"⊬","\\nvdash",!0);s(o,f,v,"⊭","\\nvDash",!0);s(o,f,v,"⋪","\\ntriangleleft");s(o,f,v,"⋬","\\ntrianglelefteq",!0);s(o,f,v,"⊊","\\subsetneq",!0);s(o,f,v,"","\\@varsubsetneq");s(o,f,v,"⫋","\\subsetneqq",!0);s(o,f,v,"","\\@varsubsetneqq");s(o,f,v,"≯","\\ngtr",!0);s(o,f,v,"","\\@ngeqslant");s(o,f,v,"","\\@ngeqq");s(o,f,v,"⪈","\\gneq",!0);s(o,f,v,"≩","\\gneqq",!0);s(o,f,v,"","\\@gvertneqq");s(o,f,v,"⋧","\\gnsim",!0);s(o,f,v,"⪊","\\gnapprox",!0);s(o,f,v,"⊁","\\nsucc",!0);s(o,f,v,"⋡","\\nsucceq",!0);s(o,f,v,"⋩","\\succnsim",!0);s(o,f,v,"⪺","\\succnapprox",!0);s(o,f,v,"≆","\\ncong",!0);s(o,f,v,"","\\@nshortparallel");s(o,f,v,"∦","\\nparallel",!0);s(o,f,v,"⊯","\\nVDash",!0);s(o,f,v,"⋫","\\ntriangleright");s(o,f,v,"⋭","\\ntrianglerighteq",!0);s(o,f,v,"","\\@nsupseteqq");s(o,f,v,"⊋","\\supsetneq",!0);s(o,f,v,"","\\@varsupsetneq");s(o,f,v,"⫌","\\supsetneqq",!0);s(o,f,v,"","\\@varsupsetneqq");s(o,f,v,"⊮","\\nVdash",!0);s(o,f,v,"⪵","\\precneqq",!0);s(o,f,v,"⪶","\\succneqq",!0);s(o,f,v,"","\\@nsubseteqq");s(o,f,q,"⊴","\\unlhd");s(o,f,q,"⊵","\\unrhd");s(o,f,v,"↚","\\nleftarrow",!0);s(o,f,v,"↛","\\nrightarrow",!0);s(o,f,v,"⇍","\\nLeftarrow",!0);s(o,f,v,"⇏","\\nRightarrow",!0);s(o,f,v,"↮","\\nleftrightarrow",!0);s(o,f,v,"⇎","\\nLeftrightarrow",!0);s(o,f,v,"△","\\vartriangle");s(o,f,b,"ℏ","\\hslash");s(o,f,b,"▽","\\triangledown");s(o,f,b,"◊","\\lozenge");s(o,f,b,"Ⓢ","\\circledS");s(o,f,b,"®","\\circledR");s(A,f,b,"®","\\circledR");s(o,f,b,"∡","\\measuredangle",!0);s(o,f,b,"∄","\\nexists");s(o,f,b,"℧","\\mho");s(o,f,b,"Ⅎ","\\Finv",!0);s(o,f,b,"⅁","\\Game",!0);s(o,f,b,"‵","\\backprime");s(o,f,b,"▲","\\blacktriangle");s(o,f,b,"▼","\\blacktriangledown");s(o,f,b,"■","\\blacksquare");s(o,f,b,"⧫","\\blacklozenge");s(o,f,b,"★","\\bigstar");s(o,f,b,"∢","\\sphericalangle",!0);s(o,f,b,"∁","\\complement",!0);s(o,f,b,"ð","\\eth",!0);s(A,c,b,"ð","ð");s(o,f,b,"╱","\\diagup");s(o,f,b,"╲","\\diagdown");s(o,f,b,"□","\\square");s(o,f,b,"□","\\Box");s(o,f,b,"◊","\\Diamond");s(o,f,b,"¥","\\yen",!0);s(A,f,b,"¥","\\yen",!0);s(o,f,b,"✓","\\checkmark",!0);s(A,f,b,"✓","\\checkmark");s(o,f,b,"ℶ","\\beth",!0);s(o,f,b,"ℸ","\\daleth",!0);s(o,f,b,"ℷ","\\gimel",!0);s(o,f,b,"ϝ","\\digamma",!0);s(o,f,b,"ϰ","\\varkappa");s(o,f,Me,"┌","\\@ulcorner",!0);s(o,f,we,"┐","\\@urcorner",!0);s(o,f,Me,"└","\\@llcorner",!0);s(o,f,we,"┘","\\@lrcorner",!0);s(o,f,v,"≦","\\leqq",!0);s(o,f,v,"⩽","\\leqslant",!0);s(o,f,v,"⪕","\\eqslantless",!0);s(o,f,v,"≲","\\lesssim",!0);s(o,f,v,"⪅","\\lessapprox",!0);s(o,f,v,"≊","\\approxeq",!0);s(o,f,q,"⋖","\\lessdot");s(o,f,v,"⋘","\\lll",!0);s(o,f,v,"≶","\\lessgtr",!0);s(o,f,v,"⋚","\\lesseqgtr",!0);s(o,f,v,"⪋","\\lesseqqgtr",!0);s(o,f,v,"≑","\\doteqdot");s(o,f,v,"≓","\\risingdotseq",!0);s(o,f,v,"≒","\\fallingdotseq",!0);s(o,f,v,"∽","\\backsim",!0);s(o,f,v,"⋍","\\backsimeq",!0);s(o,f,v,"⫅","\\subseteqq",!0);s(o,f,v,"⋐","\\Subset",!0);s(o,f,v,"⊏","\\sqsubset",!0);s(o,f,v,"≼","\\preccurlyeq",!0);s(o,f,v,"⋞","\\curlyeqprec",!0);s(o,f,v,"≾","\\precsim",!0);s(o,f,v,"⪷","\\precapprox",!0);s(o,f,v,"⊲","\\vartriangleleft");s(o,f,v,"⊴","\\trianglelefteq");s(o,f,v,"⊨","\\vDash",!0);s(o,f,v,"⊪","\\Vvdash",!0);s(o,f,v,"⌣","\\smallsmile");s(o,f,v,"⌢","\\smallfrown");s(o,f,v,"≏","\\bumpeq",!0);s(o,f,v,"≎","\\Bumpeq",!0);s(o,f,v,"≧","\\geqq",!0);s(o,f,v,"⩾","\\geqslant",!0);s(o,f,v,"⪖","\\eqslantgtr",!0);s(o,f,v,"≳","\\gtrsim",!0);s(o,f,v,"⪆","\\gtrapprox",!0);s(o,f,q,"⋗","\\gtrdot");s(o,f,v,"⋙","\\ggg",!0);s(o,f,v,"≷","\\gtrless",!0);s(o,f,v,"⋛","\\gtreqless",!0);s(o,f,v,"⪌","\\gtreqqless",!0);s(o,f,v,"≖","\\eqcirc",!0);s(o,f,v,"≗","\\circeq",!0);s(o,f,v,"≜","\\triangleq",!0);s(o,f,v,"∼","\\thicksim");s(o,f,v,"≈","\\thickapprox");s(o,f,v,"⫆","\\supseteqq",!0);s(o,f,v,"⋑","\\Supset",!0);s(o,f,v,"⊐","\\sqsupset",!0);s(o,f,v,"≽","\\succcurlyeq",!0);s(o,f,v,"⋟","\\curlyeqsucc",!0);s(o,f,v,"≿","\\succsim",!0);s(o,f,v,"⪸","\\succapprox",!0);s(o,f,v,"⊳","\\vartriangleright");s(o,f,v,"⊵","\\trianglerighteq");s(o,f,v,"⊩","\\Vdash",!0);s(o,f,v,"∣","\\shortmid");s(o,f,v,"∥","\\shortparallel");s(o,f,v,"≬","\\between",!0);s(o,f,v,"⋔","\\pitchfork",!0);s(o,f,v,"∝","\\varpropto");s(o,f,v,"◀","\\blacktriangleleft");s(o,f,v,"∴","\\therefore",!0);s(o,f,v,"∍","\\backepsilon");s(o,f,v,"▶","\\blacktriangleright");s(o,f,v,"∵","\\because",!0);s(o,f,v,"⋘","\\llless");s(o,f,v,"⋙","\\gggtr");s(o,f,q,"⊲","\\lhd");s(o,f,q,"⊳","\\rhd");s(o,f,v,"≂","\\eqsim",!0);s(o,c,v,"⋈","\\Join");s(o,f,v,"≑","\\Doteq",!0);s(o,f,q,"∔","\\dotplus",!0);s(o,f,q,"∖","\\smallsetminus");s(o,f,q,"⋒","\\Cap",!0);s(o,f,q,"⋓","\\Cup",!0);s(o,f,q,"⩞","\\doublebarwedge",!0);s(o,f,q,"⊟","\\boxminus",!0);s(o,f,q,"⊞","\\boxplus",!0);s(o,f,q,"⋇","\\divideontimes",!0);s(o,f,q,"⋉","\\ltimes",!0);s(o,f,q,"⋊","\\rtimes",!0);s(o,f,q,"⋋","\\leftthreetimes",!0);s(o,f,q,"⋌","\\rightthreetimes",!0);s(o,f,q,"⋏","\\curlywedge",!0);s(o,f,q,"⋎","\\curlyvee",!0);s(o,f,q,"⊝","\\circleddash",!0);s(o,f,q,"⊛","\\circledast",!0);s(o,f,q,"⋅","\\centerdot");s(o,f,q,"⊺","\\intercal",!0);s(o,f,q,"⋒","\\doublecap");s(o,f,q,"⋓","\\doublecup");s(o,f,q,"⊠","\\boxtimes",!0);s(o,f,v,"⇢","\\dashrightarrow",!0);s(o,f,v,"⇠","\\dashleftarrow",!0);s(o,f,v,"⇇","\\leftleftarrows",!0);s(o,f,v,"⇆","\\leftrightarrows",!0);s(o,f,v,"⇚","\\Lleftarrow",!0);s(o,f,v,"↞","\\twoheadleftarrow",!0);s(o,f,v,"↢","\\leftarrowtail",!0);s(o,f,v,"↫","\\looparrowleft",!0);s(o,f,v,"⇋","\\leftrightharpoons",!0);s(o,f,v,"↶","\\curvearrowleft",!0);s(o,f,v,"↺","\\circlearrowleft",!0);s(o,f,v,"↰","\\Lsh",!0);s(o,f,v,"⇈","\\upuparrows",!0);s(o,f,v,"↿","\\upharpoonleft",!0);s(o,f,v,"⇃","\\downharpoonleft",!0);s(o,c,v,"⊶","\\origof",!0);s(o,c,v,"⊷","\\imageof",!0);s(o,f,v,"⊸","\\multimap",!0);s(o,f,v,"↭","\\leftrightsquigarrow",!0);s(o,f,v,"⇉","\\rightrightarrows",!0);s(o,f,v,"⇄","\\rightleftarrows",!0);s(o,f,v,"↠","\\twoheadrightarrow",!0);s(o,f,v,"↣","\\rightarrowtail",!0);s(o,f,v,"↬","\\looparrowright",!0);s(o,f,v,"↷","\\curvearrowright",!0);s(o,f,v,"↻","\\circlearrowright",!0);s(o,f,v,"↱","\\Rsh",!0);s(o,f,v,"⇊","\\downdownarrows",!0);s(o,f,v,"↾","\\upharpoonright",!0);s(o,f,v,"⇂","\\downharpoonright",!0);s(o,f,v,"⇝","\\rightsquigarrow",!0);s(o,f,v,"⇝","\\leadsto");s(o,f,v,"⇛","\\Rrightarrow",!0);s(o,f,v,"↾","\\restriction");s(o,c,b,"‘","`");s(o,c,b,"$","\\$");s(A,c,b,"$","\\$");s(A,c,b,"$","\\textdollar");s(o,c,b,"%","\\%");s(A,c,b,"%","\\%");s(o,c,b,"_","\\_");s(A,c,b,"_","\\_");s(A,c,b,"_","\\textunderscore");s(o,c,b,"∠","\\angle",!0);s(o,c,b,"∞","\\infty",!0);s(o,c,b,"′","\\prime");s(o,c,b,"△","\\triangle");s(o,c,b,"Γ","\\Gamma",!0);s(o,c,b,"Δ","\\Delta",!0);s(o,c,b,"Θ","\\Theta",!0);s(o,c,b,"Λ","\\Lambda",!0);s(o,c,b,"Ξ","\\Xi",!0);s(o,c,b,"Π","\\Pi",!0);s(o,c,b,"Σ","\\Sigma",!0);s(o,c,b,"Υ","\\Upsilon",!0);s(o,c,b,"Φ","\\Phi",!0);s(o,c,b,"Ψ","\\Psi",!0);s(o,c,b,"Ω","\\Omega",!0);s(o,c,b,"A","Α");s(o,c,b,"B","Β");s(o,c,b,"E","Ε");s(o,c,b,"Z","Ζ");s(o,c,b,"H","Η");s(o,c,b,"I","Ι");s(o,c,b,"K","Κ");s(o,c,b,"M","Μ");s(o,c,b,"N","Ν");s(o,c,b,"O","Ο");s(o,c,b,"P","Ρ");s(o,c,b,"T","Τ");s(o,c,b,"X","Χ");s(o,c,b,"¬","\\neg",!0);s(o,c,b,"¬","\\lnot");s(o,c,b,"⊤","\\top");s(o,c,b,"⊥","\\bot");s(o,c,b,"∅","\\emptyset");s(o,f,b,"∅","\\varnothing");s(o,c,P,"α","\\alpha",!0);s(o,c,P,"β","\\beta",!0);s(o,c,P,"γ","\\gamma",!0);s(o,c,P,"δ","\\delta",!0);s(o,c,P,"ϵ","\\epsilon",!0);s(o,c,P,"ζ","\\zeta",!0);s(o,c,P,"η","\\eta",!0);s(o,c,P,"θ","\\theta",!0);s(o,c,P,"ι","\\iota",!0);s(o,c,P,"κ","\\kappa",!0);s(o,c,P,"λ","\\lambda",!0);s(o,c,P,"μ","\\mu",!0);s(o,c,P,"ν","\\nu",!0);s(o,c,P,"ξ","\\xi",!0);s(o,c,P,"ο","\\omicron",!0);s(o,c,P,"π","\\pi",!0);s(o,c,P,"ρ","\\rho",!0);s(o,c,P,"σ","\\sigma",!0);s(o,c,P,"τ","\\tau",!0);s(o,c,P,"υ","\\upsilon",!0);s(o,c,P,"ϕ","\\phi",!0);s(o,c,P,"χ","\\chi",!0);s(o,c,P,"ψ","\\psi",!0);s(o,c,P,"ω","\\omega",!0);s(o,c,P,"ε","\\varepsilon",!0);s(o,c,P,"ϑ","\\vartheta",!0);s(o,c,P,"ϖ","\\varpi",!0);s(o,c,P,"ϱ","\\varrho",!0);s(o,c,P,"ς","\\varsigma",!0);s(o,c,P,"φ","\\varphi",!0);s(o,c,q,"∗","*",!0);s(o,c,q,"+","+");s(o,c,q,"−","-",!0);s(o,c,q,"⋅","\\cdot",!0);s(o,c,q,"∘","\\circ",!0);s(o,c,q,"÷","\\div",!0);s(o,c,q,"±","\\pm",!0);s(o,c,q,"×","\\times",!0);s(o,c,q,"∩","\\cap",!0);s(o,c,q,"∪","\\cup",!0);s(o,c,q,"∖","\\setminus",!0);s(o,c,q,"∧","\\land");s(o,c,q,"∨","\\lor");s(o,c,q,"∧","\\wedge",!0);s(o,c,q,"∨","\\vee",!0);s(o,c,b,"√","\\surd");s(o,c,Me,"⟨","\\langle",!0);s(o,c,Me,"∣","\\lvert");s(o,c,Me,"∥","\\lVert");s(o,c,we,"?","?");s(o,c,we,"!","!");s(o,c,we,"⟩","\\rangle",!0);s(o,c,we,"∣","\\rvert");s(o,c,we,"∥","\\rVert");s(o,c,v,"=","=");s(o,c,v,":",":");s(o,c,v,"≈","\\approx",!0);s(o,c,v,"≅","\\cong",!0);s(o,c,v,"≥","\\ge");s(o,c,v,"≥","\\geq",!0);s(o,c,v,"←","\\gets");s(o,c,v,">","\\gt",!0);s(o,c,v,"∈","\\in",!0);s(o,c,v,"","\\@not");s(o,c,v,"⊂","\\subset",!0);s(o,c,v,"⊃","\\supset",!0);s(o,c,v,"⊆","\\subseteq",!0);s(o,c,v,"⊇","\\supseteq",!0);s(o,f,v,"⊈","\\nsubseteq",!0);s(o,f,v,"⊉","\\nsupseteq",!0);s(o,c,v,"⊨","\\models");s(o,c,v,"←","\\leftarrow",!0);s(o,c,v,"≤","\\le");s(o,c,v,"≤","\\leq",!0);s(o,c,v,"<","\\lt",!0);s(o,c,v,"→","\\rightarrow",!0);s(o,c,v,"→","\\to");s(o,f,v,"≱","\\ngeq",!0);s(o,f,v,"≰","\\nleq",!0);s(o,c,Ze," ","\\ ");s(o,c,Ze," ","\\space");s(o,c,Ze," ","\\nobreakspace");s(A,c,Ze," ","\\ ");s(A,c,Ze," "," ");s(A,c,Ze," ","\\space");s(A,c,Ze," ","\\nobreakspace");s(o,c,Ze,null,"\\nobreak");s(o,c,Ze,null,"\\allowbreak");s(o,c,vt,",",",");s(o,c,vt,";",";");s(o,f,q,"⊼","\\barwedge",!0);s(o,f,q,"⊻","\\veebar",!0);s(o,c,q,"⊙","\\odot",!0);s(o,c,q,"⊕","\\oplus",!0);s(o,c,q,"⊗","\\otimes",!0);s(o,c,b,"∂","\\partial",!0);s(o,c,q,"⊘","\\oslash",!0);s(o,f,q,"⊚","\\circledcirc",!0);s(o,f,q,"⊡","\\boxdot",!0);s(o,c,q,"△","\\bigtriangleup");s(o,c,q,"▽","\\bigtriangledown");s(o,c,q,"†","\\dagger");s(o,c,q,"⋄","\\diamond");s(o,c,q,"⋆","\\star");s(o,c,q,"◃","\\triangleleft");s(o,c,q,"▹","\\triangleright");s(o,c,Me,"{","\\{");s(A,c,b,"{","\\{");s(A,c,b,"{","\\textbraceleft");s(o,c,we,"}","\\}");s(A,c,b,"}","\\}");s(A,c,b,"}","\\textbraceright");s(o,c,Me,"{","\\lbrace");s(o,c,we,"}","\\rbrace");s(o,c,Me,"[","\\lbrack",!0);s(A,c,b,"[","\\lbrack",!0);s(o,c,we,"]","\\rbrack",!0);s(A,c,b,"]","\\rbrack",!0);s(o,c,Me,"(","\\lparen",!0);s(o,c,we,")","\\rparen",!0);s(A,c,b,"<","\\textless",!0);s(A,c,b,">","\\textgreater",!0);s(o,c,Me,"⌊","\\lfloor",!0);s(o,c,we,"⌋","\\rfloor",!0);s(o,c,Me,"⌈","\\lceil",!0);s(o,c,we,"⌉","\\rceil",!0);s(o,c,b,"\\","\\backslash");s(o,c,b,"∣","|");s(o,c,b,"∣","\\vert");s(A,c,b,"|","\\textbar",!0);s(o,c,b,"∥","\\|");s(o,c,b,"∥","\\Vert");s(A,c,b,"∥","\\textbardbl");s(A,c,b,"~","\\textasciitilde");s(A,c,b,"\\","\\textbackslash");s(A,c,b,"^","\\textasciicircum");s(o,c,v,"↑","\\uparrow",!0);s(o,c,v,"⇑","\\Uparrow",!0);s(o,c,v,"↓","\\downarrow",!0);s(o,c,v,"⇓","\\Downarrow",!0);s(o,c,v,"↕","\\updownarrow",!0);s(o,c,v,"⇕","\\Updownarrow",!0);s(o,c,me,"∐","\\coprod");s(o,c,me,"⋁","\\bigvee");s(o,c,me,"⋀","\\bigwedge");s(o,c,me,"⨄","\\biguplus");s(o,c,me,"⋂","\\bigcap");s(o,c,me,"⋃","\\bigcup");s(o,c,me,"∫","\\int");s(o,c,me,"∫","\\intop");s(o,c,me,"∬","\\iint");s(o,c,me,"∭","\\iiint");s(o,c,me,"∏","\\prod");s(o,c,me,"∑","\\sum");s(o,c,me,"⨂","\\bigotimes");s(o,c,me,"⨁","\\bigoplus");s(o,c,me,"⨀","\\bigodot");s(o,c,me,"∮","\\oint");s(o,c,me,"∯","\\oiint");s(o,c,me,"∰","\\oiiint");s(o,c,me,"⨆","\\bigsqcup");s(o,c,me,"∫","\\smallint");s(A,c,A0,"…","\\textellipsis");s(o,c,A0,"…","\\mathellipsis");s(A,c,A0,"…","\\ldots",!0);s(o,c,A0,"…","\\ldots",!0);s(o,c,A0,"⋯","\\@cdots",!0);s(o,c,A0,"⋱","\\ddots",!0);s(o,c,b,"⋮","\\varvdots");s(o,c,se,"ˊ","\\acute");s(o,c,se,"ˋ","\\grave");s(o,c,se,"¨","\\ddot");s(o,c,se,"~","\\tilde");s(o,c,se,"ˉ","\\bar");s(o,c,se,"˘","\\breve");s(o,c,se,"ˇ","\\check");s(o,c,se,"^","\\hat");s(o,c,se,"⃗","\\vec");s(o,c,se,"˙","\\dot");s(o,c,se,"˚","\\mathring");s(o,c,P,"","\\@imath");s(o,c,P,"","\\@jmath");s(o,c,b,"ı","ı");s(o,c,b,"ȷ","ȷ");s(A,c,b,"ı","\\i",!0);s(A,c,b,"ȷ","\\j",!0);s(A,c,b,"ß","\\ss",!0);s(A,c,b,"æ","\\ae",!0);s(A,c,b,"œ","\\oe",!0);s(A,c,b,"ø","\\o",!0);s(A,c,b,"Æ","\\AE",!0);s(A,c,b,"Œ","\\OE",!0);s(A,c,b,"Ø","\\O",!0);s(A,c,se,"ˊ","\\'");s(A,c,se,"ˋ","\\`");s(A,c,se,"ˆ","\\^");s(A,c,se,"˜","\\~");s(A,c,se,"ˉ","\\=");s(A,c,se,"˘","\\u");s(A,c,se,"˙","\\.");s(A,c,se,"¸","\\c");s(A,c,se,"˚","\\r");s(A,c,se,"ˇ","\\v");s(A,c,se,"¨",'\\"');s(A,c,se,"˝","\\H");s(A,c,se,"◯","\\textcircled");var Fa={"--":!0,"---":!0,"``":!0,"''":!0};s(A,c,b,"–","--",!0);s(A,c,b,"–","\\textendash");s(A,c,b,"—","---",!0);s(A,c,b,"—","\\textemdash");s(A,c,b,"‘","`",!0);s(A,c,b,"‘","\\textquoteleft");s(A,c,b,"’","'",!0);s(A,c,b,"’","\\textquoteright");s(A,c,b,"“","``",!0);s(A,c,b,"“","\\textquotedblleft");s(A,c,b,"”","''",!0);s(A,c,b,"”","\\textquotedblright");s(o,c,b,"°","\\degree",!0);s(A,c,b,"°","\\degree");s(A,c,b,"°","\\textdegree",!0);s(o,c,b,"£","\\pounds");s(o,c,b,"£","\\mathsterling",!0);s(A,c,b,"£","\\pounds");s(A,c,b,"£","\\textsterling",!0);s(o,f,b,"✠","\\maltese");s(A,f,b,"✠","\\maltese");var Vr='0123456789/@."';for(var Tt=0;Tt<Vr.length;Tt++){var Gr=Vr.charAt(Tt);s(o,c,b,Gr,Gr)}var Ur='0123456789!@*()-=+";:?/.,';for(var Bt=0;Bt<Ur.length;Bt++){var $r=Ur.charAt(Bt);s(A,c,b,$r,$r)}var ut="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";for(var Ct=0;Ct<ut.length;Ct++){var W0=ut.charAt(Ct);s(o,c,P,W0,W0),s(A,c,b,W0,W0)}s(o,f,b,"C","ℂ");s(A,f,b,"C","ℂ");s(o,f,b,"H","ℍ");s(A,f,b,"H","ℍ");s(o,f,b,"N","ℕ");s(A,f,b,"N","ℕ");s(o,f,b,"P","ℙ");s(A,f,b,"P","ℙ");s(o,f,b,"Q","ℚ");s(A,f,b,"Q","ℚ");s(o,f,b,"R","ℝ");s(A,f,b,"R","ℝ");s(o,f,b,"Z","ℤ");s(A,f,b,"Z","ℤ");s(o,c,P,"h","ℎ");s(A,c,P,"h","ℎ");var $="";for(var ke=0;ke<ut.length;ke++){var ce=ut.charAt(ke);$=String.fromCharCode(55349,56320+ke),s(o,c,P,ce,$),s(A,c,b,ce,$),$=String.fromCharCode(55349,56372+ke),s(o,c,P,ce,$),s(A,c,b,ce,$),$=String.fromCharCode(55349,56424+ke),s(o,c,P,ce,$),s(A,c,b,ce,$),$=String.fromCharCode(55349,56580+ke),s(o,c,P,ce,$),s(A,c,b,ce,$),$=String.fromCharCode(55349,56736+ke),s(o,c,P,ce,$),s(A,c,b,ce,$),$=String.fromCharCode(55349,56788+ke),s(o,c,P,ce,$),s(A,c,b,ce,$),$=String.fromCharCode(55349,56840+ke),s(o,c,P,ce,$),s(A,c,b,ce,$),$=String.fromCharCode(55349,56944+ke),s(o,c,P,ce,$),s(A,c,b,ce,$),ke<26&&($=String.fromCharCode(55349,56632+ke),s(o,c,P,ce,$),s(A,c,b,ce,$),$=String.fromCharCode(55349,56476+ke),s(o,c,P,ce,$),s(A,c,b,ce,$))}$="𝕜";s(o,c,P,"k",$);s(A,c,b,"k",$);for(var m0=0;m0<10;m0++){var a0=m0.toString();$=String.fromCharCode(55349,57294+m0),s(o,c,P,a0,$),s(A,c,b,a0,$),$=String.fromCharCode(55349,57314+m0),s(o,c,P,a0,$),s(A,c,b,a0,$),$=String.fromCharCode(55349,57324+m0),s(o,c,P,a0,$),s(A,c,b,a0,$),$=String.fromCharCode(55349,57334+m0),s(o,c,P,a0,$),s(A,c,b,a0,$)}var Wt="ÐÞþ";for(var Rt=0;Rt<Wt.length;Rt++){var _0=Wt.charAt(Rt);s(o,c,P,_0,_0),s(A,c,b,_0,_0)}var X0=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathnormal","textit","Math-Italic"],["mathnormal","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["","",""],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],Yr=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],Ds=function(e,t){var r=e.charCodeAt(0),i=e.charCodeAt(1),n=(r-55296)*1024+(i-56320)+65536,l=t==="math"?0:1;if(119808<=n&&n<120484){var u=Math.floor((n-119808)/26);return[X0[u][2],X0[u][l]]}else if(120782<=n&&n<=120831){var h=Math.floor((n-120782)/10);return[Yr[h][2],Yr[h][l]]}else{if(n===120485||n===120486)return[X0[0][2],X0[0][l]];if(120486<n&&n<120782)return["",""];throw new M("Unsupported character: "+e)}},gt=function(e,t,r){return ne[r][e]&&ne[r][e].replace&&(e=ne[r][e].replace),{value:e,metrics:hr(e,t,r)}},Le=function(e,t,r,i,n){var l=gt(e,t,r),u=l.metrics;e=l.value;var h;if(u){var d=u.italic;(r==="text"||i&&i.font==="mathit")&&(d=0),h=new qe(e,u.height,u.depth,d,u.skew,u.width,n)}else typeof console<"u"&&console.warn("No character metrics "+("for '"+e+"' in style '"+t+"' and mode '"+r+"'")),h=new qe(e,0,0,0,0,0,n);if(i){h.maxFontSize=i.sizeMultiplier,i.style.isTight()&&h.classes.push("mtight");var p=i.getColor();p&&(h.style.color=p)}return h},Es=function(e,t,r,i){return i===void 0&&(i=[]),r.font==="boldsymbol"&&gt(e,"Main-Bold",t).metrics?Le(e,"Main-Bold",t,r,i.concat(["mathbf"])):e==="\\"||ne[t][e].font==="main"?Le(e,"Main-Regular",t,r,i):Le(e,"AMS-Regular",t,r,i.concat(["amsrm"]))},Ns=function(e,t,r,i,n){return n!=="textord"&&gt(e,"Math-BoldItalic",t).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"}},qs=function(e,t,r){var i=e.mode,n=e.text,l=["mord"],u=i==="math"||i==="text"&&t.font,h=u?t.font:t.fontFamily;if(n.charCodeAt(0)===55349){var[d,p]=Ds(n,i);return Le(n,d,i,t,l.concat(p))}else if(h){var g,y;if(h==="boldsymbol"){var x=Ns(n,i,t,l,r);g=x.fontName,y=[x.fontClass]}else u?(g=Ga[h].fontName,y=[h]):(g=J0(h,t.fontWeight,t.fontShape),y=[h,t.fontWeight,t.fontShape]);if(gt(n,g,i).metrics)return Le(n,g,i,t,l.concat(y));if(Fa.hasOwnProperty(n)&&g.slice(0,10)==="Typewriter"){for(var k=[],S=0;S<n.length;S++)k.push(Le(n[S],g,i,t,l.concat(y)));return Va(k)}}if(r==="mathord")return Le(n,"Math-Italic",i,t,l.concat(["mathnormal"]));if(r==="textord"){var C=ne[i][n]&&ne[i][n].font;if(C==="ams"){var E=J0("amsrm",t.fontWeight,t.fontShape);return Le(n,E,i,t,l.concat("amsrm",t.fontWeight,t.fontShape))}else if(C==="main"||!C){var L=J0("textrm",t.fontWeight,t.fontShape);return Le(n,L,i,t,l.concat(t.fontWeight,t.fontShape))}else{var V=J0(C,t.fontWeight,t.fontShape);return Le(n,V,i,t,l.concat(V,t.fontWeight,t.fontShape))}}else throw new Error("unexpected type: "+r+" in makeOrd")},Is=(a,e)=>{if(l0(a.classes)!==l0(e.classes)||a.skew!==e.skew||a.maxFontSize!==e.maxFontSize)return!1;if(a.classes.length===1){var t=a.classes[0];if(t==="mbin"||t==="mord")return!1}for(var r in a.style)if(a.style.hasOwnProperty(r)&&a.style[r]!==e.style[r])return!1;for(var i in e.style)if(e.style.hasOwnProperty(i)&&a.style[i]!==e.style[i])return!1;return!0},Ls=a=>{for(var e=0;e<a.length-1;e++){var t=a[e],r=a[e+1];t instanceof qe&&r instanceof qe&&Is(t,r)&&(t.text+=r.text,t.height=Math.max(t.height,r.height),t.depth=Math.max(t.depth,r.depth),t.italic=r.italic,a.splice(e+1,1),e--)}return a},mr=function(e){for(var t=0,r=0,i=0,n=0;n<e.children.length;n++){var l=e.children[n];l.height>t&&(t=l.height),l.depth>r&&(r=l.depth),l.maxFontSize>i&&(i=l.maxFontSize)}e.height=t,e.depth=r,e.maxFontSize=i},Se=function(e,t,r,i){var n=new F0(e,t,r,i);return mr(n),n},Pa=(a,e,t,r)=>new F0(a,e,t,r),Os=function(e,t,r){var i=Se([e],[],t);return i.height=Math.max(r||t.fontMetrics().defaultRuleThickness,t.minRuleThickness),i.style.borderBottomWidth=B(i.height),i.maxFontSize=1,i},Hs=function(e,t,r,i){var n=new cr(e,t,r,i);return mr(n),n},Va=function(e){var t=new H0(e);return mr(t),t},Fs=function(e,t){return e instanceof H0?Se([],[e],t):e},Ps=function(e){if(e.positionType==="individualShift"){for(var t=e.children,r=[t[0]],i=-t[0].shift-t[0].elem.depth,n=i,l=1;l<t.length;l++){var u=-t[l].shift-n-t[l].elem.depth,h=u-(t[l-1].elem.height+t[l-1].elem.depth);n=n+u,r.push({type:"kern",size:h}),r.push(t[l])}return{children:r,depth:i}}var d;if(e.positionType==="top"){for(var p=e.positionData,g=0;g<e.children.length;g++){var y=e.children[g];p-=y.type==="kern"?y.size:y.elem.height+y.elem.depth}d=p}else if(e.positionType==="bottom")d=-e.positionData;else{var x=e.children[0];if(x.type!=="elem")throw new Error('First child must have type "elem".');if(e.positionType==="shift")d=-x.elem.depth-e.positionData;else if(e.positionType==="firstBaseline")d=-x.elem.depth;else throw new Error("Invalid positionType "+e.positionType+".")}return{children:e.children,depth:d}},Vs=function(e,t){for(var{children:r,depth:i}=Ps(e),n=0,l=0;l<r.length;l++){var u=r[l];if(u.type==="elem"){var h=u.elem;n=Math.max(n,h.maxFontSize,h.height)}}n+=2;var d=Se(["pstrut"],[]);d.style.height=B(n);for(var p=[],g=i,y=i,x=i,k=0;k<r.length;k++){var S=r[k];if(S.type==="kern")x+=S.size;else{var C=S.elem,E=S.wrapperClasses||[],L=S.wrapperStyle||{},V=Se(E,[d,C],void 0,L);V.style.top=B(-n-x-C.depth),S.marginLeft&&(V.style.marginLeft=S.marginLeft),S.marginRight&&(V.style.marginRight=S.marginRight),p.push(V),x+=C.height+C.depth}g=Math.min(g,x),y=Math.max(y,x)}var j=Se(["vlist"],p);j.style.height=B(y);var G;if(g<0){var H=Se([],[]),T=Se(["vlist"],[H]);T.style.height=B(-g);var I=Se(["vlist-s"],[new qe("​")]);G=[Se(["vlist-r"],[j,I]),Se(["vlist-r"],[T])]}else G=[Se(["vlist-r"],[j])];var R=Se(["vlist-t"],G);return G.length===2&&R.classes.push("vlist-t2"),R.height=y,R.depth=-g,R},Gs=(a,e)=>{var t=Se(["mspace"],[],e),r=ue(a,e);return t.style.marginRight=B(r),t},J0=function(e,t,r){var i="";switch(e){case"amsrm":i="AMS";break;case"textrm":i="Main";break;case"textsf":i="SansSerif";break;case"texttt":i="Typewriter";break;default:i=e}var n;return t==="textbf"&&r==="textit"?n="BoldItalic":t==="textbf"?n="Bold":t==="textit"?n="Italic":n="Regular",i+"-"+n},Ga={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathnormal:{variant:"italic",fontName:"Math-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},Ua={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]},Us=function(e,t){var[r,i,n]=Ua[e],l=new o0(r),u=new Xe([l],{width:B(i),height:B(n),style:"width:"+B(i),viewBox:"0 0 "+1e3*i+" "+1e3*n,preserveAspectRatio:"xMinYMin"}),h=Pa(["overlay"],[u],t);return h.height=n,h.style.height=B(n),h.style.width=B(i),h},w={fontMap:Ga,makeSymbol:Le,mathsym:Es,makeSpan:Se,makeSvgSpan:Pa,makeLineSpan:Os,makeAnchor:Hs,makeFragment:Va,wrapFragment:Fs,makeVList:Vs,makeOrd:qs,makeGlue:Gs,staticSvg:Us,svgData:Ua,tryCombineChars:Ls},oe={number:3,unit:"mu"},d0={number:4,unit:"mu"},Ye={number:5,unit:"mu"},$s={mord:{mop:oe,mbin:d0,mrel:Ye,minner:oe},mop:{mord:oe,mop:oe,mrel:Ye,minner:oe},mbin:{mord:d0,mop:d0,mopen:d0,minner:d0},mrel:{mord:Ye,mop:Ye,mopen:Ye,minner:Ye},mopen:{},mclose:{mop:oe,mbin:d0,mrel:Ye,minner:oe},mpunct:{mord:oe,mop:oe,mrel:Ye,mopen:oe,mclose:oe,mpunct:oe,minner:oe},minner:{mord:oe,mop:oe,mbin:d0,mrel:Ye,mopen:oe,mpunct:oe,minner:oe}},Ys={mord:{mop:oe},mop:{mord:oe,mop:oe},mbin:{},mrel:{},mopen:{},mclose:{mop:oe},mpunct:{},minner:{mop:oe}},$a={},ht={},ct={};function N(a){for(var{type:e,names:t,props:r,handler:i,htmlBuilder:n,mathmlBuilder:l}=a,u={type:e,numArgs:r.numArgs,argTypes:r.argTypes,allowedInArgument:!!r.allowedInArgument,allowedInText:!!r.allowedInText,allowedInMath:r.allowedInMath===void 0?!0:r.allowedInMath,numOptionalArgs:r.numOptionalArgs||0,infix:!!r.infix,primitive:!!r.primitive,handler:i},h=0;h<t.length;++h)$a[t[h]]=u;e&&(n&&(ht[e]=n),l&&(ct[e]=l))}function v0(a){var{type:e,htmlBuilder:t,mathmlBuilder:r}=a;N({type:e,names:[],props:{numArgs:0},handler(){throw new Error("Should never be called.")},htmlBuilder:t,mathmlBuilder:r})}var mt=function(e){return e.type==="ordgroup"&&e.body.length===1?e.body[0]:e},he=function(e){return e.type==="ordgroup"?e.body:[e]},Je=w.makeSpan,js=["leftmost","mbin","mopen","mrel","mop","mpunct"],Ws=["rightmost","mrel","mclose","mpunct"],_s={display:O.DISPLAY,text:O.TEXT,script:O.SCRIPT,scriptscript:O.SCRIPTSCRIPT},Xs={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},pe=function(e,t,r,i){i===void 0&&(i=[null,null]);for(var n=[],l=0;l<e.length;l++){var u=J(e[l],t);if(u instanceof H0){var h=u.children;n.push(...h)}else n.push(u)}if(w.tryCombineChars(n),!r)return n;var d=t;if(e.length===1){var p=e[0];p.type==="sizing"?d=t.havingSize(p.size):p.type==="styling"&&(d=t.havingStyle(_s[p.style]))}var g=Je([i[0]||"leftmost"],[],t),y=Je([i[1]||"rightmost"],[],t),x=r==="root";return jr(n,(k,S)=>{var C=S.classes[0],E=k.classes[0];C==="mbin"&&U.contains(Ws,E)?S.classes[0]="mord":E==="mbin"&&U.contains(js,C)&&(k.classes[0]="mord")},{node:g},y,x),jr(n,(k,S)=>{var C=_t(S),E=_t(k),L=C&&E?k.hasClass("mtight")?Ys[C][E]:$s[C][E]:null;if(L)return w.makeGlue(L,d)},{node:g},y,x),n},jr=function a(e,t,r,i,n){i&&e.push(i);for(var l=0;l<e.length;l++){var u=e[l],h=Ya(u);if(h){a(h.children,t,r,null,n);continue}var d=!u.hasClass("mspace");if(d){var p=t(u,r.node);p&&(r.insertAfter?r.insertAfter(p):(e.unshift(p),l++))}d?r.node=u:n&&u.hasClass("newline")&&(r.node=Je(["leftmost"])),r.insertAfter=(g=>y=>{e.splice(g+1,0,y),l++})(l)}i&&e.pop()},Ya=function(e){return e instanceof H0||e instanceof cr||e instanceof F0&&e.hasClass("enclosing")?e:null},Js=function a(e,t){var r=Ya(e);if(r){var i=r.children;if(i.length){if(t==="right")return a(i[i.length-1],"right");if(t==="left")return a(i[0],"left")}}return e},_t=function(e,t){return e?(t&&(e=Js(e,t)),Xs[e.classes[0]]||null):null},I0=function(e,t){var r=["nulldelimiter"].concat(e.baseSizingClasses());return Je(t.concat(r))},J=function(e,t,r){if(!e)return Je();if(ht[e.type]){var i=ht[e.type](e,t);if(r&&t.size!==r.size){i=Je(t.sizingClasses(r),[i],t);var n=t.sizeMultiplier/r.sizeMultiplier;i.height*=n,i.depth*=n}return i}else throw new M("Got group of unknown type: '"+e.type+"'")};function Q0(a,e){var t=Je(["base"],a,e),r=Je(["strut"]);return r.style.height=B(t.height+t.depth),t.depth&&(r.style.verticalAlign=B(-t.depth)),t.children.unshift(r),t}function Xt(a,e){var t=null;a.length===1&&a[0].type==="tag"&&(t=a[0].tag,a=a[0].body);var r=pe(a,e,"root"),i;r.length===2&&r[1].hasClass("tag")&&(i=r.pop());for(var n=[],l=[],u=0;u<r.length;u++)if(l.push(r[u]),r[u].hasClass("mbin")||r[u].hasClass("mrel")||r[u].hasClass("allowbreak")){for(var h=!1;u<r.length-1&&r[u+1].hasClass("mspace")&&!r[u+1].hasClass("newline");)u++,l.push(r[u]),r[u].hasClass("nobreak")&&(h=!0);h||(n.push(Q0(l,e)),l=[])}else r[u].hasClass("newline")&&(l.pop(),l.length>0&&(n.push(Q0(l,e)),l=[]),n.push(r[u]));l.length>0&&n.push(Q0(l,e));var d;t?(d=Q0(pe(t,e,!0)),d.classes=["tag"],n.push(d)):i&&n.push(i);var p=Je(["katex-html"],n);if(p.setAttribute("aria-hidden","true"),d){var g=d.children[0];g.style.height=B(p.height+p.depth),p.depth&&(g.style.verticalAlign=B(-p.depth))}return p}function ja(a){return new H0(a)}class Re{constructor(e,t,r){this.type=void 0,this.attributes=void 0,this.children=void 0,this.classes=void 0,this.type=e,this.attributes={},this.children=t||[],this.classes=r||[]}setAttribute(e,t){this.attributes[e]=t}getAttribute(e){return this.attributes[e]}toNode(){var e=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);this.classes.length>0&&(e.className=l0(this.classes));for(var r=0;r<this.children.length;r++)e.appendChild(this.children[r].toNode());return e}toMarkup(){var e="<"+this.type;for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="',e+=U.escape(this.attributes[t]),e+='"');this.classes.length>0&&(e+=' class ="'+U.escape(l0(this.classes))+'"'),e+=">";for(var r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e+="</"+this.type+">",e}toText(){return this.children.map(e=>e.toText()).join("")}}class E0{constructor(e){this.text=void 0,this.text=e}toNode(){return document.createTextNode(this.text)}toMarkup(){return U.escape(this.toText())}toText(){return this.text}}class Qs{constructor(e){this.width=void 0,this.character=void 0,this.width=e,e>=.05555&&e<=.05556?this.character=" ":e>=.1666&&e<=.1667?this.character=" ":e>=.2222&&e<=.2223?this.character=" ":e>=.2777&&e<=.2778?this.character="  ":e>=-.05556&&e<=-.05555?this.character=" ⁣":e>=-.1667&&e<=-.1666?this.character=" ⁣":e>=-.2223&&e<=-.2222?this.character=" ⁣":e>=-.2778&&e<=-.2777?this.character=" ⁣":this.character=null}toNode(){if(this.character)return document.createTextNode(this.character);var e=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return e.setAttribute("width",B(this.width)),e}toMarkup(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+B(this.width)+'"/>'}toText(){return this.character?this.character:" "}}var z={MathNode:Re,TextNode:E0,SpaceNode:Qs,newDocumentFragment:ja},Ie=function(e,t,r){return ne[t][e]&&ne[t][e].replace&&e.charCodeAt(0)!==55349&&!(Fa.hasOwnProperty(e)&&r&&(r.fontFamily&&r.fontFamily.slice(4,6)==="tt"||r.font&&r.font.slice(4,6)==="tt"))&&(e=ne[t][e].replace),new z.TextNode(e)},dr=function(e){return e.length===1?e[0]:new z.MathNode("mrow",e)},pr=function(e,t){if(t.fontFamily==="texttt")return"monospace";if(t.fontFamily==="textsf")return t.fontShape==="textit"&&t.fontWeight==="textbf"?"sans-serif-bold-italic":t.fontShape==="textit"?"sans-serif-italic":t.fontWeight==="textbf"?"bold-sans-serif":"sans-serif";if(t.fontShape==="textit"&&t.fontWeight==="textbf")return"bold-italic";if(t.fontShape==="textit")return"italic";if(t.fontWeight==="textbf")return"bold";var r=t.font;if(!r||r==="mathnormal")return null;var i=e.mode;if(r==="mathit")return"italic";if(r==="boldsymbol")return e.type==="textord"?"bold":"bold-italic";if(r==="mathbf")return"bold";if(r==="mathbb")return"double-struck";if(r==="mathfrak")return"fraktur";if(r==="mathscr"||r==="mathcal")return"script";if(r==="mathsf")return"sans-serif";if(r==="mathtt")return"monospace";var n=e.text;if(U.contains(["\\imath","\\jmath"],n))return null;ne[i][n]&&ne[i][n].replace&&(n=ne[i][n].replace);var l=w.fontMap[r].fontName;return hr(n,l,i)?w.fontMap[r].variant:null},Ae=function(e,t,r){if(e.length===1){var i=re(e[0],t);return r&&i instanceof Re&&i.type==="mo"&&(i.setAttribute("lspace","0em"),i.setAttribute("rspace","0em")),[i]}for(var n=[],l,u=0;u<e.length;u++){var h=re(e[u],t);if(h instanceof Re&&l instanceof Re){if(h.type==="mtext"&&l.type==="mtext"&&h.getAttribute("mathvariant")===l.getAttribute("mathvariant")){l.children.push(...h.children);continue}else if(h.type==="mn"&&l.type==="mn"){l.children.push(...h.children);continue}else if(h.type==="mi"&&h.children.length===1&&l.type==="mn"){var d=h.children[0];if(d instanceof E0&&d.text==="."){l.children.push(...h.children);continue}}else if(l.type==="mi"&&l.children.length===1){var p=l.children[0];if(p instanceof E0&&p.text==="̸"&&(h.type==="mo"||h.type==="mi"||h.type==="mn")){var g=h.children[0];g instanceof E0&&g.text.length>0&&(g.text=g.text.slice(0,1)+"̸"+g.text.slice(1),n.pop())}}}n.push(h),l=h}return n},u0=function(e,t,r){return dr(Ae(e,t,r))},re=function(e,t){if(!e)return new z.MathNode("mrow");if(ct[e.type]){var r=ct[e.type](e,t);return r}else throw new M("Got group of unknown type: '"+e.type+"'")};function Wr(a,e,t,r,i){var n=Ae(a,t),l;n.length===1&&n[0]instanceof Re&&U.contains(["mrow","mtable"],n[0].type)?l=n[0]:l=new z.MathNode("mrow",n);var u=new z.MathNode("annotation",[new z.TextNode(e)]);u.setAttribute("encoding","application/x-tex");var h=new z.MathNode("semantics",[l,u]),d=new z.MathNode("math",[h]);d.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),r&&d.setAttribute("display","block");var p=i?"katex":"katex-mathml";return w.makeSpan([p],[d])}var Wa=function(e){return new je({style:e.displayMode?O.DISPLAY:O.TEXT,maxSize:e.maxSize,minRuleThickness:e.minRuleThickness})},_a=function(e,t){if(t.displayMode){var r=["katex-display"];t.leqno&&r.push("leqno"),t.fleqn&&r.push("fleqn"),e=w.makeSpan(r,[e])}return e},Zs=function(e,t,r){var i=Wa(r),n;if(r.output==="mathml")return Wr(e,t,i,r.displayMode,!0);if(r.output==="html"){var l=Xt(e,i);n=w.makeSpan(["katex"],[l])}else{var u=Wr(e,t,i,r.displayMode,!1),h=Xt(e,i);n=w.makeSpan(["katex"],[u,h])}return _a(n,r)},Ks=function(e,t,r){var i=Wa(r),n=Xt(e,i),l=w.makeSpan(["katex"],[n]);return _a(l,r)},el={widehat:"^",widecheck:"ˇ",widetilde:"~",utilde:"~",overleftarrow:"←",underleftarrow:"←",xleftarrow:"←",overrightarrow:"→",underrightarrow:"→",xrightarrow:"→",underbrace:"⏟",overbrace:"⏞",overgroup:"⏠",undergroup:"⏡",overleftrightarrow:"↔",underleftrightarrow:"↔",xleftrightarrow:"↔",Overrightarrow:"⇒",xRightarrow:"⇒",overleftharpoon:"↼",xleftharpoonup:"↼",overrightharpoon:"⇀",xrightharpoonup:"⇀",xLeftarrow:"⇐",xLeftrightarrow:"⇔",xhookleftarrow:"↩",xhookrightarrow:"↪",xmapsto:"↦",xrightharpoondown:"⇁",xleftharpoondown:"↽",xrightleftharpoons:"⇌",xleftrightharpoons:"⇋",xtwoheadleftarrow:"↞",xtwoheadrightarrow:"↠",xlongequal:"=",xtofrom:"⇄",xrightleftarrows:"⇄",xrightequilibrium:"⇌",xleftequilibrium:"⇋","\\cdrightarrow":"→","\\cdleftarrow":"←","\\cdlongequal":"="},tl=function(e){var t=new z.MathNode("mo",[new z.TextNode(el[e.replace(/^\\/,"")])]);return t.setAttribute("stretchy","true"),t},rl={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],"\\cdrightarrow":[["rightarrow"],3,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],"\\cdleftarrow":[["leftarrow"],3,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],"\\cdlongequal":[["longequal"],3,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]},al=function(e){return e.type==="ordgroup"?e.body.length:1},il=function(e,t){function r(){var u=4e5,h=e.label.slice(1);if(U.contains(["widehat","widecheck","widetilde","utilde"],h)){var d=e,p=al(d.base),g,y,x;if(p>5)h==="widehat"||h==="widecheck"?(g=420,u=2364,x=.42,y=h+"4"):(g=312,u=2340,x=.34,y="tilde4");else{var k=[1,1,2,2,3,3][p];h==="widehat"||h==="widecheck"?(u=[0,1062,2364,2364,2364][k],g=[0,239,300,360,420][k],x=[0,.24,.3,.3,.36,.42][k],y=h+k):(u=[0,600,1033,2339,2340][k],g=[0,260,286,306,312][k],x=[0,.26,.286,.3,.306,.34][k],y="tilde"+k)}var S=new o0(y),C=new Xe([S],{width:"100%",height:B(x),viewBox:"0 0 "+u+" "+g,preserveAspectRatio:"none"});return{span:w.makeSvgSpan([],[C],t),minWidth:0,height:x}}else{var E=[],L=rl[h],[V,j,G]=L,H=G/1e3,T=V.length,I,R;if(T===1){var _=L[3];I=["hide-tail"],R=[_]}else if(T===2)I=["halfarrow-left","halfarrow-right"],R=["xMinYMin","xMaxYMin"];else if(T===3)I=["brace-left","brace-center","brace-right"],R=["xMinYMin","xMidYMin","xMaxYMin"];else throw new Error(`Correct katexImagesData or update code here to support
                    `+T+" children.");for(var W=0;W<T;W++){var ee=new o0(V[W]),ve=new Xe([ee],{width:"400em",height:B(H),viewBox:"0 0 "+u+" "+G,preserveAspectRatio:R[W]+" slice"}),le=w.makeSvgSpan([I[W]],[ve],t);if(T===1)return{span:le,minWidth:j,height:H};le.style.height=B(H),E.push(le)}return{span:w.makeSpan(["stretchy"],E,t),minWidth:j,height:H}}}var{span:i,minWidth:n,height:l}=r();return i.height=l,i.style.height=B(l),n>0&&(i.style.minWidth=B(n)),i},nl=function(e,t,r,i,n){var l,u=e.height+e.depth+r+i;if(/fbox|color|angl/.test(t)){if(l=w.makeSpan(["stretchy",t],[],n),t==="fbox"){var h=n.color&&n.getColor();h&&(l.style.borderColor=h)}}else{var d=[];/^[bx]cancel$/.test(t)&&d.push(new jt({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(t)&&d.push(new jt({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));var p=new Xe(d,{width:"100%",height:B(u)});l=w.makeSvgSpan([],[p],n)}return l.height=u,l.style.height=B(u),l},Qe={encloseSpan:nl,mathMLnode:tl,svgSpan:il};function Y(a,e){if(!a||a.type!==e)throw new Error("Expected node of type "+e+", but got "+(a?"node of type "+a.type:String(a)));return a}function fr(a){var e=bt(a);if(!e)throw new Error("Expected node of symbol group type, but got "+(a?"node of type "+a.type:String(a)));return e}function bt(a){return a&&(a.type==="atom"||Rs.hasOwnProperty(a.type))?a:null}var vr=(a,e)=>{var t,r,i;a&&a.type==="supsub"?(r=Y(a.base,"accent"),t=r.base,a.base=t,i=Bs(J(a,e)),a.base=r):(r=Y(a,"accent"),t=r.base);var n=J(t,e.havingCrampedStyle()),l=r.isShifty&&U.isCharacterBox(t),u=0;if(l){var h=U.getBaseElem(t),d=J(h,e.havingCrampedStyle());u=Pr(d).skew}var p=r.label==="\\c",g=p?n.height+n.depth:Math.min(n.height,e.fontMetrics().xHeight),y;if(r.isStretchy)y=Qe.svgSpan(r,e),y=w.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:n},{type:"elem",elem:y,wrapperClasses:["svg-align"],wrapperStyle:u>0?{width:"calc(100% - "+B(2*u)+")",marginLeft:B(2*u)}:void 0}]},e);else{var x,k;r.label==="\\vec"?(x=w.staticSvg("vec",e),k=w.svgData.vec[1]):(x=w.makeOrd({mode:r.mode,text:r.label},e,"textord"),x=Pr(x),x.italic=0,k=x.width,p&&(g+=x.depth)),y=w.makeSpan(["accent-body"],[x]);var S=r.label==="\\textcircled";S&&(y.classes.push("accent-full"),g=n.height);var C=u;S||(C-=k/2),y.style.left=B(C),r.label==="\\textcircled"&&(y.style.top=".2em"),y=w.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:n},{type:"kern",size:-g},{type:"elem",elem:y}]},e)}var E=w.makeSpan(["mord","accent"],[y],e);return i?(i.children[0]=E,i.height=Math.max(E.height,i.height),i.classes[0]="mord",i):E},Xa=(a,e)=>{var t=a.isStretchy?Qe.mathMLnode(a.label):new z.MathNode("mo",[Ie(a.label,a.mode)]),r=new z.MathNode("mover",[re(a.base,e),t]);return r.setAttribute("accent","true"),r},sl=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map(a=>"\\"+a).join("|"));N({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:(a,e)=>{var t=mt(e[0]),r=!sl.test(a.funcName),i=!r||a.funcName==="\\widehat"||a.funcName==="\\widetilde"||a.funcName==="\\widecheck";return{type:"accent",mode:a.parser.mode,label:a.funcName,isStretchy:r,isShifty:i,base:t}},htmlBuilder:vr,mathmlBuilder:Xa});N({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\c","\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["primitive"]},handler:(a,e)=>{var t=e[0],r=a.parser.mode;return r==="math"&&(a.parser.settings.reportNonstrict("mathVsTextAccents","LaTeX's accent "+a.funcName+" works only in text mode"),r="text"),{type:"accent",mode:r,label:a.funcName,isStretchy:!1,isShifty:!0,base:t}},htmlBuilder:vr,mathmlBuilder:Xa});N({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:(a,e)=>{var{parser:t,funcName:r}=a,i=e[0];return{type:"accentUnder",mode:t.mode,label:r,base:i}},htmlBuilder:(a,e)=>{var t=J(a.base,e),r=Qe.svgSpan(a,e),i=a.label==="\\utilde"?.12:0,n=w.makeVList({positionType:"top",positionData:t.height,children:[{type:"elem",elem:r,wrapperClasses:["svg-align"]},{type:"kern",size:i},{type:"elem",elem:t}]},e);return w.makeSpan(["mord","accentunder"],[n],e)},mathmlBuilder:(a,e)=>{var t=Qe.mathMLnode(a.label),r=new z.MathNode("munder",[re(a.base,e),t]);return r.setAttribute("accentunder","true"),r}});var Z0=a=>{var e=new z.MathNode("mpadded",a?[a]:[]);return e.setAttribute("width","+0.6em"),e.setAttribute("lspace","0.3em"),e};N({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium","\\\\cdrightarrow","\\\\cdleftarrow","\\\\cdlongequal"],props:{numArgs:1,numOptionalArgs:1},handler(a,e,t){var{parser:r,funcName:i}=a;return{type:"xArrow",mode:r.mode,label:i,body:e[0],below:t[0]}},htmlBuilder(a,e){var t=e.style,r=e.havingStyle(t.sup()),i=w.wrapFragment(J(a.body,r,e),e),n=a.label.slice(0,2)==="\\x"?"x":"cd";i.classes.push(n+"-arrow-pad");var l;a.below&&(r=e.havingStyle(t.sub()),l=w.wrapFragment(J(a.below,r,e),e),l.classes.push(n+"-arrow-pad"));var u=Qe.svgSpan(a,e),h=-e.fontMetrics().axisHeight+.5*u.height,d=-e.fontMetrics().axisHeight-.5*u.height-.111;(i.depth>.25||a.label==="\\xleftequilibrium")&&(d-=i.depth);var p;if(l){var g=-e.fontMetrics().axisHeight+l.height+.5*u.height+.111;p=w.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:d},{type:"elem",elem:u,shift:h},{type:"elem",elem:l,shift:g}]},e)}else p=w.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:d},{type:"elem",elem:u,shift:h}]},e);return p.children[0].children[0].children[1].classes.push("svg-align"),w.makeSpan(["mrel","x-arrow"],[p],e)},mathmlBuilder(a,e){var t=Qe.mathMLnode(a.label);t.setAttribute("minsize",a.label.charAt(0)==="x"?"1.75em":"3.0em");var r;if(a.body){var i=Z0(re(a.body,e));if(a.below){var n=Z0(re(a.below,e));r=new z.MathNode("munderover",[t,n,i])}else r=new z.MathNode("mover",[t,i])}else if(a.below){var l=Z0(re(a.below,e));r=new z.MathNode("munder",[t,l])}else r=Z0(),r=new z.MathNode("mover",[t,r]);return r}});var ll=w.makeSpan;function Ja(a,e){var t=pe(a.body,e,!0);return ll([a.mclass],t,e)}function Qa(a,e){var t,r=Ae(a.body,e);return a.mclass==="minner"?t=new z.MathNode("mpadded",r):a.mclass==="mord"?a.isCharacterBox?(t=r[0],t.type="mi"):t=new z.MathNode("mi",r):(a.isCharacterBox?(t=r[0],t.type="mo"):t=new z.MathNode("mo",r),a.mclass==="mbin"?(t.attributes.lspace="0.22em",t.attributes.rspace="0.22em"):a.mclass==="mpunct"?(t.attributes.lspace="0em",t.attributes.rspace="0.17em"):a.mclass==="mopen"||a.mclass==="mclose"?(t.attributes.lspace="0em",t.attributes.rspace="0em"):a.mclass==="minner"&&(t.attributes.lspace="0.0556em",t.attributes.width="+0.1111em")),t}N({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1,primitive:!0},handler(a,e){var{parser:t,funcName:r}=a,i=e[0];return{type:"mclass",mode:t.mode,mclass:"m"+r.slice(5),body:he(i),isCharacterBox:U.isCharacterBox(i)}},htmlBuilder:Ja,mathmlBuilder:Qa});var yt=a=>{var e=a.type==="ordgroup"&&a.body.length?a.body[0]:a;return e.type==="atom"&&(e.family==="bin"||e.family==="rel")?"m"+e.family:"mord"};N({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler(a,e){var{parser:t}=a;return{type:"mclass",mode:t.mode,mclass:yt(e[0]),body:he(e[1]),isCharacterBox:U.isCharacterBox(e[1])}}});N({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler(a,e){var{parser:t,funcName:r}=a,i=e[1],n=e[0],l;r!=="\\stackrel"?l=yt(i):l="mrel";var u={type:"op",mode:i.mode,limits:!0,alwaysHandleSupSub:!0,parentIsSupSub:!1,symbol:!1,suppressBaseShift:r!=="\\stackrel",body:he(i)},h={type:"supsub",mode:n.mode,base:u,sup:r==="\\underset"?null:n,sub:r==="\\underset"?n:null};return{type:"mclass",mode:t.mode,mclass:l,body:[h],isCharacterBox:U.isCharacterBox(h)}},htmlBuilder:Ja,mathmlBuilder:Qa});N({type:"pmb",names:["\\pmb"],props:{numArgs:1,allowedInText:!0},handler(a,e){var{parser:t}=a;return{type:"pmb",mode:t.mode,mclass:yt(e[0]),body:he(e[0])}},htmlBuilder(a,e){var t=pe(a.body,e,!0),r=w.makeSpan([a.mclass],t,e);return r.style.textShadow="0.02em 0.01em 0.04px",r},mathmlBuilder(a,e){var t=Ae(a.body,e),r=new z.MathNode("mstyle",t);return r.setAttribute("style","text-shadow: 0.02em 0.01em 0.04px"),r}});var ol={">":"\\\\cdrightarrow","<":"\\\\cdleftarrow","=":"\\\\cdlongequal",A:"\\uparrow",V:"\\downarrow","|":"\\Vert",".":"no arrow"},_r=()=>({type:"styling",body:[],mode:"math",style:"display"}),Xr=a=>a.type==="textord"&&a.text==="@",ul=(a,e)=>(a.type==="mathord"||a.type==="atom")&&a.text===e;function hl(a,e,t){var r=ol[a];switch(r){case"\\\\cdrightarrow":case"\\\\cdleftarrow":return t.callFunction(r,[e[0]],[e[1]]);case"\\uparrow":case"\\downarrow":{var i=t.callFunction("\\\\cdleft",[e[0]],[]),n={type:"atom",text:r,mode:"math",family:"rel"},l=t.callFunction("\\Big",[n],[]),u=t.callFunction("\\\\cdright",[e[1]],[]),h={type:"ordgroup",mode:"math",body:[i,l,u]};return t.callFunction("\\\\cdparent",[h],[])}case"\\\\cdlongequal":return t.callFunction("\\\\cdlongequal",[],[]);case"\\Vert":{var d={type:"textord",text:"\\Vert",mode:"math"};return t.callFunction("\\Big",[d],[])}default:return{type:"textord",text:" ",mode:"math"}}}function cl(a){var e=[];for(a.gullet.beginGroup(),a.gullet.macros.set("\\cr","\\\\\\relax"),a.gullet.beginGroup();;){e.push(a.parseExpression(!1,"\\\\")),a.gullet.endGroup(),a.gullet.beginGroup();var t=a.fetch().text;if(t==="&"||t==="\\\\")a.consume();else if(t==="\\end"){e[e.length-1].length===0&&e.pop();break}else throw new M("Expected \\\\ or \\cr or \\end",a.nextToken)}for(var r=[],i=[r],n=0;n<e.length;n++){for(var l=e[n],u=_r(),h=0;h<l.length;h++)if(!Xr(l[h]))u.body.push(l[h]);else{r.push(u),h+=1;var d=fr(l[h]).text,p=new Array(2);if(p[0]={type:"ordgroup",mode:"math",body:[]},p[1]={type:"ordgroup",mode:"math",body:[]},!("=|.".indexOf(d)>-1))if("<>AV".indexOf(d)>-1)for(var g=0;g<2;g++){for(var y=!0,x=h+1;x<l.length;x++){if(ul(l[x],d)){y=!1,h=x;break}if(Xr(l[x]))throw new M("Missing a "+d+" character to complete a CD arrow.",l[x]);p[g].body.push(l[x])}if(y)throw new M("Missing a "+d+" character to complete a CD arrow.",l[h])}else throw new M('Expected one of "<>AV=|." after @',l[h]);var k=hl(d,p,a),S={type:"styling",body:[k],mode:"math",style:"display"};r.push(S),u=_r()}n%2===0?r.push(u):r.shift(),r=[],i.push(r)}a.gullet.endGroup(),a.gullet.endGroup();var C=new Array(i[0].length).fill({type:"align",align:"c",pregap:.25,postgap:.25});return{type:"array",mode:"math",body:i,arraystretch:1,addJot:!0,rowGaps:[null],cols:C,colSeparationType:"CD",hLinesBeforeRow:new Array(i.length+1).fill([])}}N({type:"cdlabel",names:["\\\\cdleft","\\\\cdright"],props:{numArgs:1},handler(a,e){var{parser:t,funcName:r}=a;return{type:"cdlabel",mode:t.mode,side:r.slice(4),label:e[0]}},htmlBuilder(a,e){var t=e.havingStyle(e.style.sup()),r=w.wrapFragment(J(a.label,t,e),e);return r.classes.push("cd-label-"+a.side),r.style.bottom=B(.8-r.depth),r.height=0,r.depth=0,r},mathmlBuilder(a,e){var t=new z.MathNode("mrow",[re(a.label,e)]);return t=new z.MathNode("mpadded",[t]),t.setAttribute("width","0"),a.side==="left"&&t.setAttribute("lspace","-1width"),t.setAttribute("voffset","0.7em"),t=new z.MathNode("mstyle",[t]),t.setAttribute("displaystyle","false"),t.setAttribute("scriptlevel","1"),t}});N({type:"cdlabelparent",names:["\\\\cdparent"],props:{numArgs:1},handler(a,e){var{parser:t}=a;return{type:"cdlabelparent",mode:t.mode,fragment:e[0]}},htmlBuilder(a,e){var t=w.wrapFragment(J(a.fragment,e),e);return t.classes.push("cd-vert-arrow"),t},mathmlBuilder(a,e){return new z.MathNode("mrow",[re(a.fragment,e)])}});N({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler(a,e){for(var{parser:t}=a,r=Y(e[0],"ordgroup"),i=r.body,n="",l=0;l<i.length;l++){var u=Y(i[l],"textord");n+=u.text}var h=parseInt(n),d;if(isNaN(h))throw new M("\\@char has non-numeric argument "+n);if(h<0||h>=1114111)throw new M("\\@char with invalid code point "+n);return h<=65535?d=String.fromCharCode(h):(h-=65536,d=String.fromCharCode((h>>10)+55296,(h&1023)+56320)),{type:"textord",mode:t.mode,text:d}}});var Za=(a,e)=>{var t=pe(a.body,e.withColor(a.color),!1);return w.makeFragment(t)},Ka=(a,e)=>{var t=Ae(a.body,e.withColor(a.color)),r=new z.MathNode("mstyle",t);return r.setAttribute("mathcolor",a.color),r};N({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,argTypes:["color","original"]},handler(a,e){var{parser:t}=a,r=Y(e[0],"color-token").color,i=e[1];return{type:"color",mode:t.mode,color:r,body:he(i)}},htmlBuilder:Za,mathmlBuilder:Ka});N({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,argTypes:["color"]},handler(a,e){var{parser:t,breakOnTokenText:r}=a,i=Y(e[0],"color-token").color;t.gullet.macros.set("\\current@color",i);var n=t.parseExpression(!0,r);return{type:"color",mode:t.mode,color:i,body:n}},htmlBuilder:Za,mathmlBuilder:Ka});N({type:"cr",names:["\\\\"],props:{numArgs:0,numOptionalArgs:0,allowedInText:!0},handler(a,e,t){var{parser:r}=a,i=r.gullet.future().text==="["?r.parseSizeGroup(!0):null,n=!r.settings.displayMode||!r.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode");return{type:"cr",mode:r.mode,newLine:n,size:i&&Y(i,"size").value}},htmlBuilder(a,e){var t=w.makeSpan(["mspace"],[],e);return a.newLine&&(t.classes.push("newline"),a.size&&(t.style.marginTop=B(ue(a.size,e)))),t},mathmlBuilder(a,e){var t=new z.MathNode("mspace");return a.newLine&&(t.setAttribute("linebreak","newline"),a.size&&t.setAttribute("height",B(ue(a.size,e)))),t}});var Jt={"\\global":"\\global","\\long":"\\\\globallong","\\\\globallong":"\\\\globallong","\\def":"\\gdef","\\gdef":"\\gdef","\\edef":"\\xdef","\\xdef":"\\xdef","\\let":"\\\\globallet","\\futurelet":"\\\\globalfuture"},ei=a=>{var e=a.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(e))throw new M("Expected a control sequence",a);return e},ml=a=>{var e=a.gullet.popToken();return e.text==="="&&(e=a.gullet.popToken(),e.text===" "&&(e=a.gullet.popToken())),e},ti=(a,e,t,r)=>{var i=a.gullet.macros.get(t.text);i==null&&(t.noexpand=!0,i={tokens:[t],numArgs:0,unexpandable:!a.gullet.isExpandable(t.text)}),a.gullet.macros.set(e,i,r)};N({type:"internal",names:["\\global","\\long","\\\\globallong"],props:{numArgs:0,allowedInText:!0},handler(a){var{parser:e,funcName:t}=a;e.consumeSpaces();var r=e.fetch();if(Jt[r.text])return(t==="\\global"||t==="\\\\globallong")&&(r.text=Jt[r.text]),Y(e.parseFunction(),"internal");throw new M("Invalid token after macro prefix",r)}});N({type:"internal",names:["\\def","\\gdef","\\edef","\\xdef"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(a){var{parser:e,funcName:t}=a,r=e.gullet.popToken(),i=r.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(i))throw new M("Expected a control sequence",r);for(var n=0,l,u=[[]];e.gullet.future().text!=="{";)if(r=e.gullet.popToken(),r.text==="#"){if(e.gullet.future().text==="{"){l=e.gullet.future(),u[n].push("{");break}if(r=e.gullet.popToken(),!/^[1-9]$/.test(r.text))throw new M('Invalid argument number "'+r.text+'"');if(parseInt(r.text)!==n+1)throw new M('Argument number "'+r.text+'" out of order');n++,u.push([])}else{if(r.text==="EOF")throw new M("Expected a macro definition");u[n].push(r.text)}var{tokens:h}=e.gullet.consumeArg();return l&&h.unshift(l),(t==="\\edef"||t==="\\xdef")&&(h=e.gullet.expandTokens(h),h.reverse()),e.gullet.macros.set(i,{tokens:h,numArgs:n,delimiters:u},t===Jt[t]),{type:"internal",mode:e.mode}}});N({type:"internal",names:["\\let","\\\\globallet"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(a){var{parser:e,funcName:t}=a,r=ei(e.gullet.popToken());e.gullet.consumeSpaces();var i=ml(e);return ti(e,r,i,t==="\\\\globallet"),{type:"internal",mode:e.mode}}});N({type:"internal",names:["\\futurelet","\\\\globalfuture"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(a){var{parser:e,funcName:t}=a,r=ei(e.gullet.popToken()),i=e.gullet.popToken(),n=e.gullet.popToken();return ti(e,r,n,t==="\\\\globalfuture"),e.gullet.pushToken(n),e.gullet.pushToken(i),{type:"internal",mode:e.mode}}});var C0=function(e,t,r){var i=ne.math[e]&&ne.math[e].replace,n=hr(i||e,t,r);if(!n)throw new Error("Unsupported symbol "+e+" and font size "+t+".");return n},gr=function(e,t,r,i){var n=r.havingBaseStyle(t),l=w.makeSpan(i.concat(n.sizingClasses(r)),[e],r),u=n.sizeMultiplier/r.sizeMultiplier;return l.height*=u,l.depth*=u,l.maxFontSize=n.sizeMultiplier,l},ri=function(e,t,r){var i=t.havingBaseStyle(r),n=(1-t.sizeMultiplier/i.sizeMultiplier)*t.fontMetrics().axisHeight;e.classes.push("delimcenter"),e.style.top=B(n),e.height-=n,e.depth+=n},dl=function(e,t,r,i,n,l){var u=w.makeSymbol(e,"Main-Regular",n,i),h=gr(u,t,i,l);return r&&ri(h,i,t),h},pl=function(e,t,r,i){return w.makeSymbol(e,"Size"+t+"-Regular",r,i)},ai=function(e,t,r,i,n,l){var u=pl(e,t,n,i),h=gr(w.makeSpan(["delimsizing","size"+t],[u],i),O.TEXT,i,l);return r&&ri(h,i,O.TEXT),h},Dt=function(e,t,r){var i;t==="Size1-Regular"?i="delim-size1":i="delim-size4";var n=w.makeSpan(["delimsizinginner",i],[w.makeSpan([],[w.makeSymbol(e,t,r)])]);return{type:"elem",elem:n}},Et=function(e,t,r){var i=Pe["Size4-Regular"][e.charCodeAt(0)]?Pe["Size4-Regular"][e.charCodeAt(0)][4]:Pe["Size1-Regular"][e.charCodeAt(0)][4],n=new o0("inner",xs(e,Math.round(1e3*t))),l=new Xe([n],{width:B(i),height:B(t),style:"width:"+B(i),viewBox:"0 0 "+1e3*i+" "+Math.round(1e3*t),preserveAspectRatio:"xMinYMin"}),u=w.makeSvgSpan([],[l],r);return u.height=t,u.style.height=B(t),u.style.width=B(i),{type:"elem",elem:u}},Qt=.008,K0={type:"kern",size:-1*Qt},fl=["|","\\lvert","\\rvert","\\vert"],vl=["\\|","\\lVert","\\rVert","\\Vert"],ii=function(e,t,r,i,n,l){var u,h,d,p,g="",y=0;u=d=p=e,h=null;var x="Size1-Regular";e==="\\uparrow"?d=p="⏐":e==="\\Uparrow"?d=p="‖":e==="\\downarrow"?u=d="⏐":e==="\\Downarrow"?u=d="‖":e==="\\updownarrow"?(u="\\uparrow",d="⏐",p="\\downarrow"):e==="\\Updownarrow"?(u="\\Uparrow",d="‖",p="\\Downarrow"):U.contains(fl,e)?(d="∣",g="vert",y=333):U.contains(vl,e)?(d="∥",g="doublevert",y=556):e==="["||e==="\\lbrack"?(u="⎡",d="⎢",p="⎣",x="Size4-Regular",g="lbrack",y=667):e==="]"||e==="\\rbrack"?(u="⎤",d="⎥",p="⎦",x="Size4-Regular",g="rbrack",y=667):e==="\\lfloor"||e==="⌊"?(d=u="⎢",p="⎣",x="Size4-Regular",g="lfloor",y=667):e==="\\lceil"||e==="⌈"?(u="⎡",d=p="⎢",x="Size4-Regular",g="lceil",y=667):e==="\\rfloor"||e==="⌋"?(d=u="⎥",p="⎦",x="Size4-Regular",g="rfloor",y=667):e==="\\rceil"||e==="⌉"?(u="⎤",d=p="⎥",x="Size4-Regular",g="rceil",y=667):e==="("||e==="\\lparen"?(u="⎛",d="⎜",p="⎝",x="Size4-Regular",g="lparen",y=875):e===")"||e==="\\rparen"?(u="⎞",d="⎟",p="⎠",x="Size4-Regular",g="rparen",y=875):e==="\\{"||e==="\\lbrace"?(u="⎧",h="⎨",p="⎩",d="⎪",x="Size4-Regular"):e==="\\}"||e==="\\rbrace"?(u="⎫",h="⎬",p="⎭",d="⎪",x="Size4-Regular"):e==="\\lgroup"||e==="⟮"?(u="⎧",p="⎩",d="⎪",x="Size4-Regular"):e==="\\rgroup"||e==="⟯"?(u="⎫",p="⎭",d="⎪",x="Size4-Regular"):e==="\\lmoustache"||e==="⎰"?(u="⎧",p="⎭",d="⎪",x="Size4-Regular"):(e==="\\rmoustache"||e==="⎱")&&(u="⎫",p="⎩",d="⎪",x="Size4-Regular");var k=C0(u,x,n),S=k.height+k.depth,C=C0(d,x,n),E=C.height+C.depth,L=C0(p,x,n),V=L.height+L.depth,j=0,G=1;if(h!==null){var H=C0(h,x,n);j=H.height+H.depth,G=2}var T=S+V+j,I=Math.max(0,Math.ceil((t-T)/(G*E))),R=T+I*G*E,_=i.fontMetrics().axisHeight;r&&(_*=i.sizeMultiplier);var W=R/2-_,ee=[];if(g.length>0){var ve=R-S-V,le=Math.round(R*1e3),D=ws(g,Math.round(ve*1e3)),de=new o0(g,D),fe=(y/1e3).toFixed(3)+"em",ge=(le/1e3).toFixed(3)+"em",Ke=new Xe([de],{width:fe,height:ge,viewBox:"0 0 "+y+" "+le}),e0=w.makeSvgSpan([],[Ke],i);e0.height=le/1e3,e0.style.width=fe,e0.style.height=ge,ee.push({type:"elem",elem:e0})}else{if(ee.push(Dt(p,x,n)),ee.push(K0),h===null){var t0=R-S-V+2*Qt;ee.push(Et(d,t0,i))}else{var Te=(R-S-V-j)/2+2*Qt;ee.push(Et(d,Te,i)),ee.push(K0),ee.push(Dt(h,x,n)),ee.push(K0),ee.push(Et(d,Te,i))}ee.push(K0),ee.push(Dt(u,x,n))}var M0=i.havingBaseStyle(O.TEXT),kt=w.makeVList({positionType:"bottom",positionData:W,children:ee},M0);return gr(w.makeSpan(["delimsizing","mult"],[kt],M0),O.TEXT,i,l)},Nt=80,qt=.08,It=function(e,t,r,i,n){var l=ys(e,i,r),u=new o0(e,l),h=new Xe([u],{width:"400em",height:B(t),viewBox:"0 0 400000 "+r,preserveAspectRatio:"xMinYMin slice"});return w.makeSvgSpan(["hide-tail"],[h],n)},gl=function(e,t){var r=t.havingBaseSizing(),i=oi("\\surd",e*r.sizeMultiplier,li,r),n=r.sizeMultiplier,l=Math.max(0,t.minRuleThickness-t.fontMetrics().sqrtRuleThickness),u,h=0,d=0,p=0,g;return i.type==="small"?(p=1e3+1e3*l+Nt,e<1?n=1:e<1.4&&(n=.7),h=(1+l+qt)/n,d=(1+l)/n,u=It("sqrtMain",h,p,l,t),u.style.minWidth="0.853em",g=.833/n):i.type==="large"?(p=(1e3+Nt)*N0[i.size],d=(N0[i.size]+l)/n,h=(N0[i.size]+l+qt)/n,u=It("sqrtSize"+i.size,h,p,l,t),u.style.minWidth="1.02em",g=1/n):(h=e+l+qt,d=e+l,p=Math.floor(1e3*e+l)+Nt,u=It("sqrtTall",h,p,l,t),u.style.minWidth="0.742em",g=1.056),u.height=d,u.style.height=B(h),{span:u,advanceWidth:g,ruleWidth:(t.fontMetrics().sqrtRuleThickness+l)*n}},ni=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","\\surd"],bl=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱"],si=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],N0=[0,1.2,1.8,2.4,3],yl=function(e,t,r,i,n){if(e==="<"||e==="\\lt"||e==="⟨"?e="\\langle":(e===">"||e==="\\gt"||e==="⟩")&&(e="\\rangle"),U.contains(ni,e)||U.contains(si,e))return ai(e,t,!1,r,i,n);if(U.contains(bl,e))return ii(e,N0[t],!1,r,i,n);throw new M("Illegal delimiter: '"+e+"'")},xl=[{type:"small",style:O.SCRIPTSCRIPT},{type:"small",style:O.SCRIPT},{type:"small",style:O.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],wl=[{type:"small",style:O.SCRIPTSCRIPT},{type:"small",style:O.SCRIPT},{type:"small",style:O.TEXT},{type:"stack"}],li=[{type:"small",style:O.SCRIPTSCRIPT},{type:"small",style:O.SCRIPT},{type:"small",style:O.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],kl=function(e){if(e.type==="small")return"Main-Regular";if(e.type==="large")return"Size"+e.size+"-Regular";if(e.type==="stack")return"Size4-Regular";throw new Error("Add support for delim type '"+e.type+"' here.")},oi=function(e,t,r,i){for(var n=Math.min(2,3-i.style.size),l=n;l<r.length&&r[l].type!=="stack";l++){var u=C0(e,kl(r[l]),"math"),h=u.height+u.depth;if(r[l].type==="small"){var d=i.havingBaseStyle(r[l].style);h*=d.sizeMultiplier}if(h>t)return r[l]}return r[r.length-1]},ui=function(e,t,r,i,n,l){e==="<"||e==="\\lt"||e==="⟨"?e="\\langle":(e===">"||e==="\\gt"||e==="⟩")&&(e="\\rangle");var u;U.contains(si,e)?u=xl:U.contains(ni,e)?u=li:u=wl;var h=oi(e,t,u,i);return h.type==="small"?dl(e,h.style,r,i,n,l):h.type==="large"?ai(e,h.size,r,i,n,l):ii(e,t,r,i,n,l)},Sl=function(e,t,r,i,n,l){var u=i.fontMetrics().axisHeight*i.sizeMultiplier,h=901,d=5/i.fontMetrics().ptPerEm,p=Math.max(t-u,r+u),g=Math.max(p/500*h,2*p-d);return ui(e,g,!0,i,n,l)},_e={sqrtImage:gl,sizedDelim:yl,sizeToMaxHeight:N0,customSizedDelim:ui,leftRightDelim:Sl},Jr={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},Al=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","<",">","\\langle","⟨","\\rangle","⟩","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function xt(a,e){var t=bt(a);if(t&&U.contains(Al,t.text))return t;throw t?new M("Invalid delimiter '"+t.text+"' after '"+e.funcName+"'",a):new M("Invalid delimiter type '"+a.type+"'",a)}N({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1,argTypes:["primitive"]},handler:(a,e)=>{var t=xt(e[0],a);return{type:"delimsizing",mode:a.parser.mode,size:Jr[a.funcName].size,mclass:Jr[a.funcName].mclass,delim:t.text}},htmlBuilder:(a,e)=>a.delim==="."?w.makeSpan([a.mclass]):_e.sizedDelim(a.delim,a.size,e,a.mode,[a.mclass]),mathmlBuilder:a=>{var e=[];a.delim!=="."&&e.push(Ie(a.delim,a.mode));var t=new z.MathNode("mo",e);a.mclass==="mopen"||a.mclass==="mclose"?t.setAttribute("fence","true"):t.setAttribute("fence","false"),t.setAttribute("stretchy","true");var r=B(_e.sizeToMaxHeight[a.size]);return t.setAttribute("minsize",r),t.setAttribute("maxsize",r),t}});function Qr(a){if(!a.body)throw new Error("Bug: The leftright ParseNode wasn't fully parsed.")}N({type:"leftright-right",names:["\\right"],props:{numArgs:1,primitive:!0},handler:(a,e)=>{var t=a.parser.gullet.macros.get("\\current@color");if(t&&typeof t!="string")throw new M("\\current@color set to non-string in \\right");return{type:"leftright-right",mode:a.parser.mode,delim:xt(e[0],a).text,color:t}}});N({type:"leftright",names:["\\left"],props:{numArgs:1,primitive:!0},handler:(a,e)=>{var t=xt(e[0],a),r=a.parser;++r.leftrightDepth;var i=r.parseExpression(!1);--r.leftrightDepth,r.expect("\\right",!1);var n=Y(r.parseFunction(),"leftright-right");return{type:"leftright",mode:r.mode,body:i,left:t.text,right:n.delim,rightColor:n.color}},htmlBuilder:(a,e)=>{Qr(a);for(var t=pe(a.body,e,!0,["mopen","mclose"]),r=0,i=0,n=!1,l=0;l<t.length;l++)t[l].isMiddle?n=!0:(r=Math.max(t[l].height,r),i=Math.max(t[l].depth,i));r*=e.sizeMultiplier,i*=e.sizeMultiplier;var u;if(a.left==="."?u=I0(e,["mopen"]):u=_e.leftRightDelim(a.left,r,i,e,a.mode,["mopen"]),t.unshift(u),n)for(var h=1;h<t.length;h++){var d=t[h],p=d.isMiddle;p&&(t[h]=_e.leftRightDelim(p.delim,r,i,p.options,a.mode,[]))}var g;if(a.right===".")g=I0(e,["mclose"]);else{var y=a.rightColor?e.withColor(a.rightColor):e;g=_e.leftRightDelim(a.right,r,i,y,a.mode,["mclose"])}return t.push(g),w.makeSpan(["minner"],t,e)},mathmlBuilder:(a,e)=>{Qr(a);var t=Ae(a.body,e);if(a.left!=="."){var r=new z.MathNode("mo",[Ie(a.left,a.mode)]);r.setAttribute("fence","true"),t.unshift(r)}if(a.right!=="."){var i=new z.MathNode("mo",[Ie(a.right,a.mode)]);i.setAttribute("fence","true"),a.rightColor&&i.setAttribute("mathcolor",a.rightColor),t.push(i)}return dr(t)}});N({type:"middle",names:["\\middle"],props:{numArgs:1,primitive:!0},handler:(a,e)=>{var t=xt(e[0],a);if(!a.parser.leftrightDepth)throw new M("\\middle without preceding \\left",t);return{type:"middle",mode:a.parser.mode,delim:t.text}},htmlBuilder:(a,e)=>{var t;if(a.delim===".")t=I0(e,[]);else{t=_e.sizedDelim(a.delim,1,e,a.mode,[]);var r={delim:a.delim,options:e};t.isMiddle=r}return t},mathmlBuilder:(a,e)=>{var t=a.delim==="\\vert"||a.delim==="|"?Ie("|","text"):Ie(a.delim,a.mode),r=new z.MathNode("mo",[t]);return r.setAttribute("fence","true"),r.setAttribute("lspace","0.05em"),r.setAttribute("rspace","0.05em"),r}});var br=(a,e)=>{var t=w.wrapFragment(J(a.body,e),e),r=a.label.slice(1),i=e.sizeMultiplier,n,l=0,u=U.isCharacterBox(a.body);if(r==="sout")n=w.makeSpan(["stretchy","sout"]),n.height=e.fontMetrics().defaultRuleThickness/i,l=-.5*e.fontMetrics().xHeight;else if(r==="phase"){var h=ue({number:.6,unit:"pt"},e),d=ue({number:.35,unit:"ex"},e),p=e.havingBaseSizing();i=i/p.sizeMultiplier;var g=t.height+t.depth+h+d;t.style.paddingLeft=B(g/2+h);var y=Math.floor(1e3*g*i),x=gs(y),k=new Xe([new o0("phase",x)],{width:"400em",height:B(y/1e3),viewBox:"0 0 400000 "+y,preserveAspectRatio:"xMinYMin slice"});n=w.makeSvgSpan(["hide-tail"],[k],e),n.style.height=B(g),l=t.depth+h+d}else{/cancel/.test(r)?u||t.classes.push("cancel-pad"):r==="angl"?t.classes.push("anglpad"):t.classes.push("boxpad");var S=0,C=0,E=0;/box/.test(r)?(E=Math.max(e.fontMetrics().fboxrule,e.minRuleThickness),S=e.fontMetrics().fboxsep+(r==="colorbox"?0:E),C=S):r==="angl"?(E=Math.max(e.fontMetrics().defaultRuleThickness,e.minRuleThickness),S=4*E,C=Math.max(0,.25-t.depth)):(S=u?.2:0,C=S),n=Qe.encloseSpan(t,r,S,C,e),/fbox|boxed|fcolorbox/.test(r)?(n.style.borderStyle="solid",n.style.borderWidth=B(E)):r==="angl"&&E!==.049&&(n.style.borderTopWidth=B(E),n.style.borderRightWidth=B(E)),l=t.depth+C,a.backgroundColor&&(n.style.backgroundColor=a.backgroundColor,a.borderColor&&(n.style.borderColor=a.borderColor))}var L;if(a.backgroundColor)L=w.makeVList({positionType:"individualShift",children:[{type:"elem",elem:n,shift:l},{type:"elem",elem:t,shift:0}]},e);else{var V=/cancel|phase/.test(r)?["svg-align"]:[];L=w.makeVList({positionType:"individualShift",children:[{type:"elem",elem:t,shift:0},{type:"elem",elem:n,shift:l,wrapperClasses:V}]},e)}return/cancel/.test(r)&&(L.height=t.height,L.depth=t.depth),/cancel/.test(r)&&!u?w.makeSpan(["mord","cancel-lap"],[L],e):w.makeSpan(["mord"],[L],e)},yr=(a,e)=>{var t=0,r=new z.MathNode(a.label.indexOf("colorbox")>-1?"mpadded":"menclose",[re(a.body,e)]);switch(a.label){case"\\cancel":r.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":r.setAttribute("notation","downdiagonalstrike");break;case"\\phase":r.setAttribute("notation","phasorangle");break;case"\\sout":r.setAttribute("notation","horizontalstrike");break;case"\\fbox":r.setAttribute("notation","box");break;case"\\angl":r.setAttribute("notation","actuarial");break;case"\\fcolorbox":case"\\colorbox":if(t=e.fontMetrics().fboxsep*e.fontMetrics().ptPerEm,r.setAttribute("width","+"+2*t+"pt"),r.setAttribute("height","+"+2*t+"pt"),r.setAttribute("lspace",t+"pt"),r.setAttribute("voffset",t+"pt"),a.label==="\\fcolorbox"){var i=Math.max(e.fontMetrics().fboxrule,e.minRuleThickness);r.setAttribute("style","border: "+i+"em solid "+String(a.borderColor))}break;case"\\xcancel":r.setAttribute("notation","updiagonalstrike downdiagonalstrike");break}return a.backgroundColor&&r.setAttribute("mathbackground",a.backgroundColor),r};N({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,argTypes:["color","text"]},handler(a,e,t){var{parser:r,funcName:i}=a,n=Y(e[0],"color-token").color,l=e[1];return{type:"enclose",mode:r.mode,label:i,backgroundColor:n,body:l}},htmlBuilder:br,mathmlBuilder:yr});N({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,argTypes:["color","color","text"]},handler(a,e,t){var{parser:r,funcName:i}=a,n=Y(e[0],"color-token").color,l=Y(e[1],"color-token").color,u=e[2];return{type:"enclose",mode:r.mode,label:i,backgroundColor:l,borderColor:n,body:u}},htmlBuilder:br,mathmlBuilder:yr});N({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!0},handler(a,e){var{parser:t}=a;return{type:"enclose",mode:t.mode,label:"\\fbox",body:e[0]}}});N({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout","\\phase"],props:{numArgs:1},handler(a,e){var{parser:t,funcName:r}=a,i=e[0];return{type:"enclose",mode:t.mode,label:r,body:i}},htmlBuilder:br,mathmlBuilder:yr});N({type:"enclose",names:["\\angl"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!1},handler(a,e){var{parser:t}=a;return{type:"enclose",mode:t.mode,label:"\\angl",body:e[0]}}});var hi={};function Ge(a){for(var{type:e,names:t,props:r,handler:i,htmlBuilder:n,mathmlBuilder:l}=a,u={type:e,numArgs:r.numArgs||0,allowedInText:!1,numOptionalArgs:0,handler:i},h=0;h<t.length;++h)hi[t[h]]=u;n&&(ht[e]=n),l&&(ct[e]=l)}var ci={};function m(a,e){ci[a]=e}function Zr(a){var e=[];a.consumeSpaces();var t=a.fetch().text;for(t==="\\relax"&&(a.consume(),a.consumeSpaces(),t=a.fetch().text);t==="\\hline"||t==="\\hdashline";)a.consume(),e.push(t==="\\hdashline"),a.consumeSpaces(),t=a.fetch().text;return e}var wt=a=>{var e=a.parser.settings;if(!e.displayMode)throw new M("{"+a.envName+"} can be used only in display mode.")};function xr(a){if(a.indexOf("ed")===-1)return a.indexOf("*")===-1}function h0(a,e,t){var{hskipBeforeAndAfter:r,addJot:i,cols:n,arraystretch:l,colSeparationType:u,autoTag:h,singleRow:d,emptySingleRow:p,maxNumCols:g,leqno:y}=e;if(a.gullet.beginGroup(),d||a.gullet.macros.set("\\cr","\\\\\\relax"),!l){var x=a.gullet.expandMacroAsText("\\arraystretch");if(x==null)l=1;else if(l=parseFloat(x),!l||l<0)throw new M("Invalid \\arraystretch: "+x)}a.gullet.beginGroup();var k=[],S=[k],C=[],E=[],L=h!=null?[]:void 0;function V(){h&&a.gullet.macros.set("\\@eqnsw","1",!0)}function j(){L&&(a.gullet.macros.get("\\df@tag")?(L.push(a.subparse([new Ve("\\df@tag")])),a.gullet.macros.set("\\df@tag",void 0,!0)):L.push(!!h&&a.gullet.macros.get("\\@eqnsw")==="1"))}for(V(),E.push(Zr(a));;){var G=a.parseExpression(!1,d?"\\end":"\\\\");a.gullet.endGroup(),a.gullet.beginGroup(),G={type:"ordgroup",mode:a.mode,body:G},t&&(G={type:"styling",mode:a.mode,style:t,body:[G]}),k.push(G);var H=a.fetch().text;if(H==="&"){if(g&&k.length===g){if(d||u)throw new M("Too many tab characters: &",a.nextToken);a.settings.reportNonstrict("textEnv","Too few columns specified in the {array} column argument.")}a.consume()}else if(H==="\\end"){j(),k.length===1&&G.type==="styling"&&G.body[0].body.length===0&&(S.length>1||!p)&&S.pop(),E.length<S.length+1&&E.push([]);break}else if(H==="\\\\"){a.consume();var T=void 0;a.gullet.future().text!==" "&&(T=a.parseSizeGroup(!0)),C.push(T?T.value:null),j(),E.push(Zr(a)),k=[],S.push(k),V()}else throw new M("Expected & or \\\\ or \\cr or \\end",a.nextToken)}return a.gullet.endGroup(),a.gullet.endGroup(),{type:"array",mode:a.mode,addJot:i,arraystretch:l,body:S,cols:n,rowGaps:C,hskipBeforeAndAfter:r,hLinesBeforeRow:E,colSeparationType:u,tags:L,leqno:y}}function wr(a){return a.slice(0,1)==="d"?"display":"text"}var Ue=function(e,t){var r,i,n=e.body.length,l=e.hLinesBeforeRow,u=0,h=new Array(n),d=[],p=Math.max(t.fontMetrics().arrayRuleWidth,t.minRuleThickness),g=1/t.fontMetrics().ptPerEm,y=5*g;if(e.colSeparationType&&e.colSeparationType==="small"){var x=t.havingStyle(O.SCRIPT).sizeMultiplier;y=.2778*(x/t.sizeMultiplier)}var k=e.colSeparationType==="CD"?ue({number:3,unit:"ex"},t):12*g,S=3*g,C=e.arraystretch*k,E=.7*C,L=.3*C,V=0;function j(U0){for(var $0=0;$0<U0.length;++$0)$0>0&&(V+=.25),d.push({pos:V,isDashed:U0[$0]})}for(j(l[0]),r=0;r<e.body.length;++r){var G=e.body[r],H=E,T=L;u<G.length&&(u=G.length);var I=new Array(G.length);for(i=0;i<G.length;++i){var R=J(G[i],t);T<R.depth&&(T=R.depth),H<R.height&&(H=R.height),I[i]=R}var _=e.rowGaps[r],W=0;_&&(W=ue(_,t),W>0&&(W+=L,T<W&&(T=W),W=0)),e.addJot&&(T+=S),I.height=H,I.depth=T,V+=H,I.pos=V,V+=T+W,h[r]=I,j(l[r+1])}var ee=V/2+t.fontMetrics().axisHeight,ve=e.cols||[],le=[],D,de,fe=[];if(e.tags&&e.tags.some(U0=>U0))for(r=0;r<n;++r){var ge=h[r],Ke=ge.pos-ee,e0=e.tags[r],t0=void 0;e0===!0?t0=w.makeSpan(["eqn-num"],[],t):e0===!1?t0=w.makeSpan([],[],t):t0=w.makeSpan([],pe(e0,t,!0),t),t0.depth=ge.depth,t0.height=ge.height,fe.push({type:"elem",elem:t0,shift:Ke})}for(i=0,de=0;i<u||de<ve.length;++i,++de){for(var Te=ve[de]||{},M0=!0;Te.type==="separator";){if(M0||(D=w.makeSpan(["arraycolsep"],[]),D.style.width=B(t.fontMetrics().doubleRuleSep),le.push(D)),Te.separator==="|"||Te.separator===":"){var kt=Te.separator==="|"?"solid":"dashed",g0=w.makeSpan(["vertical-separator"],[],t);g0.style.height=B(V),g0.style.borderRightWidth=B(p),g0.style.borderRightStyle=kt,g0.style.margin="0 "+B(-p/2);var Br=V-ee;Br&&(g0.style.verticalAlign=B(-Br)),le.push(g0)}else throw new M("Invalid separator type: "+Te.separator);de++,Te=ve[de]||{},M0=!1}if(!(i>=u)){var b0=void 0;(i>0||e.hskipBeforeAndAfter)&&(b0=U.deflt(Te.pregap,y),b0!==0&&(D=w.makeSpan(["arraycolsep"],[]),D.style.width=B(b0),le.push(D)));var y0=[];for(r=0;r<n;++r){var V0=h[r],G0=V0[i];if(G0){var Bi=V0.pos-ee;G0.depth=V0.depth,G0.height=V0.height,y0.push({type:"elem",elem:G0,shift:Bi})}}y0=w.makeVList({positionType:"individualShift",children:y0},t),y0=w.makeSpan(["col-align-"+(Te.align||"c")],[y0]),le.push(y0),(i<u-1||e.hskipBeforeAndAfter)&&(b0=U.deflt(Te.postgap,y),b0!==0&&(D=w.makeSpan(["arraycolsep"],[]),D.style.width=B(b0),le.push(D)))}}if(h=w.makeSpan(["mtable"],le),d.length>0){for(var Ci=w.makeLineSpan("hline",t,p),Ri=w.makeLineSpan("hdashline",t,p),St=[{type:"elem",elem:h,shift:0}];d.length>0;){var Cr=d.pop(),Rr=Cr.pos-ee;Cr.isDashed?St.push({type:"elem",elem:Ri,shift:Rr}):St.push({type:"elem",elem:Ci,shift:Rr})}h=w.makeVList({positionType:"individualShift",children:St},t)}if(fe.length===0)return w.makeSpan(["mord"],[h],t);var At=w.makeVList({positionType:"individualShift",children:fe},t);return At=w.makeSpan(["tag"],[At],t),w.makeFragment([h,At])},zl={c:"center ",l:"left ",r:"right "},$e=function(e,t){for(var r=[],i=new z.MathNode("mtd",[],["mtr-glue"]),n=new z.MathNode("mtd",[],["mml-eqn-num"]),l=0;l<e.body.length;l++){for(var u=e.body[l],h=[],d=0;d<u.length;d++)h.push(new z.MathNode("mtd",[re(u[d],t)]));e.tags&&e.tags[l]&&(h.unshift(i),h.push(i),e.leqno?h.unshift(n):h.push(n)),r.push(new z.MathNode("mtr",h))}var p=new z.MathNode("mtable",r),g=e.arraystretch===.5?.1:.16+e.arraystretch-1+(e.addJot?.09:0);p.setAttribute("rowspacing",B(g));var y="",x="";if(e.cols&&e.cols.length>0){var k=e.cols,S="",C=!1,E=0,L=k.length;k[0].type==="separator"&&(y+="top ",E=1),k[k.length-1].type==="separator"&&(y+="bottom ",L-=1);for(var V=E;V<L;V++)k[V].type==="align"?(x+=zl[k[V].align],C&&(S+="none "),C=!0):k[V].type==="separator"&&C&&(S+=k[V].separator==="|"?"solid ":"dashed ",C=!1);p.setAttribute("columnalign",x.trim()),/[sd]/.test(S)&&p.setAttribute("columnlines",S.trim())}if(e.colSeparationType==="align"){for(var j=e.cols||[],G="",H=1;H<j.length;H++)G+=H%2?"0em ":"1em ";p.setAttribute("columnspacing",G.trim())}else e.colSeparationType==="alignat"||e.colSeparationType==="gather"?p.setAttribute("columnspacing","0em"):e.colSeparationType==="small"?p.setAttribute("columnspacing","0.2778em"):e.colSeparationType==="CD"?p.setAttribute("columnspacing","0.5em"):p.setAttribute("columnspacing","1em");var T="",I=e.hLinesBeforeRow;y+=I[0].length>0?"left ":"",y+=I[I.length-1].length>0?"right ":"";for(var R=1;R<I.length-1;R++)T+=I[R].length===0?"none ":I[R][0]?"dashed ":"solid ";return/[sd]/.test(T)&&p.setAttribute("rowlines",T.trim()),y!==""&&(p=new z.MathNode("menclose",[p]),p.setAttribute("notation",y.trim())),e.arraystretch&&e.arraystretch<1&&(p=new z.MathNode("mstyle",[p]),p.setAttribute("scriptlevel","1")),p},mi=function(e,t){e.envName.indexOf("ed")===-1&&wt(e);var r=[],i=e.envName.indexOf("at")>-1?"alignat":"align",n=e.envName==="split",l=h0(e.parser,{cols:r,addJot:!0,autoTag:n?void 0:xr(e.envName),emptySingleRow:!0,colSeparationType:i,maxNumCols:n?2:void 0,leqno:e.parser.settings.leqno},"display"),u,h=0,d={type:"ordgroup",mode:e.mode,body:[]};if(t[0]&&t[0].type==="ordgroup"){for(var p="",g=0;g<t[0].body.length;g++){var y=Y(t[0].body[g],"textord");p+=y.text}u=Number(p),h=u*2}var x=!h;l.body.forEach(function(E){for(var L=1;L<E.length;L+=2){var V=Y(E[L],"styling"),j=Y(V.body[0],"ordgroup");j.body.unshift(d)}if(x)h<E.length&&(h=E.length);else{var G=E.length/2;if(u<G)throw new M("Too many math in a row: "+("expected "+u+", but got "+G),E[0])}});for(var k=0;k<h;++k){var S="r",C=0;k%2===1?S="l":k>0&&x&&(C=1),r[k]={type:"align",align:S,pregap:C,postgap:0}}return l.colSeparationType=x?"align":"alignat",l};Ge({type:"array",names:["array","darray"],props:{numArgs:1},handler(a,e){var t=bt(e[0]),r=t?[e[0]]:Y(e[0],"ordgroup").body,i=r.map(function(l){var u=fr(l),h=u.text;if("lcr".indexOf(h)!==-1)return{type:"align",align:h};if(h==="|")return{type:"separator",separator:"|"};if(h===":")return{type:"separator",separator:":"};throw new M("Unknown column alignment: "+h,l)}),n={cols:i,hskipBeforeAndAfter:!0,maxNumCols:i.length};return h0(a.parser,n,wr(a.envName))},htmlBuilder:Ue,mathmlBuilder:$e});Ge({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix","matrix*","pmatrix*","bmatrix*","Bmatrix*","vmatrix*","Vmatrix*"],props:{numArgs:0},handler(a){var e={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[a.envName.replace("*","")],t="c",r={hskipBeforeAndAfter:!1,cols:[{type:"align",align:t}]};if(a.envName.charAt(a.envName.length-1)==="*"){var i=a.parser;if(i.consumeSpaces(),i.fetch().text==="["){if(i.consume(),i.consumeSpaces(),t=i.fetch().text,"lcr".indexOf(t)===-1)throw new M("Expected l or c or r",i.nextToken);i.consume(),i.consumeSpaces(),i.expect("]"),i.consume(),r.cols=[{type:"align",align:t}]}}var n=h0(a.parser,r,wr(a.envName)),l=Math.max(0,...n.body.map(u=>u.length));return n.cols=new Array(l).fill({type:"align",align:t}),e?{type:"leftright",mode:a.mode,body:[n],left:e[0],right:e[1],rightColor:void 0}:n},htmlBuilder:Ue,mathmlBuilder:$e});Ge({type:"array",names:["smallmatrix"],props:{numArgs:0},handler(a){var e={arraystretch:.5},t=h0(a.parser,e,"script");return t.colSeparationType="small",t},htmlBuilder:Ue,mathmlBuilder:$e});Ge({type:"array",names:["subarray"],props:{numArgs:1},handler(a,e){var t=bt(e[0]),r=t?[e[0]]:Y(e[0],"ordgroup").body,i=r.map(function(l){var u=fr(l),h=u.text;if("lc".indexOf(h)!==-1)return{type:"align",align:h};throw new M("Unknown column alignment: "+h,l)});if(i.length>1)throw new M("{subarray} can contain only one column");var n={cols:i,hskipBeforeAndAfter:!1,arraystretch:.5};if(n=h0(a.parser,n,"script"),n.body.length>0&&n.body[0].length>1)throw new M("{subarray} can contain only one column");return n},htmlBuilder:Ue,mathmlBuilder:$e});Ge({type:"array",names:["cases","dcases","rcases","drcases"],props:{numArgs:0},handler(a){var e={arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},t=h0(a.parser,e,wr(a.envName));return{type:"leftright",mode:a.mode,body:[t],left:a.envName.indexOf("r")>-1?".":"\\{",right:a.envName.indexOf("r")>-1?"\\}":".",rightColor:void 0}},htmlBuilder:Ue,mathmlBuilder:$e});Ge({type:"array",names:["align","align*","aligned","split"],props:{numArgs:0},handler:mi,htmlBuilder:Ue,mathmlBuilder:$e});Ge({type:"array",names:["gathered","gather","gather*"],props:{numArgs:0},handler(a){U.contains(["gather","gather*"],a.envName)&&wt(a);var e={cols:[{type:"align",align:"c"}],addJot:!0,colSeparationType:"gather",autoTag:xr(a.envName),emptySingleRow:!0,leqno:a.parser.settings.leqno};return h0(a.parser,e,"display")},htmlBuilder:Ue,mathmlBuilder:$e});Ge({type:"array",names:["alignat","alignat*","alignedat"],props:{numArgs:1},handler:mi,htmlBuilder:Ue,mathmlBuilder:$e});Ge({type:"array",names:["equation","equation*"],props:{numArgs:0},handler(a){wt(a);var e={autoTag:xr(a.envName),emptySingleRow:!0,singleRow:!0,maxNumCols:1,leqno:a.parser.settings.leqno};return h0(a.parser,e,"display")},htmlBuilder:Ue,mathmlBuilder:$e});Ge({type:"array",names:["CD"],props:{numArgs:0},handler(a){return wt(a),cl(a.parser)},htmlBuilder:Ue,mathmlBuilder:$e});m("\\nonumber","\\gdef\\@eqnsw{0}");m("\\notag","\\nonumber");N({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler(a,e){throw new M(a.funcName+" valid only within array environment")}});var Kr=hi;N({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler(a,e){var{parser:t,funcName:r}=a,i=e[0];if(i.type!=="ordgroup")throw new M("Invalid environment name",i);for(var n="",l=0;l<i.body.length;++l)n+=Y(i.body[l],"textord").text;if(r==="\\begin"){if(!Kr.hasOwnProperty(n))throw new M("No such environment: "+n,i);var u=Kr[n],{args:h,optArgs:d}=t.parseArguments("\\begin{"+n+"}",u),p={mode:t.mode,envName:n,parser:t},g=u.handler(p,h,d);t.expect("\\end",!1);var y=t.nextToken,x=Y(t.parseFunction(),"environment");if(x.name!==n)throw new M("Mismatch: \\begin{"+n+"} matched by \\end{"+x.name+"}",y);return g}return{type:"environment",mode:t.mode,name:n,nameGroup:i}}});var di=(a,e)=>{var t=a.font,r=e.withFont(t);return J(a.body,r)},pi=(a,e)=>{var t=a.font,r=e.withFont(t);return re(a.body,r)},ea={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};N({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,allowedInArgument:!0},handler:(a,e)=>{var{parser:t,funcName:r}=a,i=mt(e[0]),n=r;return n in ea&&(n=ea[n]),{type:"font",mode:t.mode,font:n.slice(1),body:i}},htmlBuilder:di,mathmlBuilder:pi});N({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1},handler:(a,e)=>{var{parser:t}=a,r=e[0],i=U.isCharacterBox(r);return{type:"mclass",mode:t.mode,mclass:yt(r),body:[{type:"font",mode:t.mode,font:"boldsymbol",body:r}],isCharacterBox:i}}});N({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it","\\cal"],props:{numArgs:0,allowedInText:!0},handler:(a,e)=>{var{parser:t,funcName:r,breakOnTokenText:i}=a,{mode:n}=t,l=t.parseExpression(!0,i),u="math"+r.slice(1);return{type:"font",mode:n,font:u,body:{type:"ordgroup",mode:t.mode,body:l}}},htmlBuilder:di,mathmlBuilder:pi});var fi=(a,e)=>{var t=e;return a==="display"?t=t.id>=O.SCRIPT.id?t.text():O.DISPLAY:a==="text"&&t.size===O.DISPLAY.size?t=O.TEXT:a==="script"?t=O.SCRIPT:a==="scriptscript"&&(t=O.SCRIPTSCRIPT),t},kr=(a,e)=>{var t=fi(a.size,e.style),r=t.fracNum(),i=t.fracDen(),n;n=e.havingStyle(r);var l=J(a.numer,n,e);if(a.continued){var u=8.5/e.fontMetrics().ptPerEm,h=3.5/e.fontMetrics().ptPerEm;l.height=l.height<u?u:l.height,l.depth=l.depth<h?h:l.depth}n=e.havingStyle(i);var d=J(a.denom,n,e),p,g,y;a.hasBarLine?(a.barSize?(g=ue(a.barSize,e),p=w.makeLineSpan("frac-line",e,g)):p=w.makeLineSpan("frac-line",e),g=p.height,y=p.height):(p=null,g=0,y=e.fontMetrics().defaultRuleThickness);var x,k,S;t.size===O.DISPLAY.size||a.size==="display"?(x=e.fontMetrics().num1,g>0?k=3*y:k=7*y,S=e.fontMetrics().denom1):(g>0?(x=e.fontMetrics().num2,k=y):(x=e.fontMetrics().num3,k=3*y),S=e.fontMetrics().denom2);var C;if(p){var L=e.fontMetrics().axisHeight;x-l.depth-(L+.5*g)<k&&(x+=k-(x-l.depth-(L+.5*g))),L-.5*g-(d.height-S)<k&&(S+=k-(L-.5*g-(d.height-S)));var V=-(L-.5*g);C=w.makeVList({positionType:"individualShift",children:[{type:"elem",elem:d,shift:S},{type:"elem",elem:p,shift:V},{type:"elem",elem:l,shift:-x}]},e)}else{var E=x-l.depth-(d.height-S);E<k&&(x+=.5*(k-E),S+=.5*(k-E)),C=w.makeVList({positionType:"individualShift",children:[{type:"elem",elem:d,shift:S},{type:"elem",elem:l,shift:-x}]},e)}n=e.havingStyle(t),C.height*=n.sizeMultiplier/e.sizeMultiplier,C.depth*=n.sizeMultiplier/e.sizeMultiplier;var j;t.size===O.DISPLAY.size?j=e.fontMetrics().delim1:t.size===O.SCRIPTSCRIPT.size?j=e.havingStyle(O.SCRIPT).fontMetrics().delim2:j=e.fontMetrics().delim2;var G,H;return a.leftDelim==null?G=I0(e,["mopen"]):G=_e.customSizedDelim(a.leftDelim,j,!0,e.havingStyle(t),a.mode,["mopen"]),a.continued?H=w.makeSpan([]):a.rightDelim==null?H=I0(e,["mclose"]):H=_e.customSizedDelim(a.rightDelim,j,!0,e.havingStyle(t),a.mode,["mclose"]),w.makeSpan(["mord"].concat(n.sizingClasses(e)),[G,w.makeSpan(["mfrac"],[C]),H],e)},Sr=(a,e)=>{var t=new z.MathNode("mfrac",[re(a.numer,e),re(a.denom,e)]);if(!a.hasBarLine)t.setAttribute("linethickness","0px");else if(a.barSize){var r=ue(a.barSize,e);t.setAttribute("linethickness",B(r))}var i=fi(a.size,e.style);if(i.size!==e.style.size){t=new z.MathNode("mstyle",[t]);var n=i.size===O.DISPLAY.size?"true":"false";t.setAttribute("displaystyle",n),t.setAttribute("scriptlevel","0")}if(a.leftDelim!=null||a.rightDelim!=null){var l=[];if(a.leftDelim!=null){var u=new z.MathNode("mo",[new z.TextNode(a.leftDelim.replace("\\",""))]);u.setAttribute("fence","true"),l.push(u)}if(l.push(t),a.rightDelim!=null){var h=new z.MathNode("mo",[new z.TextNode(a.rightDelim.replace("\\",""))]);h.setAttribute("fence","true"),l.push(h)}return dr(l)}return t};N({type:"genfrac",names:["\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,allowedInArgument:!0},handler:(a,e)=>{var{parser:t,funcName:r}=a,i=e[0],n=e[1],l,u=null,h=null,d="auto";switch(r){case"\\dfrac":case"\\frac":case"\\tfrac":l=!0;break;case"\\\\atopfrac":l=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":l=!1,u="(",h=")";break;case"\\\\bracefrac":l=!1,u="\\{",h="\\}";break;case"\\\\brackfrac":l=!1,u="[",h="]";break;default:throw new Error("Unrecognized genfrac command")}switch(r){case"\\dfrac":case"\\dbinom":d="display";break;case"\\tfrac":case"\\tbinom":d="text";break}return{type:"genfrac",mode:t.mode,continued:!1,numer:i,denom:n,hasBarLine:l,leftDelim:u,rightDelim:h,size:d,barSize:null}},htmlBuilder:kr,mathmlBuilder:Sr});N({type:"genfrac",names:["\\cfrac"],props:{numArgs:2},handler:(a,e)=>{var{parser:t,funcName:r}=a,i=e[0],n=e[1];return{type:"genfrac",mode:t.mode,continued:!0,numer:i,denom:n,hasBarLine:!0,leftDelim:null,rightDelim:null,size:"display",barSize:null}}});N({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler(a){var{parser:e,funcName:t,token:r}=a,i;switch(t){case"\\over":i="\\frac";break;case"\\choose":i="\\binom";break;case"\\atop":i="\\\\atopfrac";break;case"\\brace":i="\\\\bracefrac";break;case"\\brack":i="\\\\brackfrac";break;default:throw new Error("Unrecognized infix genfrac command")}return{type:"infix",mode:e.mode,replaceWith:i,token:r}}});var ta=["display","text","script","scriptscript"],ra=function(e){var t=null;return e.length>0&&(t=e,t=t==="."?null:t),t};N({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,allowedInArgument:!0,argTypes:["math","math","size","text","math","math"]},handler(a,e){var{parser:t}=a,r=e[4],i=e[5],n=mt(e[0]),l=n.type==="atom"&&n.family==="open"?ra(n.text):null,u=mt(e[1]),h=u.type==="atom"&&u.family==="close"?ra(u.text):null,d=Y(e[2],"size"),p,g=null;d.isBlank?p=!0:(g=d.value,p=g.number>0);var y="auto",x=e[3];if(x.type==="ordgroup"){if(x.body.length>0){var k=Y(x.body[0],"textord");y=ta[Number(k.text)]}}else x=Y(x,"textord"),y=ta[Number(x.text)];return{type:"genfrac",mode:t.mode,numer:r,denom:i,continued:!1,hasBarLine:p,barSize:g,leftDelim:l,rightDelim:h,size:y}},htmlBuilder:kr,mathmlBuilder:Sr});N({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler(a,e){var{parser:t,funcName:r,token:i}=a;return{type:"infix",mode:t.mode,replaceWith:"\\\\abovefrac",size:Y(e[0],"size").value,token:i}}});N({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:(a,e)=>{var{parser:t,funcName:r}=a,i=e[0],n=rs(Y(e[1],"infix").size),l=e[2],u=n.number>0;return{type:"genfrac",mode:t.mode,numer:i,denom:l,continued:!1,hasBarLine:u,barSize:n,leftDelim:null,rightDelim:null,size:"auto"}},htmlBuilder:kr,mathmlBuilder:Sr});var vi=(a,e)=>{var t=e.style,r,i;a.type==="supsub"?(r=a.sup?J(a.sup,e.havingStyle(t.sup()),e):J(a.sub,e.havingStyle(t.sub()),e),i=Y(a.base,"horizBrace")):i=Y(a,"horizBrace");var n=J(i.base,e.havingBaseStyle(O.DISPLAY)),l=Qe.svgSpan(i,e),u;if(i.isOver?(u=w.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:n},{type:"kern",size:.1},{type:"elem",elem:l}]},e),u.children[0].children[0].children[1].classes.push("svg-align")):(u=w.makeVList({positionType:"bottom",positionData:n.depth+.1+l.height,children:[{type:"elem",elem:l},{type:"kern",size:.1},{type:"elem",elem:n}]},e),u.children[0].children[0].children[0].classes.push("svg-align")),r){var h=w.makeSpan(["mord",i.isOver?"mover":"munder"],[u],e);i.isOver?u=w.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:h},{type:"kern",size:.2},{type:"elem",elem:r}]},e):u=w.makeVList({positionType:"bottom",positionData:h.depth+.2+r.height+r.depth,children:[{type:"elem",elem:r},{type:"kern",size:.2},{type:"elem",elem:h}]},e)}return w.makeSpan(["mord",i.isOver?"mover":"munder"],[u],e)},Ml=(a,e)=>{var t=Qe.mathMLnode(a.label);return new z.MathNode(a.isOver?"mover":"munder",[re(a.base,e),t])};N({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler(a,e){var{parser:t,funcName:r}=a;return{type:"horizBrace",mode:t.mode,label:r,isOver:/^\\over/.test(r),base:e[0]}},htmlBuilder:vi,mathmlBuilder:Ml});N({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:(a,e)=>{var{parser:t}=a,r=e[1],i=Y(e[0],"url").url;return t.settings.isTrusted({command:"\\href",url:i})?{type:"href",mode:t.mode,href:i,body:he(r)}:t.formatUnsupportedCmd("\\href")},htmlBuilder:(a,e)=>{var t=pe(a.body,e,!1);return w.makeAnchor(a.href,[],t,e)},mathmlBuilder:(a,e)=>{var t=u0(a.body,e);return t instanceof Re||(t=new Re("mrow",[t])),t.setAttribute("href",a.href),t}});N({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:(a,e)=>{var{parser:t}=a,r=Y(e[0],"url").url;if(!t.settings.isTrusted({command:"\\url",url:r}))return t.formatUnsupportedCmd("\\url");for(var i=[],n=0;n<r.length;n++){var l=r[n];l==="~"&&(l="\\textasciitilde"),i.push({type:"textord",mode:"text",text:l})}var u={type:"text",mode:t.mode,font:"\\texttt",body:i};return{type:"href",mode:t.mode,href:r,body:he(u)}}});N({type:"hbox",names:["\\hbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0,primitive:!0},handler(a,e){var{parser:t}=a;return{type:"hbox",mode:t.mode,body:he(e[0])}},htmlBuilder(a,e){var t=pe(a.body,e,!1);return w.makeFragment(t)},mathmlBuilder(a,e){return new z.MathNode("mrow",Ae(a.body,e))}});N({type:"html",names:["\\htmlClass","\\htmlId","\\htmlStyle","\\htmlData"],props:{numArgs:2,argTypes:["raw","original"],allowedInText:!0},handler:(a,e)=>{var{parser:t,funcName:r,token:i}=a,n=Y(e[0],"raw").string,l=e[1];t.settings.strict&&t.settings.reportNonstrict("htmlExtension","HTML extension is disabled on strict mode");var u,h={};switch(r){case"\\htmlClass":h.class=n,u={command:"\\htmlClass",class:n};break;case"\\htmlId":h.id=n,u={command:"\\htmlId",id:n};break;case"\\htmlStyle":h.style=n,u={command:"\\htmlStyle",style:n};break;case"\\htmlData":{for(var d=n.split(","),p=0;p<d.length;p++){var g=d[p].split("=");if(g.length!==2)throw new M("Error parsing key-value for \\htmlData");h["data-"+g[0].trim()]=g[1].trim()}u={command:"\\htmlData",attributes:h};break}default:throw new Error("Unrecognized html command")}return t.settings.isTrusted(u)?{type:"html",mode:t.mode,attributes:h,body:he(l)}:t.formatUnsupportedCmd(r)},htmlBuilder:(a,e)=>{var t=pe(a.body,e,!1),r=["enclosing"];a.attributes.class&&r.push(...a.attributes.class.trim().split(/\s+/));var i=w.makeSpan(r,t,e);for(var n in a.attributes)n!=="class"&&a.attributes.hasOwnProperty(n)&&i.setAttribute(n,a.attributes[n]);return i},mathmlBuilder:(a,e)=>u0(a.body,e)});N({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:(a,e)=>{var{parser:t}=a;return{type:"htmlmathml",mode:t.mode,html:he(e[0]),mathml:he(e[1])}},htmlBuilder:(a,e)=>{var t=pe(a.html,e,!1);return w.makeFragment(t)},mathmlBuilder:(a,e)=>u0(a.mathml,e)});var Lt=function(e){if(/^[-+]? *(\d+(\.\d*)?|\.\d+)$/.test(e))return{number:+e,unit:"bp"};var t=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(e);if(!t)throw new M("Invalid size: '"+e+"' in \\includegraphics");var r={number:+(t[1]+t[2]),unit:t[3]};if(!Ia(r))throw new M("Invalid unit: '"+r.unit+"' in \\includegraphics.");return r};N({type:"includegraphics",names:["\\includegraphics"],props:{numArgs:1,numOptionalArgs:1,argTypes:["raw","url"],allowedInText:!1},handler:(a,e,t)=>{var{parser:r}=a,i={number:0,unit:"em"},n={number:.9,unit:"em"},l={number:0,unit:"em"},u="";if(t[0])for(var h=Y(t[0],"raw").string,d=h.split(","),p=0;p<d.length;p++){var g=d[p].split("=");if(g.length===2){var y=g[1].trim();switch(g[0].trim()){case"alt":u=y;break;case"width":i=Lt(y);break;case"height":n=Lt(y);break;case"totalheight":l=Lt(y);break;default:throw new M("Invalid key: '"+g[0]+"' in \\includegraphics.")}}}var x=Y(e[0],"url").url;return u===""&&(u=x,u=u.replace(/^.*[\\/]/,""),u=u.substring(0,u.lastIndexOf("."))),r.settings.isTrusted({command:"\\includegraphics",url:x})?{type:"includegraphics",mode:r.mode,alt:u,width:i,height:n,totalheight:l,src:x}:r.formatUnsupportedCmd("\\includegraphics")},htmlBuilder:(a,e)=>{var t=ue(a.height,e),r=0;a.totalheight.number>0&&(r=ue(a.totalheight,e)-t);var i=0;a.width.number>0&&(i=ue(a.width,e));var n={height:B(t+r)};i>0&&(n.width=B(i)),r>0&&(n.verticalAlign=B(-r));var l=new Ms(a.src,a.alt,n);return l.height=t,l.depth=r,l},mathmlBuilder:(a,e)=>{var t=new z.MathNode("mglyph",[]);t.setAttribute("alt",a.alt);var r=ue(a.height,e),i=0;if(a.totalheight.number>0&&(i=ue(a.totalheight,e)-r,t.setAttribute("valign",B(-i))),t.setAttribute("height",B(r+i)),a.width.number>0){var n=ue(a.width,e);t.setAttribute("width",B(n))}return t.setAttribute("src",a.src),t}});N({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],primitive:!0,allowedInText:!0},handler(a,e){var{parser:t,funcName:r}=a,i=Y(e[0],"size");if(t.settings.strict){var n=r[1]==="m",l=i.value.unit==="mu";n?(l||t.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+r+" supports only mu units, "+("not "+i.value.unit+" units")),t.mode!=="math"&&t.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+r+" works only in math mode")):l&&t.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+r+" doesn't support mu units")}return{type:"kern",mode:t.mode,dimension:i.value}},htmlBuilder(a,e){return w.makeGlue(a.dimension,e)},mathmlBuilder(a,e){var t=ue(a.dimension,e);return new z.SpaceNode(t)}});N({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:(a,e)=>{var{parser:t,funcName:r}=a,i=e[0];return{type:"lap",mode:t.mode,alignment:r.slice(5),body:i}},htmlBuilder:(a,e)=>{var t;a.alignment==="clap"?(t=w.makeSpan([],[J(a.body,e)]),t=w.makeSpan(["inner"],[t],e)):t=w.makeSpan(["inner"],[J(a.body,e)]);var r=w.makeSpan(["fix"],[]),i=w.makeSpan([a.alignment],[t,r],e),n=w.makeSpan(["strut"]);return n.style.height=B(i.height+i.depth),i.depth&&(n.style.verticalAlign=B(-i.depth)),i.children.unshift(n),i=w.makeSpan(["thinbox"],[i],e),w.makeSpan(["mord","vbox"],[i],e)},mathmlBuilder:(a,e)=>{var t=new z.MathNode("mpadded",[re(a.body,e)]);if(a.alignment!=="rlap"){var r=a.alignment==="llap"?"-1":"-0.5";t.setAttribute("lspace",r+"width")}return t.setAttribute("width","0px"),t}});N({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(a,e){var{funcName:t,parser:r}=a,i=r.mode;r.switchMode("math");var n=t==="\\("?"\\)":"$",l=r.parseExpression(!1,n);return r.expect(n),r.switchMode(i),{type:"styling",mode:r.mode,style:"text",body:l}}});N({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(a,e){throw new M("Mismatched "+a.funcName)}});var aa=(a,e)=>{switch(e.style.size){case O.DISPLAY.size:return a.display;case O.TEXT.size:return a.text;case O.SCRIPT.size:return a.script;case O.SCRIPTSCRIPT.size:return a.scriptscript;default:return a.text}};N({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4,primitive:!0},handler:(a,e)=>{var{parser:t}=a;return{type:"mathchoice",mode:t.mode,display:he(e[0]),text:he(e[1]),script:he(e[2]),scriptscript:he(e[3])}},htmlBuilder:(a,e)=>{var t=aa(a,e),r=pe(t,e,!1);return w.makeFragment(r)},mathmlBuilder:(a,e)=>{var t=aa(a,e);return u0(t,e)}});var gi=(a,e,t,r,i,n,l)=>{a=w.makeSpan([],[a]);var u=t&&U.isCharacterBox(t),h,d;if(e){var p=J(e,r.havingStyle(i.sup()),r);d={elem:p,kern:Math.max(r.fontMetrics().bigOpSpacing1,r.fontMetrics().bigOpSpacing3-p.depth)}}if(t){var g=J(t,r.havingStyle(i.sub()),r);h={elem:g,kern:Math.max(r.fontMetrics().bigOpSpacing2,r.fontMetrics().bigOpSpacing4-g.height)}}var y;if(d&&h){var x=r.fontMetrics().bigOpSpacing5+h.elem.height+h.elem.depth+h.kern+a.depth+l;y=w.makeVList({positionType:"bottom",positionData:x,children:[{type:"kern",size:r.fontMetrics().bigOpSpacing5},{type:"elem",elem:h.elem,marginLeft:B(-n)},{type:"kern",size:h.kern},{type:"elem",elem:a},{type:"kern",size:d.kern},{type:"elem",elem:d.elem,marginLeft:B(n)},{type:"kern",size:r.fontMetrics().bigOpSpacing5}]},r)}else if(h){var k=a.height-l;y=w.makeVList({positionType:"top",positionData:k,children:[{type:"kern",size:r.fontMetrics().bigOpSpacing5},{type:"elem",elem:h.elem,marginLeft:B(-n)},{type:"kern",size:h.kern},{type:"elem",elem:a}]},r)}else if(d){var S=a.depth+l;y=w.makeVList({positionType:"bottom",positionData:S,children:[{type:"elem",elem:a},{type:"kern",size:d.kern},{type:"elem",elem:d.elem,marginLeft:B(n)},{type:"kern",size:r.fontMetrics().bigOpSpacing5}]},r)}else return a;var C=[y];if(h&&n!==0&&!u){var E=w.makeSpan(["mspace"],[],r);E.style.marginRight=B(n),C.unshift(E)}return w.makeSpan(["mop","op-limits"],C,r)},bi=["\\smallint"],z0=(a,e)=>{var t,r,i=!1,n;a.type==="supsub"?(t=a.sup,r=a.sub,n=Y(a.base,"op"),i=!0):n=Y(a,"op");var l=e.style,u=!1;l.size===O.DISPLAY.size&&n.symbol&&!U.contains(bi,n.name)&&(u=!0);var h;if(n.symbol){var d=u?"Size2-Regular":"Size1-Regular",p="";if((n.name==="\\oiint"||n.name==="\\oiiint")&&(p=n.name.slice(1),n.name=p==="oiint"?"\\iint":"\\iiint"),h=w.makeSymbol(n.name,d,"math",e,["mop","op-symbol",u?"large-op":"small-op"]),p.length>0){var g=h.italic,y=w.staticSvg(p+"Size"+(u?"2":"1"),e);h=w.makeVList({positionType:"individualShift",children:[{type:"elem",elem:h,shift:0},{type:"elem",elem:y,shift:u?.08:0}]},e),n.name="\\"+p,h.classes.unshift("mop"),h.italic=g}}else if(n.body){var x=pe(n.body,e,!0);x.length===1&&x[0]instanceof qe?(h=x[0],h.classes[0]="mop"):h=w.makeSpan(["mop"],x,e)}else{for(var k=[],S=1;S<n.name.length;S++)k.push(w.mathsym(n.name[S],n.mode,e));h=w.makeSpan(["mop"],k,e)}var C=0,E=0;return(h instanceof qe||n.name==="\\oiint"||n.name==="\\oiiint")&&!n.suppressBaseShift&&(C=(h.height-h.depth)/2-e.fontMetrics().axisHeight,E=h.italic),i?gi(h,t,r,e,l,E,C):(C&&(h.style.position="relative",h.style.top=B(C)),h)},P0=(a,e)=>{var t;if(a.symbol)t=new Re("mo",[Ie(a.name,a.mode)]),U.contains(bi,a.name)&&t.setAttribute("largeop","false");else if(a.body)t=new Re("mo",Ae(a.body,e));else{t=new Re("mi",[new E0(a.name.slice(1))]);var r=new Re("mo",[Ie("⁡","text")]);a.parentIsSupSub?t=new Re("mrow",[t,r]):t=ja([t,r])}return t},Tl={"∏":"\\prod","∐":"\\coprod","∑":"\\sum","⋀":"\\bigwedge","⋁":"\\bigvee","⋂":"\\bigcap","⋃":"\\bigcup","⨀":"\\bigodot","⨁":"\\bigoplus","⨂":"\\bigotimes","⨄":"\\biguplus","⨆":"\\bigsqcup"};N({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","∏","∐","∑","⋀","⋁","⋂","⋃","⨀","⨁","⨂","⨄","⨆"],props:{numArgs:0},handler:(a,e)=>{var{parser:t,funcName:r}=a,i=r;return i.length===1&&(i=Tl[i]),{type:"op",mode:t.mode,limits:!0,parentIsSupSub:!1,symbol:!0,name:i}},htmlBuilder:z0,mathmlBuilder:P0});N({type:"op",names:["\\mathop"],props:{numArgs:1,primitive:!0},handler:(a,e)=>{var{parser:t}=a,r=e[0];return{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!1,body:he(r)}},htmlBuilder:z0,mathmlBuilder:P0});var Bl={"∫":"\\int","∬":"\\iint","∭":"\\iiint","∮":"\\oint","∯":"\\oiint","∰":"\\oiiint"};N({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler(a){var{parser:e,funcName:t}=a;return{type:"op",mode:e.mode,limits:!1,parentIsSupSub:!1,symbol:!1,name:t}},htmlBuilder:z0,mathmlBuilder:P0});N({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler(a){var{parser:e,funcName:t}=a;return{type:"op",mode:e.mode,limits:!0,parentIsSupSub:!1,symbol:!1,name:t}},htmlBuilder:z0,mathmlBuilder:P0});N({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","∫","∬","∭","∮","∯","∰"],props:{numArgs:0},handler(a){var{parser:e,funcName:t}=a,r=t;return r.length===1&&(r=Bl[r]),{type:"op",mode:e.mode,limits:!1,parentIsSupSub:!1,symbol:!0,name:r}},htmlBuilder:z0,mathmlBuilder:P0});var yi=(a,e)=>{var t,r,i=!1,n;a.type==="supsub"?(t=a.sup,r=a.sub,n=Y(a.base,"operatorname"),i=!0):n=Y(a,"operatorname");var l;if(n.body.length>0){for(var u=n.body.map(g=>{var y=g.text;return typeof y=="string"?{type:"textord",mode:g.mode,text:y}:g}),h=pe(u,e.withFont("mathrm"),!0),d=0;d<h.length;d++){var p=h[d];p instanceof qe&&(p.text=p.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}l=w.makeSpan(["mop"],h,e)}else l=w.makeSpan(["mop"],[],e);return i?gi(l,t,r,e,e.style,0,0):l},Cl=(a,e)=>{for(var t=Ae(a.body,e.withFont("mathrm")),r=!0,i=0;i<t.length;i++){var n=t[i];if(!(n instanceof z.SpaceNode))if(n instanceof z.MathNode)switch(n.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":{var l=n.children[0];n.children.length===1&&l instanceof z.TextNode?l.text=l.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):r=!1;break}default:r=!1}else r=!1}if(r){var u=t.map(p=>p.toText()).join("");t=[new z.TextNode(u)]}var h=new z.MathNode("mi",t);h.setAttribute("mathvariant","normal");var d=new z.MathNode("mo",[Ie("⁡","text")]);return a.parentIsSupSub?new z.MathNode("mrow",[h,d]):z.newDocumentFragment([h,d])};N({type:"operatorname",names:["\\operatorname@","\\operatornamewithlimits"],props:{numArgs:1},handler:(a,e)=>{var{parser:t,funcName:r}=a,i=e[0];return{type:"operatorname",mode:t.mode,body:he(i),alwaysHandleSupSub:r==="\\operatornamewithlimits",limits:!1,parentIsSupSub:!1}},htmlBuilder:yi,mathmlBuilder:Cl});m("\\operatorname","\\@ifstar\\operatornamewithlimits\\operatorname@");v0({type:"ordgroup",htmlBuilder(a,e){return a.semisimple?w.makeFragment(pe(a.body,e,!1)):w.makeSpan(["mord"],pe(a.body,e,!0),e)},mathmlBuilder(a,e){return u0(a.body,e,!0)}});N({type:"overline",names:["\\overline"],props:{numArgs:1},handler(a,e){var{parser:t}=a,r=e[0];return{type:"overline",mode:t.mode,body:r}},htmlBuilder(a,e){var t=J(a.body,e.havingCrampedStyle()),r=w.makeLineSpan("overline-line",e),i=e.fontMetrics().defaultRuleThickness,n=w.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t},{type:"kern",size:3*i},{type:"elem",elem:r},{type:"kern",size:i}]},e);return w.makeSpan(["mord","overline"],[n],e)},mathmlBuilder(a,e){var t=new z.MathNode("mo",[new z.TextNode("‾")]);t.setAttribute("stretchy","true");var r=new z.MathNode("mover",[re(a.body,e),t]);return r.setAttribute("accent","true"),r}});N({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:(a,e)=>{var{parser:t}=a,r=e[0];return{type:"phantom",mode:t.mode,body:he(r)}},htmlBuilder:(a,e)=>{var t=pe(a.body,e.withPhantom(),!1);return w.makeFragment(t)},mathmlBuilder:(a,e)=>{var t=Ae(a.body,e);return new z.MathNode("mphantom",t)}});N({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:(a,e)=>{var{parser:t}=a,r=e[0];return{type:"hphantom",mode:t.mode,body:r}},htmlBuilder:(a,e)=>{var t=w.makeSpan([],[J(a.body,e.withPhantom())]);if(t.height=0,t.depth=0,t.children)for(var r=0;r<t.children.length;r++)t.children[r].height=0,t.children[r].depth=0;return t=w.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t}]},e),w.makeSpan(["mord"],[t],e)},mathmlBuilder:(a,e)=>{var t=Ae(he(a.body),e),r=new z.MathNode("mphantom",t),i=new z.MathNode("mpadded",[r]);return i.setAttribute("height","0px"),i.setAttribute("depth","0px"),i}});N({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:(a,e)=>{var{parser:t}=a,r=e[0];return{type:"vphantom",mode:t.mode,body:r}},htmlBuilder:(a,e)=>{var t=w.makeSpan(["inner"],[J(a.body,e.withPhantom())]),r=w.makeSpan(["fix"],[]);return w.makeSpan(["mord","rlap"],[t,r],e)},mathmlBuilder:(a,e)=>{var t=Ae(he(a.body),e),r=new z.MathNode("mphantom",t),i=new z.MathNode("mpadded",[r]);return i.setAttribute("width","0px"),i}});N({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","hbox"],allowedInText:!0},handler(a,e){var{parser:t}=a,r=Y(e[0],"size").value,i=e[1];return{type:"raisebox",mode:t.mode,dy:r,body:i}},htmlBuilder(a,e){var t=J(a.body,e),r=ue(a.dy,e);return w.makeVList({positionType:"shift",positionData:-r,children:[{type:"elem",elem:t}]},e)},mathmlBuilder(a,e){var t=new z.MathNode("mpadded",[re(a.body,e)]),r=a.dy.number+a.dy.unit;return t.setAttribute("voffset",r),t}});N({type:"internal",names:["\\relax"],props:{numArgs:0,allowedInText:!0},handler(a){var{parser:e}=a;return{type:"internal",mode:e.mode}}});N({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,argTypes:["size","size","size"]},handler(a,e,t){var{parser:r}=a,i=t[0],n=Y(e[0],"size"),l=Y(e[1],"size");return{type:"rule",mode:r.mode,shift:i&&Y(i,"size").value,width:n.value,height:l.value}},htmlBuilder(a,e){var t=w.makeSpan(["mord","rule"],[],e),r=ue(a.width,e),i=ue(a.height,e),n=a.shift?ue(a.shift,e):0;return t.style.borderRightWidth=B(r),t.style.borderTopWidth=B(i),t.style.bottom=B(n),t.width=r,t.height=i+n,t.depth=-n,t.maxFontSize=i*1.125*e.sizeMultiplier,t},mathmlBuilder(a,e){var t=ue(a.width,e),r=ue(a.height,e),i=a.shift?ue(a.shift,e):0,n=e.color&&e.getColor()||"black",l=new z.MathNode("mspace");l.setAttribute("mathbackground",n),l.setAttribute("width",B(t)),l.setAttribute("height",B(r));var u=new z.MathNode("mpadded",[l]);return i>=0?u.setAttribute("height",B(i)):(u.setAttribute("height",B(i)),u.setAttribute("depth",B(-i))),u.setAttribute("voffset",B(i)),u}});function xi(a,e,t){for(var r=pe(a,e,!1),i=e.sizeMultiplier/t.sizeMultiplier,n=0;n<r.length;n++){var l=r[n].classes.indexOf("sizing");l<0?Array.prototype.push.apply(r[n].classes,e.sizingClasses(t)):r[n].classes[l+1]==="reset-size"+e.size&&(r[n].classes[l+1]="reset-size"+t.size),r[n].height*=i,r[n].depth*=i}return w.makeFragment(r)}var ia=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"],Rl=(a,e)=>{var t=e.havingSize(a.size);return xi(a.body,t,e)};N({type:"sizing",names:ia,props:{numArgs:0,allowedInText:!0},handler:(a,e)=>{var{breakOnTokenText:t,funcName:r,parser:i}=a,n=i.parseExpression(!1,t);return{type:"sizing",mode:i.mode,size:ia.indexOf(r)+1,body:n}},htmlBuilder:Rl,mathmlBuilder:(a,e)=>{var t=e.havingSize(a.size),r=Ae(a.body,t),i=new z.MathNode("mstyle",r);return i.setAttribute("mathsize",B(t.sizeMultiplier)),i}});N({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:(a,e,t)=>{var{parser:r}=a,i=!1,n=!1,l=t[0]&&Y(t[0],"ordgroup");if(l)for(var u="",h=0;h<l.body.length;++h){var d=l.body[h];if(u=d.text,u==="t")i=!0;else if(u==="b")n=!0;else{i=!1,n=!1;break}}else i=!0,n=!0;var p=e[0];return{type:"smash",mode:r.mode,body:p,smashHeight:i,smashDepth:n}},htmlBuilder:(a,e)=>{var t=w.makeSpan([],[J(a.body,e)]);if(!a.smashHeight&&!a.smashDepth)return t;if(a.smashHeight&&(t.height=0,t.children))for(var r=0;r<t.children.length;r++)t.children[r].height=0;if(a.smashDepth&&(t.depth=0,t.children))for(var i=0;i<t.children.length;i++)t.children[i].depth=0;var n=w.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t}]},e);return w.makeSpan(["mord"],[n],e)},mathmlBuilder:(a,e)=>{var t=new z.MathNode("mpadded",[re(a.body,e)]);return a.smashHeight&&t.setAttribute("height","0px"),a.smashDepth&&t.setAttribute("depth","0px"),t}});N({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler(a,e,t){var{parser:r}=a,i=t[0],n=e[0];return{type:"sqrt",mode:r.mode,body:n,index:i}},htmlBuilder(a,e){var t=J(a.body,e.havingCrampedStyle());t.height===0&&(t.height=e.fontMetrics().xHeight),t=w.wrapFragment(t,e);var r=e.fontMetrics(),i=r.defaultRuleThickness,n=i;e.style.id<O.TEXT.id&&(n=e.fontMetrics().xHeight);var l=i+n/4,u=t.height+t.depth+l+i,{span:h,ruleWidth:d,advanceWidth:p}=_e.sqrtImage(u,e),g=h.height-d;g>t.height+t.depth+l&&(l=(l+g-t.height-t.depth)/2);var y=h.height-t.height-l-d;t.style.paddingLeft=B(p);var x=w.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t,wrapperClasses:["svg-align"]},{type:"kern",size:-(t.height+y)},{type:"elem",elem:h},{type:"kern",size:d}]},e);if(a.index){var k=e.havingStyle(O.SCRIPTSCRIPT),S=J(a.index,k,e),C=.6*(x.height-x.depth),E=w.makeVList({positionType:"shift",positionData:-C,children:[{type:"elem",elem:S}]},e),L=w.makeSpan(["root"],[E]);return w.makeSpan(["mord","sqrt"],[L,x],e)}else return w.makeSpan(["mord","sqrt"],[x],e)},mathmlBuilder(a,e){var{body:t,index:r}=a;return r?new z.MathNode("mroot",[re(t,e),re(r,e)]):new z.MathNode("msqrt",[re(t,e)])}});var na={display:O.DISPLAY,text:O.TEXT,script:O.SCRIPT,scriptscript:O.SCRIPTSCRIPT};N({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(a,e){var{breakOnTokenText:t,funcName:r,parser:i}=a,n=i.parseExpression(!0,t),l=r.slice(1,r.length-5);return{type:"styling",mode:i.mode,style:l,body:n}},htmlBuilder(a,e){var t=na[a.style],r=e.havingStyle(t).withFont("");return xi(a.body,r,e)},mathmlBuilder(a,e){var t=na[a.style],r=e.havingStyle(t),i=Ae(a.body,r),n=new z.MathNode("mstyle",i),l={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]},u=l[a.style];return n.setAttribute("scriptlevel",u[0]),n.setAttribute("displaystyle",u[1]),n}});var Dl=function(e,t){var r=e.base;if(r)if(r.type==="op"){var i=r.limits&&(t.style.size===O.DISPLAY.size||r.alwaysHandleSupSub);return i?z0:null}else if(r.type==="operatorname"){var n=r.alwaysHandleSupSub&&(t.style.size===O.DISPLAY.size||r.limits);return n?yi:null}else{if(r.type==="accent")return U.isCharacterBox(r.base)?vr:null;if(r.type==="horizBrace"){var l=!e.sub;return l===r.isOver?vi:null}else return null}else return null};v0({type:"supsub",htmlBuilder(a,e){var t=Dl(a,e);if(t)return t(a,e);var{base:r,sup:i,sub:n}=a,l=J(r,e),u,h,d=e.fontMetrics(),p=0,g=0,y=r&&U.isCharacterBox(r);if(i){var x=e.havingStyle(e.style.sup());u=J(i,x,e),y||(p=l.height-x.fontMetrics().supDrop*x.sizeMultiplier/e.sizeMultiplier)}if(n){var k=e.havingStyle(e.style.sub());h=J(n,k,e),y||(g=l.depth+k.fontMetrics().subDrop*k.sizeMultiplier/e.sizeMultiplier)}var S;e.style===O.DISPLAY?S=d.sup1:e.style.cramped?S=d.sup3:S=d.sup2;var C=e.sizeMultiplier,E=B(.5/d.ptPerEm/C),L=null;if(h){var V=a.base&&a.base.type==="op"&&a.base.name&&(a.base.name==="\\oiint"||a.base.name==="\\oiiint");(l instanceof qe||V)&&(L=B(-l.italic))}var j;if(u&&h){p=Math.max(p,S,u.depth+.25*d.xHeight),g=Math.max(g,d.sub2);var G=d.defaultRuleThickness,H=4*G;if(p-u.depth-(h.height-g)<H){g=H-(p-u.depth)+h.height;var T=.8*d.xHeight-(p-u.depth);T>0&&(p+=T,g-=T)}var I=[{type:"elem",elem:h,shift:g,marginRight:E,marginLeft:L},{type:"elem",elem:u,shift:-p,marginRight:E}];j=w.makeVList({positionType:"individualShift",children:I},e)}else if(h){g=Math.max(g,d.sub1,h.height-.8*d.xHeight);var R=[{type:"elem",elem:h,marginLeft:L,marginRight:E}];j=w.makeVList({positionType:"shift",positionData:g,children:R},e)}else if(u)p=Math.max(p,S,u.depth+.25*d.xHeight),j=w.makeVList({positionType:"shift",positionData:-p,children:[{type:"elem",elem:u,marginRight:E}]},e);else throw new Error("supsub must have either sup or sub.");var _=_t(l,"right")||"mord";return w.makeSpan([_],[l,w.makeSpan(["msupsub"],[j])],e)},mathmlBuilder(a,e){var t=!1,r,i;a.base&&a.base.type==="horizBrace"&&(i=!!a.sup,i===a.base.isOver&&(t=!0,r=a.base.isOver)),a.base&&(a.base.type==="op"||a.base.type==="operatorname")&&(a.base.parentIsSupSub=!0);var n=[re(a.base,e)];a.sub&&n.push(re(a.sub,e)),a.sup&&n.push(re(a.sup,e));var l;if(t)l=r?"mover":"munder";else if(a.sub)if(a.sup){var d=a.base;d&&d.type==="op"&&d.limits&&e.style===O.DISPLAY||d&&d.type==="operatorname"&&d.alwaysHandleSupSub&&(e.style===O.DISPLAY||d.limits)?l="munderover":l="msubsup"}else{var h=a.base;h&&h.type==="op"&&h.limits&&(e.style===O.DISPLAY||h.alwaysHandleSupSub)||h&&h.type==="operatorname"&&h.alwaysHandleSupSub&&(h.limits||e.style===O.DISPLAY)?l="munder":l="msub"}else{var u=a.base;u&&u.type==="op"&&u.limits&&(e.style===O.DISPLAY||u.alwaysHandleSupSub)||u&&u.type==="operatorname"&&u.alwaysHandleSupSub&&(u.limits||e.style===O.DISPLAY)?l="mover":l="msup"}return new z.MathNode(l,n)}});v0({type:"atom",htmlBuilder(a,e){return w.mathsym(a.text,a.mode,e,["m"+a.family])},mathmlBuilder(a,e){var t=new z.MathNode("mo",[Ie(a.text,a.mode)]);if(a.family==="bin"){var r=pr(a,e);r==="bold-italic"&&t.setAttribute("mathvariant",r)}else a.family==="punct"?t.setAttribute("separator","true"):(a.family==="open"||a.family==="close")&&t.setAttribute("stretchy","false");return t}});var wi={mi:"italic",mn:"normal",mtext:"normal"};v0({type:"mathord",htmlBuilder(a,e){return w.makeOrd(a,e,"mathord")},mathmlBuilder(a,e){var t=new z.MathNode("mi",[Ie(a.text,a.mode,e)]),r=pr(a,e)||"italic";return r!==wi[t.type]&&t.setAttribute("mathvariant",r),t}});v0({type:"textord",htmlBuilder(a,e){return w.makeOrd(a,e,"textord")},mathmlBuilder(a,e){var t=Ie(a.text,a.mode,e),r=pr(a,e)||"normal",i;return a.mode==="text"?i=new z.MathNode("mtext",[t]):/[0-9]/.test(a.text)?i=new z.MathNode("mn",[t]):a.text==="\\prime"?i=new z.MathNode("mo",[t]):i=new z.MathNode("mi",[t]),r!==wi[i.type]&&i.setAttribute("mathvariant",r),i}});var Ot={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},Ht={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};v0({type:"spacing",htmlBuilder(a,e){if(Ht.hasOwnProperty(a.text)){var t=Ht[a.text].className||"";if(a.mode==="text"){var r=w.makeOrd(a,e,"textord");return r.classes.push(t),r}else return w.makeSpan(["mspace",t],[w.mathsym(a.text,a.mode,e)],e)}else{if(Ot.hasOwnProperty(a.text))return w.makeSpan(["mspace",Ot[a.text]],[],e);throw new M('Unknown type of space "'+a.text+'"')}},mathmlBuilder(a,e){var t;if(Ht.hasOwnProperty(a.text))t=new z.MathNode("mtext",[new z.TextNode(" ")]);else{if(Ot.hasOwnProperty(a.text))return new z.MathNode("mspace");throw new M('Unknown type of space "'+a.text+'"')}return t}});var sa=()=>{var a=new z.MathNode("mtd",[]);return a.setAttribute("width","50%"),a};v0({type:"tag",mathmlBuilder(a,e){var t=new z.MathNode("mtable",[new z.MathNode("mtr",[sa(),new z.MathNode("mtd",[u0(a.body,e)]),sa(),new z.MathNode("mtd",[u0(a.tag,e)])])]);return t.setAttribute("width","100%"),t}});var la={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},oa={"\\textbf":"textbf","\\textmd":"textmd"},El={"\\textit":"textit","\\textup":"textup"},ua=(a,e)=>{var t=a.font;return t?la[t]?e.withTextFontFamily(la[t]):oa[t]?e.withTextFontWeight(oa[t]):e.withTextFontShape(El[t]):e};N({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textmd","\\textit","\\textup"],props:{numArgs:1,argTypes:["text"],allowedInArgument:!0,allowedInText:!0},handler(a,e){var{parser:t,funcName:r}=a,i=e[0];return{type:"text",mode:t.mode,body:he(i),font:r}},htmlBuilder(a,e){var t=ua(a,e),r=pe(a.body,t,!0);return w.makeSpan(["mord","text"],r,t)},mathmlBuilder(a,e){var t=ua(a,e);return u0(a.body,t)}});N({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler(a,e){var{parser:t}=a;return{type:"underline",mode:t.mode,body:e[0]}},htmlBuilder(a,e){var t=J(a.body,e),r=w.makeLineSpan("underline-line",e),i=e.fontMetrics().defaultRuleThickness,n=w.makeVList({positionType:"top",positionData:t.height,children:[{type:"kern",size:i},{type:"elem",elem:r},{type:"kern",size:3*i},{type:"elem",elem:t}]},e);return w.makeSpan(["mord","underline"],[n],e)},mathmlBuilder(a,e){var t=new z.MathNode("mo",[new z.TextNode("‾")]);t.setAttribute("stretchy","true");var r=new z.MathNode("munder",[re(a.body,e),t]);return r.setAttribute("accentunder","true"),r}});N({type:"vcenter",names:["\\vcenter"],props:{numArgs:1,argTypes:["original"],allowedInText:!1},handler(a,e){var{parser:t}=a;return{type:"vcenter",mode:t.mode,body:e[0]}},htmlBuilder(a,e){var t=J(a.body,e),r=e.fontMetrics().axisHeight,i=.5*(t.height-r-(t.depth+r));return w.makeVList({positionType:"shift",positionData:i,children:[{type:"elem",elem:t}]},e)},mathmlBuilder(a,e){return new z.MathNode("mpadded",[re(a.body,e)],["vcenter"])}});N({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler(a,e,t){throw new M("\\verb ended by end of line instead of matching delimiter")},htmlBuilder(a,e){for(var t=ha(a),r=[],i=e.havingStyle(e.style.text()),n=0;n<t.length;n++){var l=t[n];l==="~"&&(l="\\textasciitilde"),r.push(w.makeSymbol(l,"Typewriter-Regular",a.mode,i,["mord","texttt"]))}return w.makeSpan(["mord","text"].concat(i.sizingClasses(e)),w.tryCombineChars(r),i)},mathmlBuilder(a,e){var t=new z.TextNode(ha(a)),r=new z.MathNode("mtext",[t]);return r.setAttribute("mathvariant","monospace"),r}});var ha=a=>a.body.replace(/ /g,a.star?"␣":" "),s0=$a,ki=`[ \r
	]`,Nl="\\\\[a-zA-Z@]+",ql="\\\\[^\uD800-\uDFFF]",Il="("+Nl+")"+ki+"*",Ll=`\\\\(
|[ \r	]+
?)[ \r	]*`,Zt="[̀-ͯ]",Ol=new RegExp(Zt+"+$"),Hl="("+ki+"+)|"+(Ll+"|")+"([!-\\[\\]-‧‪-퟿豈-￿]"+(Zt+"*")+"|[\uD800-\uDBFF][\uDC00-\uDFFF]"+(Zt+"*")+"|\\\\verb\\*([^]).*?\\4|\\\\verb([^*a-zA-Z]).*?\\5"+("|"+Il)+("|"+ql+")");class ca{constructor(e,t){this.input=void 0,this.settings=void 0,this.tokenRegex=void 0,this.catcodes=void 0,this.input=e,this.settings=t,this.tokenRegex=new RegExp(Hl,"g"),this.catcodes={"%":14,"~":13}}setCatcode(e,t){this.catcodes[e]=t}lex(){var e=this.input,t=this.tokenRegex.lastIndex;if(t===e.length)return new Ve("EOF",new ze(this,t,t));var r=this.tokenRegex.exec(e);if(r===null||r.index!==t)throw new M("Unexpected character: '"+e[t]+"'",new Ve(e[t],new ze(this,t,t+1)));var i=r[6]||r[3]||(r[2]?"\\ ":" ");if(this.catcodes[i]===14){var n=e.indexOf(`
`,this.tokenRegex.lastIndex);return n===-1?(this.tokenRegex.lastIndex=e.length,this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)")):this.tokenRegex.lastIndex=n+1,this.lex()}return new Ve(i,new ze(this,t,this.tokenRegex.lastIndex))}}class Fl{constructor(e,t){e===void 0&&(e={}),t===void 0&&(t={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=t,this.builtins=e,this.undefStack=[]}beginGroup(){this.undefStack.push({})}endGroup(){if(this.undefStack.length===0)throw new M("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");var e=this.undefStack.pop();for(var t in e)e.hasOwnProperty(t)&&(e[t]==null?delete this.current[t]:this.current[t]=e[t])}endGroups(){for(;this.undefStack.length>0;)this.endGroup()}has(e){return this.current.hasOwnProperty(e)||this.builtins.hasOwnProperty(e)}get(e){return this.current.hasOwnProperty(e)?this.current[e]:this.builtins[e]}set(e,t,r){if(r===void 0&&(r=!1),r){for(var i=0;i<this.undefStack.length;i++)delete this.undefStack[i][e];this.undefStack.length>0&&(this.undefStack[this.undefStack.length-1][e]=t)}else{var n=this.undefStack[this.undefStack.length-1];n&&!n.hasOwnProperty(e)&&(n[e]=this.current[e])}t==null?delete this.current[e]:this.current[e]=t}}var Pl=ci;m("\\noexpand",function(a){var e=a.popToken();return a.isExpandable(e.text)&&(e.noexpand=!0,e.treatAsRelax=!0),{tokens:[e],numArgs:0}});m("\\expandafter",function(a){var e=a.popToken();return a.expandOnce(!0),{tokens:[e],numArgs:0}});m("\\@firstoftwo",function(a){var e=a.consumeArgs(2);return{tokens:e[0],numArgs:0}});m("\\@secondoftwo",function(a){var e=a.consumeArgs(2);return{tokens:e[1],numArgs:0}});m("\\@ifnextchar",function(a){var e=a.consumeArgs(3);a.consumeSpaces();var t=a.future();return e[0].length===1&&e[0][0].text===t.text?{tokens:e[1],numArgs:0}:{tokens:e[2],numArgs:0}});m("\\@ifstar","\\@ifnextchar *{\\@firstoftwo{#1}}");m("\\TextOrMath",function(a){var e=a.consumeArgs(2);return a.mode==="text"?{tokens:e[0],numArgs:0}:{tokens:e[1],numArgs:0}});var ma={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};m("\\char",function(a){var e=a.popToken(),t,r="";if(e.text==="'")t=8,e=a.popToken();else if(e.text==='"')t=16,e=a.popToken();else if(e.text==="`")if(e=a.popToken(),e.text[0]==="\\")r=e.text.charCodeAt(1);else{if(e.text==="EOF")throw new M("\\char` missing argument");r=e.text.charCodeAt(0)}else t=10;if(t){if(r=ma[e.text],r==null||r>=t)throw new M("Invalid base-"+t+" digit "+e.text);for(var i;(i=ma[a.future().text])!=null&&i<t;)r*=t,r+=i,a.popToken()}return"\\@char{"+r+"}"});var Ar=(a,e,t)=>{var r=a.consumeArg().tokens;if(r.length!==1)throw new M("\\newcommand's first argument must be a macro name");var i=r[0].text,n=a.isDefined(i);if(n&&!e)throw new M("\\newcommand{"+i+"} attempting to redefine "+(i+"; use \\renewcommand"));if(!n&&!t)throw new M("\\renewcommand{"+i+"} when command "+i+" does not yet exist; use \\newcommand");var l=0;if(r=a.consumeArg().tokens,r.length===1&&r[0].text==="["){for(var u="",h=a.expandNextToken();h.text!=="]"&&h.text!=="EOF";)u+=h.text,h=a.expandNextToken();if(!u.match(/^\s*[0-9]+\s*$/))throw new M("Invalid number of arguments: "+u);l=parseInt(u),r=a.consumeArg().tokens}return a.macros.set(i,{tokens:r,numArgs:l}),""};m("\\newcommand",a=>Ar(a,!1,!0));m("\\renewcommand",a=>Ar(a,!0,!1));m("\\providecommand",a=>Ar(a,!0,!0));m("\\message",a=>{var e=a.consumeArgs(1)[0];return e.reverse().map(t=>t.text).join(""),""});m("\\errmessage",a=>{var e=a.consumeArgs(1)[0];return console.error(e.reverse().map(t=>t.text).join("")),""});m("\\show",a=>{var e=a.popToken(),t=e.text;return a.macros.get(t),s0[t],ne.math[t],ne.text[t],""});m("\\bgroup","{");m("\\egroup","}");m("~","\\nobreakspace");m("\\lq","`");m("\\rq","'");m("\\aa","\\r a");m("\\AA","\\r A");m("\\textcopyright","\\html@mathml{\\textcircled{c}}{\\char`©}");m("\\copyright","\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}");m("\\textregistered","\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`®}");m("ℬ","\\mathscr{B}");m("ℰ","\\mathscr{E}");m("ℱ","\\mathscr{F}");m("ℋ","\\mathscr{H}");m("ℐ","\\mathscr{I}");m("ℒ","\\mathscr{L}");m("ℳ","\\mathscr{M}");m("ℛ","\\mathscr{R}");m("ℭ","\\mathfrak{C}");m("ℌ","\\mathfrak{H}");m("ℨ","\\mathfrak{Z}");m("\\Bbbk","\\Bbb{k}");m("·","\\cdotp");m("\\llap","\\mathllap{\\textrm{#1}}");m("\\rlap","\\mathrlap{\\textrm{#1}}");m("\\clap","\\mathclap{\\textrm{#1}}");m("\\mathstrut","\\vphantom{(}");m("\\underbar","\\underline{\\text{#1}}");m("\\not",'\\html@mathml{\\mathrel{\\mathrlap\\@not}}{\\char"338}');m("\\neq","\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`≠}}");m("\\ne","\\neq");m("≠","\\neq");m("\\notin","\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`∉}}");m("∉","\\notin");m("≘","\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`≘}}");m("≙","\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`≘}}");m("≚","\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`≚}}");m("≛","\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`≛}}");m("≝","\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`≝}}");m("≞","\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`≞}}");m("≟","\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`≟}}");m("⟂","\\perp");m("‼","\\mathclose{!\\mkern-0.8mu!}");m("∌","\\notni");m("⌜","\\ulcorner");m("⌝","\\urcorner");m("⌞","\\llcorner");m("⌟","\\lrcorner");m("©","\\copyright");m("®","\\textregistered");m("️","\\textregistered");m("\\ulcorner",'\\html@mathml{\\@ulcorner}{\\mathop{\\char"231c}}');m("\\urcorner",'\\html@mathml{\\@urcorner}{\\mathop{\\char"231d}}');m("\\llcorner",'\\html@mathml{\\@llcorner}{\\mathop{\\char"231e}}');m("\\lrcorner",'\\html@mathml{\\@lrcorner}{\\mathop{\\char"231f}}');m("\\vdots","\\mathord{\\varvdots\\rule{0pt}{15pt}}");m("⋮","\\vdots");m("\\varGamma","\\mathit{\\Gamma}");m("\\varDelta","\\mathit{\\Delta}");m("\\varTheta","\\mathit{\\Theta}");m("\\varLambda","\\mathit{\\Lambda}");m("\\varXi","\\mathit{\\Xi}");m("\\varPi","\\mathit{\\Pi}");m("\\varSigma","\\mathit{\\Sigma}");m("\\varUpsilon","\\mathit{\\Upsilon}");m("\\varPhi","\\mathit{\\Phi}");m("\\varPsi","\\mathit{\\Psi}");m("\\varOmega","\\mathit{\\Omega}");m("\\substack","\\begin{subarray}{c}#1\\end{subarray}");m("\\colon","\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu\\relax");m("\\boxed","\\fbox{$\\displaystyle{#1}$}");m("\\iff","\\DOTSB\\;\\Longleftrightarrow\\;");m("\\implies","\\DOTSB\\;\\Longrightarrow\\;");m("\\impliedby","\\DOTSB\\;\\Longleftarrow\\;");var da={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};m("\\dots",function(a){var e="\\dotso",t=a.expandAfterFuture().text;return t in da?e=da[t]:(t.slice(0,4)==="\\not"||t in ne.math&&U.contains(["bin","rel"],ne.math[t].group))&&(e="\\dotsb"),e});var zr={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};m("\\dotso",function(a){var e=a.future().text;return e in zr?"\\ldots\\,":"\\ldots"});m("\\dotsc",function(a){var e=a.future().text;return e in zr&&e!==","?"\\ldots\\,":"\\ldots"});m("\\cdots",function(a){var e=a.future().text;return e in zr?"\\@cdots\\,":"\\@cdots"});m("\\dotsb","\\cdots");m("\\dotsm","\\cdots");m("\\dotsi","\\!\\cdots");m("\\dotsx","\\ldots\\,");m("\\DOTSI","\\relax");m("\\DOTSB","\\relax");m("\\DOTSX","\\relax");m("\\tmspace","\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax");m("\\,","\\tmspace+{3mu}{.1667em}");m("\\thinspace","\\,");m("\\>","\\mskip{4mu}");m("\\:","\\tmspace+{4mu}{.2222em}");m("\\medspace","\\:");m("\\;","\\tmspace+{5mu}{.2777em}");m("\\thickspace","\\;");m("\\!","\\tmspace-{3mu}{.1667em}");m("\\negthinspace","\\!");m("\\negmedspace","\\tmspace-{4mu}{.2222em}");m("\\negthickspace","\\tmspace-{5mu}{.277em}");m("\\enspace","\\kern.5em ");m("\\enskip","\\hskip.5em\\relax");m("\\quad","\\hskip1em\\relax");m("\\qquad","\\hskip2em\\relax");m("\\tag","\\@ifstar\\tag@literal\\tag@paren");m("\\tag@paren","\\tag@literal{({#1})}");m("\\tag@literal",a=>{if(a.macros.get("\\df@tag"))throw new M("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"});m("\\bmod","\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}");m("\\pod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)");m("\\pmod","\\pod{{\\rm mod}\\mkern6mu#1}");m("\\mod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1");m("\\newline","\\\\\\relax");m("\\TeX","\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}");var Si=B(Pe["Main-Regular"][84][1]-.7*Pe["Main-Regular"][65][1]);m("\\LaTeX","\\textrm{\\html@mathml{"+("L\\kern-.36em\\raisebox{"+Si+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{LaTeX}}");m("\\KaTeX","\\textrm{\\html@mathml{"+("K\\kern-.17em\\raisebox{"+Si+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{KaTeX}}");m("\\hspace","\\@ifstar\\@hspacer\\@hspace");m("\\@hspace","\\hskip #1\\relax");m("\\@hspacer","\\rule{0pt}{0pt}\\hskip #1\\relax");m("\\ordinarycolon",":");m("\\vcentcolon","\\mathrel{\\mathop\\ordinarycolon}");m("\\dblcolon",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}}{\\mathop{\\char"2237}}');m("\\coloneqq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2254}}');m("\\Coloneqq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2237\\char"3d}}');m("\\coloneq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"3a\\char"2212}}');m("\\Coloneq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"2237\\char"2212}}');m("\\eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2255}}');m("\\Eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"3d\\char"2237}}');m("\\eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2239}}');m("\\Eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"2212\\char"2237}}');m("\\colonapprox",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"3a\\char"2248}}');m("\\Colonapprox",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"2237\\char"2248}}');m("\\colonsim",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"3a\\char"223c}}');m("\\Colonsim",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"2237\\char"223c}}');m("∷","\\dblcolon");m("∹","\\eqcolon");m("≔","\\coloneqq");m("≕","\\eqqcolon");m("⩴","\\Coloneqq");m("\\ratio","\\vcentcolon");m("\\coloncolon","\\dblcolon");m("\\colonequals","\\coloneqq");m("\\coloncolonequals","\\Coloneqq");m("\\equalscolon","\\eqqcolon");m("\\equalscoloncolon","\\Eqqcolon");m("\\colonminus","\\coloneq");m("\\coloncolonminus","\\Coloneq");m("\\minuscolon","\\eqcolon");m("\\minuscoloncolon","\\Eqcolon");m("\\coloncolonapprox","\\Colonapprox");m("\\coloncolonsim","\\Colonsim");m("\\simcolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}");m("\\simcoloncolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}");m("\\approxcolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}");m("\\approxcoloncolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}");m("\\notni","\\html@mathml{\\not\\ni}{\\mathrel{\\char`∌}}");m("\\limsup","\\DOTSB\\operatorname*{lim\\,sup}");m("\\liminf","\\DOTSB\\operatorname*{lim\\,inf}");m("\\injlim","\\DOTSB\\operatorname*{inj\\,lim}");m("\\projlim","\\DOTSB\\operatorname*{proj\\,lim}");m("\\varlimsup","\\DOTSB\\operatorname*{\\overline{lim}}");m("\\varliminf","\\DOTSB\\operatorname*{\\underline{lim}}");m("\\varinjlim","\\DOTSB\\operatorname*{\\underrightarrow{lim}}");m("\\varprojlim","\\DOTSB\\operatorname*{\\underleftarrow{lim}}");m("\\gvertneqq","\\html@mathml{\\@gvertneqq}{≩}");m("\\lvertneqq","\\html@mathml{\\@lvertneqq}{≨}");m("\\ngeqq","\\html@mathml{\\@ngeqq}{≱}");m("\\ngeqslant","\\html@mathml{\\@ngeqslant}{≱}");m("\\nleqq","\\html@mathml{\\@nleqq}{≰}");m("\\nleqslant","\\html@mathml{\\@nleqslant}{≰}");m("\\nshortmid","\\html@mathml{\\@nshortmid}{∤}");m("\\nshortparallel","\\html@mathml{\\@nshortparallel}{∦}");m("\\nsubseteqq","\\html@mathml{\\@nsubseteqq}{⊈}");m("\\nsupseteqq","\\html@mathml{\\@nsupseteqq}{⊉}");m("\\varsubsetneq","\\html@mathml{\\@varsubsetneq}{⊊}");m("\\varsubsetneqq","\\html@mathml{\\@varsubsetneqq}{⫋}");m("\\varsupsetneq","\\html@mathml{\\@varsupsetneq}{⊋}");m("\\varsupsetneqq","\\html@mathml{\\@varsupsetneqq}{⫌}");m("\\imath","\\html@mathml{\\@imath}{ı}");m("\\jmath","\\html@mathml{\\@jmath}{ȷ}");m("\\llbracket","\\html@mathml{\\mathopen{[\\mkern-3.2mu[}}{\\mathopen{\\char`⟦}}");m("\\rrbracket","\\html@mathml{\\mathclose{]\\mkern-3.2mu]}}{\\mathclose{\\char`⟧}}");m("⟦","\\llbracket");m("⟧","\\rrbracket");m("\\lBrace","\\html@mathml{\\mathopen{\\{\\mkern-3.2mu[}}{\\mathopen{\\char`⦃}}");m("\\rBrace","\\html@mathml{\\mathclose{]\\mkern-3.2mu\\}}}{\\mathclose{\\char`⦄}}");m("⦃","\\lBrace");m("⦄","\\rBrace");m("\\minuso","\\mathbin{\\html@mathml{{\\mathrlap{\\mathchoice{\\kern{0.145em}}{\\kern{0.145em}}{\\kern{0.1015em}}{\\kern{0.0725em}}\\circ}{-}}}{\\char`⦵}}");m("⦵","\\minuso");m("\\darr","\\downarrow");m("\\dArr","\\Downarrow");m("\\Darr","\\Downarrow");m("\\lang","\\langle");m("\\rang","\\rangle");m("\\uarr","\\uparrow");m("\\uArr","\\Uparrow");m("\\Uarr","\\Uparrow");m("\\N","\\mathbb{N}");m("\\R","\\mathbb{R}");m("\\Z","\\mathbb{Z}");m("\\alef","\\aleph");m("\\alefsym","\\aleph");m("\\Alpha","\\mathrm{A}");m("\\Beta","\\mathrm{B}");m("\\bull","\\bullet");m("\\Chi","\\mathrm{X}");m("\\clubs","\\clubsuit");m("\\cnums","\\mathbb{C}");m("\\Complex","\\mathbb{C}");m("\\Dagger","\\ddagger");m("\\diamonds","\\diamondsuit");m("\\empty","\\emptyset");m("\\Epsilon","\\mathrm{E}");m("\\Eta","\\mathrm{H}");m("\\exist","\\exists");m("\\harr","\\leftrightarrow");m("\\hArr","\\Leftrightarrow");m("\\Harr","\\Leftrightarrow");m("\\hearts","\\heartsuit");m("\\image","\\Im");m("\\infin","\\infty");m("\\Iota","\\mathrm{I}");m("\\isin","\\in");m("\\Kappa","\\mathrm{K}");m("\\larr","\\leftarrow");m("\\lArr","\\Leftarrow");m("\\Larr","\\Leftarrow");m("\\lrarr","\\leftrightarrow");m("\\lrArr","\\Leftrightarrow");m("\\Lrarr","\\Leftrightarrow");m("\\Mu","\\mathrm{M}");m("\\natnums","\\mathbb{N}");m("\\Nu","\\mathrm{N}");m("\\Omicron","\\mathrm{O}");m("\\plusmn","\\pm");m("\\rarr","\\rightarrow");m("\\rArr","\\Rightarrow");m("\\Rarr","\\Rightarrow");m("\\real","\\Re");m("\\reals","\\mathbb{R}");m("\\Reals","\\mathbb{R}");m("\\Rho","\\mathrm{P}");m("\\sdot","\\cdot");m("\\sect","\\S");m("\\spades","\\spadesuit");m("\\sub","\\subset");m("\\sube","\\subseteq");m("\\supe","\\supseteq");m("\\Tau","\\mathrm{T}");m("\\thetasym","\\vartheta");m("\\weierp","\\wp");m("\\Zeta","\\mathrm{Z}");m("\\argmin","\\DOTSB\\operatorname*{arg\\,min}");m("\\argmax","\\DOTSB\\operatorname*{arg\\,max}");m("\\plim","\\DOTSB\\mathop{\\operatorname{plim}}\\limits");m("\\bra","\\mathinner{\\langle{#1}|}");m("\\ket","\\mathinner{|{#1}\\rangle}");m("\\braket","\\mathinner{\\langle{#1}\\rangle}");m("\\Bra","\\left\\langle#1\\right|");m("\\Ket","\\left|#1\\right\\rangle");var Ai=a=>e=>{var t=e.consumeArg().tokens,r=e.consumeArg().tokens,i=e.consumeArg().tokens,n=e.consumeArg().tokens,l=e.macros.get("|"),u=e.macros.get("\\|");e.macros.beginGroup();var h=g=>y=>{a&&(y.macros.set("|",l),i.length&&y.macros.set("\\|",u));var x=g;if(!g&&i.length){var k=y.future();k.text==="|"&&(y.popToken(),x=!0)}return{tokens:x?i:r,numArgs:0}};e.macros.set("|",h(!1)),i.length&&e.macros.set("\\|",h(!0));var d=e.consumeArg().tokens,p=e.expandTokens([...n,...d,...t]);return e.macros.endGroup(),{tokens:p.reverse(),numArgs:0}};m("\\bra@ket",Ai(!1));m("\\bra@set",Ai(!0));m("\\Braket","\\bra@ket{\\left\\langle}{\\,\\middle\\vert\\,}{\\,\\middle\\vert\\,}{\\right\\rangle}");m("\\Set","\\bra@set{\\left\\{\\:}{\\;\\middle\\vert\\;}{\\;\\middle\\Vert\\;}{\\:\\right\\}}");m("\\set","\\bra@set{\\{\\,}{\\mid}{}{\\,\\}}");m("\\angln","{\\angl n}");m("\\blue","\\textcolor{##6495ed}{#1}");m("\\orange","\\textcolor{##ffa500}{#1}");m("\\pink","\\textcolor{##ff00af}{#1}");m("\\red","\\textcolor{##df0030}{#1}");m("\\green","\\textcolor{##28ae7b}{#1}");m("\\gray","\\textcolor{gray}{#1}");m("\\purple","\\textcolor{##9d38bd}{#1}");m("\\blueA","\\textcolor{##ccfaff}{#1}");m("\\blueB","\\textcolor{##80f6ff}{#1}");m("\\blueC","\\textcolor{##63d9ea}{#1}");m("\\blueD","\\textcolor{##11accd}{#1}");m("\\blueE","\\textcolor{##0c7f99}{#1}");m("\\tealA","\\textcolor{##94fff5}{#1}");m("\\tealB","\\textcolor{##26edd5}{#1}");m("\\tealC","\\textcolor{##01d1c1}{#1}");m("\\tealD","\\textcolor{##01a995}{#1}");m("\\tealE","\\textcolor{##208170}{#1}");m("\\greenA","\\textcolor{##b6ffb0}{#1}");m("\\greenB","\\textcolor{##8af281}{#1}");m("\\greenC","\\textcolor{##74cf70}{#1}");m("\\greenD","\\textcolor{##1fab54}{#1}");m("\\greenE","\\textcolor{##0d923f}{#1}");m("\\goldA","\\textcolor{##ffd0a9}{#1}");m("\\goldB","\\textcolor{##ffbb71}{#1}");m("\\goldC","\\textcolor{##ff9c39}{#1}");m("\\goldD","\\textcolor{##e07d10}{#1}");m("\\goldE","\\textcolor{##a75a05}{#1}");m("\\redA","\\textcolor{##fca9a9}{#1}");m("\\redB","\\textcolor{##ff8482}{#1}");m("\\redC","\\textcolor{##f9685d}{#1}");m("\\redD","\\textcolor{##e84d39}{#1}");m("\\redE","\\textcolor{##bc2612}{#1}");m("\\maroonA","\\textcolor{##ffbde0}{#1}");m("\\maroonB","\\textcolor{##ff92c6}{#1}");m("\\maroonC","\\textcolor{##ed5fa6}{#1}");m("\\maroonD","\\textcolor{##ca337c}{#1}");m("\\maroonE","\\textcolor{##9e034e}{#1}");m("\\purpleA","\\textcolor{##ddd7ff}{#1}");m("\\purpleB","\\textcolor{##c6b9fc}{#1}");m("\\purpleC","\\textcolor{##aa87ff}{#1}");m("\\purpleD","\\textcolor{##7854ab}{#1}");m("\\purpleE","\\textcolor{##543b78}{#1}");m("\\mintA","\\textcolor{##f5f9e8}{#1}");m("\\mintB","\\textcolor{##edf2df}{#1}");m("\\mintC","\\textcolor{##e0e5cc}{#1}");m("\\grayA","\\textcolor{##f6f7f7}{#1}");m("\\grayB","\\textcolor{##f0f1f2}{#1}");m("\\grayC","\\textcolor{##e3e5e6}{#1}");m("\\grayD","\\textcolor{##d6d8da}{#1}");m("\\grayE","\\textcolor{##babec2}{#1}");m("\\grayF","\\textcolor{##888d93}{#1}");m("\\grayG","\\textcolor{##626569}{#1}");m("\\grayH","\\textcolor{##3b3e40}{#1}");m("\\grayI","\\textcolor{##21242c}{#1}");m("\\kaBlue","\\textcolor{##314453}{#1}");m("\\kaGreen","\\textcolor{##71B307}{#1}");var zi={"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0};class Vl{constructor(e,t,r){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=t,this.expansionCount=0,this.feed(e),this.macros=new Fl(Pl,t.macros),this.mode=r,this.stack=[]}feed(e){this.lexer=new ca(e,this.settings)}switchMode(e){this.mode=e}beginGroup(){this.macros.beginGroup()}endGroup(){this.macros.endGroup()}endGroups(){this.macros.endGroups()}future(){return this.stack.length===0&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]}popToken(){return this.future(),this.stack.pop()}pushToken(e){this.stack.push(e)}pushTokens(e){this.stack.push(...e)}scanArgument(e){var t,r,i;if(e){if(this.consumeSpaces(),this.future().text!=="[")return null;t=this.popToken(),{tokens:i,end:r}=this.consumeArg(["]"])}else({tokens:i,start:t,end:r}=this.consumeArg());return this.pushToken(new Ve("EOF",r.loc)),this.pushTokens(i),t.range(r,"")}consumeSpaces(){for(;;){var e=this.future();if(e.text===" ")this.stack.pop();else break}}consumeArg(e){var t=[],r=e&&e.length>0;r||this.consumeSpaces();var i=this.future(),n,l=0,u=0;do{if(n=this.popToken(),t.push(n),n.text==="{")++l;else if(n.text==="}"){if(--l,l===-1)throw new M("Extra }",n)}else if(n.text==="EOF")throw new M("Unexpected end of input in a macro argument, expected '"+(e&&r?e[u]:"}")+"'",n);if(e&&r)if((l===0||l===1&&e[u]==="{")&&n.text===e[u]){if(++u,u===e.length){t.splice(-u,u);break}}else u=0}while(l!==0||r);return i.text==="{"&&t[t.length-1].text==="}"&&(t.pop(),t.shift()),t.reverse(),{tokens:t,start:i,end:n}}consumeArgs(e,t){if(t){if(t.length!==e+1)throw new M("The length of delimiters doesn't match the number of args!");for(var r=t[0],i=0;i<r.length;i++){var n=this.popToken();if(r[i]!==n.text)throw new M("Use of the macro doesn't match its definition",n)}}for(var l=[],u=0;u<e;u++)l.push(this.consumeArg(t&&t[u+1]).tokens);return l}expandOnce(e){var t=this.popToken(),r=t.text,i=t.noexpand?null:this._getExpansion(r);if(i==null||e&&i.unexpandable){if(e&&i==null&&r[0]==="\\"&&!this.isDefined(r))throw new M("Undefined control sequence: "+r);return this.pushToken(t),!1}if(this.expansionCount++,this.expansionCount>this.settings.maxExpand)throw new M("Too many expansions: infinite loop or need to increase maxExpand setting");var n=i.tokens,l=this.consumeArgs(i.numArgs,i.delimiters);if(i.numArgs){n=n.slice();for(var u=n.length-1;u>=0;--u){var h=n[u];if(h.text==="#"){if(u===0)throw new M("Incomplete placeholder at end of macro body",h);if(h=n[--u],h.text==="#")n.splice(u+1,1);else if(/^[1-9]$/.test(h.text))n.splice(u,2,...l[+h.text-1]);else throw new M("Not a valid argument number",h)}}}return this.pushTokens(n),n.length}expandAfterFuture(){return this.expandOnce(),this.future()}expandNextToken(){for(;;)if(this.expandOnce()===!1){var e=this.stack.pop();return e.treatAsRelax&&(e.text="\\relax"),e}throw new Error}expandMacro(e){return this.macros.has(e)?this.expandTokens([new Ve(e)]):void 0}expandTokens(e){var t=[],r=this.stack.length;for(this.pushTokens(e);this.stack.length>r;)if(this.expandOnce(!0)===!1){var i=this.stack.pop();i.treatAsRelax&&(i.noexpand=!1,i.treatAsRelax=!1),t.push(i)}return t}expandMacroAsText(e){var t=this.expandMacro(e);return t&&t.map(r=>r.text).join("")}_getExpansion(e){var t=this.macros.get(e);if(t==null)return t;if(e.length===1){var r=this.lexer.catcodes[e];if(r!=null&&r!==13)return}var i=typeof t=="function"?t(this):t;if(typeof i=="string"){var n=0;if(i.indexOf("#")!==-1)for(var l=i.replace(/##/g,"");l.indexOf("#"+(n+1))!==-1;)++n;for(var u=new ca(i,this.settings),h=[],d=u.lex();d.text!=="EOF";)h.push(d),d=u.lex();h.reverse();var p={tokens:h,numArgs:n};return p}return i}isDefined(e){return this.macros.has(e)||s0.hasOwnProperty(e)||ne.math.hasOwnProperty(e)||ne.text.hasOwnProperty(e)||zi.hasOwnProperty(e)}isExpandable(e){var t=this.macros.get(e);return t!=null?typeof t=="string"||typeof t=="function"||!t.unexpandable:s0.hasOwnProperty(e)&&!s0[e].primitive}}var pa=/^[₊₋₌₍₎₀₁₂₃₄₅₆₇₈₉ₐₑₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥₓᵦᵧᵨᵩᵪ]/,et=Object.freeze({"₊":"+","₋":"-","₌":"=","₍":"(","₎":")","₀":"0","₁":"1","₂":"2","₃":"3","₄":"4","₅":"5","₆":"6","₇":"7","₈":"8","₉":"9","ₐ":"a","ₑ":"e","ₕ":"h","ᵢ":"i","ⱼ":"j","ₖ":"k","ₗ":"l","ₘ":"m","ₙ":"n","ₒ":"o","ₚ":"p","ᵣ":"r","ₛ":"s","ₜ":"t","ᵤ":"u","ᵥ":"v","ₓ":"x","ᵦ":"β","ᵧ":"γ","ᵨ":"ρ","ᵩ":"ϕ","ᵪ":"χ","⁺":"+","⁻":"-","⁼":"=","⁽":"(","⁾":")","⁰":"0","¹":"1","²":"2","³":"3","⁴":"4","⁵":"5","⁶":"6","⁷":"7","⁸":"8","⁹":"9","ᴬ":"A","ᴮ":"B","ᴰ":"D","ᴱ":"E","ᴳ":"G","ᴴ":"H","ᴵ":"I","ᴶ":"J","ᴷ":"K","ᴸ":"L","ᴹ":"M","ᴺ":"N","ᴼ":"O","ᴾ":"P","ᴿ":"R","ᵀ":"T","ᵁ":"U","ⱽ":"V","ᵂ":"W","ᵃ":"a","ᵇ":"b","ᶜ":"c","ᵈ":"d","ᵉ":"e","ᶠ":"f","ᵍ":"g",ʰ:"h","ⁱ":"i",ʲ:"j","ᵏ":"k",ˡ:"l","ᵐ":"m",ⁿ:"n","ᵒ":"o","ᵖ":"p",ʳ:"r",ˢ:"s","ᵗ":"t","ᵘ":"u","ᵛ":"v",ʷ:"w",ˣ:"x",ʸ:"y","ᶻ":"z","ᵝ":"β","ᵞ":"γ","ᵟ":"δ","ᵠ":"ϕ","ᵡ":"χ","ᶿ":"θ"}),Ft={"́":{text:"\\'",math:"\\acute"},"̀":{text:"\\`",math:"\\grave"},"̈":{text:'\\"',math:"\\ddot"},"̃":{text:"\\~",math:"\\tilde"},"̄":{text:"\\=",math:"\\bar"},"̆":{text:"\\u",math:"\\breve"},"̌":{text:"\\v",math:"\\check"},"̂":{text:"\\^",math:"\\hat"},"̇":{text:"\\.",math:"\\dot"},"̊":{text:"\\r",math:"\\mathring"},"̋":{text:"\\H"},"̧":{text:"\\c"}},fa={á:"á",à:"à",ä:"ä",ǟ:"ǟ",ã:"ã",ā:"ā",ă:"ă",ắ:"ắ",ằ:"ằ",ẵ:"ẵ",ǎ:"ǎ",â:"â",ấ:"ấ",ầ:"ầ",ẫ:"ẫ",ȧ:"ȧ",ǡ:"ǡ",å:"å",ǻ:"ǻ",ḃ:"ḃ",ć:"ć",ḉ:"ḉ",č:"č",ĉ:"ĉ",ċ:"ċ",ç:"ç",ď:"ď",ḋ:"ḋ",ḑ:"ḑ",é:"é",è:"è",ë:"ë",ẽ:"ẽ",ē:"ē",ḗ:"ḗ",ḕ:"ḕ",ĕ:"ĕ",ḝ:"ḝ",ě:"ě",ê:"ê",ế:"ế",ề:"ề",ễ:"ễ",ė:"ė",ȩ:"ȩ",ḟ:"ḟ",ǵ:"ǵ",ḡ:"ḡ",ğ:"ğ",ǧ:"ǧ",ĝ:"ĝ",ġ:"ġ",ģ:"ģ",ḧ:"ḧ",ȟ:"ȟ",ĥ:"ĥ",ḣ:"ḣ",ḩ:"ḩ",í:"í",ì:"ì",ï:"ï",ḯ:"ḯ",ĩ:"ĩ",ī:"ī",ĭ:"ĭ",ǐ:"ǐ",î:"î",ǰ:"ǰ",ĵ:"ĵ",ḱ:"ḱ",ǩ:"ǩ",ķ:"ķ",ĺ:"ĺ",ľ:"ľ",ļ:"ļ",ḿ:"ḿ",ṁ:"ṁ",ń:"ń",ǹ:"ǹ",ñ:"ñ",ň:"ň",ṅ:"ṅ",ņ:"ņ",ó:"ó",ò:"ò",ö:"ö",ȫ:"ȫ",õ:"õ",ṍ:"ṍ",ṏ:"ṏ",ȭ:"ȭ",ō:"ō",ṓ:"ṓ",ṑ:"ṑ",ŏ:"ŏ",ǒ:"ǒ",ô:"ô",ố:"ố",ồ:"ồ",ỗ:"ỗ",ȯ:"ȯ",ȱ:"ȱ",ő:"ő",ṕ:"ṕ",ṗ:"ṗ",ŕ:"ŕ",ř:"ř",ṙ:"ṙ",ŗ:"ŗ",ś:"ś",ṥ:"ṥ",š:"š",ṧ:"ṧ",ŝ:"ŝ",ṡ:"ṡ",ş:"ş",ẗ:"ẗ",ť:"ť",ṫ:"ṫ",ţ:"ţ",ú:"ú",ù:"ù",ü:"ü",ǘ:"ǘ",ǜ:"ǜ",ǖ:"ǖ",ǚ:"ǚ",ũ:"ũ",ṹ:"ṹ",ū:"ū",ṻ:"ṻ",ŭ:"ŭ",ǔ:"ǔ",û:"û",ů:"ů",ű:"ű",ṽ:"ṽ",ẃ:"ẃ",ẁ:"ẁ",ẅ:"ẅ",ŵ:"ŵ",ẇ:"ẇ",ẘ:"ẘ",ẍ:"ẍ",ẋ:"ẋ",ý:"ý",ỳ:"ỳ",ÿ:"ÿ",ỹ:"ỹ",ȳ:"ȳ",ŷ:"ŷ",ẏ:"ẏ",ẙ:"ẙ",ź:"ź",ž:"ž",ẑ:"ẑ",ż:"ż",Á:"Á",À:"À",Ä:"Ä",Ǟ:"Ǟ",Ã:"Ã",Ā:"Ā",Ă:"Ă",Ắ:"Ắ",Ằ:"Ằ",Ẵ:"Ẵ",Ǎ:"Ǎ",Â:"Â",Ấ:"Ấ",Ầ:"Ầ",Ẫ:"Ẫ",Ȧ:"Ȧ",Ǡ:"Ǡ",Å:"Å",Ǻ:"Ǻ",Ḃ:"Ḃ",Ć:"Ć",Ḉ:"Ḉ",Č:"Č",Ĉ:"Ĉ",Ċ:"Ċ",Ç:"Ç",Ď:"Ď",Ḋ:"Ḋ",Ḑ:"Ḑ",É:"É",È:"È",Ë:"Ë",Ẽ:"Ẽ",Ē:"Ē",Ḗ:"Ḗ",Ḕ:"Ḕ",Ĕ:"Ĕ",Ḝ:"Ḝ",Ě:"Ě",Ê:"Ê",Ế:"Ế",Ề:"Ề",Ễ:"Ễ",Ė:"Ė",Ȩ:"Ȩ",Ḟ:"Ḟ",Ǵ:"Ǵ",Ḡ:"Ḡ",Ğ:"Ğ",Ǧ:"Ǧ",Ĝ:"Ĝ",Ġ:"Ġ",Ģ:"Ģ",Ḧ:"Ḧ",Ȟ:"Ȟ",Ĥ:"Ĥ",Ḣ:"Ḣ",Ḩ:"Ḩ",Í:"Í",Ì:"Ì",Ï:"Ï",Ḯ:"Ḯ",Ĩ:"Ĩ",Ī:"Ī",Ĭ:"Ĭ",Ǐ:"Ǐ",Î:"Î",İ:"İ",Ĵ:"Ĵ",Ḱ:"Ḱ",Ǩ:"Ǩ",Ķ:"Ķ",Ĺ:"Ĺ",Ľ:"Ľ",Ļ:"Ļ",Ḿ:"Ḿ",Ṁ:"Ṁ",Ń:"Ń",Ǹ:"Ǹ",Ñ:"Ñ",Ň:"Ň",Ṅ:"Ṅ",Ņ:"Ņ",Ó:"Ó",Ò:"Ò",Ö:"Ö",Ȫ:"Ȫ",Õ:"Õ",Ṍ:"Ṍ",Ṏ:"Ṏ",Ȭ:"Ȭ",Ō:"Ō",Ṓ:"Ṓ",Ṑ:"Ṑ",Ŏ:"Ŏ",Ǒ:"Ǒ",Ô:"Ô",Ố:"Ố",Ồ:"Ồ",Ỗ:"Ỗ",Ȯ:"Ȯ",Ȱ:"Ȱ",Ő:"Ő",Ṕ:"Ṕ",Ṗ:"Ṗ",Ŕ:"Ŕ",Ř:"Ř",Ṙ:"Ṙ",Ŗ:"Ŗ",Ś:"Ś",Ṥ:"Ṥ",Š:"Š",Ṧ:"Ṧ",Ŝ:"Ŝ",Ṡ:"Ṡ",Ş:"Ş",Ť:"Ť",Ṫ:"Ṫ",Ţ:"Ţ",Ú:"Ú",Ù:"Ù",Ü:"Ü",Ǘ:"Ǘ",Ǜ:"Ǜ",Ǖ:"Ǖ",Ǚ:"Ǚ",Ũ:"Ũ",Ṹ:"Ṹ",Ū:"Ū",Ṻ:"Ṻ",Ŭ:"Ŭ",Ǔ:"Ǔ",Û:"Û",Ů:"Ů",Ű:"Ű",Ṽ:"Ṽ",Ẃ:"Ẃ",Ẁ:"Ẁ",Ẅ:"Ẅ",Ŵ:"Ŵ",Ẇ:"Ẇ",Ẍ:"Ẍ",Ẋ:"Ẋ",Ý:"Ý",Ỳ:"Ỳ",Ÿ:"Ÿ",Ỹ:"Ỹ",Ȳ:"Ȳ",Ŷ:"Ŷ",Ẏ:"Ẏ",Ź:"Ź",Ž:"Ž",Ẑ:"Ẑ",Ż:"Ż",ά:"ά",ὰ:"ὰ",ᾱ:"ᾱ",ᾰ:"ᾰ",έ:"έ",ὲ:"ὲ",ή:"ή",ὴ:"ὴ",ί:"ί",ὶ:"ὶ",ϊ:"ϊ",ΐ:"ΐ",ῒ:"ῒ",ῑ:"ῑ",ῐ:"ῐ",ό:"ό",ὸ:"ὸ",ύ:"ύ",ὺ:"ὺ",ϋ:"ϋ",ΰ:"ΰ",ῢ:"ῢ",ῡ:"ῡ",ῠ:"ῠ",ώ:"ώ",ὼ:"ὼ",Ύ:"Ύ",Ὺ:"Ὺ",Ϋ:"Ϋ",Ῡ:"Ῡ",Ῠ:"Ῠ",Ώ:"Ώ",Ὼ:"Ὼ"};class L0{constructor(e,t){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new Vl(e,t,this.mode),this.settings=t,this.leftrightDepth=0}expect(e,t){if(t===void 0&&(t=!0),this.fetch().text!==e)throw new M("Expected '"+e+"', got '"+this.fetch().text+"'",this.fetch());t&&this.consume()}consume(){this.nextToken=null}fetch(){return this.nextToken==null&&(this.nextToken=this.gullet.expandNextToken()),this.nextToken}switchMode(e){this.mode=e,this.gullet.switchMode(e)}parse(){this.settings.globalGroup||this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor");try{var e=this.parseExpression(!1);return this.expect("EOF"),this.settings.globalGroup||this.gullet.endGroup(),e}finally{this.gullet.endGroups()}}subparse(e){var t=this.nextToken;this.consume(),this.gullet.pushToken(new Ve("}")),this.gullet.pushTokens(e);var r=this.parseExpression(!1);return this.expect("}"),this.nextToken=t,r}parseExpression(e,t){for(var r=[];;){this.mode==="math"&&this.consumeSpaces();var i=this.fetch();if(L0.endOfExpression.indexOf(i.text)!==-1||t&&i.text===t||e&&s0[i.text]&&s0[i.text].infix)break;var n=this.parseAtom(t);if(n){if(n.type==="internal")continue}else break;r.push(n)}return this.mode==="text"&&this.formLigatures(r),this.handleInfixNodes(r)}handleInfixNodes(e){for(var t=-1,r,i=0;i<e.length;i++)if(e[i].type==="infix"){if(t!==-1)throw new M("only one infix operator per group",e[i].token);t=i,r=e[i].replaceWith}if(t!==-1&&r){var n,l,u=e.slice(0,t),h=e.slice(t+1);u.length===1&&u[0].type==="ordgroup"?n=u[0]:n={type:"ordgroup",mode:this.mode,body:u},h.length===1&&h[0].type==="ordgroup"?l=h[0]:l={type:"ordgroup",mode:this.mode,body:h};var d;return r==="\\\\abovefrac"?d=this.callFunction(r,[n,e[t],l],[]):d=this.callFunction(r,[n,l],[]),[d]}else return e}handleSupSubscript(e){var t=this.fetch(),r=t.text;this.consume(),this.consumeSpaces();var i=this.parseGroup(e);if(!i)throw new M("Expected group after '"+r+"'",t);return i}formatUnsupportedCmd(e){for(var t=[],r=0;r<e.length;r++)t.push({type:"textord",mode:"text",text:e[r]});var i={type:"text",mode:this.mode,body:t},n={type:"color",mode:this.mode,color:this.settings.errorColor,body:[i]};return n}parseAtom(e){var t=this.parseGroup("atom",e);if(this.mode==="text")return t;for(var r,i;;){this.consumeSpaces();var n=this.fetch();if(n.text==="\\limits"||n.text==="\\nolimits"){if(t&&t.type==="op"){var l=n.text==="\\limits";t.limits=l,t.alwaysHandleSupSub=!0}else if(t&&t.type==="operatorname")t.alwaysHandleSupSub&&(t.limits=n.text==="\\limits");else throw new M("Limit controls must follow a math operator",n);this.consume()}else if(n.text==="^"){if(r)throw new M("Double superscript",n);r=this.handleSupSubscript("superscript")}else if(n.text==="_"){if(i)throw new M("Double subscript",n);i=this.handleSupSubscript("subscript")}else if(n.text==="'"){if(r)throw new M("Double superscript",n);var u={type:"textord",mode:this.mode,text:"\\prime"},h=[u];for(this.consume();this.fetch().text==="'";)h.push(u),this.consume();this.fetch().text==="^"&&h.push(this.handleSupSubscript("superscript")),r={type:"ordgroup",mode:this.mode,body:h}}else if(et[n.text]){var d=et[n.text],p=pa.test(n.text);for(this.consume();;){var g=this.fetch().text;if(!et[g]||pa.test(g)!==p)break;this.consume(),d+=et[g]}var y=new L0(d,this.settings).parse();p?i={type:"ordgroup",mode:"math",body:y}:r={type:"ordgroup",mode:"math",body:y}}else break}return r||i?{type:"supsub",mode:this.mode,base:t,sup:r,sub:i}:t}parseFunction(e,t){var r=this.fetch(),i=r.text,n=s0[i];if(!n)return null;if(this.consume(),t&&t!=="atom"&&!n.allowedInArgument)throw new M("Got function '"+i+"' with no arguments"+(t?" as "+t:""),r);if(this.mode==="text"&&!n.allowedInText)throw new M("Can't use function '"+i+"' in text mode",r);if(this.mode==="math"&&n.allowedInMath===!1)throw new M("Can't use function '"+i+"' in math mode",r);var{args:l,optArgs:u}=this.parseArguments(i,n);return this.callFunction(i,l,u,r,e)}callFunction(e,t,r,i,n){var l={funcName:e,parser:this,token:i,breakOnTokenText:n},u=s0[e];if(u&&u.handler)return u.handler(l,t,r);throw new M("No function handler for "+e)}parseArguments(e,t){var r=t.numArgs+t.numOptionalArgs;if(r===0)return{args:[],optArgs:[]};for(var i=[],n=[],l=0;l<r;l++){var u=t.argTypes&&t.argTypes[l],h=l<t.numOptionalArgs;(t.primitive&&u==null||t.type==="sqrt"&&l===1&&n[0]==null)&&(u="primitive");var d=this.parseGroupOfType("argument to '"+e+"'",u,h);if(h)n.push(d);else if(d!=null)i.push(d);else throw new M("Null argument, please report this as a bug")}return{args:i,optArgs:n}}parseGroupOfType(e,t,r){switch(t){case"color":return this.parseColorGroup(r);case"size":return this.parseSizeGroup(r);case"url":return this.parseUrlGroup(r);case"math":case"text":return this.parseArgumentGroup(r,t);case"hbox":{var i=this.parseArgumentGroup(r,"text");return i!=null?{type:"styling",mode:i.mode,body:[i],style:"text"}:null}case"raw":{var n=this.parseStringGroup("raw",r);return n!=null?{type:"raw",mode:"text",string:n.text}:null}case"primitive":{if(r)throw new M("A primitive argument cannot be optional");var l=this.parseGroup(e);if(l==null)throw new M("Expected group as "+e,this.fetch());return l}case"original":case null:case void 0:return this.parseArgumentGroup(r);default:throw new M("Unknown group type as "+e,this.fetch())}}consumeSpaces(){for(;this.fetch().text===" ";)this.consume()}parseStringGroup(e,t){var r=this.gullet.scanArgument(t);if(r==null)return null;for(var i="",n;(n=this.fetch()).text!=="EOF";)i+=n.text,this.consume();return this.consume(),r.text=i,r}parseRegexGroup(e,t){for(var r=this.fetch(),i=r,n="",l;(l=this.fetch()).text!=="EOF"&&e.test(n+l.text);)i=l,n+=i.text,this.consume();if(n==="")throw new M("Invalid "+t+": '"+r.text+"'",r);return r.range(i,n)}parseColorGroup(e){var t=this.parseStringGroup("color",e);if(t==null)return null;var r=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(t.text);if(!r)throw new M("Invalid color: '"+t.text+"'",t);var i=r[0];return/^[0-9a-f]{6}$/i.test(i)&&(i="#"+i),{type:"color-token",mode:this.mode,color:i}}parseSizeGroup(e){var t,r=!1;if(this.gullet.consumeSpaces(),!e&&this.gullet.future().text!=="{"?t=this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size"):t=this.parseStringGroup("size",e),!t)return null;!e&&t.text.length===0&&(t.text="0pt",r=!0);var i=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(t.text);if(!i)throw new M("Invalid size: '"+t.text+"'",t);var n={number:+(i[1]+i[2]),unit:i[3]};if(!Ia(n))throw new M("Invalid unit: '"+n.unit+"'",t);return{type:"size",mode:this.mode,value:n,isBlank:r}}parseUrlGroup(e){this.gullet.lexer.setCatcode("%",13),this.gullet.lexer.setCatcode("~",12);var t=this.parseStringGroup("url",e);if(this.gullet.lexer.setCatcode("%",14),this.gullet.lexer.setCatcode("~",13),t==null)return null;var r=t.text.replace(/\\([#$%&~_^{}])/g,"$1");return{type:"url",mode:this.mode,url:r}}parseArgumentGroup(e,t){var r=this.gullet.scanArgument(e);if(r==null)return null;var i=this.mode;t&&this.switchMode(t),this.gullet.beginGroup();var n=this.parseExpression(!1,"EOF");this.expect("EOF"),this.gullet.endGroup();var l={type:"ordgroup",mode:this.mode,loc:r.loc,body:n};return t&&this.switchMode(i),l}parseGroup(e,t){var r=this.fetch(),i=r.text,n;if(i==="{"||i==="\\begingroup"){this.consume();var l=i==="{"?"}":"\\endgroup";this.gullet.beginGroup();var u=this.parseExpression(!1,l),h=this.fetch();this.expect(l),this.gullet.endGroup(),n={type:"ordgroup",mode:this.mode,loc:ze.range(r,h),body:u,semisimple:i==="\\begingroup"||void 0}}else if(n=this.parseFunction(t,e)||this.parseSymbol(),n==null&&i[0]==="\\"&&!zi.hasOwnProperty(i)){if(this.settings.throwOnError)throw new M("Undefined control sequence: "+i,r);n=this.formatUnsupportedCmd(i),this.consume()}return n}formLigatures(e){for(var t=e.length-1,r=0;r<t;++r){var i=e[r],n=i.text;n==="-"&&e[r+1].text==="-"&&(r+1<t&&e[r+2].text==="-"?(e.splice(r,3,{type:"textord",mode:"text",loc:ze.range(i,e[r+2]),text:"---"}),t-=2):(e.splice(r,2,{type:"textord",mode:"text",loc:ze.range(i,e[r+1]),text:"--"}),t-=1)),(n==="'"||n==="`")&&e[r+1].text===n&&(e.splice(r,2,{type:"textord",mode:"text",loc:ze.range(i,e[r+1]),text:n+n}),t-=1)}}parseSymbol(){var e=this.fetch(),t=e.text;if(/^\\verb[^a-zA-Z]/.test(t)){this.consume();var r=t.slice(5),i=r.charAt(0)==="*";if(i&&(r=r.slice(1)),r.length<2||r.charAt(0)!==r.slice(-1))throw new M(`\\verb assertion failed --
                    please report what input caused this bug`);return r=r.slice(1,-1),{type:"verb",mode:"text",body:r,star:i}}fa.hasOwnProperty(t[0])&&!ne[this.mode][t[0]]&&(this.settings.strict&&this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+t[0]+'" used in math mode',e),t=fa[t[0]]+t.slice(1));var n=Ol.exec(t);n&&(t=t.substring(0,n.index),t==="i"?t="ı":t==="j"&&(t="ȷ"));var l;if(ne[this.mode][t]){this.settings.strict&&this.mode==="math"&&Wt.indexOf(t)>=0&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+t[0]+'" used in math mode',e);var u=ne[this.mode][t].group,h=ze.range(e),d;if(Cs.hasOwnProperty(u)){var p=u;d={type:"atom",mode:this.mode,family:p,loc:h,text:t}}else d={type:u,mode:this.mode,loc:h,text:t};l=d}else if(t.charCodeAt(0)>=128)this.settings.strict&&(qa(t.charCodeAt(0))?this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+t[0]+'" used in math mode',e):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+t[0]+'"'+(" ("+t.charCodeAt(0)+")"),e)),l={type:"textord",mode:"text",loc:ze.range(e),text:t};else return null;if(this.consume(),n)for(var g=0;g<n[0].length;g++){var y=n[0][g];if(!Ft[y])throw new M("Unknown accent ' "+y+"'",e);var x=Ft[y][this.mode]||Ft[y].text;if(!x)throw new M("Accent "+y+" unsupported in "+this.mode+" mode",e);l={type:"accent",mode:this.mode,loc:ze.range(e),label:x,isStretchy:!1,isShifty:!0,base:l}}return l}}L0.endOfExpression=["}","\\endgroup","\\end","\\right","&"];var Mr=function(e,t){if(!(typeof e=="string"||e instanceof String))throw new TypeError("KaTeX can only parse string typed expression");var r=new L0(e,t);delete r.gullet.macros.current["\\df@tag"];var i=r.parse();if(delete r.gullet.macros.current["\\current@color"],delete r.gullet.macros.current["\\color"],r.gullet.macros.get("\\df@tag")){if(!t.displayMode)throw new M("\\tag works only in display equations");i=[{type:"tag",mode:"text",body:i,tag:r.subparse([new Ve("\\df@tag")])}]}return i},Mi=function(e,t,r){t.textContent="";var i=Tr(e,r).toNode();t.appendChild(i)};typeof document<"u"&&document.compatMode!=="CSS1Compat"&&(typeof console<"u"&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype."),Mi=function(){throw new M("KaTeX doesn't work in quirks mode.")});var Gl=function(e,t){var r=Tr(e,t).toMarkup();return r},Ul=function(e,t){var r=new or(t);return Mr(e,r)},Ti=function(e,t,r){if(r.throwOnError||!(e instanceof M))throw e;var i=w.makeSpan(["katex-error"],[new qe(t)]);return i.setAttribute("title",e.toString()),i.setAttribute("style","color:"+r.errorColor),i},Tr=function(e,t){var r=new or(t);try{var i=Mr(e,r);return Zs(i,e,r)}catch(n){return Ti(n,e,r)}},$l=function(e,t){var r=new or(t);try{var i=Mr(e,r);return Ks(i,e,r)}catch(n){return Ti(n,e,r)}},tt={version:"0.16.8",render:Mi,renderToString:Gl,ParseError:M,SETTINGS_SCHEMA:at,__parse:Ul,__renderToDomTree:Tr,__renderToHTMLTree:$l,__setFontMetrics:ks,__defineSymbol:s,__defineFunction:N,__defineMacro:m,__domTree:{Span:F0,Anchor:cr,SymbolNode:qe,SvgNode:Xe,PathNode:o0,LineNode:jt}};const Yl="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAArFJREFUWEetlzmIFEEUhr8fXc0UvCIxEVRYj0Q8EpVdWQ00MRCWDVxQ1FjwQhQvPMBcRSNvFBUzb0EDIwOPwBUM1wvWRNDA49lvqF56e7t7ama6kmHo1/V/9dd79apFzcPMdgP7gG/AdkkvqiRUp76ZnQD2Z+b8CWyQ9KxMpzYAMzsZVp7XcoiNkp4WQdQCYGbHgQMVbpZCdAyQrHwy8APoarKdhRAdA7iome0FTkXk06+QE6Pb0RZAkmyTgH5gWNLjAOGZ73nQbDiE58QTD2wJINnricAgcBCYk/z+BbZIutouRBSAmU0ABoBDwNzcEjuBWF8JYGb+fHMieBhYUOHtv+DElRadGCoFMLNu4BqwuNmmhud5iJjEHCkEMLPZwDtgaqR4GtYqxK4ygLPAzhbFW4U4JunQOICQ6V+BaW0C+Gt5J/YApzPzNcQLy9DM1gH3OxAvc8K75FE/sCQdSYOKHLgIbK0BIHViUNLlUB1dkn5n5x4DEOz/AkyvCSCF6EtPvvy8eYA+4EGN4ulUA5K8pMeNPMAFYFsJwMtwDHuCedyaSNCPfohJ+lMJEOz/DMwomXhVer0ys/nA+0gA7xWXymJHHTCztcCjksARYJEkB/T2uxB4GwEwBHRL8n5ROLIA5/0SWRDl4r2SXpuZd8B5oe0ujQDol3SjKq4BELqdr25mLjgrvgx42MLx7A4tkWQxAL1A42KRGVnx5aE6WukNmyTdbeZS6sA5YEeN4q8kxWwRCvZ/AmYFgPzK3fYpzVaSef49cWulpA8x7zhAD9C4nwGdiPuX0HVPUEnezKKGA6Sttx1xF70D3ASeV5VbaRma2XBC7Xf7HklvzMwTrsp2F72d1PetdkWzMO6An1JnmojXKjoGIP1jZitCqaUJ17G9MUmQPQm9W60G7tVlbwzAfxIHOJe26mbaAAAAAElFTkSuQmCC",jl="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAcCAYAAAAAwr0iAAAAAXNSR0IArs4c6QAAAeNJREFUSEvNlz1rVFEQhp+XpPCrEETtDAoGBYtghICg2wj7CyS/QLGKWGzlbiNWiYTEctOI5SIIFlEsEmJjZ0RsJR+dERIEwUJl3FnOwuW6h8257s1myj173nnuuWfemSszGwFqwF3gPAcTG8ASMCszewg8Ppi8/2RpOMAWcG5IANsOYJnkq8ADYKckoFPAE6Da1c8DVCW9LSl5R9bMbgDvYgDXJb0vGWACWE8CMLPRUCUVQMAa0JT0KxXWzNIAzOx4SDiZS/YBqEj6kQJRBGABuB9J8lRSbK3nliIAX4ALEYANSbG1gQF8Bc5EAL5Jiq0NDKAF3I4AvJAUWxsYwBjwETiZU/wOTEjaLPUSBvNwCHewm5kyrKUmD1ppZZj4dEeAOnALuAx8BhqSVro6yVWwXwAzOw28Aa7m9vwG7kh6VtoJmJm74+tsk+kBPi2pVcoJmNkMsNjntHaBK8DZffWC4P/Xggf4zPBJUrZ1d7vbxSDolt0vXgKP+gKY2VTb/Z4D4xnFbWDef2/b7154n5eA5cRRzke/ZrQbtsvtRPsivQL8RveKP4DPdH65HCA1/DS9rDuRH0juhac8lqpa9P95gJ/A0aJiRfblAYpo/NeeQwEw9LF8mB8mdX8Fw/o0cy+Y+wsqhR+Bw0RqKwAAAABJRU5ErkJggg==",Wl="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAA4tJREFUWEe9l0/IlVUQxn+PpkkY2iLSSsIwS9pIRkhgRaAuglJMFAuVCNpFhuKiwjQqChf9gRZJhEpaYmK6sEhpU0GZ6Cr6B9EfzF1pBhbZ+D5+536de773/d57v0sNXC733Jk5z5mZ88wc0YNExFzgPuAO4BrgakDAyfT5GNgv6VgP7rpU7KRRImIp8Cwwp0fH3wFPVfrvSIpebGoBRMR1wC7g9l6c1Oh8AayS9G2b/QgAEbEAeBe4ss245f9fgRWSPhxNrwtA2vwwMLEw+hs4ALwHHE15d4inA/NSfSypsTtf6d8j6YMmEMMAUtjtvDz5QWC9pG9a6mUm8CJwf6H3GzBf0td19jmAT4qc+4RPSnqun1RExGPAVmB8ZnfCkZL0T+nrIoBU7fuKP5/od/OOfUQ8Crxc+FsjaUcTgC+Lq3ZQ0r39nLzUjYjdwMps/Qfg+jIKSiRzPFN0wd3clvM2cBExo7qKrptJme4CSSatYTGATdWvp7O1fZKWtW3Qy/8RsR1YnelulbShBHAEuDtbXC1pZy8btOnU1NZRSbeVAL4CbswW50jy2sASEdcCP2WOTkpyL+lKwRng8mxtqqTTA+8+dLsuAf5KjcsuTUwT80J0Dfxe3dvJ2YZTJBnUwJIA/AmMS87MLQbgQr8oBmCGmp3tdlMTa/WLKCIc7p8zu9OSppYp+Ai4K1t8QJI74cASEeYS94+OHJN0awngGVNutrhH0oqBdx+qgTeAhzJfr0oySw6LU+Bu5v7dEReN0/D9ICAiwp3SA8plmZ9FZXvu9AIPDrMyxb2Slg8I4E1gbebjR2DmCCq2QkSYs83duayrCualsYCIiIer2XFbYfuIpNdLf50I+PtzIC8Q39nHJb3SD4i0+WvVLDkhszsLTKto+I9aACkKNwCfVW30ikLpbWCjJIewUVLOPTvkYc/13weWSDIv/FuE+Y+IWAgcKoYJq5wD9nj0TiPZqWR3VTWq3WLHqfXmBVcHdgSIuqF0cXULfOouwii8mdH86TBcP1nqAtE0lrs5GYQfJGMR5zyn99LHMIjGh0lE+HQPVqfcAvid0Iu4TvyQeSuN9o5mkwyBaPOagPiB4qfZnelZNi2F303LZPNpNXR6ej7SuecR4UnINTMaiBdaAdQBTKDG5V2tQa8NxKkxAWiLWnGzRgPxy38OwGBGScfz/wuABOLSav7YDKxJV9gD66YLGVoxBpBVqYMAAAAASUVORK5CYII=",_l={class:"chat_main"},Xl={class:"flex_between header_view"},Jl={class:"mobile flex_column_start"},Ql={class:"chat_list flex_1",id:"chat_list"},Zl={key:0,class:"flex_start"},Kl={class:"tsy_view"},e1={class:"botQuestions"},t1=["onClick"],r1={key:0,class:"flex_column_end",style:{"align-items":"flex-end"}},a1=["innerHTML"],i1={key:1,class:"chat_bot"},n1=["innerHTML"],s1={key:1},l1={class:"flex_start_0",style:{"margin-top":"10px"}},o1=["href"],u1={style:{width:"100%"}},h1={key:0,class:"flex_start tool_ul"},c1=["onClick"],m1={key:1,class:"send_view send_view_pc"},d1={class:"flex_end",style:{"padding-top":"10px"}},p1={class:"grey_bottom margin_bottom_16",style:{"padding-top":"4px"}},f1={class:"flex_between"},v1={class:"flex_end"},g1={key:1,class:"send_img",src:wa},b1={__name:"index",setup(a){const e=Li();va();const t=ie(""),r=ie([]);ie(""),ie(!1),ie(!1);const i=ie(!0);ie(""),ie(null);const n=ie(""),l=ie(!1),u=ie(""),h=ie([{id:"1",title:"生成教案",url:Yl},{id:"2",title:"生成图片",url:jl},{id:"4",title:"资源搜索",url:Wl}]),d=ie([]),p=I=>{let R=K(I,{sanitize:!0,breaks:!0});return R=R.replace(/\$\$(.*?)\$\$/g,(_,W)=>tt.renderToString(W,{displayMode:!0,throwOnError:!1})),R=R.replace(/(^|[^\\])\$(.*?)\$|\\(\((.*?)\\)|\\\((.*?)\\\)/g,(_,W,ee,ve,le,D)=>ee?W+tt.renderToString(ee,{displayMode:!1,throwOnError:!1}):le?W+tt.renderToString(le,{displayMode:!1,throwOnError:!1}):D?W+tt.renderToString(D,{displayMode:!1,throwOnError:!1}):_),R.replace(/\s+/g," ").trim()};function g(I){let R=[];return I.forEach(_=>{R.push(_.content)}),R}function y(){u.value="",t.value="",T()}function x(I){i.value&&(u.value=I,T(),I=="1"&&(l.value=!0,zt(()=>{G()})))}async function k(I){let R=await on();R&&(n.value=R.data,V(),localStorage.setItem("bkConversationId",n.value))}function S(I){t.value=I,i.value&&E()}function C(){event.key==="Enter"&&!event.shiftKey&&(event.preventDefault(),E())}function E(){if(!i.value||!t.value||t.value.trim()=="")return;i.value=!1,r.value.push({messageType:"user",messageText:t.value}),r.value.push({messageType:"ai",messageText:"",messageUrl:[],type:u.value}),G();let I={bkConversationsId:n.value,type:u.value?u.value:1,content:t.value};t.value="";let R=Vi();fetch("http://ttxs.haianedu.net/api/dxs/ai/conversations/bk/chat",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json","X-Haian-Platform-Member-Token":R?`Bearer ${R}`:void 0},mode:"cors",body:JSON.stringify(I)}).then(async _=>{L(_)}).catch(_=>{})}async function L(I){const R=I.body.getReader(),_=new TextDecoder("utf-8");let W="";for(;;){const{done:ee,value:ve}=await R.read();if(ee){i.value=!0,setTimeout(()=>{G()},200);break}const le=_.decode(ve,{stream:!0});W+=le;const D=W.split(`
`);W=D.pop(),D.forEach((de,fe)=>{const ge=de.substring(5).trim();if(u.value!=1&&u.value)ge.startsWith("[{")?r.value[r.value.length-1].messageUrl=JSON.parse(ge):r.value[r.value.length-1].messageText+=ge;else try{let Ke=JSON.parse(ge);Ke.content,Ke.content.startsWith("[{")?r.value[r.value.length-1].messageUrl=JSON.parse(Ke.content):r.value[r.value.length-1].messageText+=Ke.content}catch{}G()})}}async function V(I){let R={id:n.value},_=await hn(R);_&&(r.value=_.data,_.data.forEach(W=>{W.messageUrl&&(W.messageUrl=JSON.parse(W.messageUrl))}),zt(async()=>{r.value.length>0&&(await j(),G())}))}const j=()=>{const R=Array.from(document.querySelectorAll(".chat_bot img")).map(_=>_.complete?Promise.resolve():new Promise(W=>{_.addEventListener("load",W),_.addEventListener("error",W)}));return Promise.all(R)};function G(){zt(()=>{let I=document.getElementById("chat_list");I.scrollTo({top:I.scrollHeight,behavior:"instant"}),I.scrollHeight})}async function H(){let I={id:n.value};await cn(I)&&(r.value=[],k())}async function T(){let I={type:u.value?u.value:1},R=await un(I);R&&(d.value=R.data)}return ga(async()=>{T(),localStorage.getItem("bkConversationId")?(n.value=localStorage.getItem("bkConversationId"),V()):k()}),Oi(()=>{}),(I,R)=>{const _=Ce("Close"),W=Ce("el-icon"),ee=Ce("el-image"),ve=Ce("van-loading"),le=Ce("el-input");return Q(),ae("div",_l,[F("div",Xl,[R[3]||(R[3]=F("div",{class:"flex_start left"},[F("img",{src:Hi,alt:""}),F("p",null,"备课助手")],-1)),be(W,{onClick:R[0]||(R[0]=D=>X(e).back())},{default:Oe(()=>[be(_)]),_:1})]),F("div",Jl,[F("div",Ql,[r.value.length==0?(Q(),ae("div",Zl,[F("div",Kl,[R[5]||(R[5]=F("img",{src:"https://cdn.tqxxkj.cn/static/aizntpt/ai.png"},null,-1)),R[6]||(R[6]=F("p",{class:"botName"},"备课助手",-1)),R[7]||(R[7]=F("p",{class:"botSlogan"}," 你好！我是备课小能手，能快速分析学科知识，为您生成完整教学设计。从教学目标到作业、反思，都精心规划。我还会依据学情调整内容，提高教学适配度。提供多种教学形式提示，助力您高效备课，让教学更轻松！ ",-1)),F("div",e1,[R[4]||(R[4]=F("p",{class:"text_14 grey2"},"你可以尝试这样问：",-1)),F("ul",null,[(Q(!0),ae(i0,null,n0(d.value,(D,de)=>(Q(),ae("li",{key:de,onClick:fe=>S(D.question)},rt(D.question),9,t1))),128))])])])])):Be("",!0),(Q(!0),ae(i0,null,n0(r.value,(D,de)=>(Q(),ae("div",{key:de},[D.messageType=="user"?(Q(),ae("div",r1,[F("p",{class:"chat_my",innerHTML:D.messageText},null,8,a1)])):Be("",!0),D.messageType!="user"?(Q(),ae("div",i1,[D.messageText?(Q(),ae("div",{key:0,class:"chat_answer",innerHTML:p(D.messageText)},null,8,n1)):Be("",!0),D.messageUrl&&D.messageUrl.length>0?(Q(),ae("div",s1,[(Q(!0),ae(i0,null,n0(D.messageUrl,(fe,ge)=>(Q(),ae("div",{key:ge},[fe.type=="image"?(Q(),w0(ee,{key:ge,src:fe.content,"zoom-rate":1.2,"max-scale":7,"min-scale":.2,"show-progress":"","initial-index":ge,"preview-src-list":g(D.messageUrl)},null,8,["src","initial-index","preview-src-list"])):Be("",!0),Pt(F("div",l1,[R[8]||(R[8]=F("p",{style:{"white-space":"nowrap"}},"文件：",-1)),F("a",{target:"_blank",style:{color:"#508cff"},href:fe.content},rt(fe.name),9,o1)],512),[[Vt,fe.type=="resource"||fe.type=="docx"]])]))),128))])):Be("",!0)])):Be("",!0),!i.value&&de==r.value.length-1?(Q(),w0(ve,{key:2,style:{width:"20px",color:"#1A61F7"}})):Be("",!0)]))),128))]),l.value?(Q(),w0(ln,{key:0,onSendMsg:S,onBackCommon:R[1]||(R[1]=D=>(l.value=!1,u.value=""))})):Be("",!0),F("div",u1,[u.value?Be("",!0):(Q(),ae("ul",h1,[(Q(!0),ae(i0,null,n0(h.value,(D,de)=>(Q(),ae("li",{key:de,class:"flex_center",onClick:fe=>x(D.id)},rt(D.title),9,c1))),128))])),l.value?Be("",!0):(Q(),ae("div",m1,[F("div",d1,[u.value?(Q(),ae("img",{key:0,src:ya,alt:"",class:"back",onClick:y})):Be("",!0)]),F("div",p1,[be(le,{class:"",autosize:{minRows:1,maxRows:8},rows:1,type:"textarea",resize:"none",id:"text_input",onKeydown:C,modelValue:t.value,"onUpdate:modelValue":R[2]||(R[2]=D=>t.value=D),placeholder:u.value=="4"?"请输入资源名称":u.value=="2"?"请告诉我您想生成的图片内容":"请您输入问题，shift+回车换行，回车发送"},null,8,["modelValue","placeholder"])]),F("div",f1,[F("p",{class:"clear_text",onClick:H},R[9]||(R[9]=[F("img",{src:Fi},null,-1),Gt("清空上下文")])),F("div",v1,[t.value&&i.value&&t.value.trim()!=""?(Q(),ae("img",{key:0,class:"send_img",onClick:E,src:xa})):(Q(),ae("img",g1))])])]))])])])}}},T1=ba(b1,[["__scopeId","data-v-3321350f"]]);export{T1 as default};

<template>
   <div class="sort flex_start">
   
         <div class="time-sort flex_center" @click="exchangeOrderRule">
             <img src="@/assets/pc/choice.png" alt="" v-show="form.orderRule=='ASC'">
              <img src="@/assets/pc/timedesc.png" alt="" v-show="form.orderRule=='DESC'">
             <p>{{form.orderRule=='ASC'?'时间正序':'时间倒序'}}</p>
         </div>
        
        <el-select
        v-model="form.orderBy"
        placeholder="请选择"
        style="width: 160px;margin-right: 10px;"
        clearable
        @change="changeForm"
        >
        <template #prefix>
            <img src="@/assets/pc/choice.png" alt="" class="choice" />
        </template>
        <template #label="{ label, value }">
        <div>
        
            <span style="font-size: 16px;color: #2B2C33;">{{ label }}</span>
        </div>
        </template>
        <el-option
            v-for="item in typeList"
            :key="item.id"
            :label="item.title"
            :value="item.id"
        />
    </el-select>

    <el-date-picker
     v-if="!isshowOwn"
     style="max-width: 246px"
      v-model="form.daterange"
      type="daterange"
      @change="changeForm"
      value-format="YYYY-MM-DD"
      range-separator="至"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
    />
     <el-popover
        class="box-item"
        title=""
        content=""
        placement=""
        :width="100"
      >
      <template #default>
          <ul class="view-sort">
             <li class="flex_start" @click="changeView">
                <img src="@/assets/pc/bcheck.png" alt="" v-show="form.isViewed===''" />
                <img src="@/assets/pc/bchecked.png" alt="" v-show="form.isViewed===false" />
                <p>未阅读</p>
             </li>
             <li class="flex_start" @click="changeLike">
               <img src="@/assets/pc/bcheck.png" alt="" v-show="form.isLike===''" />
                <img src="@/assets/pc/bchecked.png" alt="" v-show="form.isLike===false" />
                <p>未点赞</p>
             </li>
             <li class="flex_start" @click="changeComment">
                <img src="@/assets/pc/bcheck.png" alt="" v-show="form.isCommented===''" />
                <img src="@/assets/pc/bchecked.png" alt="" v-show="form.isCommented===false" />
                <p>未评论</p>
             </li>
          </ul>
        </template>
        <template #reference>
            <el-button style="height:44px;margin-left:10px">高级筛选<el-icon><CaretBottom /></el-icon></el-button>
        </template>
      </el-popover>
  </div>
   
</template>

<script setup>
import {reactive, ref,defineEmits,defineProps} from 'vue'
let form=reactive({
  orderBy:'',
  daterange:'',
  orderRule:'DESC',
  isViewed:'',
  isLike:'',
  isCommented:''
})
let props=defineProps(['isshowOwn'])
let emits=defineEmits('emitForm')
let typeList=ref([
    
    {id:'VIEWS',title:'阅读数排序'}, 
    {id:'LIKES',title:'点赞数排序'}, 
    {id:'SCORE',title:'得星数排序'}, 
    {id:'COMMENTS',title:'评论数排序'}, 
    {id:'EVALUATIONS',title:'评价数排序'},
])
let changeView=()=>{
    if(form.isViewed===""){
       Object.assign(form,{
        isViewed:false
       })
     }else{
       Object.assign(form,{
        isViewed:''
       })
     }
     emits('emitForm',form)
}
let changeLike=()=>{

       if(form.isLike===""){
       Object.assign(form,{
        isLike:false
       })
     }else{
       Object.assign(form,{
         isLike:''
       })
     }
     emits('emitForm',form)
}
let changeComment=()=>{
 if(form.isCommented===""){
       Object.assign(form,{
        isCommented:false
       })
 }else{
       Object.assign(form,{
        isCommented:''
       })
 }
 emits('emitForm',form)
}
let changeForm=()=>{
   emits('emitForm',form)
}
let exchangeOrderRule=()=>{
  if(form.orderRule=='ASC'){
       form.orderRule='DESC'
  }else{
      form.orderRule='ASC'
  }
    emits('emitForm',form)
}
</script>

<style lang="scss" scoped>
  .sort{
   margin-bottom: 10px;
    // ul{
    //     li{
    //         font-size: 16px;
    //         cursor: pointer;
    //         color: #2B2C33;
    //         margin-right: 30px;
    //         &:last-child{
    //             margin-right: 0;
               
    //         } 
    //         &.active{
    //             color: #496EEE;
    //         }
    //     }
    // }
    .time-sort{
      font-size: 16px;
      color: #2B2C33;
      line-height: 22px;
      height: 44px;
     background: #fff;
     padding: 0 12px;
     cursor: pointer;
     margin-right: 10px;
     white-space: nowrap;
      img{
        width: 18px;
        height: auto;
        margin-right: 4px;
      }
    }
  
  }
  .choice{
    width: 18px;
    height: auto;
    margin-right: 10px;
  }
  ::v-deep .el-select{
    height: 44px;
  }
  ::v-deep .el-select__wrapper{
    height: 44px;
  }
  ::v-deep .el-date-editor{
    height: 44px;
  }
  :deep(.el-date-editor .el-range-input){
    font-size: 16px;
  }
  :deep(.el-select__placeholder){
    font-size: 16px;
  }
    .view-sort{
      li{
        cursor: pointer;
        margin-bottom: 16px;
        &:last-child{
          margin-bottom: 0;
        }
        img{
          width: 16px;
          height: auto;
          margin-right: 8px;
        }
        p{
          font-size: 16px;
          color: #2B2C33;
          line-height: 22px;
        }
      }
    }
</style>
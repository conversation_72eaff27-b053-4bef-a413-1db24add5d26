<template>
<div class="tab-box">
  <div class="model-tab flex_between">
        <div class="flex_1  left"></div>
        <div class="middle flex_between">
            <div class="flex_start yxs-logo">
                <img src="@/assets/pc/yxs.png" alt="" />
                 <h3 class="logo">行知研习社</h3>
            </div>
             
             <ul class="flex_around tab-list flex_1" >
                <li :class="['flex_center','flex_column',$route.meta.model===0?'active':'']"  @click="goModel(0)">
                     <img src="@/assets/pc/0-0.png" alt="" v-show="$route.meta.model===0">
                    <img src="@/assets/pc/0.png" alt="" v-show="$route.meta.model!=0">
                    
                    <p :style="$route.meta.model==0?'color:#1E78E6':''">首页</p>
                </li>
                <li :class="['flex_center','flex_column',$route.meta.model==1?'active':'']" id="personcenter"  @click="goModel(1)">
                     <img src="@/assets/pc/1-1.png" alt="" v-show="$route.meta.model==1">
                    <img src="@/assets/pc/1.png" alt="" v-show="$route.meta.model!=1">
                    
                    <p :style="$route.meta.model==1?'color:#1E78E6':''">个人空间</p>
                </li>
                <li :class="['flex_center','flex_column',$route.meta.model==2?'active':'']"  @click="goModel(2)">
                    <img src="@/assets/pc/2.png" alt=""  v-show="$route.meta.model!=2">
                     <img src="@/assets/pc/2-2.png" alt=""  v-show="$route.meta.model==2">
                    <p :style="$route.meta.model==2?'color:#1E78E6':''">日记广场</p>
                </li>
                <li :class="['flex_center','flex_column',$route.meta.model==3?'active':'']" @click="goModel(3)">
                    <img src="@/assets/pc/3.png" alt=""  v-show="$route.meta.model!=3">
                     <img src="@/assets/pc/3-3.png" alt=""  v-show="$route.meta.model==3">
                    <p :style="$route.meta.model==3?'color:#1E78E6':''">主题交流</p>
                </li>
                 <li :class="['flex_center','flex_column',$route.meta.model==4?'active':'']" @click="goModel(4)" id="statistic">
                    <img src="@/assets/pc/p6.png" alt=""  v-show="$route.meta.model!=4">
                     <img src="@/assets/pc/p6-6.png" alt=""  v-show="$route.meta.model==4">
                    <p :style="$route.meta.model==4?'color:#1E78E6':''">数据统计</p>
                </li>
                 <li :class="['flex_center','flex_column',$route.meta.model==5?'active':'']" @click="goModel(5)" id="statistic" v-show="userStore.roles.indexOf('MEMBER')<0">
                    <img src="@/assets/pc/8.png" alt=""  v-show="$route.meta.model!=5">
                     <img src="@/assets/pc/8-8.png" alt=""  v-show="$route.meta.model==5">
                    <p :style="$route.meta.model==5?'color:#1E78E6':''">通知公告</p>
                </li>
             </ul>
            <div></div>
        </div>
        <div class="flex_1  right flex_start">
         
            <!-- <div class="up-notice flex_start" @click="goUpNotice"  >
                <img src="@/assets/pc/fly.png" alt="">
                <p>发布公告</p>
            </div> -->
           
             <el-popover placement="bottom" :width="280" trigger="hover" v-if="userStore.userInfo.user">
                <template #reference>
                    <div class="notice">
                         <img src="@/assets/pc/notice.png" alt=""  style="margin-right:30px;cursor:pointer">
                         <div class="number" v-show="noticeUnView>0">{{noticeUnView}}</div>
                    </div>
                   
                </template>
                <Toast :unViewTableData='unViewTableData' @emitDetail='emitDetail'/>
                </el-popover>
            
            <div class="flex_start">
                <!-- <img src="@/assets/pc/teacher.png" alt=""> -->
                 <!-- <el-avatar :src="userInfo.user?.avatarUrl || DefaultAvatar" class="avatar head" :size="32"  style="margin-right:8px" /> -->
                <p class="identify-tag" v-show="userStore.userInfo?.xingZhiShe">  {{identifyMap[userStore.userInfo?.xingZhiShe?.clubRole]}}</p>
            </div>
          
            <div class="flex_start honner-star">
                 <img src="@/assets/pc/score/star.png" alt=""  v-if="userInfo.xingZhiShe?.isActiveStar" />
                 <img src="@/assets/pc/score/good.png" alt="" v-if="userInfo.xingZhiShe?.isAwesomeAuthor" />
            </div>
        </div>
    </div>
</div>
   <HistoryList :historyListVisible='historyListVisible' @emitDetail='emitDetail'/>
   <HistoryItem :historyItemVisible="historyItemVisible" :noticeDetailRow='noticeDetailRow' />
</template>
<script setup>
import {onMounted, ref,watch} from 'vue'
import {useRouter} from 'vue-router'
import { useUserInfoStore } from "@/store/modules/pc"
import Toast from './toast.vue'
import {noticeNew,noticeDetail,noticeView} from '@/api/pc/index.js'
import {storeToRefs} from 'pinia'
import HistoryList from './historyList.vue'
import HistoryItem from './historyItem.vue'
import DefaultAvatar from "@/assets/layouts/default-avatar.png"
let userStore=useUserInfoStore()
let unViews=ref(0)
let router=useRouter()
let {userInfo,loginVisibleShow,noticeUnView,historyItemForm,historyItemVisible}=storeToRefs(userStore)
let toastShow=ref(false)
let identifyMap={
    'DIRECTOR':'社长',
    'VICE_DIRECTOR':'副社长',
    'TUTOR':'导师',
    'LEADER':'组长',
    'MEMBER':'成员'
}

let unViewTableData=ref([])
let noticeDetailRow=ref({})
let emitDetail=async(row,type)=>{
    // console.log(row,type)
    historyItemForm.value=type
    // historyItemVisible.value=true
    let res=await noticeDetail({
        clubNoticeId:row.noticeId
    })
    if(res){
        noticeDetailRow.value=res.data
        let res1=await noticeView({
            clubNoticeId:row.noticeId
        })
        if(res1){
            getnoticeUnViewCount()
        }
    }
}
let goModel=(type)=>{ 
    if(userInfo.value.user){
             if(type==1){
                router.push('/actResearchAssociation/personcenter')
            }else if(type==2){
                router.push('/actResearchAssociation/square/index')
            }else if(type==3){
                router.push('/actResearchAssociation/theme')
            }else if(type==4){
                if(userStore.roles.indexOf('TUTOR')>-1||userStore.roles.indexOf('LEADER')>-1){
                    router.push('/actResearchAssociation/statistic/diary')
                }else{
                    router.push('/actResearchAssociation/statistic/joiner')
                }
                
            }else if(type==5){
               router.push('/actResearchAssociation/notice')
            }else{
                router.push('/actResearchAssociation/home')
            }
    }else{
      loginVisibleShow.value=true
    }
   
}
let goUpNotice=()=>{
     router.push('/actResearchAssociation/notice/upload')
}
let getnoticeUnViewCount=async()=>{
   let res=await noticeNew()
   if(res){
    // console.log(res)
    noticeUnView.value=res.data.number
    unViewTableData.value=res.data.entries
   }
}
onMounted(()=>{
    getnoticeUnViewCount()
    
    if(userStore.roles.indexOf('TUTOR')>-1||userStore.roles.indexOf('DIRECTOR')>-1||userStore.roles.indexOf('VICE_DIRECTOR')>-1||userStore.roles.indexOf('LEADER')>-1){
           document.getElementById('statistic').style.display='flex'
    }else{
         document.getElementById('statistic').style.display='none'
    } 
})
watch(()=>userStore.userInfo,(newval)=>{
    // if(userStore.roles.indexOf('TUTOR')>-1){
    //        document.getElementById('personcenter').style.display='none'
    // }else{
    //      document.getElementById('personcenter').style.display='flex'
    // }

      if(userStore.roles.indexOf('TUTOR')>-1||userStore.roles.indexOf('DIRECTOR')>-1||userStore.roles.indexOf('VICE_DIRECTOR')>-1||userStore.roles.indexOf('LEADER')>-1){
           document.getElementById('statistic').style.display='flex'
    }else{
         document.getElementById('statistic').style.display='none'
    }
},{
    deep:true
})
</script>
<style lang='scss' scoped>
.tab-box{
 background: #fff;
}
.model-tab{
    width: 79%;
    margin: 0 auto;
    .middle{
        width: 100%;
        .yxs-logo{
            img{
                width: 20px;
                height: auto;
                margin-right: 4px;
            }
          .logo{
                font-size: 22px;
                color: #2B2C33;
                font-weight: bold;
            } 
        }
      
       .tab-list{
        padding: 11px 0 0 0;
        // width: 680px;
        margin: 0 50px;
        li{
            font-size: 12px;
            cursor: pointer;
            color: #94959C;
            // margin-right: 130px;
            &:last-child{
                margin-right: 0;
            }
            &::after{
                content: '';
                display: block;
                height: 4px;
                width: 100%;
                background: #fff;
                // background: linear-gradient( 270deg, #496EEE 0%, #0AC1FA 100%);
                margin-top: 6px;
            }
            img{
                width: 18px;
                height: auto;
                margin-bottom: 6px
            }
        }
        .active{
              &::after{
                content: '';
                display: block;
                height: 4px;
                width: 100%;
                // background: #fff;
                background: linear-gradient( 270deg, #496EEE 0%, #0AC1FA 100%);
                margin-top: 6px;
            }
        }
       }
    }
    .right{
        font-size: 16px;
       color: #2B2C33;
       font-weight: bold;
       position: relative;
       white-space:nowrap;
        img{
            width: 32px;
            height: auto;
            margin-right: 8px;
        }
      
        .up-notice{
            font-size: 14px;
            color: #FFFFFF;
            cursor: pointer;
            background: linear-gradient( 270deg, #508CFF 0%, #535DFF 100%);
             border-radius: 12px;
            padding: 0 10px;
             height: 24px;
             cursor: pointer;
             margin-right: 32px;
             white-space: nowrap;
             img{
                width: 14px;
                height: auto;
             }
        }
    }
}
@media screen and (max-width:1200px) {
    .model-tab .middle .logo{
        font-size:18px;
    }
    .model-tab .middle .tab-list li{
      //  margin-right: 80px;
    }
}
.notice{
    position: relative;
    
    .number{
        width: 16px;
        height: 16px;
        color: #fff;
        background: #E72C4A;
        position: absolute;
        top:0;
        left:24px;
        border-radius: 200px;
        font-size: 11px;
        line-height: 16px;
        text-align: center;
    }
}
.identify-tag{
    padding: 4px 12px;
    background: #C197FF;
    border-radius: 14px;
    line-height: 14px;
    font-size: 12px;
    color: #fff;
}
.honner-star{
    margin-left: 12px;
    img{
        width: 24px;
        height: auto;
    }
}
</style>
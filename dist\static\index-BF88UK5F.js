import{_ as ee,a as te,b as le,c as ae}from"./state-grey-dChy74ls.js";import{a as z,h as se,d as oe}from"./element-BwwoSxMX.js";import{t as ne,a as ie,b as re,c as de}from"./jky-em_s15p3.js";import{r as _,h as Q,ag as u,z as m,A as d,B as i,J as $,P as X,a6 as O,E as W,O as P,R as K,Q as l,I as s,M as v,aE as ue,az as pe,H as I,K as b,u as ce}from"./vue-CelzWiMA.js";import{_ as me}from"./del-file-DWVE5eI3.js";import{b as _e}from"./pc-upload-DDnPogYD.js";import{d as fe}from"./download-BRAgUBVe.js";import{_ as Z}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DCRFd5B4.js";import"./vant-C4eZMtet.js";import"./FileSaver.min-CRzxiuUX.js";const ve="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAAoCAYAAACb3CikAAAAAXNSR0IArs4c6QAAA41JREFUWEftmG2IVFUYx3/njuayazOz9sHWVbTYcgkScz9IH9SUEiLdXZMIlEDHyRc2QhBXIdBBEJKdstpeFoIwSPoQEX0JtBZFitANaoWgCDXat9nZmdnZcXfu7rzcY3dkdXb3jvfc6c6HYO+HYeD8n+f87nme+7/3HEHR1XxGrtY8HAc2A8uAhcXjKv8TkUSmSsv0N6ys2vlxW+1vKjGmRpg/oZDUems4YghOCahSDbbSxQdieBdMsOeVOuPK9clnO4O+ayr5CiCtYdkOnFEJsNNMg7z1xkoGhjOy+/rUhq6g9ye7ONHyrnxaGPQAi+zEKuPFIKY+MpKRV3rSW947VHv5QfGiJSy/ELBbZRIVzWyQAkwsKy/1ZrZ2Bhb/UCqH2BGW/RLqVSZR0cQHY1TLcU4eXjVDHo1n5ffX9G0fHfR9Z5VHtIZlDvCoTKKiSUZH0eNxzp5omCMfSWRl9y+TOz543fvt7EETRKpMoKrRx3X6/vinsCIr6ua2XXw0Jy9cnXj1wwP+r4pzug5i3tWt3hs0Pr6Iw4HllvzxZJaLP+q7Ott8X04LXAcxE6diY0T+jvDay0vZtN5vCZNI5uju0fecDXo/NwWWIKa5NDwKvmq1guQN+HMQ0pn7+sitIdLJ22x//hFe3LQEzVOwrBlXMpXjws/6/vf3ez+1BHnpGQhuUYOYVvXF4c1zRTFSEu0bwWzeVfVVvLCxlicfq8b38AI07b5uLJXjYs9UwBKkuQn2PucMZHAU2j6bG6NP6IwOJUinJjAM6+ei2r94xBLEo8FTy8HvoDS/98NYujS8NCS5bI58Lj9HpGlaviLN6mwt76rnQSrurOWU5YE+8kQd+GvKTTsrTsLNKMRul85n7SPrIGh+LLp4mU/u3k8gpVsndc1H7JhNkH1dkCzxiFuCaMKZj9hBmDZ2cxiGkg5LY5e4EuPzPvI/8xEBq5epv/RUesZs1mjKYbNuWwf7KuAjga7Sb2jLZt3eBAGH3yN2q2L6iGMQ00ca690tzY1hGB5zWBq7u6vE+LyPWPmIq1vOMsuWd30TXg6IgAHR+o48j2RXOQncipFwXjR3yDWaKBzUPORWYod5pvIaTYV9YEuHPCYEbztM4IpcSNq/OSo67h3m/VrD0X+P9kL/9TBPlU7CpICTa8cJh0LCmLEz3hmWjXk4jmAzkrpyjjdtQLIIhpBc8hic/rpd/DWtvwMl0YMQdii72wAAAABJRU5ErkJggg==",ge={class:"publish-add-main"},ke={class:"flex_start tab-ul text_16"},ye=["onClick"],be={class:"upload-file-box"},we={class:"flex_start text_16 blue",style:{width:"calc(100% - 24px)"}},xe={class:"ellipsis1 file-name"},he=["onClick"],Ce={class:"mb_24"},Ae={class:"flex_between mb_12"},Ne={class:"flex_end"},Te={class:"flex_end"},Ve={__name:"Add",props:{id:0},emits:["close"],setup(j,{emit:F}){const w=function(t={}){this.taskName=t.taskName||"",this.applicationDeadline=t.applicationDeadline||"",this.remarkContent=t.remarkContent||"",this.taskFiles=t.taskFiles||[],this.taskTargets=t.taskTargets||[]},E=_([{id:1,title:"基本信息"},{id:2,title:"指标设置"}]),c=_(1),h=j,C=_(null),k=_({taskName:[{required:!0,message:"请输入课题任务名称",trigger:"blur"}],applicationDeadline:[{required:!0,message:"请选择申请截止时间",trigger:"blur"}]}),A=_([]),U=t=>t<new Date-864e5,N=F,r=_(new w({}));_([]);const D=t=>{c.value=t.id};function B(t,e){return new URL(t).searchParams.get(e)}const S=t=>{var e=t.lastIndexOf(".");return e!=-1&&e!=0&&e+1!=t.length?t.substr(e+1):""},n=t=>{let e=S(t.name),f=["doc","docx","pdf","xls","xlsx","txt"];if(!f.includes(e))return z.warning("请上传以下格式的文件："+f.join("、")),!1;const g=10*1024*1024;return t.size>g?(z.warning("文件大小不能超过10MB"),!1):!0},o=t=>{_e(t,e=>{r.value.taskFiles.push({url:e,name:t.file.name,fileId:B(e,"fileId")}),r.value.taskFiles})},R=async t=>{t.file;const e=new FormData;e.append("file",t.file);let f=await ie(e);f&&(r.value.taskTargets=f.data)},T=t=>{r.value.taskFiles.splice(t,1)},M=async()=>{let t=await ne(h.id);t&&(r.value=new w(t.data),r.value.taskFiles.forEach(e=>{e.name=e.fileName}))},Y=async()=>{fe.excel("/jky/task-target","模板")},y=async()=>{C.value&&await C.value.validate(async(t,e)=>{if(t){r.value;return}else{if(c.value,c.value==1)return;z.error("切换到基本信息页面，填写必填信息")}})};return Q(()=>{h.id&&h.id!=0&&M()}),(t,e)=>{const f=u("el-input"),g=u("el-form-item"),H=u("el-date-picker"),a=u("el-button"),x=u("el-upload"),G=u("el-form"),L=u("el-table-column"),q=u("el-input-number"),J=u("el-table");return d(),m("div",ge,[i("ul",ke,[(d(!0),m(X,null,O(E.value,p=>(d(),m("li",{key:p.id,class:W(c.value==p.id?"active-tab":""),onClick:V=>D(p)},P(p.title),11,ye))),128))]),$(l(G,{model:r.value,ref_key:"addRef",ref:C,rules:k.value,class:"search_form"},{default:s(()=>[l(g,{label:"课题任务名称",prop:"taskName",style:{"margin-right":"16px"}},{default:s(()=>[l(f,{modelValue:r.value.taskName,"onUpdate:modelValue":e[0]||(e[0]=p=>r.value.taskName=p),class:"input_48",placeholder:"请输入"},null,8,["modelValue"])]),_:1}),l(g,{label:"申请截止时间",prop:"applicationDeadline",style:{"margin-right":"16px"}},{default:s(()=>[l(H,{modelValue:r.value.applicationDeadline,"onUpdate:modelValue":e[1]||(e[1]=p=>r.value.applicationDeadline=p),class:"my-date-picker","disabled-date":U,type:"date","value-format":"YYYY-MM-DD",placeholder:"选择日期"},null,8,["modelValue"])]),_:1}),l(g,{label:"备注",style:{"margin-right":"16px"}},{default:s(()=>[l(f,{modelValue:r.value.remarkContent,"onUpdate:modelValue":e[2]||(e[2]=p=>r.value.remarkContent=p),type:"textarea",class:"my-textarea2",rows:4,placeholder:"请输入"},null,8,["modelValue"])]),_:1}),l(g,{label:"材料清单",style:{"margin-right":"16px"},prop:"taskFiles"},{default:s(()=>[e[6]||(e[6]=i("p",{class:"text_12 grey upload-tip"},"（支持上传pdf、word、excel，单个文件大小不超过10MB）",-1)),i("div",be,[(d(!0),m(X,null,O(r.value.taskFiles,(p,V)=>(d(),m("div",{class:"flex_between mb_16",key:V,style:{width:"100%"}},[i("p",we,[e[4]||(e[4]=i("img",{src:ve,class:"file-icon",alt:""},null,-1)),i("span",xe,P(p.name),1)]),i("img",{onClick:je=>T(V),src:me,class:"file-close-icon pointer",alt:""},null,8,he)]))),128))]),l(x,{"file-list":r.value.taskFiles,action:"Fake Action","show-file-list":"false",multiple:"","http-request":o,"on-preview":t.handlePictureCardPreview,"on-remove":t.handleRemove,"before-upload":n},{default:s(()=>[l(a,{type:"plain",class:"upload-plain"},{default:s(()=>e[5]||(e[5]=[v("本地上传")])),_:1})]),_:1},8,["file-list","on-preview","on-remove"])]),_:1})]),_:1},8,["model","rules"]),[[K,c.value==1]]),$(i("div",Ce,[i("div",Ae,[e[8]||(e[8]=i("p",{class:"text_16_bold"},"单位列表",-1)),i("div",Ne,[l(x,{"file-list":A.value,action:"Fake Action","show-file-list":"false",multiple:"","http-request":R,"on-preview":t.handlePictureCardPreview,"on-remove":t.handleRemove,"before-upload":n},{default:s(()=>[l(a,{type:"primary",class:"green_btn btn_80_32"},{default:s(()=>e[7]||(e[7]=[v("导入")])),_:1})]),_:1},8,["file-list","on-preview","on-remove"]),i("p",{class:"download-btn",onClick:Y},"模板下载")])]),l(J,{data:r.value.taskTargets,style:{width:"100%","margin-top":"16px"},"header-cell-style":{color:"#94959C",fontSize:"16px",background:"#F8F9FF"}},{default:s(()=>[l(L,{prop:"unitCode",label:"代码","show-overflow-tooltip":"",width:"80"}),l(L,{prop:"unitName",label:"单位名称"}),l(L,{prop:"targetNumber",label:"指标总数",fixed:"right"},{default:s(p=>[l(q,{modelValue:p.row.targetNumber,"onUpdate:modelValue":V=>p.row.targetNumber=V,min:1,precision:0,class:"input_40"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data"])],512),[[K,c.value==2]]),i("div",Te,[l(a,{class:"plain-btn btn_96_48",type:"plain",onClick:e[3]||(e[3]=p=>N("close"))},{default:s(()=>e[9]||(e[9]=[v("取消")])),_:1}),l(a,{class:"primary-btn btn_96_48",type:"primary",onClick:y},{default:s(()=>e[10]||(e[10]=[v("发布")])),_:1})])])}}},Ie=Z(Ve,[["__scopeId","data-v-ba88ad64"]]),ze={class:"jky-main"},Fe={class:"radius-box"},Ee={class:"flex_end",style:{width:"100%"}},Ue={class:"radius-box"},De={class:"flex_between"},Be={class:"flex_start"},Se={key:0,src:ee,class:"state-icon",alt:""},Re={key:1,src:te,class:"state-icon",alt:""},Me={key:2,src:le,class:"state-icon",alt:""},Ye={key:3,src:ae,class:"state-icon",alt:""},He={class:"flex_start",style:{height:"100%"}},Le={key:1,class:"table-line"},Xe={key:3,class:"table-line"},Oe={key:5,class:"table-line"},Pe={__name:"index",setup(j){const F=ue();pe();const w=_(0),E=function(n={}){this.taskTitle=n.taskTitle||"",this.reviewUserName=n.reviewUserName||"",this.name=n.name||"",this.pageNum=n.pageNum||1,this.pageSize=n.pageSize||10},c=_(new E({})),h=_(0),C=_([{state:1,taskTitle:"xxx课题研究",createTime:"2025-02-23",deadline:"2025-04-09"},{state:2,taskTitle:"xxx课题研究",createTime:"2025-02-23",deadline:"2025-04-09"},{state:3,taskTitle:"xxx课题研究",createTime:"2025-02-23",deadline:"2025-04-09"},{state:4,taskTitle:"xxx课题研究",createTime:"2025-02-23",deadline:"2025-04-09"},{state:1,taskTitle:"xxx课题研究",createTime:"2025-02-23",deadline:"2025-04-09"}]),k=_(!1);_(!1),_(!1);const A=async()=>{let n=c.value;await re(n)},U=()=>{N()},N=()=>{c.value.pageNum=1,A()},r=n=>{F.push(`/jky/lxpj/detail/${n.taskMasterId}`)},D=n=>{oe.confirm("确认删除该条任务吗?","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{await de(n.taskMasterId)&&(z.success("删除成功"),N())})},B=n=>{c.value.pageNum=n,A()},S=n=>{k.value=!0,w.value=n.taskMasterId};return Q(()=>{A()}),(n,o)=>{const R=u("el-input"),T=u("el-form-item"),M=u("el-option"),Y=u("el-select"),y=u("el-button"),t=u("el-form"),e=u("el-table-column"),f=u("el-table"),g=u("el-pagination"),H=u("el-dialog");return d(),m("div",ze,[i("div",Fe,[l(t,{inline:"",model:c.value,class:"search_form flex_start"},{default:s(()=>[l(T,{label:"任务名称",style:{"margin-right":"16px",width:"calc(33% - 16px)"}},{default:s(()=>[l(R,{modelValue:c.value.taskTitle,"onUpdate:modelValue":o[0]||(o[0]=a=>c.value.taskTitle=a),onInput:n.changeUserName,class:"input_40",clearable:"",placeholder:"请输入"},null,8,["modelValue","onInput"])]),_:1}),l(T,{label:"任务状态",style:{"margin-right":"16px",width:"calc(33% - 16px)"}},{default:s(()=>[l(Y,{class:"select_40",placeholder:"请选择",onChange:n.changeExtend},{default:s(()=>[(d(!0),m(X,null,O(n.expert_select,a=>(d(),I(M,{key:a.value,value:a.value,label:a.label},null,8,["value","label"]))),128))]),_:1},8,["onChange"])]),_:1}),l(T,{label:"操作",class:"hidden-label",style:{width:"132px","margin-right":"0"}},{default:s(()=>[i("div",Ee,[l(y,{class:"primary-btn btn_132_40",type:"primary",onClick:U},{default:s(()=>o[4]||(o[4]=[v("查询")])),_:1})])]),_:1})]),_:1},8,["model"])]),i("div",Ue,[i("div",De,[o[6]||(o[6]=i("p",{class:"text_20_bold"},"任务列表",-1)),l(y,{class:"primary-btn btn_132_40",type:"primary",icon:ce(se),onClick:o[1]||(o[1]=a=>(k.value=!0,w.value=0))},{default:s(()=>o[5]||(o[5]=[v("新建任务")])),_:1},8,["icon"])]),l(f,{data:C.value,style:{width:"100%","margin-top":"16px"},"header-cell-style":{color:"#94959C",fontSize:"16px",background:"#F9F9F9"}},{default:s(()=>[l(e,{type:"index",label:"序号",width:"80"}),l(e,{prop:"taskTitle",label:"课题任务","show-overflow-tooltip":""}),l(e,{prop:"createTime",label:"创建时间"}),l(e,{prop:"deadline",label:"申请截止时间"}),l(e,{prop:"deadline",label:"待我评鉴"}),l(e,{prop:"reviewUserNames",label:"任务状态"},{default:s(a=>[i("div",Be,[a.row.state==1?(d(),m("img",Se)):a.row.state==2?(d(),m("img",Re)):a.row.state==3?(d(),m("img",Me)):(d(),m("img",Ye)),i("span",{class:W(a.row.state==1?"blue":a.row.state==2?"orange":a.row.state==3?"green":"grey")},P(a.row.state==1?"申报中":a.row.state==2?"待分配":a.row.state==3?"评鉴中":"已结束"),3)])]),_:1}),l(e,{label:"操作",width:"200"},{default:s(a=>[i("div",He,[a.row.state==1?(d(),I(y,{key:0,type:"primary",link:"",style:{"font-size":"16px",color:"#E72C4A","font-weight":"400"},onClick:x=>D(a.row)},{default:s(()=>o[7]||(o[7]=[v("删除")])),_:2},1032,["onClick"])):b("",!0),a.row.state==1?(d(),m("p",Le)):b("",!0),a.row.state==1?(d(),I(y,{key:2,type:"primary",link:"",style:{"font-size":"16px","font-weight":"400"},onClick:x=>S(a.row)},{default:s(()=>o[8]||(o[8]=[v("编辑")])),_:2},1032,["onClick"])):b("",!0),a.row.state==1?(d(),m("p",Xe)):b("",!0),a.row.state!=1?(d(),I(y,{key:4,type:"primary",link:"",style:{"font-size":"16px","font-weight":"400"},onClick:x=>r(a.row)},{default:s(()=>o[9]||(o[9]=[v("任务信息")])),_:2},1032,["onClick"])):b("",!0),a.row.state!=1?(d(),m("p",Oe)):b("",!0),l(y,{type:"primary",link:"",style:{"font-size":"16px","font-weight":"400"},onClick:x=>r(a.row)},{default:s(()=>o[10]||(o[10]=[v("详情")])),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"]),l(g,{background:"",layout:"total,prev, pager, next",total:h.value,class:"mt-4","current-page":c.value.pageNum,"page-size":c.value.pageSize,onCurrentChange:B},null,8,["total","current-page","page-size"])]),k.value?(d(),I(H,{key:0,modelValue:k.value,"onUpdate:modelValue":o[3]||(o[3]=a=>k.value=a),title:"新建任务",width:"456px",center:"",class:"my-dialog"},{default:s(()=>[l(Ie,{id:w.value,onClose:o[2]||(o[2]=a=>(k.value=!1,N()))},null,8,["id"])]),_:1},8,["modelValue"])):b("",!0)])}}},at=Z(Pe,[["__scopeId","data-v-c55321a7"]]);export{at as default};

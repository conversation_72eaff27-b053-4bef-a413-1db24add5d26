import{az as n,h as c,z as a,A as _}from"./vue-CelzWiMA.js";import{k as i,a as u}from"./index-DCRFd5B4.js";import{_ as d}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./element-BwwoSxMX.js";import"./vant-C4eZMtet.js";const l={class:"message"},m=750,p={__name:"wxland",setup(f){let e=n(),o=i(),t=u();const s=()=>document.body.getBoundingClientRect().width-1<m;return c(()=>{o.resetToken(),s?o.wxLoginByCode(e.query.code):t.wxLoginByCode(e.query.code)}),(r,x)=>(_(),a("div",l))}},M=d(p,[["__scopeId","data-v-6e04ca54"]]);export{M as default};

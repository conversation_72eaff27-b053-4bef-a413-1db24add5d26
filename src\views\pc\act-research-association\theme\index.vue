<template>
    <div class="content-box flex_start_0 mt10">
       
       <div class="left">
        
          <ThemeLeft @emitThemes='emitThemes' />
       </div>
       <div class="flex_1 right">
          <div class="flex_between" style="margin-bottom:10px">
                <div></div>
                 <div class="ipt flex_start">
                      <input type="text" v-model="searchForm.keyword" placeholder="搜索内容" @input="changeKeyWord"  @keyup="keyupKeyWord" />
                 </div>
                 <div >
                    <button class="upload flex_center" @click="goupload" v-pcpermission='["LEADER","TUTOR","DIRECTOR","VICE_DIRECTOR"]'>
                        <img src="@/assets/pc/write.png" alt="">
                        发布讨论  
                    </button>
                 </div>
                
          </div>
            <el-empty description="暂无数据" v-show="total==0" />
             <div class="themes" style="overflow:auto"  ref="themeRef">
               <ThemeItem v-for='item in themes' :userInfo='userInfo.user' :key="item.id" :clubPostType='searchForm.clubPostType' :themeItem='item' @emitDelTheme='emitDelTheme' @emitScrollTop='emitScrollTop'/>
            </div>
            <el-pagination background v-show="total!=0"  style="margin-bottom:10px" layout="prev, pager, next" :page-size='searchForm.pageSize' :total="total" @current-change='handlelCurrentChange' />
       </div>
       
    </div>
</template>
<script setup>
import {onMounted, reactive, ref,onActivated} from 'vue'
import {themeList,themeDel} from '@/api/pc/index.js'

import FriendLink from '@/views/pc/component/act-research-association/friendLink.vue'
import ThemeItem from '@/views/pc/component/act-research-association/themeItem.vue'
import ThemeLeft from '@/views/pc/component/act-research-association/themeLeft.vue'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'
import {useRouter} from 'vue-router'
import { ElMessage } from 'element-plus'
let userInfoStore=useUserInfoStoreHook()
let {themeScrollTop,userInfo}=storeToRefs(userInfoStore)
let router=useRouter()
let activeIndex=ref(1)
let searchForm=reactive({
    pageSize:10,
    pageNum:1,
    clubPostType:'',
    keyword:'',
    clubTopicName:''
})
let total=ref(0)
let loading=false
let themeRef=ref()
let themes=ref([])
let maxPage=0
// let load=()=>{
    
//       if(searchForm.pageNum<maxPage){
//         Object.assign(searchForm,{
//            pageNum:searchForm.pageNum+1
//         })
//          getThemeList()
//     }
// }
let emitDelTheme=async(row)=>{
  console.log(row)
  let res=await themeDel({
    clubPostId:row.id
  })
  if(res){
    ElMessage.success('删除成功')
    getThemeList()
  }
}
function debounce(func, wait = 300, immediate = false) {
  let timeout;
  return function(...args) {
    const context = this;
    if (timeout) clearTimeout(timeout);
    if (immediate) {
      // 立即执行，若后续触发则取消前一次
      const callNow = !timeout;
      timeout = setTimeout(() => {
        timeout = null;
      }, wait);
      if (callNow) func.apply(context, args);
    } else {
      timeout = setTimeout(() => {
        func.apply(context, args);
      }, wait);
    }
  };
}

let changeKeyWordAction=()=>{
  themes.value=[]
     Object.assign(searchForm,{
           pageNum:1
        })
    getThemeList()
}
let keyupKeyWord=(e)=>{
   
    if(e.code=='Enter'){
        themes.value=[]
        getThemeList()
    }
}
let changeKeyWord= debounce(changeKeyWordAction,800)
let handlelCurrentChange=(val)=>{
    themeRef.value.scrollTop=0
    themeScrollTop.value=0
    Object.assign(searchForm,{
        pageNum:val
    })
  getThemeList()
}
let getThemeList=async()=>{
    let params={
         pageSize:searchForm.pageSize,
            pageNum:searchForm.pageNum,
            postType:searchForm.clubPostType,
            keyword:searchForm.keyword,
            clubTopicName:searchForm.clubTopicName
       }
    let res=await themeList(params)
    if(res){
        // console.log(res)
        themes.value=res.data.list
        maxPage=res.data.pages
         total.value=res.data.total
    }
}
let emitThemes=async(id,name)=>{
    // console.log(val)
//    themes.value=[]
    Object.assign(searchForm,{
        clubPostType:id,
        clubTopicName:name,
        pageNum:1
    })
      let params={
           pageSize:searchForm.pageSize,
            pageNum:searchForm.pageNum,
            postType:searchForm.clubPostType,
            keyword:searchForm.keyword,
            clubTopicName:searchForm.clubTopicName
       }
     let res=await themeList(params)
    if(res){
        // console.log(res)
        themes.value=res.data.list
        maxPage=res.data.pages
        total.value=res.data.total
    }
}
let emitScrollTop=()=>{

  themeScrollTop=themeRef.value.scrollTop
}
let goupload=()=>{
   router.push('/actResearchAssociation/theme/upload?clubPostType='+searchForm.clubPostType)
}
onMounted(()=>{
//  themes.value=[]
})
router.beforeEach((to,from)=>{
   
    if(from.name=='ActResearchAssociation-theme-upload'){
        //  themeRef.value.scrollTop=0
           Object.assign(searchForm,{
            pageNum:1
          })
         themes.value=[]
         getThemeList()
    }
})
onActivated(()=>{
  
    // console.log(themeScrollTop,'themeScrollTop')
    themeRef.value.scrollTop=themeScrollTop
    // if(searchForm.clubPostType){
    //     themes.value=[]
    //     Object.assign(searchForm,{
    //        pageNum:1
    //     })
    //    getThemeList()
    // }
})
</script>
<style lang="scss" scoped>

.mt10{
    margin-top: 10px;
}
.left{
    width: 180px;
}
.right{
    margin-left: 10px;
    .ipt{
        width: 400px;
        height: 40px;
        background: #FFFFFF;
        border-radius: 20px;
        padding: 2px 20px;
        input{
            border:none;
            outline: none;
            width: 100%;
        }
        
    }
   button{
    width: 120px;
height: 40px;
background: #508CFF;
border-radius: 20px;
border:none;
color: #fff;
margin-left: 10px;cursor: pointer;
line-height:40px;
img{
    width: 12px;
    height: auto;
    margin-right: 8px;
}
   }
   .themes{
    //   max-height: calc(100vh - 200px);
      margin-bottom: 10px;
   }
}
</style>
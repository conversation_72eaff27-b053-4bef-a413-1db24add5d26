import { request } from "@/utils/service"

// 全部
export function getAllDiaryListApi(params) {
  return request({
    url: "xzs/club-record/public",
    method: "get",
    params
  })
}

// 已读
export function getReadDiaryListApi(params) {
  return request({
    url: "xzs/club-record/public/view",
    method: "get",
    params
  })
}

// 未读
export function getUnreadDiaryListApi(params) {
  return request({
    url: "xzs/club-record/public/unview",
    method: "get",
    params
  })
}

// 详情
export function getDiaryDetailApi(recordId) {
  return request({
    url: `xzs/club-record/public/${recordId}`,
    method: "get"
  })
}

// 隐藏日记
export function hideDiaryApi(recordId, data) {
  return request({
    url: `xzs/club-record/${recordId}/hide`,
    method: "post",
    data
  })
}

// 删除日记
export function deleteDiaryApi(recordId) {
  return request({
    url: `xzs/club-record/${recordId}`,
    method: "delete"
  })
}

// 获取组（我的）
export function getMyGroupApi(params) {
  return request({
    url: "xzs/club-group/me",
    method: "get",
    params
  })
}

// 推荐日记
export function getRecommendDiaryApi(params) {
  return request({
    url: "xzs/club-record/public/recommended-record",
    method: "get",
    params
  })
}

// 社长日记
export function getDirectorDiaryApi(params) {
  return request({
    url: "xzs/club-record/public/director-user-record",
    method: "get",
    params
  })
}

// 社长推荐
export function getDirectorRecommendApi(params) {
  return request({
    url: "xzs/club-record/public/director-recommended-record",
    method: "get",
    params
  })
}

// 导师日记
export function getTutorDiaryApi(params) {
  return request({
    url: "xzs/club-record/public/tutor-user-record",
    method: "get",
    params
  })
}

// 导师推荐
export function getTutorRecommendApi(params) {
  return request({
    url: "xzs/club-record/public/tutor-recommended-record",
    method: "get",
    params
  })
}

export function getWxMpRecommendApi(params) {
  return request({
    url: "xzs/club-record/public/wx-mp-recommended-record",
    method: "get",
    params
  })
}

// 全部 - 日记位置
export function getAllDiaryLocationApi(params) {
  return request({
    url: "xzs/club-record/public/location",
    method: "get",
    params
  })
}

// 社长日记 - 日记位置
export function getDirectorDiaryLocationApi(params) {
  return request({
    url: "xzs/club-record/public/director-user-record/location",
    method: "get",
    params
  })
}

// 社长推荐 - 日记位置
export function getDirectorRecommendDiaryLocationApi(params) {
  return request({
    url: "xzs/club-record/public/director-recommended-record/location",
    method: "get",
    params
  })
}

// 导师日记 - 日记位置
export function getTutorDiaryLocationApi(params) {
  return request({
    url: "xzs/club-record/public/tutor-user-record/location",
    method: "get",
    params
  })
}

// 导师推荐 - 日记位置
export function getTutorRecommendDiaryLocationApi(params) {
  return request({
    url: "xzs/club-record/public/tutor-recommended-record/location",
    method: "get",
    params
  })
}

// 公众号推荐 - 日记位置
export function getWxMpRecommendDiaryLocationApi(params) {
  return request({
    url: "xzs/club-record/public/wx-mp-recommended-record/location",
    method: "get",
    params
  })
}

// 特点用户日记
export function getUserDiaryApi(userId, params) {
  return request({
    url: `xzs/club-record/public/user/${userId}/record`,
    method: "get",
    params
  })
}

// 特点用户日记 - 日记位置
export function getUserDiaryLocationApi(userId, params) {
  return request({
    url: `xzs/club-record/public/user/${userId}/record/location`,
    method: "get",
    params
  })
}

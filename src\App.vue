<script setup>
import zhCn from "element-plus/es/locale/lang/zh-cn"
// import { onMounted, onUnmounted } from 'vue'
// import { setupRouteSwitcher } from '@/router'

// let cleanup

// onMounted(() => {
//   cleanup = setupRouteSwitcher()
// })

// onUnmounted(() => {
//   if (cleanup) cleanup()
// })
</script>

<template>
  <el-config-provider :locale="zhCn">
    <router-view />
  </el-config-provider>
</template>


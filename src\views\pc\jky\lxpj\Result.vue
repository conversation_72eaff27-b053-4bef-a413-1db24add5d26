<template>
    <div class="result-main">
        <ul class="flex_start tab-ul text_16">
            <li v-for="item in tab_list" :key="item.id" :class="current_tab === item.id? 'active-tab' : ''" @click="current_tab = item.id">{{item.title}}</li>
        </ul>
        <div v-if="current_tab == 1" class="flex_start flex_wrap search-box">
            <el-select class="select_40 mr_24" v-model="search_form.sch" placeholder="全部组别" @change="changeExtend">
                <el-option v-for="item in stage_list" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
            <el-select class="select_40 mr_24" v-model="search_form.sch" placeholder="所在单位" @change="changeExtend">
                <el-option v-for="item in stage_list" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
            <el-select class="select_40 mr_24" v-model="search_form.sch" placeholder="全部学科" @change="changeExtend">
                <el-option v-for="item in stage_list" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
            <el-select class="select_40 mr_24" v-model="search_form.sch" placeholder="全部学段" @change="changeExtend">
                <el-option v-for="item in stage_list" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
            <el-select class="select_40 mr_24" v-model="search_form.sch" placeholder="是否青年专项" @change="changeExtend">
                <el-option v-for="item in stage_list" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
            <el-input v-model="search_form.taskContent" class="input_40" placeholder="请输入得分"></el-input>
            <span class="mlr_5 grey mb_16"> — </span>
            <el-input v-model="search_form.taskContent" class="input_40" placeholder="请输入得分"></el-input>
            <el-button type="primary" class="btn_132_40 mb_16 ml_24 green_btn" @click="search">导出成绩单</el-button>
            <el-button type="primary" class="btn_132_40 mb_16 primary-btn" @click="search">导出申请表</el-button>
        </div>
        <div v-else class="flex_between">
            <el-input v-model="search_form.input" class="input_40" style="width:192px;" placeholder="请输入通过率">
                <template #append>%</template>
            </el-input>
            <div class="flex_start">
                <el-button type="primary" class="btn_132_40 mb_16 primary-btn" @click="search">生成通过名单</el-button>
                <el-button type="primary" class="btn_132_40 mb_16 ml_24 green_btn" @click="search">导出成绩单</el-button>
            </div>
        </div>
         <el-table :data="material_list" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F8F9FF"}'>
            <el-table-column prop="taskTitle" label="组内序号" min-width="60" show-overflow-tooltip></el-table-column>
            <el-table-column prop="taskTitle" label="学段" show-overflow-tooltip></el-table-column>
            <el-table-column prop="createTime" label="学科" show-overflow-tooltip />
            <el-table-column prop="deadline" label="课题名称" show-overflow-tooltip />
            <el-table-column prop="deadline" label="所在单位" show-overflow-tooltip />
            <el-table-column prop="deadline" label="主持人" show-overflow-tooltip />
            <el-table-column prop="deadline" label="是否青年专项" min-width="90" show-overflow-tooltip />
            <el-table-column prop="deadline" label="总得分" show-overflow-tooltip />
        </el-table>
        <el-pagination background layout="total,prev, pager, next" :total="total" class="mt-4" :current-page='search_form.pageNum' @current-change='currentChange' />
        <div v-if="current_tab == 2" class="flex_center">
            <el-button class="plain-btn btn_96_48" type="plain" @click="emit('close')">取消</el-button>
            <el-button class="primary-btn btn_96_48" type="primary" @click="submitClick">确认</el-button>
        </div>
    </div>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    const formObj = function(obj = {}){
        this.name = obj.name || ''
        this.pageNum = obj.pageNum || 1
        this.pageSize = obj.pageSize || 10
    }
    const total = ref(0) 
    const search_form = ref(new formObj({}))
    const tab_list = ref([{id:1,title:'总成绩单'},{id:2,title:'立项通过名单'}])
    const current_tab = ref(2)

    onMounted(() => {
    })
</script>
<style lang="scss" scoped>
.result-main{
    .tab-ul{ border-bottom: 1px solid #E2E3E6; margin-bottom:20px; display:inline-flex;
        li{ margin-right: 32px; color: #94959C; padding: 6px 0; cursor: pointer; }
        .active-tab{ font-weight: bold; color: #508CFF; border-bottom: 2px solid #508CFF; }
    }
    .search-box{
        :deep(.el-select){ width: 192px;margin-bottom: 16px;}
        :deep(.el-input){width: 146px;margin-bottom: 16px;}
    }
}
:deep(.el-dialog__header){padding: 0;}
:deep(.el-input-group__append){background: transparent; border: none; box-shadow: none; padding: 0; right: 24px;}
</style>
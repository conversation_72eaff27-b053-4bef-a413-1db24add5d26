<template>
    <div class="content-box mt10">
        <div class="flex_between">
            <el-form :inline="true" :model="searchForm" class="demo-form-inline" label-position="top">
                <el-form-item label="发布范围">
                    <el-select
                        v-model="searchForm.clubGroupId"
                        placeholder="请选择发布范围"
                        clearable
                        style="width:160px"
                        @change="search"
                    >
                        <el-option :value="item.value" v-for="item in groups" :key="item.value" :label='item.label'/>
                    </el-select>
                </el-form-item>
                <el-form-item label="发布日期">
                     <el-date-picker
                       @change="search"
                     style="width:280px"
                        v-model="searchForm.dateRang"
                        type="daterange"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="YYYY-MM-DD"
                        />
                </el-form-item>
          </el-form>
          <el-button type="primary" style="background:rgb(80,140,255);height:36px" @click="$router.push('/actResearchAssociation/notice/upload')">发布公告</el-button>
        </div>
        
         <el-table :data="tableData" height="550" style="width: 100%">
            <el-table-column prop="noticeTitle" label="公告标题" />
            <el-table-column prop="noticeRangeLabel" label="发布范围"  />
            <el-table-column prop="createTime" label="发布日期" />
              <el-table-column label="操作">
                 <template #default='scope'>
                     <el-button link type="primary" @click='goSee(scope.row)' style="color:rgb(80,140,255)">查看</el-button>
                    <el-button link type="danger" @click='goDel(scope.row)'>删除</el-button>
                 </template>
              </el-table-column>
        </el-table>
      <el-pagination background v-show="total!=0"  style="margin-bottom:10px" layout="prev, pager, next" :page-size='searchForm.pageSize' :total="total" @current-change='handlelCurrentChange' /> 
    </div>
     <HistoryItem :historyItemVisible="historyItemVisible" :noticeDetailRow='noticeDetailRow' />
</template>
<script setup>
import {onActivated, onMounted, reactive, ref} from 'vue'
import {mynoticeDel,mynoticeNews,groupList,noticeDetail} from '@/api/pc/index.js'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'
import HistoryItem from '@/views/pc/component/act-research-association/historyItem.vue'
import {ElMessageBox,ElMessage} from 'element-plus'
import {useRouter} from 'vue-router'
let userInfo=useUserInfoStoreHook()
let router=useRouter()
let {historyItemVisible,historyItemForm}=storeToRefs(userInfo)
let searchForm=reactive({
    pageNum:1,
    pageSize:10,
    clubGroupId:'',
    dateRang:[]
})
var noticeRef=ref()
let url=ref('/actResearchAssociation/notice/detail')
let total=ref(0)
let groups=ref([])
let tableData=ref([])
let noticeDetailRow=ref({})
let goSee=async(row)=>{
  historyItemVisible.value=true
   historyItemForm.value='toast'
    let res=await noticeDetail({
        clubNoticeId:row.noticeId
    })
    if(res){
        noticeDetailRow.value=res.data
        
    }
}
let getgroupList=async()=>{
    let res=await groupList()
    if(res){
        
         groups.value=res.data
         groups.value.unshift({
            label:'全社',
            value:'0'
         })
    }
}
let goDel=(row)=>{
    
        ElMessageBox.confirm(
            '确认删除吗',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
           let res=await mynoticeDel({
            noticeId:row.noticeId,
           
            })
            if(res){
                ElMessage.success('删除成功')
                getList()
           }
           
          
        })
        .catch(() => {
      
        })
}
let handlelCurrentChange=(val)=>{
     Object.assign(searchForm,{
        pageNum:val
    })
    getList()
}
onActivated(()=>{
    getList()
})
let search=()=>{
       Object.assign(searchForm,{
        pageNum:1
    })
    getList()
}
let getList=async()=>{
    let res=await mynoticeNews({
        pageNum:searchForm.pageNum,
        pageSize:searchForm.pageSize,
        clubGroupId:searchForm.clubGroupId,
        beginCreateTime:(searchForm.dateRang&&searchForm.dateRang[0])?searchForm.dateRang[0]:'',
        endCreateTime:(searchForm.dateRang&&searchForm.dateRang[1])?searchForm.dateRang[1]:''
    })
    if(res){
        tableData.value=res.data.list
        total.value=res.data.total
    }
}

onActivated(()=>{
   getgroupList()
})
// onMounted(()=>{
//   getList()
// })
</script>
<style lang="scss" scoped>
.mt10{
    margin-top: 10px;
}
.content-box{
    background: #fff;
    padding: 24px;
}
:deep .el-form-item__label{
    font-size: 16px;
    color: #2B2C33;
    font-weight: bold;
}
:deep .el-select__wrapper,:deep .el-range-editor.el-input__wrapper{
    height: 36px;
}
</style>
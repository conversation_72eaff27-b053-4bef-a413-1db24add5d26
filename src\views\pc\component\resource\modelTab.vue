<template>
<div class="tab-box">
   <div class="model-tab flex_between">
        <div class="flex_1  left"></div>
        <div class="middle flex_between">
             <h3 class="logo">资源中心</h3>
             <ul class="flex_center tab-list" >
                <li :class="['flex_center','flex_column',$route.meta.model==1?'active':'']"  @click="goModel(1)">
                     <img src="@/assets/pc/1-1.png" alt="" v-show="$route.meta.model==1">
                    <img src="@/assets/pc/1.png" alt="" v-show="$route.meta.model!=1">
                    
                    <p :style="$route.meta.model==1?'color:#1E78E6':''">教学资源</p>
                </li>
                <li :class="['flex_center','flex_column',$route.meta.model==2?'active':'']"  @click="goModel(2)">
                    <img src="@/assets/pc/2.png" alt=""  v-show="$route.meta.model!=2">
                     <img src="@/assets/pc/2-2.png" alt=""  v-show="$route.meta.model==2">
                    <p :style="$route.meta.model==2?'color:#1E78E6':''">题库</p>
                </li>
                <li :class="['flex_center','flex_column',$route.meta.model==3?'active':'']" @click="goModel(3)">
                    <img src="@/assets/pc/3.png" alt=""  v-show="$route.meta.model!=3">
                     <img src="@/assets/pc/3-3.png" alt=""  v-show="$route.meta.model==3">
                    <p :style="$route.meta.model==3?'color:#1E78E6':''">智慧课堂</p>
                </li>
                
             </ul>
            <div></div>
        </div>
        <div class="flex_1  right flex_start">
            
         
        </div>
    </div>
</div>
  
</template>
<script setup>
import {ref} from 'vue'
import {useRouter} from 'vue-router'
import { useUserInfoStore } from "@/store/modules/pc"

let userStore=useUserInfoStore()
let router=useRouter()
let toastShow=ref(false)
let goModel=(type)=>{
    if(type==1){
         router.push('/resource/teach/index')
    }else if(type==2){
        router.push('/resource/question/index')
    }else if(type==3){
        //    router.push('/actResearchAssociation/theme')
        // alert('开发ing')
         router.push('/resource/wisdom/index')
    }
}
let goUpNotice=()=>{
     router.push('/actResearchAssociation/notice/upload')
}
</script>
<style lang='scss' scoped>
.tab-box{
 background: #fff;
}
.model-tab{

    margin: 0 auto;
    .middle{
        width: 100%;
         max-width: 1040px;
       .logo{
        font-size: 22px;
        color: #2B2C33;
        font-weight: bold;
       }
       .tab-list{
        padding: 11px 0 0 0;
        width: 480px;
        li{
            font-size: 12px;
            cursor: pointer;
            color: #94959C;
            margin-right: 130px;
            &:last-child{
                margin-right: 0;
            }
            &::after{
                content: '';
                display: block;
                height: 4px;
                width: 100%;
                background: #fff;
                // background: linear-gradient( 270deg, #496EEE 0%, #0AC1FA 100%);
                margin-top: 6px;
            }
            img{
                width: 18px;
                height: auto;
                margin-bottom: 6px
            }
        }
        .active{
              &::after{
                content: '';
                display: block;
                height: 4px;
                width: 100%;
                // background: #fff;
                background: linear-gradient( 270deg, #496EEE 0%, #0AC1FA 100%);
                margin-top: 6px;
            }
        }
       }
    }
    .right{
        font-size: 16px;
       color: #2B2C33;
       font-weight: bold;
       position: relative;
       white-space:nowrap;
        img{
            width: 32px;
            height: auto;
            margin-right: 8px;
        }
      
        .up-notice{
            font-size: 14px;
            color: #FFFFFF;
            cursor: pointer;
            background: linear-gradient( 270deg, #508CFF 0%, #535DFF 100%);
             border-radius: 12px;
            padding: 0 10px;
             height: 24px;
             cursor: pointer;
             margin-right: 32px;
             white-space: nowrap;
             img{
                width: 14px;
                height: auto;
             }
        }
    }
}
</style>
<script setup>
import Logo from "../Logo/index.vue"
import Login from '../login.vue'
// import { useUserStore } from "@/store/modules/user"
import {useUserInfoStore} from "@/store/modules/pc"
import {useRoute,useRouter} from 'vue-router'
import Sidebar from "../Sidebar/index.vue"
import { useDevice } from "@/hooks/useDevice"
import DefaultAvatar from "@/assets/layouts/default-avatar.png"
import ModelTab from '@/views/pc/component/act-research-association/modelTab.vue'
import ModelTabResource from '@/views/pc/component/resource/modelTab.vue'
import ModelTabBk from '@/views/pc/component/bk/modelTab.vue'
import ModelTabJky from '@/views/pc/jky/modelTab.vue'
import ModelTabMsjt from '@/views/pc/msjt/modelTab.vue'
import Bindwx from '@/views/pc/component/bindwx.vue'
import ChangeIdentify from '@/views/pc/component/changeIdentify.vue'
import {onMounted, ref, watch} from 'vue'
import {getCancelBindStatus} from '@/api/pc/index.js'
import {ElMessageBox,ElMessage} from 'element-plus'
import {uploadHeaderFileFunc} from '@/utils/pc-upload.js'
import ChangePassword from '@/layout/components/changePassword.vue'
const { isMobile } = useDevice()
// const userStore = useUserStore()
let useUserInfo=useUserInfoStore()
let route=useRoute()
let router=useRouter()
import {storeToRefs} from 'pinia'
let { loginVisibleShow,userInfo,currentInfo } = storeToRefs(useUserInfo)
let bindwxVisibleShow=ref(false)
let menuList=ref([
  {path:'/',title:'首页',id:'/index'},
  {path:'/resource/teach/index',title:'资源中心',id:'/resource'},
  {path:'/bk/res/index',title:'备课中心',id:'/bk'},
  // {path:'',title:'课题研究与管理'},
  // {path:'',title:'应用中心'},
  {path:'/actResearchAssociation/home',title:'行知研习社',id:'/actResearchAssociation'},
  {path:'/msjt',title:'名师讲堂',id:'/msjt'},

]) 
let roleName=ref('')
let activeIndex=ref('')
let dialogVisible=ref(false)
let changePasswordVisible=ref(false)
let unbindwx=()=>{
   ElMessageBox.confirm(
           '确认解绑微信吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
           let res=await getCancelBindStatus()
           if(res){
            ElMessage.success('解绑成功')
            useUserInfo.getInfo()
           }
            
        })
        .catch(() => {
      
        })
}
watch(()=>currentInfo,(newval)=>{
 
  if(newval.value.type=='student'){
    menuList.value=[
       {path:'/',title:'首页',id:'/index'},
       {path:'/resource/teach/index',title:'资源中心',id:'/resource'},
    ]
  }
},{
  deep:true
})
let commandUser=(val)=>{
  // console.log(val,loginVisibleShow)
  if(val=='login'){
    loginVisibleShow.value=true
  }else if(val=='bindwx'){
    bindwxVisibleShow.value=true
  }else if(val=='unbindwx'){
    unbindwx()
  }else if(val=='changeUserAvatar'){
    dialogVisible.value=true
  }else if(val=='changePassword'){
     changePasswordVisible.value=true
  }else{
    
    useUserInfo.logout()
  }
}
let emitBindWx=(arg)=>{
  bindwxVisibleShow.value=arg
}
let emitChangePasswordVisible=()=>{
  changePasswordVisible.value=false
}

let changeActive=(row,idx)=>{
  activeIndex.value=row.id
  
  router.push(row.path)
}
let changeAvatar=(e)=>{
  const getExtensionString = (str) => {
    var index = str.lastIndexOf('.')
    // 如果找到了第一个"."并且它不在字符串的起始处或结尾处
    if (index != -1 && index != 0 && index + 1 != str.length) {
        return str.substr(index + 1)
    } else {
        return ''
    }
  }
  let type=getExtensionString(e.target.files[0].name)
  let whiteList=['png','jpg','jpeg','webp']
  if(whiteList.indexOf(type)>-1){
        uploadHeaderFileFunc(e.target.files[0],data=>{
          userInfo.value.user.avatarUrl=data
          dialogVisible.value=false
           ElMessage.success('上传成功')
        })
  }else{
    ElMessage.warning('请上传图片')
  }

}
onMounted(()=>{
  if(route.path.indexOf('/resource')>-1){
   
    // activeIndex.value=2
  }else if(route.path.indexOf('/actResearchAssociation')>-1){
  
        //  activeIndex.value=1
        if(useUserInfo?.roles.indexOf('TUTOR')>-1){
          roleName.value="（导师）"
        }else  if(useUserInfo?.roles.indexOf('LEADER')>-1){
          roleName.value="（组长）"
        }else  if(useUserInfo?.roles.indexOf('DIRECTOR')>-1){
          roleName.value="（社长）"
        }else if(useUserInfo?.roles.indexOf('VICE_DIRECTOR')>-1){
          roleName.value='(社长)'
        }else{
          roleName.value=''
  }
  }


})
watch(()=>useUserInfo.userInfo,(newval)=>{
  console.log(newval,'newval')
  if(newval.xingZhiShe?.clubRole=='TUTOR'){
    roleName.value="（导师）"
  }else  if(newval.xingZhiShe?.clubRole=='LEADER'){
    roleName.value="（组长）"
  }else  if(newval.xingZhiShe?.clubRole=='DIRECTOR'){
    roleName.value="（社长）"
  }else if(newval.xingZhiShe?.clubRole=='VICE_DIRECTOR'){
    roleName.value='(社长)'
  }else{
     roleName.value=''
  }
  
},{
  deep:true
})
</script>

<template>
    <div>
      <Login />
      <ChangeIdentify />
      <!--  -->
      <div class="navigation-bar">
       
        <div class="main-box flex_between">
            <Logo />
          
            <ul class="flex_start menu-list">
                <li v-for="(item,index) in menuList" :key="index" @click="changeActive(item,index)" :class="['flex_center',route.path.indexOf(item.id)==0?'active':'']"><p>{{ item.title }}</p><el-icon><ArrowDown /></el-icon></li>
            </ul>
            <div class="right-menu">
                <div class="right-menu-avatar">
                  <el-avatar :src="userInfo.user?.avatarUrl || DefaultAvatar" class="avatar" :size="32" />
                  <!-- <label for="avatar">
                     
                  </label>
                  <input type="file" id="avatar" v-show="false" accept="image/*" @change="changeAvatar" :disabled='!userInfo.user'> -->
                   
                    <!-- <span class="user-name">用户名</span> -->
                     <el-dropdown @command="commandUser">
                      <div class="el-dropdown-link user-name flex_start">
                        <p style="white-space:nowrap" v-if="$route.path.indexOf('/actResearchAssociation')>-1"> {{useUserInfo.userInfo.user?useUserInfo.userInfo.user.name:'用户名'}}</p>
                          <p style="white-space:nowrap" v-else> {{currentInfo.name?currentInfo.name:'用户名'}}</p>
                        <p><el-icon class="el-icon--right">
                          <arrow-down />
                        </el-icon></p>
                      </div>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item command='changeUserAvatar'  v-if="userInfo.user">更换头像</el-dropdown-item>
                           <el-dropdown-item command='bindwx' v-if="userInfo.user&&!userInfo?.wxMpUser" >微信绑定</el-dropdown-item>
                            <el-dropdown-item command='unbindwx' v-if="userInfo.user&&userInfo?.wxMpUser">微信解绑</el-dropdown-item>
                           <el-dropdown-item command='login' v-if="!userInfo.user">登录</el-dropdown-item>
                              <el-dropdown-item command='changePassword'  v-if="userInfo.user">修改密码</el-dropdown-item>
                           <el-dropdown-item command='loginout'  v-if="userInfo.user">退出登录</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                </div>
            </div>
        </div>
    </div>
    <ModelTab v-if="$route.path.indexOf('/actResearchAssociation')>-1"/>
    <ModelTabJky v-if="$route.path.indexOf('/jky')>-1"/>
    <ModelTabMsjt v-if="$route.path.indexOf('/msjt')>-1"/>
    <ModelTabResource v-if="$route.path.indexOf('/resource')>-1"/>
    <ModelTabBk v-if="$route.path.indexOf('/bk')>-1"/>
    <ChangePassword :changePasswordVisible='changePasswordVisible' @emitChangePasswordVisible='emitChangePasswordVisible' />
    <Bindwx :bindwxVisibleShow='bindwxVisibleShow' @emitBindWx='emitBindWx'/>
        <el-dialog
        v-model="dialogVisible"
        title="更换头像"
        width="408"
        
    >
        <div class="flex_center">
          <img :src="userInfo.user?.avatarUrl || DefaultAvatar" alt="" style="width:200px;height:auto" />
        </div>
         <label for="avatar">
                     <p class="gochoiceheader">更换头像</p>
        </label>
        <input type="file" id="avatar" v-show="false" accept="image/*" @change="changeAvatar" :disabled='!userInfo.user'>
    </el-dialog>
    </div>
</template>

<style lang="scss" scoped>
:deep(.navigation-bar .right-menu .right-menu-avatar .user-name){
  outline: none;
  cursor: pointer;
}
.gochoiceheader{
  font-weight: 500;
font-size: 16px;
color: #508CFF;
margin-top: 12px;
cursor: pointer;
}
.navigation-bar {
  height: var(--v3-navigationbar-height);
  // overflow: hidden;
  color: var(--v3-navigationbar-text-color);
  background-color: #508CFF;

  .main-box{
    width: 80%;
   margin: 0 auto;height: 100%;
  }
  .menu-list{
    height: 100%;
    // margin: 0 64px 0 53px;
    li{
        color: #fff;
        font-size: 14px;
        padding: 0 6px;
         width: 107px;
        height: 100%;
      cursor: pointer;
        p{
            white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        }
        &.active{
            background: #fff;
            color:  #508CFF;
            font-weight: bold;
        }
        i{
            margin-left: 4px;
        }
    }
  }
  .hamburger {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 15px;
    cursor: pointer;
  }
  .breadcrumb {
    flex: 1;
    // 参考 Bootstrap 的响应式设计将宽度设置为 576
    @media screen and (max-width: 576px) {
      display: none;
    }
  }
  .sidebar {
    flex: 1;
    // 设置 min-width 是为了让 Sidebar 里的 el-menu 宽度自适应
    min-width: 0px;
    :deep(.el-menu) {
      background-color: transparent;
    }
    :deep(.el-sub-menu) {
      &.is-active {
        .el-sub-menu__title {
          color: var(--el-color-primary) !important;
        }
      }
    }
  }
  .right-menu {
    height: 100%;
    display: flex;
    align-items: center;
    .right-menu-item {
      padding: 0 10px;
      cursor: pointer;
    }
    .right-menu-avatar {
      display: flex;
      align-items: center;
      margin-right: 14px;

      .el-avatar {
        margin-right: 10px;
      }
     
      .user-name {
        color: #fff;
        font-size: 16px;
      }
    }
  }
   @media screen and ( max-width: 1300px ) {
     .menu-list{
      // margin: 0 32px 0 26px;
     }
      .right-menu {
  
   
    .right-menu-avatar {
   
      .user-name {
        color: #fff;
        font-size: 12px;
      }
    }
  }
}
}

</style>

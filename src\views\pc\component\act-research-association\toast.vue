<template>
  <div class="toast">
       <h3>最新消息</h3>
       <!-- <ul class="flex_between tab">
          <li :class="activeIndex==1?'active':''" @click="changeTab(1)">总社公告</li>
          <li :class="activeIndex==2?'active':''" @click="changeTab(2)">小组公告</li>
       </ul> -->
        <el-empty description="暂无通知" style="height:120px" v-if="props.unViewTableData?.length==0"  :image="noticeEmpty" image-size='130'/>
       <ul class="msg" v-if="props.unViewTableData?.length!=0">
         <li class="flex_start_0"  v-for="item in props.unViewTableData" :key="item.id" @click="goDetail(item)">
            <img src="@/assets/pc/loud.png" alt="">
            <div>
                <div class="flex_start">
                     <p class="tag">{{item.noticeRangeLabel}}</p>
                     <p class="notice flex_1">{{item.noticeTitle}}</p>
                </div>
                <div class="flex_start">
                     <p class="date">{{item.userName}}</p>
                      <p class="date">{{item.postTime}}</p>
                     <div class="new">
                        新
                     </div>
                </div>
               
            </div>
         </li>
       </ul>
       <div class="flex_center seemore" @click="goSeeMore">
        <img src="@/assets/pc/notice-more.png" alt="" style="width:22px;height:auto"/>
          <p>历史消息</p>
          
          <el-icon><ArrowRight /></el-icon>
       </div>
  </div>
  <!-- <HistoryList :historyListVisible='historyListVisible' /> -->
</template>

<script setup>
import {ref,onMounted,defineProps,defineEmits} from 'vue'
// import {groupNotice,noticeNew} from '@/api/pc/index.js'
import noticeEmpty from '@/assets/pc/notice-empty.png'
import { useUserInfoStore } from "@/store/modules/pc"
import {storeToRefs} from 'pinia'
let userStore=useUserInfoStore()
let {historyListVisible,historyItemVisible}=storeToRefs(userStore)
import {useRouter} from 'vue-router'
let activeIndex=ref('1')
let router=useRouter()
let tableData=ref([])
let emits=defineEmits(['emitDetail'])
let props=defineProps(['unViewTableData'])
let goSeeMore=()=>{
    historyListVisible.value=true
    // router.push('/actResearchAssociation/notice')
}

let getList=async()=>{
    // let res=await noticeNew()
    // if(res){
    //     console.log(res,'22')
    //     tableData.value=res.data.list
    // }
}
let goDetail=(row)=>{
    historyItemVisible.value=true
      emits('emitDetail',row,'toast')
//   router.push('/actResearchAssociation/notice/detail?id='+row.id)
}
onMounted(()=>{
//    getList()
})
</script>

<style lang='scss' scoped>
:deep(.el-empty__image){
    width: 50px;
}
 .toast{
    width: 248px;
    h3{
        font-size: 16px;
        color: #2B2C33;
        font-weight: bold;
    }
    .tag{
        font-size: 12px;
        color: #508CFF;
        line-height: 12px;
        text-shadow: 0px 0px 6px rgba(0,0,0,0.16);
        background: #DFEAFF;
        box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.16);
        border-radius: 10px;
        padding: 2px 8px;
        margin-right: 8px;
    }
    .msg{
        height: 300px;
        overflow: auto;
        li{
            margin-top: 20px;
            cursor: pointer;
           img{
            width: 16px;
            height: 20px;
            margin-right: 8px;
           }
           .notice{
            font-size: 14px;
             color: #2B2C33;
             width: 140px;
             overflow: hidden;
             white-space: nowrap;
             text-overflow: ellipsis;
             &:hover{
                color: #508CFF;
             }
           }
           .date{
            font-size: 12px;
            color: #94959C;
            margin-top: 4px;
            margin-right: 8px;
           }
           .new{
            width: 16px;
            height: 16px;
            line-height: 16px;
            text-align: center;
            background: #EF6556;
            color: #fff;
            font-size: 12px;
           }
       }
    }
    .seemore{
        margin-top: 24px;cursor: pointer;
        font-size: 14px;
        color: #94959C;line-height: 20px;
        background: #F9F9F9;
        box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.16);
        border-radius: 4px;
        padding: 10px 0;
        width:100%;
    }
 }
</style>
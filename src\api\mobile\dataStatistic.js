import { request } from "@/utils/service"

export function getCountByAgeApi() {
  return request({
    url: "xzs/dashboard/statistics/number-of-user-group-by-age-range",
    method: "get"
  })
}

export function getCountByGroupApi() {
  return request({
    url: "xzs/dashboard/statistics/number-of-user-group-by-group",
    method: "get"
  })
}

export function getCountByStageApi() {
  return request({
    url: "xzs/dashboard/statistics/number-of-user-group-by-stage",
    method: "get"
  })
}

export function getCountBySubjectApi() {
  return request({
    url: "xzs/dashboard/statistics/number-of-user-group-by-subject",
    method: "get"
  })
}

export function getCountByUnitApi() {
  return request({
    url: "xzs/dashboard/statistics/number-of-user-group-by-tenant",
    method: "get"
  })
}

export function getCountByGroupDiaryApi() {
  return request({
    url: "xzs/dashboard/statistics/number-of-record-group-by-group",
    method: "get"
  })
}

export function getCountByPersonalDiaryApi() {
  return request({
    url: "xzs/dashboard/statistics/number-of-record-group-by-user",
    method: "get"
  })
}

export function exportByAgeApi() {
  return request({
    url: "xzs/dashboard/statistics/number-of-user-group-by-age-range/file",
    method: "get",
    responseType: "blob"
  })
}

export function exportByGroupApi() {
  return request({
    url: "xzs/dashboard/statistics/number-of-user-group-by-group/file",
    method: "get",
    responseType: "blob"
  })
}


export function exportByStageApi() {
  return request({
    url: "xzs/dashboard/statistics/number-of-user-group-by-stage/file",
    method: "get",
    responseType: "blob"
  })
}

export function exportBySubjectApi() {
  return request({
    url: "xzs/dashboard/statistics/number-of-user-group-by-subject/file",
    method: "get",
    responseType: "blob"
  })
}

export function exportByUnitApi() {
  return request({
    url: "xzs/dashboard/statistics/number-of-user-group-by-tenant/file",
    method: "get",
    responseType: "blob"
  })
}

export function exportByGroupDiaryApi() {
  return request({
    url: "xzs/dashboard/statistics/number-of-record-group-by-group/file",
    method: "get",
    responseType: "blob"
  })
}

export function exportByPersonalDiaryApi() {
  return request({
    url: "xzs/dashboard/statistics/number-of-record-group-by-user/file",
    method: "get",
    responseType: "blob"
  })
}


/* 里程统计 */

// 社长相关

// 社团里程排名（本周）
export function getWeekClubRankApi() {
  return request({
    url: 'xzs/mileage/stat/club/current-weekly-ranking-list',
    method: 'get',
  })
}

// 社团里程排名（本月）
export function getMonthClubRankApi() {
  return request({
    url: 'xzs/mileage/stat/club/current-monthly-ranking-list',
    method: 'get',
  })
}

// 社团里程排名（历史）
export function getHistoryClubRankApi() {
  return request({
    url: 'xzs/mileage/stat/club/history-ranking-list',
    method: 'get',
  })
}

// 小组里程排名（本周）
export function getWeekGroupRankApi() { 
  return request({
    url: '/xzs/mileage/stat/club/current-weekly-group-ranking-list',
    method: 'get',
  })
}

// 小组里程排名（本月）
export function getMonthGroupRankApi() { 
  return request({
    url: 'xzs/mileage/stat/club/current-monthly-group-ranking-list',
    method: 'get',
  })
}

// 小组里程排名（历史）
export function getHistoryGroupRankApi() { 
  return request({
    url: 'xzs/mileage/stat/club/history-group-ranking-list',
    method: 'get',
  })
}

// 荣誉统计（本周）
export function getWeekHonorRankApi() { 
  return request({
    url: 'xzs/mileage/stat/club/current-weekly-group-honour',
    method: 'get',
  })
}

// 荣誉统计（本月）
export function getMonthHonorRankApi() { 
  return request({
    url: 'xzs/mileage/stat/club/current-monthly-group-honour',
    method: 'get',
  })
}

// 荣誉统计（历史）
export function getHistoryHonorRankApi() { 
  return request({
    url: 'xzs/mileage/stat/club/history-group-honour',
    method: 'get',
  })
}

// 社长-里程分布（本周）
export function getDirectorWeekClubMileageDistributionApi() { 
  return request({
    url: 'xzs/mileage/stat/club/current-weekly-event-group-points',
    method: 'get',
  })
}

// 社长-里程分布（本月）
export function getDirectorMonthClubMileageDistributionApi() { 
  return request({
    url: 'xzs/mileage/stat/club/current-monthly-event-group-points',
    method: 'get',
  })
}

// 社长-里程分布（历史）
export function getDirectorHistoryClubMileageDistributionApi() { 
  return request({
    url: 'xzs/mileage/stat/club/history-event-group-points',
    method: 'get',
  })
}

// 社长-近30天总里程趋势
export function getDirectorThirtyDaysTotalMileageTrendApi() { 
  return request({
    url: 'xzs/mileage/stat/club/recent-day30-points',
    method: 'get',
  })
} 


// 导师相关

// 组内里程排名（本周）
export function getWeekGroupMileageRankApi() { 
  return request({
    url: 'xzs/mileage/stat/club-group/current-weekly-ranking-list',
    method: 'get',
  })
}

// 组内里程排名（本月）
export function getMonthGroupMileageRankApi() { 
  return request({
    url: 'xzs/mileage/stat/club-group/current-monthly-ranking-list',
    method: 'get',
  })
}

// 组内里程排名（历史）
export function getHistoryGroupMileageRankApi() { 
  return request({
    url: 'xzs/mileage/stat/club-group/history-ranking-list',
    method: 'get',
  })
}

// 导师-里程分布（本周）
export function getTutorWeekGroupMileageDistributionApi() { 
  return request({
    url: 'xzs/mileage/stat/club-group/current-weekly-event-group-points',
    method: 'get',
  })
}

// 导师-里程分布（本月）
export function getTutorMonthGroupMileageDistributionApi() { 
  return request({
    url: 'xzs/mileage/stat/club-group/current-monthly-event-group-points',
    method: 'get',
  })
}

// 导师-里程分布（历史）
export function getTutorHistoryGroupMileageDistributionApi() { 
  return request({
    url: 'xzs/mileage/stat/club-group/history-event-group-points',
    method: 'get',
  })
}

// 导师-近30天总里程趋势
export function getTutorThirtyDaysTotalMileageTrendApi() { 
  return request({
    url: 'xzs/mileage/stat/club-group/recent-day30-points',
    method: 'get',
  })
}
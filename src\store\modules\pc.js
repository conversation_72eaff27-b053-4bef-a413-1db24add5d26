import { ref } from "vue"
import { pinia } from "@/store"
import { defineStore } from "pinia"
import { getToken, removeToken, setToken } from "@/utils/cache/cookies"
import { resetRouter, router } from "@/router"
import { getUserInfoApi, loginApi,logoutapi } from "@/api/login"
import {getQrLoginStatus,sendCaptchaToLogin}  from "@/api/pc/index.js"
import {wxLogin} from '@/api/common/index.js'
import routeSettings from "@/config/route"
// import { getHashSearchParam } from "@/utils"
import { ElMessage } from "element-plus"
import { useRoute } from "vue-router"

export const useUserInfoStore = defineStore("userInfo", () => {
  let historyListVisible=ref(false)
  let historyItemVisible=ref(false)
  let historyItemForm=ref('list') //是从列表进入还是从toast里面进入
  let noticeUnView=ref(0)
    let stages=ref([
        {
            id:'10',
            title:'幼儿园'
        },
        {
            id:'20',
            title:'小学'
        },
        {
            id:'30',
            title:'初中'
        },{
            id:'40',
            title:'高中'
        }
    ])
    let qrloginStatus=ref('PENDING')

    let questionCategoryMap=ref({
      '1':'选择题',
      '2':'应用题',
      '3':'解答题',
      '4':'填空题',
      '5':'计算题',
      '6':'判断题',
      '7':'操作题',
      '8': '现代文阅读',
      '9':'古诗词赏析',
      '10':'作文',
      '11':'语言运用',
      '12':'综合读写',
      '13':'文言文阅读',
      '14':'基础知识',
      '15':'名著阅读',
      '16':'默写',
      '17':'翻译',
      '18':'名著阅读',
      '19':'阅读理解',
      '20':'排序',
      '21':'词汇应用',
      '22':'句型转换',
      '23': '完形填空',
      '24':'阅读表达',
      '25':'书面表达',
      '26':'补全对话',
      '27':'多选题',
      '28':'作图题',
      '29':'实验探究',
      '30':'综合能力',
       '31': '推断题',
       '32':'语言表达',
       '33':'连线题',
       '34':'问答题',
       '35':'判断',
       '36':'听力',
       '37':'改错',
       '40':'辨析题',
       '41': '材料题',
       '42':'简答题',
       '43':'评析题',
       '44':'阐述见解题',
       '45': '材料分析题',
       '46':'诗歌阅读',
       '47':'语言文字应用',
       '48':'语法填空',
       '49':'信息匹配',
       '50':'短文改错',
       '51':'实验题',
       '52':'听力题',
       '53':'名著导读',
       '54':'名著导读',
       '55': '翻译题',
  })
  // 对照表
  let questionCategory = ref([
    {
      id: '1',
      title: '选择题'
    },
    {
      id: '2',
      title: '应用题'
    },
    {
      id: '3',
      title: '解答题'
    },
    {
      id: '4',
      title: '填空题'
    },
    {
      id: '5',
      title: '计算题'
    },
    {
      id: '6',
      title: '判断题'
    },
  
    {
      id: '7',
      title: '操作题'
    },
    {
      id: '8',
      title: '现代文阅读'
    },
    {
      id: '9',
      title: '古诗词赏析'
    },
    {
      id: '10',
      title: '作文'
    },
    {
      id: '11',
      title: '语言运用'
    },
  
    {
      id: '12',
      title: '综合读写'
    },
    {
      id: '13',
      title: '文言文阅读'
    },
  
    {
      id: '14',
      title: '基础知识'
    },
  
    {
      id: '15',
      title: '名著阅读'
    },
  
    {
      id: '16',
      title: '默写'
    },
    {
      id: '17',
      title: '翻译'
    },
  
    {
      id: '18',
      title: '名著阅读'
    },
    {
      id: '19',
      title: '阅读理解'
    },
    {
      id: '20',
      title: '排序'
    }, {
      id: '21',
      title: '词汇应用'
    },
    {
      id: '22',
      title: '句型转换'
    },
    {
      id: '23',
      title: '完形填空'
    },
    {
      id: '24',
      title: '阅读表达'
    },
  
    {
      id: '25',
      title: '书面表达'
    },
    {
      id: '26',
      title: '补全对话'
    }, {
      id: '27',
      title: '多选题'
    },
  
    {
      id: '28',
      title: '作图题'
    },
    {
      id: '29',
      title: '实验探究'
    },
    {
      id: '30',
      title: '综合能力'
    },
    {
      id: '31',
      title: '推断题'
    },
  
    {
      id: '32',
      title: '语言表达'
    },
    {
      id: '33',
      title: '连线题'
    }, {
      id: '34',
      title: '问答题'
    },
  
    {
      id: '35',
      title: '判断'
    },
    {
      id: '36',
      title: '听力'
    },
    {
      id: '37',
      title: '改错'
    },
  
    {
      id: '40',
      title: '辨析题'
    }, {
      id: '41',
      title: '材料题'
    },
  
    {
      id: '42',
      title: '简答题'
    },
    {
      id: '43',
      title: '评析题'
    },
    {
      id: '44',
      title: '阐述见解题'
    },
    {
      id: '45',
      title: '材料分析题'
    },
  
    {
      id: '46',
      title: '诗歌阅读'
    },
    {
      id: '47',
      title: '语言文字应用'
    }, {
      id: '48',
      title: '语法填空'
    },
  
    {
      id: '49',
      title: '信息匹配'
    },
    {
      id: '50',
      title: '短文改错'
    },
    {
      id: '51',
      title: '实验题'
    },
    {
      id: '52',
      title: '听力题'
    },
    {
      id: '53',
      title: '名著导读'
    },
    {
      id: '54',
      title: '单词拼写'
    },
    {
      id: '55',
      title: '翻译题'
    },
  ])

    let questionDegree=ref([
        {id:'30',title:'简单'},{id:'60',title:'一般'},{id:'90',title:'困难'}
    ])
    let squareCurrentInfo=ref({}) //当前日记广场所在的recordId 用于切换上一篇下一篇
    let personcenterCurrentInfo=ref({})  //当前个人空间所在的recordId 用于切换上一篇下一篇
//     课件	COURSEWARE
// 教案	SYLLABUS
// 音视频	AUDIO_VIDEO
// 其他文档资源	OTHER
let ResourceCategory=ref([
  {
    id:'1',title:'课件'
  },
  {
    id:'2',title:'教案'
  },
  {
    id:'3',title:'音视频'
  },
  {
    id:'4',title:'其他文档资源'
  }
])
    let themeScrollTop=ref(0)
    let noticeScrollTop=ref(0)
    let squareScrollTop=ref(0)
    let squareUserCenterScrollTop=ref(0)
    let squareCommonScrollTop=ref(0)
    let loginVisibleShow=ref(false) 
    let choiceIdentifyVisibleShow=ref(false)
    let currentInfo=ref({}) //当前用户
    let allInfo=ref([])  //所有身份
    // let getUserInfo = async () => {
    
    // }
    let whiteList=['msjt-live-detail','Home','msjt-live-detail']
    const token = ref(getToken() || "")
    const userInfo = ref({})
    const roles = ref([])
    const jky_role = ref({})
     const wxLoginByCode=async(code)=>{
      try{
        let res=await wxLogin({
          code
        })
        if(res){
            setToken(res.data.token)
            token.value = res.data.token
            // ElMessage.success('登录成功')
            let msg='登录成功  '+(res.actionPoint?(res.actionPoint+'分'):'')
            ElMessage.success(msg)
            getInfo('login')
        }
      }catch(error){
        router.push({
          path:'/',
          
        })
      }
      
     }
    /** 登录 */
    const codeLogin=async(row)=>{
      let res=await sendCaptchaToLogin(row)
      if(res){
          setToken(res.data.token)
         
          token.value = res.data.token
          // ElMessage.success('登录成功')
          let msg='登录成功  '+(res.actionPoint?(res.actionPoint+'分'):'')
          ElMessage.success(msg)
          getInfo('login')
          
      }
    }
  
    const qrlogin=async(authorizeKey)=>{
      
      try {
        const res = await getQrLoginStatus({
          authorizeKey
        })
        if (res) {
          qrloginStatus.value=res.data.status
          // console.log(res)
          if(res.data.status=="OK"){
            setToken(res.data.token)
           
            token.value = res.data.token
            // ElMessage.success('登录成功')
            let msg='登录成功  '+(res.actionPoint?(res.actionPoint+'分'):'')
            ElMessage.success(msg)
            getInfo()
          }
          
        }
      } catch (error) {
        console.log(error)
      }
    }
    const login = async (row) => {
 
  
      try {
        const res = await loginApi({
          username:  row?.username,
          password: row?.password
        })
        if (res) {
          setToken(res.data.token)
          token.value = res.data.token
          let msg='登录成功  '+(res.actionPoint?(res.actionPoint+'分'):'')
          ElMessage.success(msg)
          getInfo('login')
          
        }
      } catch (error) {
        console.log(error)
      }
    }
    /** 获取用户详情 */
    const getInfo = async (action) => {
      const { data } = await getUserInfoApi()
      if (data) {
        userInfo.value = data
        if(data.teacher&&data.parent&&data.students&&data.students.length>0){
          //学生和家长
          choiceIdentifyVisibleShow.value=true
          allInfo.value=[
            {
              type:'teacher',
              name:data.user?.name
            },
            {
              type:'student',
              name:data.students[0]?(data.students[0].name+'-'+data.students[0].relation):data.user?.name
            }
          ]
          currentInfo.value={
            type:'teacher',
            name:data.user?.name
          }
        }
        if(data.teacher&&!(data.parent&&data.students&&data.students.length>0)){
          //老师
          // choiceIdentifyVisibleShow.value=true
          currentInfo.value={
            type:'teacher',
            name:data.user?.name
          }
        }
        if(!data.teacher&&data.parent&&data.students&&data.students.length>0){
          // choiceIdentifyVisibleShow.value=true
          //学生
          currentInfo.value={
            type:'student',
            name:data.students[0]?(data.students[0].name+'-'+data.students[0].relation):data.user?.name
          }
        }
        /** 
         * clubRole 
         * 成员: "MEMBER",
         * 组长： "LEADER",
         * 导师： "TUTOR",
         * 社长: "DIRECTOR"
         * 
         * 在此处根据 clubRole 的值来设置 roles 的值， 可通过v-permission指令来控制权限
         */
          
        roles.value = data?.xingZhiShe.clubRole ? [data?.xingZhiShe.clubRole] : routeSettings.defaultRoles
        jky_role.value = data?.jiaoKeYan?.unitUsers ? data.jiaoKeYan.unitUsers[0] : {}
        if(action=='login'){
          let routeName=router.currentRoute.value.name
          if(whiteList.indexOf(routeName)>-1){
            window.location.reload()
          }else{
            router.push({
              path:'/',
              
            })
          }
        }
      }
    }
    /** 模拟角色变化 */
    const changeRoles = async (role) => {
      const newToken = "token-" + role
      token.value = newToken
      setToken(newToken)
      // 用刷新页面代替重新登录
      window.location.reload()
    }

    const changeJkyRole = async (role) => {
      jky_role.value = role
      // window.location.reload()
    }

   
    /** 登出 */
    const logout = async(code) => {
 
        let res=await logoutapi()
        if(res){
         
            removeToken()
            userInfo.value={}
            token.value = ""
            roles.value = []
            jky_role.value={}
            currentInfo.value={}
            allInfo.value=[]
            localStorage.clear()
            if(code!=401){
              ElMessage.success('注销成功')
            }
            router.push("/")
            resetRouter()
        }
    }
    /** 重置 Token */
    const resetToken = () => {
      removeToken()
      token.value = ""
      roles.value = []
      jky_role.value={}
    }
    return { 
        themeScrollTop,
        noticeScrollTop,
        squareUserCenterScrollTop,
        squareCommonScrollTop,
        squareScrollTop,
        loginVisibleShow,
        choiceIdentifyVisibleShow,
        currentInfo,
        token,
       
        questionCategory,
        questionCategoryMap,
        stages,
        roles,
        jky_role,
        questionDegree,
        userInfo,
        login,
        getInfo,
        changeRoles,
        changeJkyRole,
        logout,
        resetToken,
        codeLogin,
        wxLoginByCode,
        ResourceCategory,
        qrlogin,
        qrloginStatus,
        allInfo,
        squareCurrentInfo,
        personcenterCurrentInfo,
        noticeUnView,
        historyListVisible,
        historyItemVisible,
        historyItemForm
    }
}, {
    persist: {
        // 指定存储位置，默认是 localStorage
        storage: localStorage,
        // 指定存储的键名
        key: 'userInfo',
        // 可以指定哪些状态需要持久
    }
})

/**
 * 在 SPA 应用中可用于在 pinia 实例被激活前使用 store
 * 在 SSR 应用中可用于在 setup 外使用 store
 */
export function useUserInfoStoreHook() {
    return useUserInfoStore(pinia)
}

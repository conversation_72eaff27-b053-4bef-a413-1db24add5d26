<script setup>
import { ref, watch, onMounted } from "vue"
import { EchartsUI, useEcharts } from "@/components/Echart"
import { color } from "echarts";

const chartRef = ref()
const { renderEcharts } = useEcharts(chartRef)

const props = defineProps({
  chartData: {
    type: Object,
    default: () => ({
      userData: [],       // 用户数据
      avgData: [],        // 社团平均数据
      indicator: [],      // 雷达图指标
      userColor: '#508CFF', // 用户数据颜色
      avgColor: '#FEAC12',   // 社团平均数据颜色
      userLegendColor: '#2189FD', // 用户图例颜色
      avgLegendColor: '#FDC535'   // 社团平均图例颜色
    })
  }
})

watch(
  () => props.chartData,
  () => {
    paintChart()
  },
  { deep: true }
)

onMounted(() => {
  paintChart()
})

const paintChart = () => {
  const userData = props.chartData.userData || []
  const avgData = props.chartData.avgData || []
  let indicator = props.chartData.indicator || [
    { name: '自我学习进步', max: 100 },
    { name: '行知成果进步', max: 100 },
    { name: '阶段成长进步', max: 100 },
    { name: '互动交流进步', max: 100 }
  ]

  // 动态处理第二个和第四个指标的换行
  if (indicator && indicator.length >= 4) {
    // 复制数组，避免直接修改原始数据
    indicator = indicator.map((item, index) => {
      if (index === 1 || index === 3) {
        // 仅处理第二个和第四个指标
        const nameArr = item.name.split('进步');
        if (nameArr.length > 1) {
          return {
            ...item,
            name: nameArr[0] + '\n进步'
          };
        }
      }
      return item;
    });
  }

  const userColor = props.chartData.userColor || '#508CFF'
  const avgColor = props.chartData.avgColor || '#FEAC12'
  const userLegendColor = props.chartData.userLegendColor || '#2189FD'
  const avgLegendColor = props.chartData.avgLegendColor || '#FDC535'
  renderEcharts({
    backgroundColor: "transparent",
    radar: {
      indicator: indicator,
      shape: 'polygon',
      splitNumber: 5,
      axisName: {
        color: '#333',
        fontSize: 14,
        formatter: function (text) {
          if (text.includes('\n')) {
            const parts = text.split('\n');
            return '{a|' + parts[0] + '}\n{a|' + parts[1] + '}';
          }
          return '{a|' + text + '}';
        },
        rich: {
          a: {
            color: '#333',
            fontSize: 14,
            align: 'center',
            lineHeight: 18
          }
        }
      },
      splitArea: {
        areaStyle: {
          color: ['#F5F8FC', '#E2EAF7', '#F5F8FC', '#E2EAF7', '#F5F8FC']
        }
      },
      axisLine: {
        lineStyle: {
          color: '#DBE4F1'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#DBE4F1'
        }
      }
    },
    legend: {
      data: [
        { name: '你的步数', icon: 'circle', color: userLegendColor },
        { name: '社团平均步数', icon: 'circle', color: avgLegendColor }
      ],
      right: 0,
      bottom: 0,
      itemWidth: 8,
      itemHeight: 8,
      textStyle: {
        fontSize: 12
      },
      orient: 'vertical'
    },
    series: [
      {
        type: 'radar',
        name: '你的步数',
        data: [
          {
            value: userData,
            name: '你的步数',
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              color: userColor
            },
            itemStyle: {
              color: userColor
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: `rgba(80, 140, 255, 0.6)` },
                  { offset: 1, color: `rgba(80, 140, 255, 0.1)` }
                ]
              }
            }
          },
          {
            value: avgData,
            name: '社团平均步数',
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              color: avgColor
            },
            itemStyle: {
              color: avgColor
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: `rgba(254, 172, 18, 0.6)` },
                  { offset: 1, color: `rgba(254, 172, 18, 0.1)` }
                ]
              }
            }
          }
        ]
      }
    ]
  })
}
</script>

<template>
  <EchartsUI ref="chartRef" :height="'260px'" />
</template>

<style scoped></style>

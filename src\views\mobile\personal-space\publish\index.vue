<script setup>
import { ref, onMounted, onBeforeUnmount, onActivated } from "vue"
import WangEditor from "@/components/WangEditor/index.vue"
import { publishNoticeApi } from "@/api/mobile/message"
import { useRouter } from "vue-router";
import { showToast, showSuccessToast } from "vant";
import { getMyGroupApi } from "@/api/mobile/memberOperation"

const router = useRouter()

const addForm = ref({
  content: "",
  title: "",
  noticeRange: "CLUB",
  clubGroupId: "",
  clubGroupName: "",
})


const groupList = ref([])
const getGroupList = async () => {
  try {
    const res = await getMyGroupApi()
    if (res) {
      groupList.value = res.data.map(item => ({
        text: item.label,
        value: item.value
      }))
    }
  } catch (error) {
    console.log(error)
  }
}

const showPicker = ref(false)
const openSelectGroup = () => {
  showPicker.value = true
}

const onConfirm = ({ selectedOptions }) => {
  addForm.value.clubGroupId = selectedOptions?.[0].value
  addForm.value.clubGroupName = selectedOptions?.[0].text

  showPicker.value = false
}

const onCancel = () => {
  showPicker.value = false
}


// 提交日记
const submitDiary = async () => {
  try {

    if (!addForm.value.title) {
      showToast('请输入标题')
      return
    }
    if (!addForm.value.content || addForm.value.content === '<p><br></p>') {
      showToast('请输入通知内容')
      return
    }
    if (addForm.value.noticeRange === "CLUB_GROUP" && !addForm.value.clubGroupId) {
      showToast('请选择小组')
      return
    }
    const res = await publishNoticeApi({
      content: addForm.value.content,
      title: addForm.value.title,
      noticeRange: addForm.value.noticeRange,
      clubGroupId: addForm.value.noticeRange === "CLUB_GROUP" ? addForm.value.clubGroupId : "",
    })
    if (res) {
      showSuccessToast('提交成功')
      router.back()
    }
  } catch (error) {
    console.log(error)
  }
}

onActivated(() => {
  getGroupList()

  addForm.value.title = ""
  addForm.value.content = ""
  addForm.value.noticeRange = "CLUB"
  addForm.value.clubGroupId = ""
  addForm.value.clubGroupName = ""
})

</script>

<template>
  <div class="add-diary-page">
    <div class="back-area m-pt10 m-pb10 flex-between-center">
      <div class="flex-start-center pointer" @click="router.back()">
        <van-icon name="arrow-left" size="14" />
        <span class="m-text-14-20-400 grey1">返回</span>
      </div>
      <div class="m-text-16-24-400 dark1">发布通知</div>
      <div style="width: 42px;"></div>
    </div>
    <div class="add-form">
      <div class="form-item flex-column-start-start">
        <div class="label">标题</div>
        <van-field v-model="addForm.title" placeholder="请输入标题" />
      </div>
      <div class="form-item flex-column-start-start">
        <div class="label">通知类型</div>
        <van-radio-group v-model="addForm.noticeRange" direction="horizontal">
          <van-radio name="CLUB">全社</van-radio>
          <van-radio name="CLUB_GROUP">小组</van-radio>
        </van-radio-group>
      </div>
      <div class="form-item flex-column-start-start" v-if="addForm.noticeRange === 'CLUB_GROUP'">
        <div class="label">小组</div>
        <van-field v-model="addForm.clubGroupName" @click="openSelectGroup" readonly placeholder="请选择小组" />
        <van-popup v-model:show="showPicker" position="bottom">
          <van-picker title="选择小组" :columns="groupList" @confirm="onConfirm" @cancel="onCancel"></van-picker>
        </van-popup>
      </div>
      <div class="form-item editor-container flex-column-start-start">
        <div class="label">通知内容</div>
        <div class="editor-content">
          <WangEditor v-model="addForm.content" :height="'300px'" />
        </div>
      </div>
      <div class="m-btn-container flex-start-center m-pt20">
        <van-button class="m-submit-btn" @click="submitDiary">提交</van-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "@/views/mobile/styles/custom-global-mobile-style.scss";

.add-diary-page {
  position: relative;
  width: 100%;
  height: 100%;
  padding-top: 44px;

  .back-area {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background-color: #f5f7fa;
  }
  .add-form {
    background-color: #fff;
    padding: 20px 15px;
    overflow-y: auto;
  }

  .form-item {
    .label {
      font-weight: 400;
      font-size: 14px;
      color: #2b2c33;
      line-height: 24px;
      margin-right: 27px;
    }

        :deep(.van-radio-group) {
          height: 44px;
          line-height: 44px;
    
          .van-radio {
    
            &__label {
              font-size: 14px;
            }
          }
        }
  }

  :deep(.van-field) {
    padding-left: 0;
    padding-right: 0;
  }

  .choose-topic {

    .topic-item {
      padding: 3px 10px;
      height: 24px;
      border-radius: 19px;
      margin-right: 10px;
      border: 1px solid #e2e3e6;

      &.active {
        background-color: #508cff;
        border-color: #508cff;
        color: #fff;
      }
    }
  }

  .clocking-time {
    :deep(.van-field) {
      padding: 0;
    }
  }
}
</style>

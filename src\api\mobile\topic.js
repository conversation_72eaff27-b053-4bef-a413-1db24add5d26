import { request } from "@/utils/service"

// 新增主题
export function addTopicApi(data) {
  return request({
    url: "xzs/club-topic",
    method: "post",
    data
  })
}

// 更新主题
export function updateTopicApi(topicId, data) {
  return request({
    url: `xzs/club-topic/${topicId}`,
    method: "put",
    data
  })
}

// 主题选项/列表
export function getTopicListApi() {
  return request({
    url: "xzs/club-topic/option",
    method: "get"
  })
}

// 删除主题
export function deleteTopicApi(topicId) {
  return request({
    url: `xzs/club-topic/${topicId}`,
    method: "delete"
  })
}
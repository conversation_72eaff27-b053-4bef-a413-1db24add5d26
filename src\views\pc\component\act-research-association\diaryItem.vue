<template>
  <div class="flex_start_0 diary">
        <el-avatar :src="row.userAvatarUrl || DefaultAvatar" class="avatar head" :size="32" @click="goPerson(row)" />
          <!-- <img src="@/assets/pc/teacher.png" alt="" class="head" /> -->
          <div class="flex_1">
                 <div class="flex_between">
                    <div class="info">
                        <div class="flex_start ">
                            <p class="teacher" @click="goPerson(row)">{{row.userName}}</p>
                            
                             <div class="flex_start stars" v-show="row.userClubRole=='LEADER'||row.userClubRole=='MEMBER'">
                                <div v-for="item in 6" :key="item" class="star-box">
                                      <img src="@/assets/pc/star.png" alt="" v-if="item>row.score">
                                      <img src="@/assets/pc/stared.png" alt="" v-else>
                                </div>
                             </div>
                        </div>
                        <p class="date">
                            {{row.recordDate}}{{row.userClubRole=='DIRECTOR'?("（"+row.week+"）"):""}}
                             {{row.lastUpdateTime?"（最后修改日期："+row.lastUpdateTime+"）":''}}
                        </p>
                    </div>
                     <ul class="flex_start action-list">
                        <li class="flex_start">
                            <img src="@/assets/pc/eyed.png" alt="" v-show="row.isViewed"/>
                            <img src="@/assets/pc/eye.png" alt="" v-show="!row.isViewed"/>
                            <p>{{row.views}}</p>
                        </li>
                        <li class="flex_start" @click="gofavorite(row)">
                            <img src="@/assets/pc/comment-star.png" alt="" v-show="!row.isFavorite">
                             <img src="@/assets/pc/comment-stared.png" alt="" v-show="row.isFavorite">
                             <p>{{row.favorites?row.favorites:0}}</p>
                        </li>
                        <li class="flex_start">
                            <img src="@/assets/pc/comment.png" alt="" v-show="!row.isCommented" />
                             <img src="@/assets/pc/commented.png" alt="" v-show="row.isCommented" />
                            <p>{{row.comments}}</p>
                        </li>
                        <li class="flex_start" @click="row.isLikeCapable&&goThumb(row)">
                            <img src="@/assets/pc/thumb.png" alt="" v-show="!row.isLike">
                            <img src="@/assets/pc/thumbed.png" alt="" v-show="row.isLike">
                            <p>{{row.likes}}</p>
                        </li>
                     </ul>
                 </div>
                 <div class="rich" >
                    <h3 style="margin-top:14px;font-size:14px" v-show="row.title">标题：{{row.title?row.title:'无'}}</h3>
                    <div :class="!isopen?'rich-html':'rich-open-html'" ref="richRef" v-html="row.content" @click="goDetail(row)">
                     
                     </div>
                     <!-- <p class="has-more" v-show="hasmore" @click="isopen=!isopen">
                         {{ isopen?'收起':'全部' }}
                     </p> -->
                 </div>
                 <div class="bottom flex_between_0">
                    <ul class="flex_start tag-list flex_wrap">
                        <li class="flex_start" v-for="item in row.topics" :key="item.id">
                            <img src="@/assets/pc/tag.png" alt="">
                            <p>{{item.name}}</p>
                        </li>
                       
                    </ul>
                  
                    <div class='flex_start_0 btn-list' >
                         <button class='flex_center edit' v-show="canEdit" @click="goEdit(row)">
                              <el-icon :size="20" style="margin-right:10px">
                                <Edit />
                            </el-icon>
                           编辑
                         </button>
                        <button class='flex_center del' v-show="row.isDeleteCapable||canDel" @click="goDel(row)">
                           <img src='@/assets/pc/person-del.png' />
                           删除
                        </button>
                        <button  class='flex_center hide' @click="goHide(row)" v-show="!row.isHidden&&row.isHideCapable">
                           <img src='@/assets/pc/person-hide.png' />
                           隐藏
                        </button>
                         <button class='flex_center hashide'  v-show="row.isHidden&&row.isHideCapable" >
                           <img src='@/assets/pc/person-hashide.png' />
                           已隐藏
                        </button>
                        <div class="recommend flex_center" @click="goRecommend(row,'1')" v-show="$route.path!='/actResearchAssociation/personcenter'&&row.isRecommended!==null&&!row.isRecommended">
                            栏目推荐
                        </div>
                         <div class="hasrecommend flex_center" v-show="$route.path!='/actResearchAssociation/personcenter'&&row.isRecommended!==null&&row.isRecommended"  @click="cancelCancelRecommend(row,'1')">
                            取消栏目推荐
                            <!-- 已设为推荐<span v-show="row.isCancelRecommendCapable">（取消推荐）</span> -->
                        </div>
                         <div class="wxrecommend flex_center" style="width:126px;margin-left:10px" @click="goRecommend(row,'2')" v-show="$route.path!='/actResearchAssociation/personcenter'&&row.isWxMpRecommended!==null&&!row.isWxMpRecommended">
                            公众号推荐
                        </div>
                        <div class="haswxrecommend flex_center" style="margin-left:10px" v-show="$route.path!='/actResearchAssociation/personcenter'&&row.isWxMpRecommended!==null&&row.isWxMpRecommended" @click="cancelCancelRecommend(row,'2')">
                            取消公众号推荐
                        </div>
                    </div>
                 </div>
          </div>
  </div>
 <el-image-viewer
        v-if="showPreview"
        :url-list="previewurlList"
        show-progress
        :initial-index="0"
        @close="showPreview = false"
      />
</template>

<script setup>
import DefaultAvatar from "@/assets/layouts/default-avatar.png"
import {onMounted, ref,defineProps, nextTick} from 'vue'
import {ElMessageBox,ElMessage} from 'element-plus'
import {thumbSubmit,favoriteSubmit,diaryDel,diaryHide,diaryRecommend,cancelDiaryRecommend} from '@/api/pc/index.js'
import {useRouter,useRoute} from 'vue-router'
let props=defineProps(['row','url','canDel','canEdit',])
let emits=defineEmits(['emitIsLike','emitDel','emitHide','emitRecommend','emitGoDetail','emitUserCenter'])
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
let userInfo=useUserInfoStoreHook()
let showPreview=ref(false)
let starnum=ref(3)
let richRef=ref()
let hasmore=ref(false)
let isopen=ref(false)
let router=useRouter()
let route=useRoute()
let previewurlList=ref([])
function checkLines() {
    // 获取文字容器元素
    const textElement = richRef.value;
    // console.log(textElement.scrollHeight,textElement.clientHeight)
    // 比较元素的 scrollHeight 和 clientHeight
    if (textElement.scrollHeight > textElement.clientHeight) {
        // console.log('文字超过了四行');
        hasmore.value=true
    } 
}
let goPreview=(row)=>{
    if(row.userAvatarUrl){
         previewurlList.value=[row.userAvatarUrl]
        nextTick(()=>{
             showPreview.value=true
        })
    }
}
let goEdit=(row)=>{
    router.push('/actResearchAssociation/personcenter/upload?id='+row.id)
}
let goPerson=(row)=>{
    // userInfo
    emits('emitUserCenter')
 router.push('/actResearchAssociation/square/user?userId='+row.userId+'&userName='+row.userName)
}
let cancelCancelRecommend=async(row,recommendType)=>{

        ElMessageBox.confirm(
             recommendType==1?'确认将该日记取消推荐吗?':'确认将该日记取消公众号推荐吗',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
           let res=await cancelDiaryRecommend({
            recordId:row.id,
            recommendType
            })
            if(res){
                ElMessage.success('设置成功')
                emits('emitRecommend',row,recommendType)
           }
           
          
        })
        .catch(() => {
      
        })
   
}
let goRecommend=(row,recommendType)=>{
      ElMessageBox.confirm(
           recommendType==1?'确认将该日记设置为推荐吗?':'确认将该日记设置为公众号推荐吗',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
            let res=await diaryRecommend({
                recordId:row.id,
                recommendType
            })
           if(res){
             ElMessage.success('设置成功')
            emits('emitRecommend',row,recommendType)
          }  
        })
        .catch(() => {
      
        })
}
let goDel=(row)=>{
    ElMessageBox.confirm(
           '确认删除该日记吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
            let res=await diaryDel({
                recordId:row.id
            })
           ElMessage.success('删除成功')
            emits('emitDel',row.id)
             
        })
        .catch(() => {
      
        })
}
let goHide=(row)=>{
    ElMessageBox.confirm(
           '确认隐藏该日记吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
              let res=await diaryHide({
                recordId:row.id
            })
           if(res){
             ElMessage.success('隐藏成功')
                emits('emitHide',row)
           }
           
            
        })
        .catch(() => {
      
        })
}
let goThumb=async(row)=>{
    // console.log(route)
    
    // if(route.path!='/actResearchAssociation/personcenter'){
      let res=await thumbSubmit({
        recordId:row.id
     })
     if(res){
        emits('emitIsLike',row.isLike,row.id,'thumb')
     }
    // }
    
}
let gofavorite=async(row)=>{
   
    //  if(route.path!='/actResearchAssociation/personcenter'){
        let res=await favoriteSubmit({
          recordId:row.id,
          favorite:!row.isFavorite
   })
    if(res){
        emits('emitIsLike',row.isFavorite,row.id,'favorite')
     }
    // }
   
}
let goDetail=(row)=>{
    emits('emitGoDetail',row.id)
    if(route.name=='ActResearchAssociation-square-userRecord'){
       router.push(props.url+'?recordId='+row.id+'&userId='+row.userId)
    }else{
     router.push(props.url+'?recordId='+row.id)
    }
  
   
}
onMounted(()=>{
   nextTick(()=>{
     checkLines()
   })
})
</script>

<style lang="scss" scoped>
.diary{
    margin-bottom: 10px;
    background: #fff;
    padding: 16px 19px;
    &:last-child{
        margin-bottom: 0;
    }
    .head{
        width: 52px;
        height: 52px;
        margin-right: 10px;
    }
    .info{
        .teacher{
            font-size: 16px;
            color: #2B2C33;
            font-weight: bold;
            margin-right: 24px;
            cursor: pointer;
        }
        .stars{
            .star-box{
                margin-right: 4px;
            }
            img{
                width: 24px;
                height: auto;
            }
        }
    }
    .date{
        font-size: 14px;
        color: #94959C;
        margin-top: 4px;
    }
    .action-list{
        li{
            font-size: 14px;
            color: #94959C;
            margin-right: 24px;
            cursor: pointer;
            &:last-child{
                margin-right: 0;
            }
            img{
                width: 14px;
                height: auto;
                margin-right: 4px;
            }
        }
    }
    .rich{
        position: relative;
        cursor: pointer;
        :deep(.rich-html){
        margin-top:8px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        font-size: 16px;
        color: #2B2C33;
        padding-right: 4ch;
        line-height: 24px;
      img{
        max-width:90%
      }
    }
    :deep(.rich-open-html){
        margin-top:8px;
        line-height: 24px;
         font-size: 16px;
        color: #2B2C33;
         img{
        max-width:90%
      }
    }
    .has-more{
        font-size: 16px;
        color: #496EEE;
        position: absolute;
        right: 0;
        bottom:0;
        cursor: pointer;
    }
    }
    .bottom{
        .tag-list{
            li{
                padding: 4px 10px;
                border-radius: 100px;
                font-size: 16px;
                border-radius: 19px;
                border: 1px solid #E2E3E6;
                color: #2B2C33;
                margin: 10px 10px 0 0;cursor: pointer;
                img{
                    width: 16px;
                    height: auto;
                    margin-right: 8px;
                    
                }
            }
        }
        .btn-list{
            margin-top: 10px;
            button{
                cursor: pointer;
             width: 96px;
             height: 30px;
             background: #FFFFFF;
             border-radius: 4px;
             border: 1px solid #E2E3E6;
             img{
                width: 14px;
                height: auto;
                margin-right: 6px;
             }
             font-size: 16px;
             line-height: 16px;
            }
            .del{
                color: #E72C4A;  
                margin-right: 10px;
            }
            .edit{
                color: #508CFF; 
                margin-right: 10px;
            }
            .hide{
                color: #508CFF;
            }
            .hashide{
                color: #B4B6BE;
                border: 1px solid #E6E6E6
            }
            .recommend{
                width: 112px;
                height: 30px;
                cursor: pointer;
                color: #FF9B00;
                background: #fff;
                border: 1px solid #E2E3E6;
                // background: linear-gradient( 270deg, #FF9F50 0%, #FFCF55 100%), #508CFF;
                border-radius: 4px;  
                font-size: 16px;
               line-height: 16px;
            }
            .hasrecommend{
                   width: 112px;
                height: 30px;
                // background: #FFF3E5;
               background: linear-gradient( #FFA919 0%, #FFC33B 100%), #FFFFFF;
                border-radius: 4px;  
                font-size: 16px;
                line-height: 16px;color: #fff;cursor: pointer;
            
            }
               .wxrecommend{
              width: 128px;
                height: 30px;
                cursor: pointer;
                color: #60AE24;
                background: #fff;
                border: 1px solid #E2E3E6;
                // background: linear-gradient( 270deg, #FF9F50 0%, #FFCF55 100%), #508CFF;
                border-radius: 4px;  
                font-size: 16px;
               line-height: 16px;
            }
            .haswxrecommend{
                height: 30px;
                 width: 128px;
                // background: #FFF3E5;
                background: linear-gradient( 180deg, #8CE049 0%, #6DBB31 100%), #FFFFFF;
                border-radius: 4px;  
                font-size: 16px;
                line-height: 16px;color: #fff;cursor: pointer;
            
            }
        }
    }
}
</style>
<template>
     <div class="content-box  upload mt10" >
        <!-- <div class="select-form">
             <div class="flex_start title" @click="goCancel">
                <el-icon style="margin-right: 4px;"><ArrowLeft /></el-icon>
                题集篮
            </div>
          
        </div> -->
         <div class="btn-list flex_end">
              <el-buton class="submit" @click="goSaveExcel">下载为DOC</el-buton>
             <el-buton class="submit" @click="goSave">下载为PDF</el-buton>
        </div>
       <div class="question-list" ref="contentRef" id="question-list" style="margin-bottom:10px">
        <!-- <img src="@/assets/pc/logo.png" alt=""> -->
             <h3 class="question-name" style="text-align:center">{{name}}</h3>
            <div class="question-item" v-if="qdList.length!=0"  >
                 <div v-for="(item,index) in qdList" :key="item.id">
                  <h3 class="question-type">{{questionCategoryMap[item.questionCategory]}}</h3>
                   <div class=" question-content" style="position: relative;"  >
                      <table style="border-collapse: collapse;border:1px solid #fff">
                        <tr  style="border:1px solid #fff">
                            <td style="border:1px solid #fff;vertical-align: text-top;">{{index+1}}.</td>
                            <td v-html="item.questionContent"  style="border:1px solid #fff" class="html"></td>
                        </tr>
                    </table>
                  </div>
                 </div>
                
            </div>
              <!-- <div class="question-item" v-if="qList3.length!=0"  >
                 <h3 class="question-type">解答题</h3>
                 <div class=" question-content" style="position: relative;" v-for="(item,index) in qList3" :key="item.id" >
                   <table style="border-collapse: collapse;border:1px solid #fff">
                        <tr  style="border:1px solid #fff">
                            <td style="border:1px solid #fff;vertical-align: text-top;">{{index+1}}.</td>
                            <td v-html="item.questionContent"  style="border:1px solid #fff" class="html"></td>
                        </tr>
                </table>
                 </div>
            </div>
              <div class="question-item" v-if="qList4.length!=0"  >
                 <h3 class="question-type">填空题</h3>
                 <div class=" question-content" style="position: relative;" v-for="(item,index) in qList4" :key="item.id" >
                   <table style="border-collapse: collapse;border:1px solid #fff">
                        <tr  style="border:1px solid #fff">
                            <td style="border:1px solid #fff;vertical-align: text-top;">{{index+1}}.</td>
                            <td v-html="item.questionContent"  style="border:1px solid #fff" class="html"></td>
                        </tr>
                </table>
                 </div>
            </div>
            <div class="question-item" v-if="qList6.length!=0"  >
                 <h3 class="question-type">判断题</h3>
                 <div class=" question-content" style="position: relative;" v-for="(item,index) in qList6" :key="item.id" >
                   <table style="border-collapse: collapse;border:1px solid #fff">
                        <tr  style="border:1px solid #fff">
                            <td style="border:1px solid #fff;vertical-align: text-top;">{{index+1}}.</td>
                            <td v-html="item.questionContent"  style="border:1px solid #fff" class="html"></td>
                        </tr>
                </table>
                 </div>
            </div> -->
       </div>
       
    </div>
</template>
<script setup>
import WangEditor from '@/views/pc/component/wangeditor.vue'
import {questionSetDetail} from '@/api/pc/index.js'
import {ref,reactive, onMounted, nextTick} from 'vue'
// import htmlDocx from 'html-docx-js';
// import htmlToDocx from 'html-docx-js';
import exportWord from 'html-doc-js'
import {ElMessage,ElMessageBox} from 'element-plus'
import {useRouter,useRoute} from 'vue-router'
import html2pdf from 'html2pdf.js';
import { ElLoading } from 'element-plus'
import handleExportWord from "xh-htmlword";
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'
let userInfo=useUserInfoStoreHook()
let {questionCategoryMap}=storeToRefs(userInfo)
const contentRef = ref(null);
let router=useRouter()
let route=useRoute()
let questionSetId=ref('')
let subjects=ref([])

//1 选择 3 解答 4填空 6判断
 let qdList=ref([])
// let qList1=ref([])
// let qList3=ref([])
// let qList4=ref([])
// let qList6=ref([])

// let qList50=ref([])
// let qList51=ref([])
// let qList52=ref([])
// let qList53=ref([])
// let qList54=ref([])
// let qList55=ref([])

let formRef=ref()
let name=ref('')
let goSaveExcel=async()=>{
const loadingInstance = ElLoading.service({ fullscreen: true,text:'下载中，请稍后' })
const element = contentRef.value;
handleExportWord({
  dom: ".question-list",
  fileName: name.value?name.value:'题集',
  timeOut: 3000,
  callBack: (res) => {
    ElMessage.success('下载成功')
    loadingInstance.close()
  },
  defultImg: "",
  className: ".question-list", //一般给导出文件的最外层盒子的class 用于标记
  drawCanvas: [],
});
}
let goSave=async()=>{
const loadingInstance = ElLoading.service({ fullscreen: true,text:'下载中，请稍后' })
 const element = contentRef.value;
  const opt = {
    margin: 10,
    filename:(name.value?name.value:'题集')+'.pdf',
    image: { type: 'jpeg', quality: 0.98 },
    html2canvas: { scale: 2 },
     pagebreak: { mode: 'avoid-all'},
    jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
  };

 html2pdf().from(element).set(opt).save().then((data)=>{
      ElMessage.success('下载成功')
    loadingInstance.close()
    // console.log(data,'.....................................')
 }).catch(()=>{
     ElMessage.warning('下载失败')
     loadingInstance.close()
 });
}
async function convertImageToBase64(img) {
            return new Promise((resolve, reject) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                canvas.width = img.width;
                canvas.height = img.height;

                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                try {
                    const base64 = canvas.toDataURL();
                    resolve(base64);
                } catch (error) {
                    reject(error);
                }
            });
        }
let getDetail=async()=>{
   let res=await questionSetDetail({
    questionSetId:questionSetId.value
   })
   if(res){
         name.value=res.data.name
         qdList.value=res.data.questions
        // let tempqList1=[]
        //  let tempqList3=[]
        //  let tempqList4=[]
        //  let tempqList6=[]
        //   let tempqList50=[]
        //   let tempqList51=[]
        //   let tempqList52=[]
        //   let tempqList53=[]
        //   let tempqList54=[]
        //   let tempqList55=[]
        //  res.data.questions.forEach(item=>{
        //     if(item.questionCategory==1){
        //         tempqList1.push(item)
        //     }
        //     if(item.questionCategory==3){
        //         tempqList3.push(item)
        //     }
        //     if(item.questionCategory==4){
        //         tempqList4.push(item)
        //     } 
        //     if(item.questionCategory==6){
        //         tempqList6.push(item)
        //     } 
            
        //      if(item.questionCategory==50){
        //         tempqList50.push(item)
        //     } 
        //      if(item.questionCategory==51){
        //         tempqList51.push(item)
        //     } 
        //      if(item.questionCategory==52){
        //         tempqList52.push(item)
        //     } 
        //      if(item.questionCategory==53){
        //         tempqList53.push(item)
        //     } 
        //      if(item.questionCategory==54){
        //         tempqList54.push(item)
        //     } 
        //      if(item.questionCategory==55){
        //         tempqList55.push(item)
        //     } 
       // })
            // qList1.value=tempqList1
        
            // qList3.value=tempqList3
            // qList4.value=tempqList4
            // qList6.value=tempqList6
            nextTick(()=>{
                const images = document.getElementById('question-list').querySelectorAll('img');
                for (const img of images){
                    img.crossOrigin="anonymous"
                }
            })
   }
}
onMounted(()=>{
    
questionSetId.value=route.query.id
  getDetail()
  setTimeout(async()=>{
    const images = document.getElementById('question-list').querySelectorAll('img');
      for (const img of images) {
        try {
            const base64 = await convertImageToBase64(img);
            img.src = base64;
        } catch (error) {
            console.error('转换图片时出错:', error);
    }
    }
  },2000)
})
</script>
<style lang="scss" scoped>

.question-list{

    padding-bottom: 24px;
    background: #FFFFFF;
   border-radius: 8px;
   margin-top: 10px;
   .question-name{
    text-align: center;
    font-size: 24px;
    padding: 24px 0 0 0;
   }
   .choice-question{
    font-size: 16px;
    color: #4260FD;
    background: #FAFBFF;
    border-radius: 12px;
    padding: 20px 0;
    margin:16px 24px 0 24px;
    cursor: pointer;
    img{
        width: 24px;
        margin-right: 8px;
    }
   }
  .question-item{
  
    padding: 24px 0 0 0;
    .question-type{
        font-size: 18px;
        color: #2B2C33;
        font-weight: bold;
       
        padding:6px 24px 6px 24px;
    }
    .question-content{
        min-height: 168px;
        padding: 24px;
          &:nth-child(2n){
           background: #f9f9f9;
    }
    }
    .del-btn{
        padding: 6px 12px;
        background: #fff;
        border-radius: 100px;
        font-size: 14px;
        height: 32px;
        color: #6D6F75;
        cursor: pointer;
        border: 1px solid #E2E3E6;
    }
  }
}
 .btn-list{
    padding: 10px 0 10px 0;
  
    .submit{
        padding: 11px 44px;
         line-height: 26px;
        background: #508CFF;
        border-radius: 12px;
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        margin-left: 16px;
        cursor: pointer;
    }
 }
 .flex_start_0{
    display: flex;
    justify-content: flex-start;
 }
:deep(.html){
    img{
        max-width: 100%
     
      }
    .question{
        margin-bottom:16px;
      img{
        max-width: 100% !important;
        max-height: 100% !important;
      }
    
    }
     ol{
      list-style-type:upper-alpha;
      li{
        margin-bottom: 8px;
      }
      }
}
</style>
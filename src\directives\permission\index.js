import { useUserStore } from "@/store/modules/user"
import { useUserInfoStoreHook } from "@/store/modules/pc"

export function hasPermission(role) {
  const userInfo = sessionStorage.getItem("userInfo") ? JSON.parse(sessionStorage.getItem("userInfo")) : {}
  const { roles } = userInfo
  return roles.some((role) => role.includes(role))
}

/** 权限指令，和权限判断函数 checkPermission 功能类似 */
export const permission = {
  mounted(el, binding) {
    const { value: permissionRoles } = binding
    const userInfo = sessionStorage.getItem("userInfo") ? JSON.parse(sessionStorage.getItem("userInfo")) : {}
    const { roles } = userInfo
    if (Array.isArray(permissionRoles) && permissionRoles.length > 0) {
      const hasPermission = roles.some((role) => permissionRoles.includes(role))
      // hasPermission || (el.style.display = "none") // 隐藏
      hasPermission || el.parentNode?.removeChild(el) // 销毁
    } else {
      throw new Error(`need roles! Like v-permission="['admin','editor']"`)
    }
  }
}

/** 权限指令，和权限判断函数 checkPermission 功能类似 */
export const pcpermission = {
  mounted(el, binding) {
    const { value: permissionRoles } = binding
    const { roles } = useUserInfoStoreHook()
    if (Array.isArray(permissionRoles) && permissionRoles.length > 0) {
      const hasPermission = roles.some((role) => permissionRoles.includes(role))
      // hasPermission || (el.style.display = "none") // 隐藏
      hasPermission || el.parentNode?.removeChild(el) // 销毁
    } else {
      throw new Error(`need roles! Like v-permission="['admin','editor']"`)
    }
  }
}

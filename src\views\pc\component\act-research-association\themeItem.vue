<template>
  <div class="flex_start_0 diary">
        <el-avatar :src="themeItem.userAvatarUrl || DefaultAvatar" class="avatar head" :size="32" @click="goPreview(themeItem)" />
          <!-- <img src="@/assets/pc/teacher.png" alt="" class="head" /> -->
          <div class="flex_1">
                 <div class="flex_between">
                    <div class="info">
                       <p class="teacher">{{themeItem.userName}}</p>
                        <p class="date">{{themeItem.postTime}}</p>
                    </div>
                    
                 </div>
                 <div class="rich">
                    <div :class="!isopen?'rich-html':'rich-open-html'" ref="richRef" @click="goDetail" v-html='themeItem.content'>
                      
                     </div>
                    
                 </div>
               
                 <div class="bottom flex_between">
                    <ul class="flex_start tag-list">
                        <li class="flex_start" >
                            <img src="@/assets/pc/tag.png" alt="">
                            <p>{{themeItem.clubTopicName}}</p>
                        </li>
                    </ul>
                    <div class="flex_start btn-list" v-show="userInfo.id==themeItem.userId">
                         
                            <button class='flex_center edit'  @click="goEdit(themeItem)">
                              <el-icon :size="20" style="margin-right:10px">
                                <Edit />
                            </el-icon>
                           编辑
                         </button>
                        <button class='flex_center del'  @click="goDel(themeItem)">
                           <img src='@/assets/pc/person-del.png' />
                           删除
                        </button>
                    </div>
                 </div>
                
          </div>
  </div>
   <el-image-viewer
        v-if="showPreview"
        :url-list="previewurlList"
        show-progress
        :initial-index="0"
        @close="showPreview = false"
      />
</template>

<script setup>
import {onMounted, ref,defineProps,defineEmits} from 'vue'
import {useRouter} from 'vue-router'
import { ElMessage,ElMessageBox } from 'element-plus'
// import {showImageViewer} from '@/utils/imagePreview.js'
import DefaultAvatar from "@/assets/layouts/default-avatar.png"
let starnum=ref(3)
let richRef=ref()
let hasmore=ref(false)
let props=defineProps(['themeItem','clubPostType','userInfo'])
let emits=defineEmits(['emitScrollTop','emitDelTheme'])
let isopen=ref(false)
let previewurlList=ref([])
let showPreview=ref(false)
function checkLines() {
    // 获取文字容器元素
    const textElement = richRef.value;
    // 比较元素的 scrollHeight 和 clientHeight
    if (textElement.scrollHeight > textElement.clientHeight) {
        console.log('文字超过了四行');
        hasmore.value=true
    } 
}
let router=useRouter()
let goDetail=()=>{
  emits('emitScrollTop')
   router.push('/actResearchAssociation/theme/detail?clubPostId='+props.themeItem?.id+'&clubPostType='+props.clubPostType)
}
let goEdit=(row)=>{
    router.push('/actResearchAssociation/theme/edit?id='+row.id+'&clubPostType='+props.clubPostType)
}
let goDel=(row)=>{
   ElMessageBox.confirm(
            '确认删除吗',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
           emits('emitDelTheme',row)
        })
        .catch(() => {
      
        })
}
let goPreview=(row)=>{
    if(row.userAvatarUrl){
         previewurlList.value=[row.userAvatarUrl]
        showPreview.value=true
    }
}
onMounted(()=>{
    checkLines()
})
</script>

<style lang="scss" scoped>
.diary{
    margin-bottom: 10px;
    background: #fff;
    padding: 16px 19px;
    cursor: pointer;
    &:last-child{
        margin-bottom: 0;
    }
    .head{
        width: 52px;
        height: 52px;
        margin-right: 10px;
    }
    .info{
        .teacher{
            font-size: 16px;
            color: #2B2C33;
            font-weight: bold;
            margin-right: 24px;
        }
     
    }
    .date{
        font-size: 14px;
        color: #94959C;
        margin-top: 4px;
    }
  
    .rich{
        position: relative;
        .rich-html{
        margin-top:8px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        font-size: 16px;
        color: #2B2C33;
      padding-right: 4ch;
      line-height: 24px;
    }
   

    }
    .bottom{
        .tag-list{
            li{
                padding: 4px 10px;
                border-radius: 100px;
                font-size: 16px;
                border-radius: 19px;
                border: 1px solid #E2E3E6;
                color: #2B2C33;
                margin: 10px 10px 0 0;cursor: pointer;
                img{
                    width: 16px;
                    height: auto;
                    margin-right: 8px;
                    
                }
            }
        }
    }
}
 .btn-list{
           
            button{
                cursor: pointer;
             width: 96px;
             height: 30px;
             background: #FFFFFF;
             border-radius: 4px;
             border: 1px solid #E2E3E6;
             img{
                width: 14px;
                height: auto;
                margin-right: 6px;
             }
             font-size: 16px;
             line-height: 16px;
            }
            .del{
                color: #E72C4A;  
                margin-right: 10px;
            }
            .edit{
                color: #508CFF; 
                margin-right: 10px;
            }
            }
</style>
<template>
    <div class="content-box flex_start_0 mt10">
      
       <div class="left">
           <div class="person-column">
           
              <p class="title">里程管理</p>
              <ul class="list">
                <li :class="['flex_between',item.id==activeIndex?'active':'']" v-for="item in diaryList" :key="item.id" @click="changeActive(item)">
                    <div class="flex_start">
                        
                        <p>{{item.title}}</p>
                    </div>
                    <el-icon><ArrowRight /></el-icon>
                </li>
              </ul>
           </div>
       </div>
      
       <div class="flex_1 right">
       
          <router-view></router-view>
       </div>
       
    </div>
</template>
<script setup>
import {onActivated, onMounted, reactive, ref} from 'vue'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {useRouter} from 'vue-router'
import {recordProfile,memberDiary,leaderDiary,leaderRecommendedDiary,tutorR<PERSON>ommended<PERSON>iary,myCollect,} from '@/api/pc/index.js'
import FriendLink from '@/views/pc/component/act-research-association/friendLink.vue'

import {storeToRefs} from 'pinia'
let userInfoStore=useUserInfoStoreHook()
let {personcenterCurrentInfo,userInfo}=storeToRefs(userInfoStore)
let diaryList=ref([{id:'1',title:'规则设置'},{id:'5',title:'线下录入'}])
let activeIndex=ref(1)
let router=useRouter()
let num=ref('')
let tableData=ref([
    {
        date:'登录成功成功',
        num:''
    }
])

</script>
<style lang="scss" scoped>
.mt10{
    margin-top: 10px;
}
.left{
    width: 180px;
    .person-column{
        border-radius: 8px;
        background: #fff;
        padding-bottom: 16px;
        .title{
             padding: 18px 0  24px 18px;
             font-weight: bold;
             font-size: 20px;
             color: #2B2C33;
        }
        .list{
            li{
                cursor: pointer;
                padding: 10px 8px  8px 18px;
                font-size: 16px;
                color: #2B2C33;
                margin-bottom: 6px;
                p{
                    line-height: 16px;
                }
                &.active{
                    background: #DFEAFF;color: #508CFF;
                }
                img{
                    width: 14px;
                    margin-right: 8px;
                    height: auto;
                }
            }
        }
    }
  
}
.right{
    margin-left: 10px;
    height: calc(100vh - 140px);
    overflow: auto;
  
}
</style>
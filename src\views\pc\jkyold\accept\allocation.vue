<template>
    <div>
        <el-tabs v-model="activeTab" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane v-for="item in review_section" :key="item.reviewSectionId" :label="item.reviewSectionName" :name="item.reviewSectionName">
                <el-table :data="item.schoolReviews" class="my-table" style="width: 100%;margin-top:8px" :header-cell-style='{"color":"#94959C",fontSize:"14px",background: "#F8F9FF"}'>
                    <el-table-column prop="schoolReviewUserId" label="专家姓名" show-overflow-tooltip width="200">
                        <template  #default='scope'>
                            <el-select v-model="scope.row.schoolReviewUserId" :disabled="!isAssignCapable" class="select_40" clearable placeholder="请选择">
                                <el-option v-for="item in user_list" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </template>
                    </el-table-column> 
                    <el-table-column prop="address" label="材料">
                        <template #default='scope'>
                            <div class="flex_between" style="height:100%">
                                <div class="mr_32" v-if="scope.row.schoolReviewMaterials && scope.row.schoolReviewMaterials.length > 0">
                                    <p class="material-name" v-for="(i,index) in scope.row.schoolReviewMaterials" :key="i.fileId">{{i.fileName}}<el-icon class="ml_20 pointer" v-if="isAssignCapable" @click="deleteFile(scope.row.schoolReviewMaterials, index)"><Close /></el-icon></p>
                                </div>
                                <div class="flex_end" v-if="isAssignCapable">
                                    <el-upload
                                        :file-list="scope.row.schoolReviewMaterials"
                                        action="Fake Action"
                                        show-file-list="false"
                                        multiple
                                        :http-request="(file) => uploadFile(file, scope.row)"
                                        :on-remove="handleRemove"
                                        :before-upload='beforeUpload'
                                    >
                                        <el-button type="text" class="upload-text-btn" :icon="Plus">点击上传</el-button>
                                    </el-upload>
                                    <p class="ml_20 pointer flex_start red text_16" @click="deleteTask(item.schoolReviews, scope.row)"><el-icon class="mr_3"><Delete /></el-icon></p>
                                </div>
                            </div> 
                        </template>
                    </el-table-column>
                </el-table>
                <el-button class="plain-blue-btn btn_96_48 mt_30" type="plain" v-if="isAssignCapable" :icon="Plus" style="width:100%;" @click="addClick(item)">新增</el-button>
            </el-tab-pane>
        </el-tabs>
        <div class="flex_end mt_24">
            <el-button class="plain-btn btn_96_48" type="plain" @click="emit('close')">取消</el-button>
            <el-button class="primary-btn btn_96_48" v-if="isAssignCapable" type="primary" @click="submitClick">确认</el-button>
        </div>
    </div>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue' 
    import { Plus, Close, Delete } from '@element-plus/icons-vue'
    import { userOption, schAllocationDetail, schAllocate } from '@/api/pc/jky'
    import {ElMessage,ElLoading,ElMessageBox} from 'element-plus'
    import {uploadFileFunc} from '@/utils/pc-upload.js'
    const activeTab = ref('')
    const expert_list = ref([{}])
    const review_section = ref([])
    const user_list = ref([])
    const props = defineProps({
        id: '', // 默认值
    });
    const emit = defineEmits(['close'])
    const current_info = ref({})
    const isAssignCapable = ref(null)

    const getUserList = async () => {
        let res = await userOption()
        if(res){
            user_list.value = res.data
        }
    }

    const getDetail = async () => {
        let res = await schAllocationDetail(props.id)
        if(res){
            review_section.value = res.data.reviewSections
            isAssignCapable.value = res.data.isAssignCapable
            activeTab.value = res.data.reviewSections[0].reviewSectionName
            current_info.value = res.data.reviewSections[0]
        }
    }
    const addClick = (item) => {
        if(!item.schoolReviews) item.schoolReviews = []
        item.schoolReviews.push({
            schoolReviewUserName: '',
            schoolReviewMaterials: [],
        })
    }

    const deleteFile = (arr, index) => {
        arr.splice(index, 1)
    }

    function getQueryParams(src,type) {
        const url = new URL(src);
        const params = url.searchParams.get(type);
        return params
    }
    const getExtensionString = (str) => {
        var index = str.lastIndexOf('.')
        // 如果找到了第一个"."并且它不在字符串的起始处或结尾处
        if (index != -1 && index != 0 && index + 1 != str.length) {
            return str.substr(index + 1)
        } else {
            return ''
        }
    }
    const beforeUpload=(file)=>{
        let type = getExtensionString(file.name)
        let allowType=['doc','docx','pdf']
        if(allowType.indexOf(type)>-1){
            return true
        }else{
            ElMessage.warning('请上传以下格式的文件'+allowType.join('，'))
            return false
        }
    }
    const uploadFile = (file,row) => {
        console.log(file,'filesdfr')
        uploadFileFunc(file.file,(url) => {
            row.schoolReviewMaterials.push({
                url:url,
                fileName:file.file.name,
                fileId:getQueryParams(url,'fileId')
            })
            console.log(add_form.value.reviewSections,'上传文件')

        })
    }

    const deleteTask = (arr, row) => {
        arr.splice(arr.indexOf(row), 1)
    }

    const submitClick = async () => {
        let res = await schAllocate(props.id,{reviewSections:review_section.value})
        if(res){
            ElMessage.success('发布成功')
            emit('close')
        }
    }

    onMounted(() => {
        getDetail()
        getUserList()
    })
</script>
<style lang="scss" scoped>
:deep(.el-upload-list){display: none;}
:deep(.el-button [class*=el-icon]+span){margin-left: 3px;}
:deep(.el-tabs__item){font-family: HarmonyOS_Sans_SC; font-size: 16px; line-height: 20px; text-align: left; font-style: normal;}
.upload-text-btn{font-family: HarmonyOS_Sans_SC;font-weight: 400; font-size: 16px; color: #508CFF; line-height: 20px; text-align: left; font-style: normal;}
:deep(.el-table .cell){height: auto!important;}
.material-name{line-height: normal;margin: 10px 0;}
</style>
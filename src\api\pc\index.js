import { request } from "@/utils/service"
///fs/file
export function uploadFile(data) {
  return request({
    url: "/fs/file",
    method: "post",
    data
  })
}
//
export function groupList() {
  return request({
    url: "/xzs/club-group/me",
    method: "get",
  })
}
export function uploadHeaderFile(data) {
  return request({
    url: "/me/avatar",
    method: "patch",
    data
  })
}
export function uploadHtmlFile(data) {
  return request({
    url: "/xzs/club-record/me/image",
    method: "post",
    data
  })
}
//获取通知数量

export function noticeUnViewCount() {
  // return request({
  //   url: "/xzs/club-notice/unView-count",
  //   method: "get",
  // })
}
//通知浏览 /xzs/club-notice/{}/view
// export function noticeView(data) {
//   return request({
//     url: "/xzs/club-notice/"+data.clubNoticeId+'/view',
//     method: "get",
//   })
// }
export function login(data) {
  return request({
    url: "/login",
    method: "post",
    data
  })
}
///dxs/xzs/club-record/public/location
export function userInfo() {
  return request({
    url: "/user-details",
    method: "get",

  })
}
export function themeAdd(data) {
  return request({
    url: "/xzs/club-topic",
    method: "post",
    data
  })
}
// export function themeEdit(data) {
//   return request({
//     url: "/xzs/club-topic/" + data.id,
//     method: "put",
//     data
//   })
// }
export function themeEdit(data) {
  return request({
    url: "/xzs/club-post/me/" + data.clubPostId,
    method: "put",
    data
  })
}
// {}/xzs/club-post/me/{clubPostId}
export function themeDel(data) {
  return request({
    url: "/xzs/club-post/me/" + data.clubPostId,
    method: "delete",
   
  })
}
export function myGroups(data) {
  return request({
    url: "/xzs/club-group/me",
    method: "get",
    params: data
  })
}
export function themes(data) {
  return request({
    url: "/xzs/club-topic/option",
    method: "get",
    params: data
  })
}

// export function themeDel(data) {
//   return request({
//     url: "/xzs/club-topic/" + data.id,
//     method: "delete",
//   })
// }


export function diaryEdit(data) {
  return request({
    url: "/xzs/club-record/me/" + data.recordId,
    method: "put",
    data
  })
}
export function draftSave(data) {
  return request({
    url: "/xzs/club-record-draft/me",
    method: "post",
    data
  })
}
export function draftGet(data) {
  return request({
    url: "/xzs/club-record-draft/me",
    method: "get",
    params: data
  })
}

export function draftDel(data) {
  return request({
    url: "/xzs/club-record-draft/me",
    method: "delete",
    data
  })
}
export function draftSubmit(data) {
  return request({
    url: "/xzs/club-record/me",
    method: "post",
    data
  })
}
//
export function recordProfile(data) {
  return request({
    url: "/xzs/club-record/me/profile",
    method: "get",

  })
}

export function recordDetail(data) {
  return request({
    url: "/xzs/club-record/me/" + data.recordId,
    method: "get",

  })
}
///xzs/club-record/{recordId}/view
export function recordView(data) {
  return request({
    url: "/xzs/club-record/" + data.recordId + '/view',
    method: "get",

  })
}

export function memberDiary(data) {
  return request({
    url: "/xzs/club-record/me",
    method: "get",
    params: data
  })
}
export function memberDiaryLocation(data) {
  return request({
    url: "/xzs/club-record/me/location",
    method: "get",
    params: data
  })
}
export function leaderDiary(data) {
  return request({
    url: "/xzs/club-record/me/director-record",
    method: "get",
    params: data
  })
}
export function directorDiary(data) {
  return request({
    url: "/xzs/club-record/public/director-user-record",
    method: "get",
    params: data
  })
}
export function directorDiaryRecommend(data) {
  return request({
    url: "/xzs/club-record/public/director-recommended-record",
    method: "get",
    params: data
  })
}
export function tutorDiary(data) {
  return request({
    url: "/xzs/club-record/public/tutor-user-record",
    method: "get",
    params: data
  })
}
export function tutorDiaryRecommend(data) {
  return request({
    url: "/xzs/club-record/public/tutor-recommended-record",
    method: "get",
    params: data
  })
}
export function leaderRecommendedDiary(data) {
  return request({
    url: "/xzs/club-record/me/director-recommended-record",
    method: "get",
    params: data
  })
}
export function tutorRecommendedDiary(data) {
  return request({
    url: "/xzs/club-record/me/tutor-recommended-record",
    method: "get",
    params: data
  })
}

export function myCollect(data) {
  return request({
    url: "/xzs/club-record/me/favorite",
    method: "get",
    params: data
  })
}
export function myCollectLocation(data) {
  return request({
    url: "/xzs/club-record/me/favorite/location",
    method: "get",
    params: data
  })
}
//
export function thumbSubmit(data) {
  return request({
    url: "/xzs/club-record/" + data.recordId + "/like",
    method: "post",

  })
}
export function favoriteSubmit(data) {
  return request({
    url: "/xzs/club-record/" + data.recordId + "/favorite?favorite=" + data.favorite,
    method: "post",

  })
}
///xzs/club-record/{recordId}/comment

export function sendComment(data) {
  return request({
    url: "/xzs/club-record/" + data.recordId + '/comment',
    method: "post",
    data
  })
}
//{}
export function editComment(data) {
  return request({
    url: "/xzs/club-record-comment/" + data.commentId,
    method: "put",
    data
  })
}
export function delComment(data) {
  return request({
    url: "/xzs/club-record-comment/" + data.commentId,
    method: "delete",
    data
  })
}
export function recordComment(data) {
  return request({
    url: "/xzs/club-record-comment/" + data.commentId + '/like?like=' + data.like,
    method: "post",

  })
}
export function commentList(data) {
  return request({
    url: "/xzs/club-record/" + data.recordId + '/comment',
    method: "get",
    params: data
  })
}

///xzs/club-record/{recordId}/rate
export function commentStar(data) {
  return request({
    url: "/xzs/club-record/" + data.recordId + '/rate',
    method: "post",
    data: data
  })
}
export function likeList(data) {
  return request({
    url: "/xzs/club-record/" + data.recordId + '/like',
    method: "get",

  })
}
export function squareAll(data) {
  return request({
    url: "/xzs/club-record/public",
    method: "get",
    params: data
  })
}
// export function squareHasRead(data){
//   return request({
//       url: "/xzs/club-record/public/view",
//       method: "get",
//       params:data
//     })
// }
// export function squareNoRead(data){
//   return request({
//       url: "/xzs/club-record/public/unview",
//       method: "get",
//       params:data
//     })
// }

export function squareLocation(data) {
  return request({
    url: "/xzs/club-record/public/location" ,
    method: "get",
    params:data
  })
}
//
export function squareDirectorLocation(data) {
  return request({
    url: "/xzs/club-record/public/director-user-record/location" ,
    method: "get",
    params:data
  })
}
export function squareDirectorRecommendLocation(data) {
  return request({
    url: "/xzs/club-record/public/director-recommended-record/location" ,
    method: "get",
    params:data
  })
}
export function squareTutorLocation(data) {
  return request({
    url: "/xzs/club-record/public/tutor-user-record/location" ,
    method: "get",
    params:data
  })
}
export function squareTutorRecommendLocation(data) {
  return request({
    url: "/xzs/club-record/public/tutor-recommended-record/location" ,
    method: "get",
    params:data
  })
}
export function wxMpRecommendLocation(data) {
  return request({
    url: "/xzs/club-record/public/wx-mp-recommended-record/location" ,
    method: "get", 
    params:data
  })
}
export function squareDetail(data) {
  return request({
    url: "/xzs/club-record/public/" + data.recordId,
    method: "get",
  })
}

export function themeList(data) {
  return request({
    url: "/xzs/club-post/me",
    method: "get",
    params: data
  })
}
export function themeDetail(data) {
  return request({
    url: "/xzs/club-post/me/" + data.clubPostId,
    method: "get",

  })
}
///xzs/club-post/me
export function themeGroups(){
  return request({
    url: "/xzs/club-post/groups",
    method: "get",
  })
}
//
export function themePostAdd(data) {
  return request({
    url: "/xzs/club-post/me",
    method: "post",
    data
  })
}
export function themeComment(data) {
  return request({
    url: "/xzs/club-post/" + data.postId + '/pageable-comment', 
    method: "get",
    params: data
  })
}

export function themeCommentAdd(data) {
  return request({
    url: "/xzs/club-post/" + data.postId + '/comment',
    method: "post",
    data
  })
}
export function themeCommentDel(data) {
  return request({
    url: "/xzs/club-post-comment/" + data.commentId,
    method: "delete",

  })
}
export function themeCommentEdit(data) {
  return request({
    url: "/xzs/club-post-comment/" + data.commentId,
    method: "put",
    data
  })
}
//
export function clubNotice(data) {
  return request({
    url: "/xzs/club-notice/me/club-notice",
    method: "get",
    params: data
  })
}
export function groupNotice(data) {
  return request({
    url: '/xzs/club-notice/me/group-notice',
    method: "get",
    params: data
  })
}
export function noticeAdd(data) {
  return request({
    url: '/xzs/club-notice',
    method: "post",
    data
  })
}

export function noticeDetail(data) {
  return request({
    url: '/xzs/club-notice/' + data.clubNoticeId,
    method: "get",
    params: data
  })
}
export function noticeView(data) {
  return request({
    url: '/xzs/club-notice/' + data.clubNoticeId + '/view',
    method: "get",
    params: data
  })
}
//{recordId}/hide
export function diaryHide(data) {
  return request({
    url: '/xzs/club-record/' + data.recordId + '/hide',
    method: "post",

  })
}
export function diaryDel(data) {
  return request({
    url: '/xzs/club-record/' + data.recordId,
    method: "delete",

  })
}
export function diaryRecommend(data) {
  return request({
    url: '/xzs/club-record/' + data.recordId + '/recommend',
    method: "post",
    data
  })
}
//

export function wxRecommend(data) {
  return request({
    url: '/xzs/club-record/public/wx-mp-recommended-record',
    method: "get",
    params:data
  })
}
export function cancelDiaryRecommend(data) {
  return request({
    url: '/xzs/club-record/' + data.recordId + '/recommend',
    method: "delete",
    params:data
  })
}
// 题库接口

export function subjectList(data) {
  return request({
    url: '/k12/subject/option',
    method: "get",
    params: data
  })
}

export function editionList(data) {
  return request({
    url: '/qb/book/edition/option',
    method: "get",
    params: data
  })
}

export function volumeList(data) {
  return request({
    url: '/qb/book/volume/option',
    method: "get",
    params: data
  })
}

export function chapterTree(data) {
  return request({
    url: '/qb/chapter/tree',
    method: "get",
    params: data
  })
}
//
export function questionAdd(data) {
  return request({
    url: '/qb/question',
    method: "post",
    data
  })
}
export function questionEdit(data) {
  return request({
    url: '/qb/question/' + data.questionId,
    method: "put",
    data
  })
}

export function questionDel(data) {
  return request({
    url: '/qb/question/' + data.questionId,
    method: "delete",
  })
}
export function questionDetail(data) {
  return request({
    url: '/qb/question/' + data.questionId,
    method: "get",
  })
}
export function questionList(data) {
  return request({
    url: '/qb/question',
    method: "get",
    params: data
  })
}
export function basketAdd(data) {
  return request({
    url: '/qb/question-basket',
    method: "post",
    data
  })
}
export function basketCount(data) {
  return request({
    url: '/qb/question-basket/count',
    method: "get",
  })
}
export function basketDel(data) {
  return request({
    url: '/qb/question-basket',
    method: "delete",
    data
  })
}


export function basketEntry() {
  return request({
    url: '/qb/question-basket/entry',
    method: "get",
  })
}


export function saveQuestionSet(data) {
  return request({
    url: '/qb/question-set',
    method: "post",
    data
  })
}
export function saveQuestionEdit(data) {
  return request({
    url: '/qb/question-set/'+data.questionSetId,
    method: "put",
    data
  })
}
export function questionSetList(data) {
  return request({
    url: '/qb/question-set?keyword=' + data.keyword  + '&pageSize=' + data.pageSize + '&pageNum=' + data.pageNum + '&beginCreateTime=' + (data.beginCreateTime?data.beginCreateTime.replace(" ", '%20'):'') + '&endCreateTime=' +  (data.endCreateTime?data.endCreateTime.replace(" ", '%20'):""),
    method: "get",
    //  params:{
    //   name:data.name,
    //   beginCreateTime:data.beginCreateTime.replace(" ",'%20'),
    //   endCreateTime:data.endCreateTime.replace(" ",'%20'),
    //   pageSize:data.pageSize,
    //   pageNum:data.pageNum
    //  }
  })
}
export function questionSetDetail(data) {
  return request({
    url: '/qb/question-set/' + data.questionSetId,
    method: "get",
    params: data
  })
}
//统计分析

export function numberByAge(data) {
  return request({
    url: '/xzs/dashboard/statistics/number-of-user-group-by-age-range',
    method: "get",
    params: data
  })
}
export function exportByAge() {
  return request({
    url: '/xzs/dashboard/statistics/number-of-user-group-by-age-range/file',
    method: "get",
  })
}
export function numberByGroup(data) {
  return request({
    url: '/xzs/dashboard/statistics/number-of-user-group-by-group',
    method: "get",
    params: data
  })
}
export function numberByStage(data) {
  return request({
    url: '/xzs/dashboard/statistics/number-of-user-group-by-stage',
    method: "get",
    params: data
  })
}
export function numberBySubject(data) {
  return request({
    url: '/xzs/dashboard/statistics/number-of-user-group-by-subject',
    method: "get",
    params: data
  })
}
export function numberByTenant(data) {
  return request({
    url: '/xzs/dashboard/statistics/number-of-user-group-by-tenant',
    method: "get",
    params: data
  })
}

//
export function numberByMember() {
  return request({
    url: '/xzs/dashboard/statistics/number-of-record-group-by-group',
    method: "get",
  })
}

export function numberByMemberUser() {
  return request({
    url: '/xzs/dashboard/statistics/number-of-record-group-by-user',
    method: "get",
  })
}

//
export function recommendRecord(data) {
  return request({
    url: '/xzs/club-record/public/recommended-record',
    method: "get",
    params: data
  })
}

//
export function resourceBatchUpload(data) {
  return request({
    url: '/rcs/admin/resource/batch',
    method: "post",
    data
  })
}
//
export function resources(data) {
  return request({
    url: '/rcs/portal/resource',
    method: "get",
    params: data
  })
}
///dxs
export function userTenant(data) {
  return request({
    url: '/user-tenant',
    method: "get",

  })
}
//{}
export function userTenantToParent(data) {
  return request({
    url: '/parent-tenant-id/'+data.tenantId,
    method: "get",

  })
}
export function resourcePortalDetail(data) {
  return request({
    url: '/rcs/portal/resource/' + data.resourceId,
    method: "get",

  })
}
export function resourceAdminDetail(data) {
  return request({
    url: '/rcs/admin/resource/' + data.resourceId,
    method: "get",

  })
}
export function resourceRescommend(data) {
  return request({
    url: '/rcs/portal/resource/recommend/' + data.resourceId,
    method: "get",

  })
}
export function resourceFavourite(data) {
  return request({
    url: '/rcs/portal/resource/favourite/' + data.resourceId,
    method: "put",

  })
}
export function resourceLike(data) {
  return request({
    url: '/rcs/portal/resource/like/' + data.resourceId,
    method: "put",

  })
}
//{resourceId}
export function resourceDel(data) {
  return request({
    url: '/rcs/admin/resource/' + data.resourceId,
    method: "delete",

  })
}
export function resourceAdminEdit(data) {
  return request({
    url: '/rcs/admin/resource/' + data.resourceId,
    method: "put",
    data
  })
}
export function resourceDetail(data) {
  return request({
    url: '/rcs/admin/resource/' + data.resourceId,
    method: "get",

  })
}
export function resourceShare(data) {
  return request({
    url: '/rcs/portal/resource/share/' + data.resourceId,
    method: "put",

  })
}

//
export function resourceUpLevel(data) {
  return request({
    url: '/rcs/portal/resource/process',
    method: "post",
    data
  })
}
//
export function resourceCheckList(data) {
  return request({
    url: '/rcs/admin/resource/process',
    method: "get",
    params: data
  })
}
export function resourceCheck(data) {
  return request({
    url: '/rcs/admin/resource/process/' + data.processId+"?status="+data.status,
    method: "post",
    data
  })
}
//
export function sendCaptcha(data) {
  return request({
    url: '/captcha-sms',
    method: "post",
    data
  })
}
export function sendCaptchaToLogin(data) {
  return request({
    url: "/mobile/login",
    method: "post",
    data
  })
}
export function resetPassword(data) {
  return request({
    url: '/reset-password',
    method: "patch",
    data
  })
}
export function getQrCode(data) {
  return request({
    url: '/wx/mp/bind/qrcode',
    method: "get",
  })
}
//
export function getQrLoginCode(data) {
  return request({
    url: '/wx/login/authorize/qrcode',
    method: "get",
  })
}
//
export function getQrLoginStatus(data) {
  return request({
    url: '/wx/login/authorize/qrcode/status',
    method: "get",
    params:data
  })
}
//
export function getBindStatus(data) {
  return request({
    url: '/wx/mp/bind/status',
    method: "get",
    params:data
  })
}
//
export function getCancelBindStatus(data) {
  return request({
    url: '/wx/mp/bind/cancel',
    method: "delete",
    
  })
}

export function exportSixStarRecord(data) {
  return request({
    url: '/xzs/club-record/public/group/' + data.clubGroupId + '/six-star-record/file',
    method: "get",
  })
}

// 
export function exportGroupAllRecord(data) {
  return request({
    url: '/xzs/club-record/public/group/' + data.clubGroupId + '/record/file',
    method: "get",
  })
}

//{userId}/record

export function userRecordList(data) {
  return request({
    url: '/xzs/club-record/public/user/' + data.userId + '/record',
    method: "get",
    params:data
  })
}
export function userRecordLocation(data) {
  return request({
    url: '/xzs/club-record/public/user/' + data.userId + '/record/location',
    method: "get",
    params:data
  })
}
// 查看历史消息
export function noticeHistory(data) {
  return request({
    url: '/xzs/club-notice/me/received-history-notice',
    method: "get",
    params:data
  })
}
//
export function noticeNew(data) {
  return request({
    url: '/xzs/club-notice/me/not-viewed',
    method: "get",
    params:data
  })
}
//
export function mynoticeNews(data) {
  return request({
    url: '/xzs/club-notice/me/released-notice',
    method: "get",
    params:data
  })
}

export function mynoticeDel(data) {
  return request({
    url: '/xzs/club-notice/'+data.noticeId,
    method: "delete",
   
  })
}
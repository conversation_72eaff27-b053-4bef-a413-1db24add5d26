<template>
    <div class="content-box  question mt10">
       <div class="flex_between top">
            <div class="flex_start title" @click="$router.go(-1)">
                <el-icon style="font-siez:20px"><ArrowLeft /></el-icon>
                <h3>备课组管理</h3>
            </div>
            <button class="flex_center" @click="dialogVisible=true;groupId=''">
                <img src="@/assets/pc/add-group-bk.png" alt="">
                新建备课组
            </button>
       </div>
   
       <el-table :data="tableData" style="width: 100%;margin-top:16px" :header-cell-style='{"color":"#94959C",fontSize:"16px",background: "#F9F9F9"}'>
            <el-table-column prop="name" label="序号">
                <template #default='scope'>
                    {{((form.pageNum-1)*form.pageSize)+(scope.$index+1)}}
                </template>
             </el-table-column>
            <el-table-column prop="name" label="备课组名称" show-overflow-tooltip />
            <el-table-column prop="subjectName" label="学科" />
            <el-table-column prop="gradeName" label="年级" />
            <el-table-column prop="leaderName" label="备课组组长" show-overflow-tooltip />
            <el-table-column prop="memberNum" label="备课组人员数量" />  
            <el-table-column prop="address" label="操作">
                <template #default='scope'>
                    <el-button type="primary" link style="font-size:16px"  @click="goEdit(scope.row)">编辑</el-button>
                    <el-button type="primary" link style="font-size:16px" @click="goDel(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
          <el-pagination
                background
                layout="total,prev, pager, next"
                :total="total"
                v-model:page-size='form.pageSize'
                class="mt-4"
                @current-change='currentChange'
         />
         <el-dialog
            v-model="dialogVisible"
            :title="groupId?'编辑备课组':'新增备课组'"
            width="920"
            :before-close="cancel"
            :close-on-click-modal='false'
        >
      
             <el-form ref="formRef"  :model="formInline" :rules="rules" class="demo-form-inline flex_between flex_wrap" label-position="top">
                <el-form-item label="学校" prop='tenantId' style="width:48%">
                    <el-select
                        v-model="formInline.tenantId"
                        placeholder="请选择"
                        clearable
                        @change="changeTenant"
                    >
                        <el-option :label="item.tenantName" :value="item.tenantId" v-for="item in userTenants" :key="item.tenantId" />
                    </el-select>
                </el-form-item>
                 <el-form-item label="学段" prop='stage' style="width:48%">
                    <el-select
                        v-model="formInline.stage"
                        placeholder="请选择"
                        clearable
                        @change="changeStage"
                    >
                        <el-option :label="item.title" :value="item.id" v-for="item in stages" :key="item.id" />
                        
                    </el-select>
                </el-form-item>
                <el-form-item label="备课组名称" prop='name' style="width:48%">
                    <el-input  v-model="formInline.name"  placeholder="请选择"></el-input>
                   
                </el-form-item>
                <el-form-item label="学科" prop='subjectId' style="width:48%">
                    <el-select
                        v-model="formInline.subjectId"
                        placeholder="请选择"
                        clearable
                    >
                       <el-option :label="item.label" :value="item.value" v-for="item in subjectOptions" :key="item.value"/>
                    </el-select>
                </el-form-item>
                <el-form-item label="年级" prop='gradeId' style="width:48%">
                    <el-select
                        v-model="formInline.gradeId"
                        placeholder="请选择"
                        clearable
                    >
                        <el-option :label="item.name" :value="item.id" v-for="item in gradeOptions" :key="item.id"/>
                        
                    </el-select>
                </el-form-item>
                <el-form-item label="备课组长" prop='leaderId' style="width:48%">
                    <el-select
                        v-model="formInline.leaderId"
                        placeholder="请选择"
                        clearable
                    >
                        <el-option :label="item.name" :value="item.id" v-for="item in memberOptions" :key="item.id"/>
                      
                    </el-select>
                </el-form-item>
                <el-form-item label="备课组人员" prop='memberIds' style="width:100%">
                    <el-select
                        v-model="formInline.memberIds"
                        placeholder="请选择"
                        clearable
                          multiple
                    >
                        <el-option :label="item.name" :value="item.id" v-for="item in memberOptions" :key="item.id"/>
                    </el-select>
                </el-form-item>
             </el-form>
            <template #footer>
            <div class="dialog-footer">
                <el-button @click="cancel">取消</el-button>
                <el-button type="primary" @click="confirm">
                确认
                </el-button>
            </div>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
import {onMounted, reactive, ref} from 'vue'
import {bkgroupList,memberList,gradeList,bkgroupDetail,bkgroupDel,bkgroupAdd,bkgroupEdit} from '@/api/pc/bk.js'
import {subjectList} from '@/api/pc/index.js'
import {useUserInfoStoreHook} from '@/store/modules/pc.js'
import {storeToRefs} from 'pinia'
import {useRouter} from 'vue-router'
import { ElMessage,ElMessageBox } from 'element-plus'
let userInfoStore=useUserInfoStoreHook()
let {userInfo,stages}=storeToRefs(userInfoStore)
let tableData=ref([{}])
let total=ref(0)
let form=reactive({
    pageSize:10,
    pageNum:1
})
let userTenants=ref([])
let dialogVisible=ref(false)
let formInline=reactive({
    tenantId:'',
    stage:'',
    name:'',
    subjectId:'',
    gradeId:'',
    leaderId:'',
    memberIds:[]
})
let groupId=ref('')
let formRef=ref()
let subjectOptions=ref([])
let gradeOptions=ref([])
let memberOptions=ref([])
let rules={
    tenantId:[{required: true,message: '请选择单位',trigger: 'change'}],
    stage:[{required: true,message: '请选择学段',trigger: 'change'}],
    name:[{required: true,message: '请输入组名',trigger: 'change'}],
    subjectId:[{required: true,message: '请选择学科',trigger: 'change'}],
    gradeId:[{required: true,message: '请选择年级',trigger: 'change'}],
    leaderId:[{required: true,message: '请选择备课组组长',trigger: 'change'}],
    memberIds:[{required: true,message: '请选择备课组成员',trigger: 'change'}],
}
let goDel=(row)=>{
        ElMessageBox.confirm(
            '确认删除备课组'+row.name+'吗?',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        .then(async() => {
            let res=await bkgroupDel({
                groupId:row.id
            })
            if(res){
               ElMessage.success('删除成功')
               getList()
            }
              
        })
        .catch(() => {
      
        })
}
let goEdit=async(row)=>{
    dialogVisible.value=true
    groupId.value=row.id
    let res=await bkgroupDetail({
        groupId:row.id
     })
     if(res){
        Object.assign(formInline,{
            tenantId:res.data.tenantId,
            stage:res.data.stage.toString(),
            name:res.data.name,
            subjectId:res.data.subjectId,
            gradeId:res.data.gradeId,
            leaderId:res.data.leaderId,
            memberIds:res.data.members.map(item=>item.id)
        })
        changeTenant('edit')
        changeStage()
     }
}
let changeTenant=async(type)=>{
      let res1=await memberList({
         tenantId:formInline.tenantId
    })
    if(res1){
       memberOptions.value=res1.data
    }
    let res=await gradeList({
        tenantId:formInline.tenantId
    })
    if(res){
       gradeOptions.value=res.data
       if(type!='edit'){
            Object.assign(formInline,{
                gradeId:'',
                memberIds:[]
        })
       }
     
    }
  
}
let currentChange=(val)=>{
    Object.assign(form,{
        pageNum:val
    })
    getList()
}
let getList=async()=>{
    let res=await bkgroupList(form)
    if(res){
        tableData.value=res.data.list
        total.value=res.data.total
    }
}
let changeStage=async()=>{
  let res=await subjectList({
    stage:formInline.stage
  })
  if(res){
       subjectOptions.value=res.data
  }
}
let confirm=async()=>{
    if (!formRef.value) return
  await formRef.value.validate(async(valid, fields) => {
    if (valid) {
        let params={
            tenantId:formInline.tenantId,
            stage:formInline.stage,
            name:formInline.name,
            subjectId:formInline.subjectId,
            gradeId:formInline.gradeId,
            leaderId:formInline.leaderId,
            memberIds:formInline.memberIds,
        }
        if(groupId.value){
            params.groupId=groupId.value
            let res=await bkgroupEdit(params)
            if(res){
                ElMessage.success('编辑成功')
                cancel()
                
                getList()
            }
        }else{
            let res=await bkgroupAdd(params)
            if(res){
                ElMessage.success('新增成功')
                cancel()
                Object.assign(form,{
                    pageNum:1
                })
                getList()
            }
        }
       
      
    } else {
      console.log('error submit!', fields)
    }
  })
}
let cancel=()=>{
  dialogVisible.value=false
  Object.assign(formInline,{
        tenantId:'',
        stage:'',
        name:'',
        subjectId:'',
        gradeId:'',
        leaderId:'',
        memberIds:[]
  })
  formRef.value.resetFields()
}
onMounted(()=>{
    userTenants.value=userInfo.value.userTenants.filter(item=>{
        return item.tenantType==50
    })
    getList()
})
</script>
<style scoped lang='scss'>
.content-box{
    background: #fff;
    padding: 24px;
    .top{
        .title{
            cursor: pointer;
           h3{
            font-size: 20px;
             color: #2B2C33;
           }
        }
        button{
            width: 132px;
            height: 40px;
            background: #508CFF;
            border-radius: 8px;
            border: 1px solid #508CFF;
            border:none;
            cursor: pointer;
            color: #FFFFFF;
            img{
                width: 16px;
                height: auto;
                margin-right: 8px;
            }
        }
    }
}
</style>
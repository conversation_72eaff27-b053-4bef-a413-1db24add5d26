import { computed } from "vue"
import { useAppStoreHook } from "@/store/modules/app"
import { DeviceEnum } from "@/constants/app-key"

const appStore = useAppStoreHook()
console.log("useDevice", appStore.device, DeviceEnum.Mobile)
const isMobile = computed(() => appStore.device === DeviceEnum.Mobile)
const isDesktop = computed(() => appStore.device === DeviceEnum.Desktop)

export function useDevice() {
  return { isMobile, isDesktop }
}

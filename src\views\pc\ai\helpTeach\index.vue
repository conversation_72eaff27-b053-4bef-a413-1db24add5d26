<template>
   <Header />
   <!--  -->
   <div class="mainer flex_center flex_column">
       <div class="chat-box" id="chat_list">
            <div class="example-box" v-show="messageList.length==0">
                 <img src="@/assets/pc/ai-header.png" alt="" />
                 <p class="desc">嗨，您好呀！我是您的AI助教，很高兴能与您交流。我的使命是为您提供最优质的教学资源，配合您完成教学任务。</p>
                 <p class="ask">你可以尝试这样问：</p>
                 <ul class="question-list">
                    <li class="question-item flex_start" v-for="(item,index) in questionList" :key="index" @click="choiceQuestion(item)">{{item.question}}</li>
                 </ul>
            </div>
            <div >
                <div :class="['answer-item',item.role=='user'?'flex_end':'']" v-for="(item,index) in messageList" :key="index">
                      <p class="me" v-if="item.role=='user'" v-html="item.content"></p>
                      <p class="ai" v-if='item.role=="assistant"'  v-loading.lock="!item.isComplete" :style="item.content?'':'background: none;padding:24px'" v-html="item.content"></p>
                </div>
            </div>
       </div>
       <div class="ipt-box ">
            <el-input placeholder="请您输入问题" v-model="content" type="textarea" rows="2" resize="none"></el-input>
            <div class="action-list flex_between">
                 <button class="clear flex_start" @click="goClear">
                    <img src="@/assets/pc/cleanall.png" alt="" />
                    <p>清空上下文</p>
                 </button>
                
                 <div class="flex_start send-way">
                     <p @click="stopRecording" class="stop-recording" v-show="isRecording">停止录音</p>
                     <el-tooltip
                        class="box-item"
                        effect="dark"
                        content="语音输入"
                        placement="top"
                    >   
                    <div>
                        <canvas id="recordingCanvas" class="recording-icon" v-show="isRecording"></canvas>
                        <img src="@/assets/pc/audio-say.png" alt="" class="audio" @click="startRecording" v-show="!isRecording"/>
                    </div>
                        
                    </el-tooltip>
                   
                    <img src="@/assets/pc/send.png" alt="" class="send" @click="sendChat" v-show="!isRecording">
                 </div>
            </div>
       </div>
   </div>
</template>

<script setup>
import {onMounted, ref,nextTick,onUnmounted} from 'vue'
import MarkdownIt from 'markdown-it';
import katex from 'markdown-it-katex';
import Header from '@/views/pc/component/ai/header.vue'
import { getToken } from "@/utils/cache/cookies"
import {createConversations,getExampleQuestion,getAllList,clearAll,audioToText} from '@/api/pc/ai.js'
import { ElMessage } from 'element-plus'
import { initAudioRecorder } from '@/utils/checkAudio'
// import lamejs from 'lamejs';
import RecordRTC from 'recordrtc'

let content=ref('')
let fullscreenLoading=ref(true)
let exampleVisible=ref(true)
let questionList=ref([])
let messageList=ref([])
let conversation=ref('') //会话id
let abortController='' //用于终止请求
let canSend=ref(true)
//语音录制
const isRecording = ref(false)
const recorder = ref(null)
const stream = ref(null)
let  audioBlob=ref(null)
let seconds=ref(0)
let interval=null
const md = new MarkdownIt({
  html: true,          // 允许使用 HTML 标签
  linkify: true,       // 自动识别 URL
  typographer: true    // 智能标点符号转换
}).use(katex, {
    throwOnError: false, // 遇到错误时不抛出异常
    errorColor: '#cc0000',
    // 可选：启用显示模式（块级公式）
    displayMode: true
  });

const initRecorder = async () => {
  if(!initAudioRecorder()){
    return
  }
  try {
    // 获取麦克风权限
    stream.value = await navigator.mediaDevices.getUserMedia({ 
      audio: true 
    })
    
    // 创建 MediaRecorder
    recorder.value = new RecordRTC(stream.value, {
      type: 'audio',
      mimeType: 'audio/wav',
      recorderType: RecordRTC.StereoAudioRecorder,
      numberOfAudioChannels: 1, // 单声道
      desiredSampRate: 44100,   // 采样率
      bitRate: 128             // 比特率
    })
    
    isRecording.value = true
    recorder.value.startRecording()
    interval=setInterval(()=>{
        seconds.value++
    },1000)
    
    console.log('录音初始化成功')
  } catch (error) {
    console.error('初始化录音失败:', error)
  }
}
// 开始录音
const startRecording = () => {
   initRecorder()
}
// 停止录音
const stopRecording = () => {
  if (!recorder.value) return
  
  recorder.value.stopRecording(async() => {
    // 获取录音文件
    const blob = recorder.value.getBlob()
    audioBlob.value = new File([blob], `recording-${Date.now()}.wav`, {
      type: 'audio/wav',
      lastModified: new Date().getTime()
    })
     isRecording.value = false
     clearInterval(interval)
     if(seconds.value>60){
        seconds.value=0
        ElMessage.warning('录音时间过长，请重新录音')
        return
     }
     seconds.value=0
     let res=await audioToText({
        file:audioBlob.value,
        voiceFormat:'wav',
        dataLen:audioBlob.value.size
    })
      
    if(res){
        console.log(res,'res')
        content.value=res.data
        sendChat()
    }
   
  })
}
// 清理资源
const cleanup = () => {
 if (stream.value) {
    stream.value.getTracks().forEach(track => track.stop())
  }
  if (recorder.value) {
    recorder.value.destroy()
  }
}



// const convertAudioBlobToFile =async (audioBlob) => {
//    convertTowav(audioBlob).then(async(data)=>{
//     console.log(data,'data')
//    }) 
// }
 


let choiceQuestion=(item)=>{
    exampleVisible.value=false
    content.value=item.question
    sendChat()
}
let init=async()=>{
    let res=await createConversations()
    if(res){
        // console.log(res.data)
        conversation.value=res.data
        getContext()
        localStorage.setItem('conversation',conversation.value)
    }
}
let goClear=async()=>{
  let res=await clearAll({
    id:conversation.value
  })
  if(res){
    ElMessage.success("清除成功")
    localStorage.removeItem('conversation')   
     messageList.value=[]
    init()
  }
}
let getContext=async()=>{
    let res=await getAllList({
        id:conversation.value
    })
    if(res){ 
        if(res.data.length>0){
            exampleVisible.value=false
        }else{
            exampleVisible.value=true
        }
      res.data.forEach(item=>{
          messageList.value.push({
            role:item.messageType=='user'?'user':'assistant',
            content:md.render(item.messageText),
            content_type: 'text',
            reasoning_content:'',
            isComplete: true
          })
      })
      scrollBottom()
    }
}
let sendChatQuestion=async()=>{
        canSend.value=false

        let data={
            teachingConversationsId:conversation.value,
            content:content.value
        }
    	  abortController = new AbortController();
		    const signal = abortController.signal;
         let token=getToken()
         content.value=''
       fetch(
			import.meta.env.VITE_BASE_URL + `/ai/conversations/teaching/chat`,
			{
				method: "POST",
				headers: {
                    'Accept': 'text/event-stream',
					"Content-Type": "application/json",
					"X-Haian-Platform-Member-Token": token ? `Bearer ${token}` : undefined,
				},
				mode: "cors",
				body: JSON.stringify(data),
				signal: signal,
               
			}
		)
			.then(async (response) => {
               
				processStream(response);
			})
			.catch((error) => { });
}
let sendChat=()=>{
    
    if(!canSend.value) return
    if(!content.value) return
      messageList.value.push({
			role: "user",
			content:content.value,
			content_type: 'text',
			reasoning_content:'',
            isComplete: true
		});
		messageList.value.push({
			role: "assistant",
			content: "",
			content_type: 'text',
			reasoning_content: '',
			isComplete: false
		});
        scrollBottom();
        sendChatQuestion()
}
function scrollBottom() {
		nextTick(() => {
			let  container= document.getElementById("chat_list");
            // console.log(container.scrollTop)
        //    console.log(scrollWrap.scrollHeight)
			container.scrollTo({
				top: container.scrollHeight,
				behavior: "instant", //auto-自动滚动 instant-瞬间滚动 smooth-平滑滚动
			});
        
		});
}
async function processStream(response) {
		const reader = response.body.getReader();
		const decoder = new TextDecoder("utf-8");
		let buffer = "";
		// let num = 0
		let content = ''
		// let imgList = ''
		while (true) {
			const { done, value } = await reader.read();
			if (done) {
				// 流结束
				// is_send.value = true
				// if (imgList) {
				// 	messageList.value.push({
				// 		role: "assistant",
				// 		content: JSON.parse(imgList),
				// 		content_type: 'object_string',
				// 		reasoning_content: '',
				// 		isComplete: true,
				// 		isMore: true
				// 	});
				// }
                canSend.value=true
                messageList.value[messageList.value.length - 1].isComplete=true
				setTimeout(() => {
					scrollBottom();
				}, 200);
				// getChatRecord()
				break;
			}
			const chunk = decoder.decode(value, { stream: true });
            // console.log(chunk)
			buffer += chunk;
			const lines = buffer.split("\n");
			buffer = lines.pop(); // 保留最后一行，因为它可能是不完整的
			lines.forEach((line, index) => {
				if (index + 1 < lines.length&&line.length>0) {
					const data = line.substring(5).trim(); // 去掉'data:'前缀和任何前导空格
					// 尝试解析JSON数据
					try {
                        //  console.log(data,'data') 
						const message = JSON.parse(data);
						// chatId.value = message.chat_id
						if (message.content) {
							  content += message.content
								messageList.value[messageList.value.length - 1].content=md.render(content)
								// if (num > 0) {
								// 	messageList.value[messageList.value.length - 1].isComplete = true
								// }
						}
						
						// 
                        scrollBottom();
					} catch (error) {
						console.error("Error parsing JSON:", error);
					}
				}
			});
		}
	}
let getQuestion=async()=>{
    let res=await getExampleQuestion()
    if(res){
        
        questionList.value=res.data
    }
}
let initCanvas=()=>{
       const canvas = document.getElementById('recordingCanvas');
       const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        canvas.width = 60;
        canvas.height = 30;
        
        // 音频条的数量和配置
        const barCount = 5;
        const barWidth = 4;
        const barGap = 4;
        const maxHeight = 20;
        
        // 平滑动画版本
        let barHeights = new Array(barCount).fill(0);
        let targetHeights = new Array(barCount).fill(0);
        
        function drawAudioIcon() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制录制指示器
            ctx.beginPath();
            ctx.arc(10, 15, 5, 0, Math.PI * 2);
            ctx.fillStyle = 'red';
            ctx.fill();
            
            // 更新目标高度
            for (let i = 0; i < barCount; i++) {
                if (Math.random() < 0.1) {  // 10%的概率更新目标高度
                    targetHeights[i] = Math.random() * maxHeight;
                }
                
                // 平滑过渡到目标高度
                barHeights[i] += (targetHeights[i] - barHeights[i]) * 0.1;
                
                const x = 20 + i * (barWidth + barGap);
                const y = (canvas.height - barHeights[i]) / 2;
                
                ctx.fillStyle = '#333';
                ctx.fillRect(x, y, barWidth, barHeights[i]);
            }
            
            requestAnimationFrame(drawAudioIcon);
        }
        
        // 开始动画
        drawAudioIcon();
}
onUnmounted(() => {
  cleanup()
})
onMounted(()=>{
    initCanvas()
   
    getQuestion()
      if(localStorage.getItem('conversation')){
        conversation.value=localStorage.getItem('conversation')
        // sendChatQuestion()
        getContext()
      }else{
         init()
      }
})
</script>   

<style lang="scss" scoped>
.mainer{
    height: calc(100vh - 64px);
    background: url(@/assets/pc/ai-bg.png) no-repeat;
    background-size: cover;
     .recording-icon {
            width: 60px;
            height: 30px;
            background: #f0f0f0;
            border-radius: 15px;
            padding: 5px;
            margin-right: 12px;
        }
    .chat-box{
       height: calc(100vh - 200px);
       width: 94%;
       max-width: 754px;
       overflow: auto;
       
       
    //    background: #fff;
       margin: 32px 0 10px 0;
       .example-box{
          padding: 16px 24px;
          background: #fff;
          border-radius: 8px;
          img{
            width: 54px;
            height: auto;
          }
          .desc{
            font-size: 16px;
           color: #2B2C33;
           margin-top: 12px;
           font-weight: bold;
          }
          .ask{
            margin-top: 32px;
            font-size: 16px;
            color: #2B2C33;
            font-weight: bold;
          }
          .question-list{
            .question-item{
                font-size: 16px;
                color: #508CFF;
               margin-top: 12px;
               cursor: pointer;
               &::before{
                content:'';
                display:block;
                width: 4px;
                  background: #508CFF;
                height: 4px;
                border-radius: 20px;
                margin-right: 6px;

               }
            }
          }
       }
       .answer-item{
            margin-bottom: 24px;
            &:last-child{
               
                margin-bottom: 0;
            }
            :deep(h3){
                margin: 8px  0;
            }
            .me{
                background: #508CFF;
                border-radius: 8px 0px 8px 8px;
                color: #fff;
                padding: 12px 16px;
                display: inline-block;
                font-size: 16px;
            }
            .ai{
                background: #FFFFFF;
                border-radius: 0px 8px 8px 8px;
                color: #2B2C33;
                font-size: 16px;
                 display: inline-block;
                    padding: 12px 16px;
            }
       }
    }
    .ipt-box{
        width: 94%;
        max-width: 754px;
        background: #FFFFFF;
        border-radius: 12px;
        border: 1px solid #508CFF;
        overflow: hidden;
        padding: 12px 16px;
        :deep(.el-textarea__inner){
           box-shadow: none;
           padding: 0;
        }
        .action-list{
            button{
                border-radius: 14px;
                border: 1px solid #E2E3E6;
                font-size: 14px;
                color: #C2C9CE;
                line-height: 20px;
                padding: 4px 8px;
                cursor: pointer;
                background: #fff;
               img{
                width: 12px;
                height: auto;
                margin-right: 2px;
               }
            }
            .send-way{
                cursor: pointer;
                .stop-recording{
                    font-size: 14px;
                    cursor: pointer;
                    color: #508CFF;
                    margin-right: 12px;
                }
               .audio{
                  width: 13px;
                  height: auto;
                  margin-right: 16px;
               }
               .send{
                   width: 20px;
                   height: auto;
               }
            }
        }
    }
}
:deep(.el-loading-mask){
    background: none;
    .el-loading-spinner .circular{
        width: 32px;
        height: 32px;
    }
}
</style>
<template>
    <el-form :model="add_form" inline ref="addRef" :rules="rules" class="search_form add-form mt_24">
        <el-form-item label="课题名称" prop="taskTitle" style="margin-right: 40px;">
            <el-input v-model="add_form.taskTitle" class="input_48" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="是否青年专项" style="margin-right: 0;">
            <el-radio-group v-model="add_form.canReplay" class="ml-4" :disabled="route.params.id != 0">
                <el-radio label="是" :value="true" size="large"></el-radio>
                <el-radio label="否" :value="false" size="large"></el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="姓名" prop="taskTitle" style="margin-right: 40px;">
            <el-input v-model="add_form.taskTitle" class="input_48" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="性别" style="margin-right: 0;">
            <el-radio-group v-model="add_form.canReplay" class="ml-4" :disabled="route.params.id != 0">
                <el-radio label="男" :value="true" size="large"></el-radio>
                <el-radio label="女" :value="false" size="large"></el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="出生年月" style="margin-right: 40px;">
            <el-date-picker v-model="add_form.recordDate" class="my-date-picker" type="date" value-format="YYYY-MM" placeholder="请选择"> </el-date-picker>
        </el-form-item>
        <el-form-item label="学段" style="margin-right: 0;">
            <el-select class="select_48" v-model="add_form.sch" placeholder="请选择" @change="changeExtend">
                <el-option v-for="item in stage_list" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="学科" style="margin-right: 40px;">
            <el-select class="select_48" v-model="add_form.sch" placeholder="请选择" @change="changeExtend">
                <el-option v-for="item in subject_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="联系电话" style="margin-right: 0;">
            <el-input v-model="add_form.taskContent" class="input_48" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="学历" style="margin-right: 40px;">
            <el-select class="select_48" v-model="add_form.sch" placeholder="请选择" @change="changeExtend">
                <el-option v-for="item in subject_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="职称" style="margin-right: 0;">
            <el-select class="select_48" v-model="add_form.sch" placeholder="请选择" @change="changeExtend">
                <el-option v-for="item in subject_select" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="专业称号" style="margin-right: 40px;">
            <el-input v-model="add_form.taskContent" class="input_48" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="工作单位" style="margin-right: 0;">
            <el-input v-model="add_form.taskContent" class="input_48" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="电子邮件" style="margin-right: 40px;">
            <el-input v-model="add_form.taskContent" class="input_48" placeholder="请输入"></el-input>
        </el-form-item>
    </el-form>
</template>
<script setup>
    import { reactive,ref,onBeforeMount,onMounted} from 'vue'
    import { useRouter, useRoute } from 'vue-router'
    const router = useRouter()
    const route = useRoute()
    const formObj = function(obj = {}){
        this.taskTitle = obj.taskTitle || ''
        this.taskContent = obj.taskContent || ''
        this.reviewSections = obj.reviewSections || []
    }
    const add_form = reactive(new formObj({}))
    const rules = ref({ 
        taskTitle:[{required: true,message: '请输入课题名称',trigger: 'blur'}],
        reviewSections:[{required: true,message: '请上传材料清单',trigger: 'blur'}]
    })
    onMounted(() => {
    })
</script>
<style lang="scss" scoped>
.add-form{
    :deep(.el-form-item){width: calc(50% - 40px);display: inline-block!important;}
}
</style>
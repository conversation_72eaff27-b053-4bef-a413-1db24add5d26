<script setup>
import { ref, onMounted, onBeforeUnmount, computed, onActivated } from "vue"
import WangE<PERSON><PERSON> from "@/components/WangEditor/index.vue"
import { saveMyDraft<PERSON>pi, getMyDraft<PERSON>pi, submitDiary<PERSON>pi } from "@/api/mobile/memberOperation"
import { getDetail<PERSON>pi, editDiary<PERSON>pi } from "@/api/mobile/personalSpace"
import { getTopicListApi } from "@/api/mobile/topic"
import { ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { showToast, showSuccessToast } from "vant";
import { useUserStore } from "@/store/modules/user"

const userStore = useUserStore()
const route = useRoute()
const router = useRouter()


// 添加计算属性来判断用户角色
const isDirector = computed(() => userStore.roles?.includes("DIRECTOR"))
const isViceDirector = computed(() => userStore.roles?.includes("VICE_DIRECTOR"))
const isMember = computed(() => userStore.roles?.includes("MEMBER"))
const isTutor = computed(() => userStore.roles?.includes("TUTOR"))

const getDetail = async () => {
  try {
    const res = await getDetailApi(route.query.id)
    if (res) {
      addForm.value.title = res.data?.title || ""
      addForm.value.topic = res.data?.topics?.map((item) => item.id) || []
      addForm.value.content = res.data?.content || ""
      addForm.value.chooseClockingTime = res.data?.recordDate?.split("-") || []
      addForm.value.clockingTime = res.data?.recordDate || ""
    }
  } catch (err) {
    console.log(err)
  }
}

const addForm = ref({
  title: "",
  topic: [],
  clockingTime: "",
  chooseClockingTime: [],
  content: ""
})

const topicList = ref([])

// 获取主题列表
const getTopicList = async () => {
  try {
    const res = await getTopicListApi()
    if (res) {
      topicList.value = res.data
    }
  } catch (error) {
    console.log(error)
  }
}

const chooseTopic = (id) => {
  if (addForm.value.topic.includes(id)) {
    addForm.value.topic = []
  } else {
    addForm.value.topic = [id]
  }
}

const showClockingTimePicker = ref(false)
const minDate = ref()
const maxDate = ref()
const openClockingTimePicker = () => {
  // 获取当前年月日
  const currentDate = new Date()
  const year = currentDate.getFullYear()
  const month = currentDate.getMonth()
  const day = currentDate.getDate()

  addForm.value.chooseClockingTime = [year, month + 1, day]
  maxDate.value = new Date(year, month, day)

  showClockingTimePicker.value = true
}

const confirmClockingTime = ({ selectedValues }) => {
  addForm.value.chooseClockingTime = selectedValues
  addForm.value.clockingTime = selectedValues?.join("-")

  showClockingTimePicker.value = false
}

const cancelClockingTime = () => {
  showClockingTimePicker.value = false
}

// 提交日记
const submitDiary = async () => {
  try {
    if (addForm.value.topic.length === 0 && !isDirector.value && !isViceDirector.value) {
      showToast('请选择主题')
      return
    }

    if (!addForm.value.clockingTime) {
      showToast('请选择时间')
      return
    }

    if (!addForm.value.content || addForm.value.content === '<p><br></p>') {
      showToast('请输入日记内容')
      return
    }

    let res
    if (!route.query.id) {
      res = await submitDiaryApi({
        title: addForm.value.title,
        content: addForm.value.content,
        clubTopicIds: addForm.value.topic.length ? addForm.value.topic : [],
        recordDate: addForm.value.clockingTime
      })
    } else {
      res = await editDiaryApi(route.query.id, {
        title: addForm.value.title,
        content: addForm.value.content,
        clubTopicIds: addForm.value.topic,
        recordDate: addForm.value.clockingTime
      })
    }

    if (res) {
      showToast('提交成功' + (res.actionPoint ? ` ${res.actionPoint}步` : ""))
      router.replace({
        path: '/personalSpace',
        query: {
          from: 'add'
        }
      })
    }
  } catch (error) {
    console.log(error)
  }
}

const initializeData = () => {
  addForm.value.title = ""
  addForm.value.topic = []
  addForm.value.clockingTime = ""
  addForm.value.content = ""
  addForm.value.chooseClockingTime = []

  getTopicList()
  if (route.query.id) {
    getDetail()
  }
}

// onMounted(() => {
//   initializeData()
// })


onActivated(() => {
  initializeData()
})
</script>

<template>
  <div class="add-diary-page">
    <div class="back-area m-pt10 m-pb10 flex-between-center">
      <div class="flex-start-center pointer" @click="router.back()">
        <van-icon name="arrow-left" size="14" />
        <span class="m-text-14-20-400 grey1">返回</span>
      </div>
      <div class="m-text-16-24-400 dark1">上传日记</div>
      <div style="width: 42px;"></div>
    </div>
    <div class="add-form">
      <div class="form-item clocking-time flex-start-center">
        <div class="label">日记标题</div>
        <div class="flex-start-center">
          <div class="clocking-time-item">
            <van-field v-model="addForm.title" placeholder="请输入日记标题（选填）" maxlength="100" />
          </div>
        </div>
      </div>
      <div class="form-item choose-topic flex-start-start m-pt20" v-if="!isDirector && !isViceDirector">
        <div class="label">选择主题</div>
        <div class="topic-list flex-start-center flex-wrap flex-1">
          <div class="topic-item m-mb5" v-for="item in topicList" :key="item.id"
            :class="{ active: addForm.topic.includes(item.id) }" @click="chooseTopic(item.id)">
            <div class="flex-start-center">
              <img v-if="addForm.topic.includes(item.id)" src="@/assets/mobile/personal-space/label-active.png" alt=""
                width="12" height="12" />
              <img v-else src="@/assets/mobile/personal-space/label.png" alt="" width="12" height="12" />
              <span class="topic-item-name m-text-12-18-400 m-pl6">{{ item.name }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="form-item clocking-time flex-start-center m-pt20">
        <div class="label">发布日期</div>
        <div class="flex-start-center">
          <div class="clocking-time-item">
            <van-field v-model="addForm.clockingTime" placeholder="请点击选择" @click="openClockingTimePicker" readonly />
          </div>

          <van-popup v-model:show="showClockingTimePicker" position="bottom">
            <van-date-picker v-model="addForm.chooseClockingTime" title="选择日期" :min-date="minDate" :max-date="maxDate"
              @confirm="confirmClockingTime" @cancel="cancelClockingTime" />
          </van-popup>
        </div>
      </div>
      <div class="form-item editor-container flex-column-start-start m-pt20">
        <div class="label">日记内容</div>
        <div class="editor-content">
          <WangEditor v-model="addForm.content" />
        </div>
      </div>
      <div class="m-btn-container flex-start-center m-pt20">
        <!-- <van-button class="m-save-btn" @click="saveMyDraft">保存</van-button> -->
        <van-button class="m-submit-btn" @click="submitDiary">提交</van-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "@/views/mobile/styles/custom-global-mobile-style.scss";

.add-diary-page {
  position: relative;
  width: 100%;
  height: 100%;
  padding-top: 44px;

  .back-area {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    background-color: #f5f6fa;
  }

  .add-form {
    background-color: #fff;
    padding: 20px 15px;
    overflow-y: auto;
  }

  .form-item {
    .label {
      font-weight: 400;
      font-size: 14px;
      color: #2b2c33;
      line-height: 24px;
      margin-right: 27px;
    }
  }

  .choose-topic {

    .topic-list {
      max-height: 150px;
      overflow-y: auto;
    }

    .topic-item {
      padding: 3px 10px;
      height: 24px;
      border-radius: 19px;
      margin-right: 10px;
      border: 1px solid #e2e3e6;

      &.active {
        background-color: #508cff;
        border-color: #508cff;
        color: #fff;
      }
    }
  }

  .clocking-time {
    :deep(.van-field) {
      padding: 0;
    }
  }
}
</style>

<template>
    <div class="item" @click="goDetail">
        <div class="cover">
            <div class="common-img doc flex_center" >
                <img :src="row.url" alt="" v-if="row.url"/>
                <img src="@/assets/pc/emptyvideo.png" alt="" v-else />
            </div>
           
        </div>
        <div class="desc">
              <h4>{{row.name}}</h4>
              <div class="flex_start read">
                  <div class="flex_start like">
                     <img src="@/assets/pc/r-comment.png" alt="" />
                     <p>{{row.favorites}}</p>
                  </div>
                   <div class="flex_start play">
                     <img src="@/assets/pc/play.png" alt="" />
                     <p>{{row.views}}</p>
                  </div>
              </div>
        </div>
    </div>
</template>
<script setup>
import {ref,defineProps}from 'vue'
import {useRouter,useRoute} from 'vue-router'
let type=ref('video')
let router=useRouter()
let route=useRoute()
let props=defineProps(['row'])
let goDetail=()=>{
    if(route.path=='/resource/wisdom/index'){
       router.push('/resource/wisdom/detail?id='+props.row.resourceId)
    }else{
       router.push('/resource/teach/preview?id='+props.row.id)
    }
 
}
</script>
<style lang="scss" scoped>
.item{
    background: #FFFFFF;
    cursor: pointer;
    border-radius: 12px;
   &:hover{
        box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.12);
        transform: translate(0, -5px);
        transition-delay: 0 !important;
        transition: all 300ms;
    }
    .cover{
      
       .common-img{
          border-top-left-radius: 12px;
          border-top-right-radius: 12px;
          height: 126px;
          img{
            width: 100%;
            height: 126px;
             border-top-left-radius: 12px;
          border-top-right-radius: 12px;
          }
          .cover-img{
            border-top-left-radius: 12px;
          border-top-right-radius: 12px;
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
       }
       .doc{
          background: #DCECFE;
       }
       .ppt{
          background: #FFEAE3;
       }
       .pdf{
           background: #FFE6E5;
       }
       .excel{
            background: #D5F5E9;
       }
       .other{
        background: #E9ECFF;
       }
       .audio{
        background: #EEE9FF;
       }
    }
    .desc{
      padding: 10px;
      h4{
        font-size: 14px;
        color: #2B2C33;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .read{
        margin-top: 8px;
        p{
            font-size: 12px;
           color: #94959C;
           line-height: 12px;
           margin-left: 4px;
        }
        .like{
            margin-right: 16px;
        }
        .like img{
            width: 12px;
            height: auto;    
        }
         .play img{
            width: 12px;
            height: auto;    
        }
      }
    }
}
</style>